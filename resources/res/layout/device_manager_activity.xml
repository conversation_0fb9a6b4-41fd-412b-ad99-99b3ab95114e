<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:background="@mipmap/bg_app" android:layout_width="match_parent" android:layout_height="match_parent">
    <RelativeLayout android:id="@+id/rly_bs_title" android:background="@mipmap/tab_navi_bars_bg_short" android:layout_width="match_parent" android:layout_height="@dimen/dp114" android:layout_marginStart="@dimen/dp166">
        <ImageView android:id="@+id/iv_bt_back" android:layout_width="@dimen/dp127" android:layout_height="match_parent" android:src="@mipmap/icon_btsetting_back" android:scaleType="center" android:contentDescription="iv_bt_back"/>
        <ImageView android:id="@+id/iv_bt_scan" android:visibility="gone" android:layout_width="@dimen/dp72" android:layout_height="@dimen/dp72" android:src="@mipmap/icon_btsetting_scan" android:layout_centerVertical="true" android:contentDescription="iv_bt_scan" android:layout_marginEnd="@dimen/dp100" android:layout_alignParentEnd="true"/>
        <ImageView android:id="@+id/pb_scan" android:visibility="gone" android:layout_width="@dimen/dp72" android:layout_height="@dimen/dp72" android:src="@mipmap/icon_small_circular" android:layout_centerVertical="true" android:layout_marginEnd="@dimen/dp100" android:layout_alignParentEnd="true"/>
        <com.yfve.ici.appcustomviewlib.textview.CommonTextView android:textSize="@dimen/sp38" android:textColor="@color/color_ffffff" android:gravity="center" android:id="@+id/tab_paired" android:layout_width="@dimen/dp350" android:layout_height="match_parent" android:text="@string/text_tab_paired" android:layout_toRightOf="@+id/iv_bt_back" android:contentDescription="我的电话" app:typeface="sans"/>
        <ImageView android:id="@+id/iv_paired_selected" android:layout_width="@dimen/dp350" android:layout_height="wrap_content" android:src="@mipmap/img_selected_tab_bg" android:layout_toRightOf="@+id/iv_bt_back" android:layout_alignParentBottom="true" android:layout_centerHorizontal="true" android:contentDescription="iv_paired_selected"/>
        <com.yfve.ici.appcustomviewlib.textview.CommonTextView android:textSize="@dimen/sp38" android:textColor="@color/color_ffffff" android:gravity="center" android:id="@+id/tab_discover" android:layout_width="@dimen/dp350" android:layout_height="match_parent" android:text="@string/text_tab_discovered" android:layout_toRightOf="@+id/tab_paired" android:contentDescription="可用电话" app:typeface="sans"/>
        <ImageView android:id="@+id/iv_discover_selected" android:layout_width="@dimen/dp350" android:layout_height="wrap_content" android:src="@mipmap/img_selected_tab_bg" android:layout_toRightOf="@+id/tab_paired" android:layout_alignParentBottom="true" android:layout_centerHorizontal="true" android:contentDescription="iv_discover_selected"/>
        <ImageView android:layout_width="match_parent" android:layout_height="wrap_content" android:src="@mipmap/icon_connectivity_top_table_libe" android:layout_alignParentBottom="true"/>
    </RelativeLayout>
    <ImageView android:id="@+id/iv_bt_add" android:layout_width="wrap_content" android:layout_height="@dimen/dp520" android:layout_marginTop="@dimen/dp40" android:src="@drawable/bt_add_selector" android:layout_below="@+id/rly_bs_title" android:contentDescription="iv_bt_add" android:layout_marginStart="@dimen/dp196"/>
    <FrameLayout android:id="@+id/fl_paired" android:layout_width="match_parent" android:layout_height="match_parent" android:layout_marginTop="@dimen/dp40" android:layout_below="@+id/rly_bs_title" android:contentDescription="fl_paired" android:layout_toEndOf="@+id/iv_bt_add"/>
    <FrameLayout android:id="@+id/fl_discover" android:visibility="gone" android:layout_width="match_parent" android:layout_height="match_parent" android:layout_marginTop="@dimen/dp40" android:layout_below="@+id/rly_bs_title" android:contentDescription="fl_discover" android:layout_toEndOf="@+id/iv_bt_add"/>
    <FrameLayout android:id="@+id/fl_countdown" android:layout_width="match_parent" android:layout_height="match_parent" android:contentDescription="fl_countdown"/>
</RelativeLayout>
