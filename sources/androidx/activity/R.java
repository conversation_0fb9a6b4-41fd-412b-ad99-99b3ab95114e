package androidx.activity;

/* loaded from: classes.dex */
public final class R {

    /* loaded from: classes.dex */
    public static final class attr {
        public static final int alpha = 0x7f030027;
        public static final int font = 0x7f030087;
        public static final int fontProviderAuthority = 0x7f030089;
        public static final int fontProviderCerts = 0x7f03008a;
        public static final int fontProviderFetchStrategy = 0x7f03008b;
        public static final int fontProviderFetchTimeout = 0x7f03008c;
        public static final int fontProviderPackage = 0x7f03008d;
        public static final int fontProviderQuery = 0x7f03008e;
        public static final int fontStyle = 0x7f03008f;
        public static final int fontVariationSettings = 0x7f030090;
        public static final int fontWeight = 0x7f030091;
        public static final int ttcIndex = 0x7f030164;
    }

    /* loaded from: classes.dex */
    public static final class color {
        public static final int notification_action_color_filter = 0x7f05008b;
        public static final int notification_icon_bg_color = 0x7f05008c;
        public static final int ripple_material_light = 0x7f050097;
        public static final int secondary_text_default_material_light = 0x7f050099;
    }

    /* loaded from: classes.dex */
    public static final class dimen {
        public static final int compat_button_inset_horizontal_material = 0x7f06005a;
        public static final int compat_button_inset_vertical_material = 0x7f06005b;
        public static final int compat_button_padding_horizontal_material = 0x7f06005c;
        public static final int compat_button_padding_vertical_material = 0x7f06005d;
        public static final int compat_control_corner_material = 0x7f06005e;
        public static final int compat_notification_large_icon_max_height = 0x7f06005f;
        public static final int compat_notification_large_icon_max_width = 0x7f060060;
        public static final int notification_action_icon_size = 0x7f06045f;
        public static final int notification_action_text_size = 0x7f060460;
        public static final int notification_big_circle_margin = 0x7f060461;
        public static final int notification_content_margin_start = 0x7f060462;
        public static final int notification_large_icon_height = 0x7f060463;
        public static final int notification_large_icon_width = 0x7f060464;
        public static final int notification_main_column_padding_top = 0x7f060465;
        public static final int notification_media_narrow_margin = 0x7f060466;
        public static final int notification_right_icon_size = 0x7f060467;
        public static final int notification_right_side_padding_top = 0x7f060468;
        public static final int notification_small_icon_background_padding = 0x7f060469;
        public static final int notification_small_icon_size_as_large = 0x7f06046a;
        public static final int notification_subtext_size = 0x7f06046b;
        public static final int notification_top_pad = 0x7f06046c;
        public static final int notification_top_pad_large_text = 0x7f06046d;
    }

    /* loaded from: classes.dex */
    public static final class drawable {
        public static final int notification_action_background = 0x7f07009a;
        public static final int notification_bg = 0x7f07009b;
        public static final int notification_bg_low = 0x7f07009c;
        public static final int notification_bg_low_normal = 0x7f07009d;
        public static final int notification_bg_low_pressed = 0x7f07009e;
        public static final int notification_bg_normal = 0x7f07009f;
        public static final int notification_bg_normal_pressed = 0x7f0700a0;
        public static final int notification_icon_background = 0x7f0700a1;
        public static final int notification_template_icon_bg = 0x7f0700a2;
        public static final int notification_template_icon_low_bg = 0x7f0700a3;
        public static final int notification_tile_bg = 0x7f0700a4;
        public static final int notify_panel_notification_icon_bg = 0x7f0700a5;
    }

    /* loaded from: classes.dex */
    public static final class id {
        public static final int accessibility_action_clickable_span = 0x7f090006;
        public static final int accessibility_custom_action_0 = 0x7f090007;
        public static final int accessibility_custom_action_1 = 0x7f090008;
        public static final int accessibility_custom_action_10 = 0x7f090009;
        public static final int accessibility_custom_action_11 = 0x7f09000a;
        public static final int accessibility_custom_action_12 = 0x7f09000b;
        public static final int accessibility_custom_action_13 = 0x7f09000c;
        public static final int accessibility_custom_action_14 = 0x7f09000d;
        public static final int accessibility_custom_action_15 = 0x7f09000e;
        public static final int accessibility_custom_action_16 = 0x7f09000f;
        public static final int accessibility_custom_action_17 = 0x7f090010;
        public static final int accessibility_custom_action_18 = 0x7f090011;
        public static final int accessibility_custom_action_19 = 0x7f090012;
        public static final int accessibility_custom_action_2 = 0x7f090013;
        public static final int accessibility_custom_action_20 = 0x7f090014;
        public static final int accessibility_custom_action_21 = 0x7f090015;
        public static final int accessibility_custom_action_22 = 0x7f090016;
        public static final int accessibility_custom_action_23 = 0x7f090017;
        public static final int accessibility_custom_action_24 = 0x7f090018;
        public static final int accessibility_custom_action_25 = 0x7f090019;
        public static final int accessibility_custom_action_26 = 0x7f09001a;
        public static final int accessibility_custom_action_27 = 0x7f09001b;
        public static final int accessibility_custom_action_28 = 0x7f09001c;
        public static final int accessibility_custom_action_29 = 0x7f09001d;
        public static final int accessibility_custom_action_3 = 0x7f09001e;
        public static final int accessibility_custom_action_30 = 0x7f09001f;
        public static final int accessibility_custom_action_31 = 0x7f090020;
        public static final int accessibility_custom_action_4 = 0x7f090021;
        public static final int accessibility_custom_action_5 = 0x7f090022;
        public static final int accessibility_custom_action_6 = 0x7f090023;
        public static final int accessibility_custom_action_7 = 0x7f090024;
        public static final int accessibility_custom_action_8 = 0x7f090025;
        public static final int accessibility_custom_action_9 = 0x7f090026;
        public static final int action_container = 0x7f09002e;
        public static final int action_divider = 0x7f090030;
        public static final int action_image = 0x7f090031;
        public static final int action_text = 0x7f090037;
        public static final int actions = 0x7f090038;
        public static final int async = 0x7f09003e;
        public static final int blocking = 0x7f090041;
        public static final int chronometer = 0x7f09004d;
        public static final int dialog_button = 0x7f09006f;
        public static final int forever = 0x7f09007f;
        public static final int icon = 0x7f090089;
        public static final int icon_group = 0x7f09008a;
        public static final int info = 0x7f0900c0;
        public static final int italic = 0x7f0900c2;
        public static final int line1 = 0x7f0900d9;
        public static final int line3 = 0x7f0900da;
        public static final int normal = 0x7f0900ea;
        public static final int notification_background = 0x7f0900eb;
        public static final int notification_main_column = 0x7f0900ec;
        public static final int notification_main_column_container = 0x7f0900ed;
        public static final int right_icon = 0x7f090100;
        public static final int right_side = 0x7f090101;
        public static final int tag_accessibility_actions = 0x7f090130;
        public static final int tag_accessibility_clickable_spans = 0x7f090131;
        public static final int tag_accessibility_heading = 0x7f090132;
        public static final int tag_accessibility_pane_title = 0x7f090133;
        public static final int tag_screen_reader_focusable = 0x7f090134;
        public static final int tag_transition_group = 0x7f090135;
        public static final int tag_unhandled_key_event_manager = 0x7f090136;
        public static final int tag_unhandled_key_listeners = 0x7f090137;
        public static final int text = 0x7f090138;
        public static final int text2 = 0x7f090139;
        public static final int time = 0x7f09013c;
        public static final int title = 0x7f09013d;
    }

    /* loaded from: classes.dex */
    public static final class integer {
        public static final int status_bar_notification_info_maxnum = 0x7f0a0006;
    }

    /* loaded from: classes.dex */
    public static final class layout {
        public static final int custom_dialog = 0x7f0c0033;
        public static final int notification_action = 0x7f0c006f;
        public static final int notification_action_tombstone = 0x7f0c0070;
        public static final int notification_template_custom_big = 0x7f0c0071;
        public static final int notification_template_icon_group = 0x7f0c0072;
        public static final int notification_template_part_chronometer = 0x7f0c0073;
        public static final int notification_template_part_time = 0x7f0c0074;
    }

    /* loaded from: classes.dex */
    public static final class string {
        public static final int status_bar_notification_info_overflow = 0x7f0e006f;
    }

    /* loaded from: classes.dex */
    public static final class style {
        public static final int TextAppearance_Compat_Notification = 0x7f0f00f4;
        public static final int TextAppearance_Compat_Notification_Info = 0x7f0f00f5;
        public static final int TextAppearance_Compat_Notification_Line2 = 0x7f0f00f6;
        public static final int TextAppearance_Compat_Notification_Time = 0x7f0f00f7;
        public static final int TextAppearance_Compat_Notification_Title = 0x7f0f00f8;
        public static final int Widget_Compat_NotificationActionContainer = 0x7f0f0164;
        public static final int Widget_Compat_NotificationActionText = 0x7f0f0165;
    }

    /* loaded from: classes.dex */
    public static final class styleable {
        public static final int ColorStateListItem_alpha = 0x00000002;
        public static final int ColorStateListItem_android_alpha = 0x00000001;
        public static final int ColorStateListItem_android_color = 0x00000000;
        public static final int FontFamilyFont_android_font = 0x00000000;
        public static final int FontFamilyFont_android_fontStyle = 0x00000002;
        public static final int FontFamilyFont_android_fontVariationSettings = 0x00000004;
        public static final int FontFamilyFont_android_fontWeight = 0x00000001;
        public static final int FontFamilyFont_android_ttcIndex = 0x00000003;
        public static final int FontFamilyFont_font = 0x00000005;
        public static final int FontFamilyFont_fontStyle = 0x00000006;
        public static final int FontFamilyFont_fontVariationSettings = 0x00000007;
        public static final int FontFamilyFont_fontWeight = 0x00000008;
        public static final int FontFamilyFont_ttcIndex = 0x00000009;
        public static final int FontFamily_fontProviderAuthority = 0x00000000;
        public static final int FontFamily_fontProviderCerts = 0x00000001;
        public static final int FontFamily_fontProviderFetchStrategy = 0x00000002;
        public static final int FontFamily_fontProviderFetchTimeout = 0x00000003;
        public static final int FontFamily_fontProviderPackage = 0x00000004;
        public static final int FontFamily_fontProviderQuery = 0x00000005;
        public static final int GradientColorItem_android_color = 0x00000000;
        public static final int GradientColorItem_android_offset = 0x00000001;
        public static final int GradientColor_android_centerColor = 0x00000007;
        public static final int GradientColor_android_centerX = 0x00000003;
        public static final int GradientColor_android_centerY = 0x00000004;
        public static final int GradientColor_android_endColor = 0x00000001;
        public static final int GradientColor_android_endX = 0x0000000a;
        public static final int GradientColor_android_endY = 0x0000000b;
        public static final int GradientColor_android_gradientRadius = 0x00000005;
        public static final int GradientColor_android_startColor = 0x00000000;
        public static final int GradientColor_android_startX = 0x00000008;
        public static final int GradientColor_android_startY = 0x00000009;
        public static final int GradientColor_android_tileMode = 0x00000006;
        public static final int GradientColor_android_type = 0x00000002;
        public static final int[] ColorStateListItem = {16843173, 16843551, com.ici.connectivity.R.attr.alpha};
        public static final int[] FontFamily = {com.ici.connectivity.R.attr.fontProviderAuthority, com.ici.connectivity.R.attr.fontProviderCerts, com.ici.connectivity.R.attr.fontProviderFetchStrategy, com.ici.connectivity.R.attr.fontProviderFetchTimeout, com.ici.connectivity.R.attr.fontProviderPackage, com.ici.connectivity.R.attr.fontProviderQuery};
        public static final int[] FontFamilyFont = {16844082, 16844083, 16844095, 16844143, 16844144, com.ici.connectivity.R.attr.font, com.ici.connectivity.R.attr.fontStyle, com.ici.connectivity.R.attr.fontVariationSettings, com.ici.connectivity.R.attr.fontWeight, com.ici.connectivity.R.attr.ttcIndex};
        public static final int[] GradientColor = {16843165, 16843166, 16843169, 16843170, 16843171, 16843172, 16843265, 16843275, 16844048, 16844049, 16844050, 16844051};
        public static final int[] GradientColorItem = {16843173, 16844052};
    }
}
