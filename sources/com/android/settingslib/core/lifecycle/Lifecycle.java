package com.android.settingslib.core.lifecycle;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LifecycleRegistry;
import androidx.lifecycle.OnLifecycleEvent;
import androidx.preference.PreferenceScreen;
import b.a.a.b.y;
import b.a.a.c.c.a.b;
import b.a.a.c.c.a.c;
import b.a.a.c.c.a.d;
import b.a.a.c.c.a.e;
import b.a.a.c.c.a.f;
import b.a.a.c.c.a.g;
import b.a.a.c.c.a.h;
import b.a.a.c.c.a.i;
import b.a.a.c.c.a.j;
import b.a.a.c.c.a.k;
import b.a.a.c.c.a.l;
import java.util.ArrayList;
import java.util.List;

/* loaded from: classes.dex */
public class Lifecycle extends LifecycleRegistry {
    public static final String TAG = "LifecycleObserver";
    public final List<LifecycleObserver> mObservers;
    public final LifecycleProxy mProxy;

    /* loaded from: classes.dex */
    public class LifecycleProxy implements androidx.lifecycle.LifecycleObserver {
        public LifecycleProxy() {
        }

        @OnLifecycleEvent(Lifecycle.Event.ON_ANY)
        public void onLifecycleEvent(LifecycleOwner lifecycleOwner, Lifecycle.Event event) {
            switch (event.ordinal()) {
                case 1:
                    Lifecycle.this.onStart();
                    return;
                case 2:
                    Lifecycle.this.onResume();
                    return;
                case 3:
                    Lifecycle.this.onPause();
                    return;
                case 4:
                    Lifecycle.this.onStop();
                    return;
                case 5:
                    Lifecycle.this.onDestroy();
                    return;
                case 6:
                    Log.wtf(Lifecycle.TAG, "Should not receive an 'ANY' event!");
                    return;
                default:
                    return;
            }
        }
    }

    public Lifecycle(@NonNull LifecycleOwner lifecycleOwner) {
        super(lifecycleOwner);
        this.mObservers = new ArrayList();
        LifecycleProxy lifecycleProxy = new LifecycleProxy();
        this.mProxy = lifecycleProxy;
        addObserver(lifecycleProxy);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void onDestroy() {
        int size = this.mObservers.size();
        for (int i = 0; i < size; i++) {
            LifecycleObserver lifecycleObserver = this.mObservers.get(i);
            if (lifecycleObserver instanceof d) {
                ((d) lifecycleObserver).onDestroy();
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void onPause() {
        int size = this.mObservers.size();
        for (int i = 0; i < size; i++) {
            LifecycleObserver lifecycleObserver = this.mObservers.get(i);
            if (lifecycleObserver instanceof f) {
                ((f) lifecycleObserver).a();
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void onResume() {
        int size = this.mObservers.size();
        for (int i = 0; i < size; i++) {
            LifecycleObserver lifecycleObserver = this.mObservers.get(i);
            if (lifecycleObserver instanceof h) {
                ((h) lifecycleObserver).onResume();
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void onStart() {
        int size = this.mObservers.size();
        for (int i = 0; i < size; i++) {
            LifecycleObserver lifecycleObserver = this.mObservers.get(i);
            if (lifecycleObserver instanceof j) {
                ((j) lifecycleObserver).onStart();
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void onStop() {
        int size = this.mObservers.size();
        for (int i = 0; i < size; i++) {
            LifecycleObserver lifecycleObserver = this.mObservers.get(i);
            if (lifecycleObserver instanceof k) {
                ((k) lifecycleObserver).onStop();
            }
        }
    }

    @Override // androidx.lifecycle.LifecycleRegistry, androidx.lifecycle.Lifecycle
    public void addObserver(androidx.lifecycle.LifecycleObserver lifecycleObserver) {
        y.g();
        super.addObserver(lifecycleObserver);
        if (lifecycleObserver instanceof LifecycleObserver) {
            this.mObservers.add((LifecycleObserver) lifecycleObserver);
        }
    }

    public void onAttach(Context context) {
        int size = this.mObservers.size();
        for (int i = 0; i < size; i++) {
            LifecycleObserver lifecycleObserver = this.mObservers.get(i);
            if (lifecycleObserver instanceof b.a.a.c.c.a.a) {
                ((b.a.a.c.c.a.a) lifecycleObserver).a(context);
            }
        }
    }

    public void onCreate(Bundle bundle) {
        int size = this.mObservers.size();
        for (int i = 0; i < size; i++) {
            LifecycleObserver lifecycleObserver = this.mObservers.get(i);
            if (lifecycleObserver instanceof b) {
                ((b) lifecycleObserver).a(bundle);
            }
        }
    }

    public void onCreateOptionsMenu(Menu menu, @Nullable MenuInflater menuInflater) {
        int size = this.mObservers.size();
        for (int i = 0; i < size; i++) {
            LifecycleObserver lifecycleObserver = this.mObservers.get(i);
            if (lifecycleObserver instanceof c) {
                ((c) lifecycleObserver).a(menu, menuInflater);
            }
        }
    }

    public boolean onOptionsItemSelected(MenuItem menuItem) {
        int size = this.mObservers.size();
        for (int i = 0; i < size; i++) {
            LifecycleObserver lifecycleObserver = this.mObservers.get(i);
            if ((lifecycleObserver instanceof e) && ((e) lifecycleObserver).a(menuItem)) {
                return true;
            }
        }
        return false;
    }

    public void onPrepareOptionsMenu(Menu menu) {
        int size = this.mObservers.size();
        for (int i = 0; i < size; i++) {
            LifecycleObserver lifecycleObserver = this.mObservers.get(i);
            if (lifecycleObserver instanceof g) {
                ((g) lifecycleObserver).a(menu);
            }
        }
    }

    public void onSaveInstanceState(Bundle bundle) {
        int size = this.mObservers.size();
        for (int i = 0; i < size; i++) {
            LifecycleObserver lifecycleObserver = this.mObservers.get(i);
            if (lifecycleObserver instanceof i) {
                ((i) lifecycleObserver).a(bundle);
            }
        }
    }

    @Override // androidx.lifecycle.LifecycleRegistry, androidx.lifecycle.Lifecycle
    public void removeObserver(androidx.lifecycle.LifecycleObserver lifecycleObserver) {
        y.g();
        super.removeObserver(lifecycleObserver);
        if (lifecycleObserver instanceof LifecycleObserver) {
            this.mObservers.remove(lifecycleObserver);
        }
    }

    public void setPreferenceScreen(PreferenceScreen preferenceScreen) {
        int size = this.mObservers.size();
        for (int i = 0; i < size; i++) {
            LifecycleObserver lifecycleObserver = this.mObservers.get(i);
            if (lifecycleObserver instanceof l) {
                ((l) lifecycleObserver).setPreferenceScreen(preferenceScreen);
            }
        }
    }
}
