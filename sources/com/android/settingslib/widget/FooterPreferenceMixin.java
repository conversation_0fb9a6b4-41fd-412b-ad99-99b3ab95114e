package com.android.settingslib.widget;

import android.content.Context;
import androidx.preference.PreferenceFragment;
import androidx.preference.PreferenceScreen;
import b.a.a.c.c.a.l;
import b.a.a.e.a;
import com.android.settingslib.core.lifecycle.Lifecycle;
import com.android.settingslib.core.lifecycle.LifecycleObserver;

/* loaded from: classes.dex */
public class FooterPreferenceMixin implements LifecycleObserver, l {
    public a mFooterPreference;
    public final PreferenceFragment mFragment;

    public FooterPreferenceMixin(PreferenceFragment preferenceFragment, Lifecycle lifecycle) {
        this.mFragment = preferenceFragment;
        lifecycle.addObserver(this);
    }

    private Context getPrefContext() {
        return this.mFragment.getPreferenceManager().getContext();
    }

    public a createFooterPreference() {
        PreferenceScreen preferenceScreen = this.mFragment.getPreferenceScreen();
        a aVar = this.mFooterPreference;
        if (aVar != null && preferenceScreen != null) {
            preferenceScreen.removePreference(aVar);
        }
        a aVar2 = new a(getPrefContext(), null);
        this.mFooterPreference = aVar2;
        if (preferenceScreen != null) {
            preferenceScreen.addPreference(aVar2);
        }
        return this.mFooterPreference;
    }

    public boolean hasFooter() {
        return this.mFooterPreference != null;
    }

    @Override // b.a.a.c.c.a.l
    public void setPreferenceScreen(PreferenceScreen preferenceScreen) {
        a aVar = this.mFooterPreference;
        if (aVar != null) {
            preferenceScreen.addPreference(aVar);
        }
    }
}
