package com.android.settingslib;

/* loaded from: classes.dex */
public final class R$layout {
    public static int abc_action_bar_title_item;
    public static int abc_action_bar_up_container;
    public static int abc_action_menu_item_layout;
    public static int abc_action_menu_layout;
    public static int abc_action_mode_bar;
    public static int abc_action_mode_close_item_material;
    public static int abc_activity_chooser_view;
    public static int abc_activity_chooser_view_list_item;
    public static int abc_alert_dialog_button_bar_material;
    public static int abc_alert_dialog_material;
    public static int abc_alert_dialog_title_material;
    public static int abc_cascading_menu_item_layout;
    public static int abc_dialog_title_material;
    public static int abc_expanded_menu_layout;
    public static int abc_list_menu_item_checkbox;
    public static int abc_list_menu_item_icon;
    public static int abc_list_menu_item_layout;
    public static int abc_list_menu_item_radio;
    public static int abc_popup_menu_header_item_layout;
    public static int abc_popup_menu_item_layout;
    public static int abc_screen_content_include;
    public static int abc_screen_simple;
    public static int abc_screen_simple_overlay_action_mode;
    public static int abc_screen_toolbar;
    public static int abc_search_dropdown_item_icons_2line;
    public static int abc_search_view;
    public static int abc_select_dialog_material;
    public static int abc_tooltip;
    public static int access_point_friction_widget;
    public static int expand_button;
    public static int notification_action;
    public static int notification_action_tombstone;
    public static int notification_media_action;
    public static int notification_media_cancel_action;
    public static int notification_template_big_media;
    public static int notification_template_big_media_custom;
    public static int notification_template_big_media_narrow;
    public static int notification_template_big_media_narrow_custom;
    public static int notification_template_custom_big;
    public static int notification_template_icon_group;
    public static int notification_template_lines_media;
    public static int notification_template_media;
    public static int notification_template_media_custom;
    public static int notification_template_part_chronometer;
    public static int notification_template_part_time;
    public static int preference;
    public static int preference_access_point;
    public static int preference_category;
    public static int preference_category_divider;
    public static int preference_category_material;
    public static int preference_category_material_settings;
    public static int preference_category_material_settings_with_divider;
    public static int preference_dialog_edittext;
    public static int preference_dropdown;
    public static int preference_dropdown_material;
    public static int preference_dropdown_material_settings;
    public static int preference_footer;
    public static int preference_information;
    public static int preference_information_material;
    public static int preference_list_fragment;
    public static int preference_material;
    public static int preference_recyclerview;
    public static int preference_two_target;
    public static int preference_two_target_divider;
    public static int preference_widget_checkbox;
    public static int preference_widget_seekbar;
    public static int preference_widget_seekbar_material;
    public static int preference_widget_switch;
    public static int preference_widget_switch_compat;
    public static int restricted_icon;
    public static int restricted_switch_preference;
    public static int restricted_switch_widget;
    public static int select_dialog_item_material;
    public static int select_dialog_multichoice_material;
    public static int select_dialog_singlechoice_material;
    public static int settings_with_drawer;
    public static int support_simple_spinner_dropdown_item;
    public static int user_preference;
    public static int zen_mode_condition;
    public static int zen_mode_duration_dialog;
    public static int zen_mode_radio_button;
    public static int zen_mode_turn_on_dialog_container;
}
