package com.android.settingslib;

/* loaded from: classes.dex */
public final class R$color {
    public static int abc_background_cache_hint_selector_material_dark;
    public static int abc_background_cache_hint_selector_material_light;
    public static int abc_btn_colored_borderless_text_material;
    public static int abc_btn_colored_text_material;
    public static int abc_color_highlight_material;
    public static int abc_hint_foreground_material_dark;
    public static int abc_hint_foreground_material_light;
    public static int abc_input_method_navigation_guard;
    public static int abc_primary_text_disable_only_material_dark;
    public static int abc_primary_text_disable_only_material_light;
    public static int abc_primary_text_material_dark;
    public static int abc_primary_text_material_light;
    public static int abc_search_url_text;
    public static int abc_search_url_text_normal;
    public static int abc_search_url_text_pressed;
    public static int abc_search_url_text_selected;
    public static int abc_secondary_text_material_dark;
    public static int abc_secondary_text_material_light;
    public static int abc_tint_btn_checkable;
    public static int abc_tint_default;
    public static int abc_tint_edittext;
    public static int abc_tint_seek_thumb;
    public static int abc_tint_spinner;
    public static int abc_tint_switch_track;
    public static int accent_material_dark;
    public static int accent_material_light;
    public static int background_floating_material_dark;
    public static int background_floating_material_light;
    public static int background_material_dark;
    public static int background_material_light;
    public static int batterymeter_bolt_color;
    public static int batterymeter_plus_color;
    public static int bright_foreground_disabled_material_dark;
    public static int bright_foreground_disabled_material_light;
    public static int bright_foreground_inverse_material_dark;
    public static int bright_foreground_inverse_material_light;
    public static int bright_foreground_material_dark;
    public static int bright_foreground_material_light;
    public static int button_material_dark;
    public static int button_material_light;
    public static int dark_mode_icon_color_dual_tone_background;
    public static int dark_mode_icon_color_dual_tone_fill;
    public static int dim_foreground_disabled_material_dark;
    public static int dim_foreground_disabled_material_light;
    public static int dim_foreground_material_dark;
    public static int dim_foreground_material_light;
    public static int disabled_text_color;
    public static int error_color_material_dark;
    public static int error_color_material_light;
    public static int foreground_material_dark;
    public static int foreground_material_light;
    public static int highlighted_text_material_dark;
    public static int highlighted_text_material_light;
    public static int light_mode_icon_color_dual_tone_background;
    public static int light_mode_icon_color_dual_tone_fill;
    public static int material_blue_grey_800;
    public static int material_blue_grey_900;
    public static int material_blue_grey_950;
    public static int material_deep_teal_200;
    public static int material_deep_teal_500;
    public static int material_grey_100;
    public static int material_grey_300;
    public static int material_grey_50;
    public static int material_grey_600;
    public static int material_grey_800;
    public static int material_grey_850;
    public static int material_grey_900;
    public static int meter_background_color;
    public static int meter_consumed_color;
    public static int notification_action_color_filter;
    public static int notification_icon_bg_color;
    public static int notification_material_background_media_default_color;
    public static int preference_fallback_accent_color;
    public static int primary_dark_material_dark;
    public static int primary_dark_material_light;
    public static int primary_material_dark;
    public static int primary_material_light;
    public static int primary_text_default_material_dark;
    public static int primary_text_default_material_light;
    public static int primary_text_disabled_material_dark;
    public static int primary_text_disabled_material_light;
    public static int ripple_material_dark;
    public static int ripple_material_light;
    public static int secondary_text_default_material_dark;
    public static int secondary_text_default_material_light;
    public static int secondary_text_disabled_material_dark;
    public static int secondary_text_disabled_material_light;
    public static int switch_thumb_disabled_material_dark;
    public static int switch_thumb_disabled_material_light;
    public static int switch_thumb_material_dark;
    public static int switch_thumb_material_light;
    public static int switch_thumb_normal_material_dark;
    public static int switch_thumb_normal_material_light;
    public static int tooltip_background_dark;
    public static int tooltip_background_light;
    public static int usage_graph_dots;
}
