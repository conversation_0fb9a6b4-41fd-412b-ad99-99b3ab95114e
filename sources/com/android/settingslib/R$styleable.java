package com.android.settingslib;

/* loaded from: classes.dex */
public final class R$styleable {
    public static final int[] ActionBar = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    public static int ActionBar_background = 0;
    public static int ActionBar_backgroundSplit = 1;
    public static int ActionBar_backgroundStacked = 2;
    public static int ActionBar_contentInsetEnd = 3;
    public static int ActionBar_contentInsetEndWithActions = 4;
    public static int ActionBar_contentInsetLeft = 5;
    public static int ActionBar_contentInsetRight = 6;
    public static int ActionBar_contentInsetStart = 7;
    public static int ActionBar_contentInsetStartWithNavigation = 8;
    public static int ActionBar_customNavigationLayout = 9;
    public static int ActionBar_displayOptions = 10;
    public static int ActionBar_divider = 11;
    public static int ActionBar_elevation = 12;
    public static int ActionBar_height = 13;
    public static int ActionBar_hideOnContentScroll = 14;
    public static int ActionBar_homeAsUpIndicator = 15;
    public static int ActionBar_homeLayout = 16;
    public static int ActionBar_icon = 17;
    public static int ActionBar_indeterminateProgressStyle = 18;
    public static int ActionBar_itemPadding = 19;
    public static int ActionBar_logo = 20;
    public static int ActionBar_navigationMode = 21;
    public static int ActionBar_popupTheme = 22;
    public static int ActionBar_progressBarPadding = 23;
    public static int ActionBar_progressBarStyle = 24;
    public static int ActionBar_subtitle = 25;
    public static int ActionBar_subtitleTextStyle = 26;
    public static int ActionBar_title = 27;
    public static int ActionBar_titleTextStyle = 28;
    public static final int[] ActionBarLayout = {16842931};
    public static int ActionBarLayout_android_layout_gravity = 0;
    public static final int[] ActionMenuItemView = {16843071};
    public static int ActionMenuItemView_android_minWidth = 0;
    public static final int[] ActionMenuView = new int[0];
    public static final int[] ActionMode = {0, 0, 0, 0, 0, 0};
    public static int ActionMode_background = 0;
    public static int ActionMode_backgroundSplit = 1;
    public static int ActionMode_closeItemLayout = 2;
    public static int ActionMode_height = 3;
    public static int ActionMode_subtitleTextStyle = 4;
    public static int ActionMode_titleTextStyle = 5;
    public static final int[] ActivityChooserView = {0, 0};
    public static int ActivityChooserView_expandActivityOverflowButtonDrawable = 0;
    public static int ActivityChooserView_initialActivityCount = 1;
    public static final int[] AlertDialog = {0, 0, 0, 0, 0, 0, 0, 16842994};
    public static int AlertDialog_buttonIconDimen = 0;
    public static int AlertDialog_buttonPanelSideLayout = 1;
    public static int AlertDialog_listItemLayout = 2;
    public static int AlertDialog_listLayout = 3;
    public static int AlertDialog_multiChoiceItemLayout = 4;
    public static int AlertDialog_showTitle = 5;
    public static int AlertDialog_singleChoiceItemLayout = 6;
    public static int AlertDialog_android_layout = 7;
    public static final int[] AppCompatImageView = {0, 0, 0, 16843033};
    public static int AppCompatImageView_srcCompat = 0;
    public static int AppCompatImageView_tint = 1;
    public static int AppCompatImageView_tintMode = 2;
    public static int AppCompatImageView_android_src = 3;
    public static final int[] AppCompatSeekBar = {0, 0, 0, 16843074};
    public static int AppCompatSeekBar_tickMark = 0;
    public static int AppCompatSeekBar_tickMarkTint = 1;
    public static int AppCompatSeekBar_tickMarkTintMode = 2;
    public static int AppCompatSeekBar_android_thumb = 3;
    public static final int[] AppCompatTextHelper = {16842804, 16843117, 16843118, 16843119, 16843120, 16843666, 16843667};
    public static int AppCompatTextHelper_android_textAppearance = 0;
    public static int AppCompatTextHelper_android_drawableTop = 1;
    public static int AppCompatTextHelper_android_drawableBottom = 2;
    public static int AppCompatTextHelper_android_drawableLeft = 3;
    public static int AppCompatTextHelper_android_drawableRight = 4;
    public static int AppCompatTextHelper_android_drawableStart = 5;
    public static int AppCompatTextHelper_android_drawableEnd = 6;
    public static final int[] AppCompatTextView = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16842804};
    public static int AppCompatTextView_autoSizeMaxTextSize = 0;
    public static int AppCompatTextView_autoSizeMinTextSize = 1;
    public static int AppCompatTextView_autoSizePresetSizes = 2;
    public static int AppCompatTextView_autoSizeStepGranularity = 3;
    public static int AppCompatTextView_autoSizeTextType = 4;
    public static int AppCompatTextView_firstBaselineToTopHeight = 5;
    public static int AppCompatTextView_fontFamily = 6;
    public static int AppCompatTextView_lastBaselineToBottomHeight = 7;
    public static int AppCompatTextView_lineHeight = 8;
    public static int AppCompatTextView_textAllCaps = 9;
    public static int AppCompatTextView_android_textAppearance = 10;
    public static final int[] AppCompatTheme = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16842839, 16842926};
    public static int AppCompatTheme_actionBarDivider = 0;
    public static int AppCompatTheme_actionBarItemBackground = 1;
    public static int AppCompatTheme_actionBarPopupTheme = 2;
    public static int AppCompatTheme_actionBarSize = 3;
    public static int AppCompatTheme_actionBarSplitStyle = 4;
    public static int AppCompatTheme_actionBarStyle = 5;
    public static int AppCompatTheme_actionBarTabBarStyle = 6;
    public static int AppCompatTheme_actionBarTabStyle = 7;
    public static int AppCompatTheme_actionBarTabTextStyle = 8;
    public static int AppCompatTheme_actionBarTheme = 9;
    public static int AppCompatTheme_actionBarWidgetTheme = 10;
    public static int AppCompatTheme_actionButtonStyle = 11;
    public static int AppCompatTheme_actionDropDownStyle = 12;
    public static int AppCompatTheme_actionMenuTextAppearance = 13;
    public static int AppCompatTheme_actionMenuTextColor = 14;
    public static int AppCompatTheme_actionModeBackground = 15;
    public static int AppCompatTheme_actionModeCloseButtonStyle = 16;
    public static int AppCompatTheme_actionModeCloseDrawable = 17;
    public static int AppCompatTheme_actionModeCopyDrawable = 18;
    public static int AppCompatTheme_actionModeCutDrawable = 19;
    public static int AppCompatTheme_actionModeFindDrawable = 20;
    public static int AppCompatTheme_actionModePasteDrawable = 21;
    public static int AppCompatTheme_actionModePopupWindowStyle = 22;
    public static int AppCompatTheme_actionModeSelectAllDrawable = 23;
    public static int AppCompatTheme_actionModeShareDrawable = 24;
    public static int AppCompatTheme_actionModeSplitBackground = 25;
    public static int AppCompatTheme_actionModeStyle = 26;
    public static int AppCompatTheme_actionModeWebSearchDrawable = 27;
    public static int AppCompatTheme_actionOverflowButtonStyle = 28;
    public static int AppCompatTheme_actionOverflowMenuStyle = 29;
    public static int AppCompatTheme_activityChooserViewStyle = 30;
    public static int AppCompatTheme_alertDialogButtonGroupStyle = 31;
    public static int AppCompatTheme_alertDialogCenterButtons = 32;
    public static int AppCompatTheme_alertDialogStyle = 33;
    public static int AppCompatTheme_alertDialogTheme = 34;
    public static int AppCompatTheme_autoCompleteTextViewStyle = 35;
    public static int AppCompatTheme_borderlessButtonStyle = 36;
    public static int AppCompatTheme_buttonBarButtonStyle = 37;
    public static int AppCompatTheme_buttonBarNegativeButtonStyle = 38;
    public static int AppCompatTheme_buttonBarNeutralButtonStyle = 39;
    public static int AppCompatTheme_buttonBarPositiveButtonStyle = 40;
    public static int AppCompatTheme_buttonBarStyle = 41;
    public static int AppCompatTheme_buttonStyle = 42;
    public static int AppCompatTheme_buttonStyleSmall = 43;
    public static int AppCompatTheme_checkboxStyle = 44;
    public static int AppCompatTheme_checkedTextViewStyle = 45;
    public static int AppCompatTheme_colorAccent = 46;
    public static int AppCompatTheme_colorBackgroundFloating = 47;
    public static int AppCompatTheme_colorButtonNormal = 48;
    public static int AppCompatTheme_colorControlActivated = 49;
    public static int AppCompatTheme_colorControlHighlight = 50;
    public static int AppCompatTheme_colorControlNormal = 51;
    public static int AppCompatTheme_colorError = 52;
    public static int AppCompatTheme_colorPrimary = 53;
    public static int AppCompatTheme_colorPrimaryDark = 54;
    public static int AppCompatTheme_colorSwitchThumbNormal = 55;
    public static int AppCompatTheme_controlBackground = 56;
    public static int AppCompatTheme_dialogCornerRadius = 57;
    public static int AppCompatTheme_dialogPreferredPadding = 58;
    public static int AppCompatTheme_dialogTheme = 59;
    public static int AppCompatTheme_dividerHorizontal = 60;
    public static int AppCompatTheme_dividerVertical = 61;
    public static int AppCompatTheme_dropDownListViewStyle = 62;
    public static int AppCompatTheme_dropdownListPreferredItemHeight = 63;
    public static int AppCompatTheme_editTextBackground = 64;
    public static int AppCompatTheme_editTextColor = 65;
    public static int AppCompatTheme_editTextStyle = 66;
    public static int AppCompatTheme_homeAsUpIndicator = 67;
    public static int AppCompatTheme_imageButtonStyle = 68;
    public static int AppCompatTheme_listChoiceBackgroundIndicator = 69;
    public static int AppCompatTheme_listDividerAlertDialog = 70;
    public static int AppCompatTheme_listMenuViewStyle = 71;
    public static int AppCompatTheme_listPopupWindowStyle = 72;
    public static int AppCompatTheme_listPreferredItemHeight = 73;
    public static int AppCompatTheme_listPreferredItemHeightLarge = 74;
    public static int AppCompatTheme_listPreferredItemHeightSmall = 75;
    public static int AppCompatTheme_listPreferredItemPaddingLeft = 76;
    public static int AppCompatTheme_listPreferredItemPaddingRight = 77;
    public static int AppCompatTheme_panelBackground = 78;
    public static int AppCompatTheme_panelMenuListTheme = 79;
    public static int AppCompatTheme_panelMenuListWidth = 80;
    public static int AppCompatTheme_popupMenuStyle = 81;
    public static int AppCompatTheme_popupWindowStyle = 82;
    public static int AppCompatTheme_radioButtonStyle = 83;
    public static int AppCompatTheme_ratingBarStyle = 84;
    public static int AppCompatTheme_ratingBarStyleIndicator = 85;
    public static int AppCompatTheme_ratingBarStyleSmall = 86;
    public static int AppCompatTheme_searchViewStyle = 87;
    public static int AppCompatTheme_seekBarStyle = 88;
    public static int AppCompatTheme_selectableItemBackground = 89;
    public static int AppCompatTheme_selectableItemBackgroundBorderless = 90;
    public static int AppCompatTheme_spinnerDropDownItemStyle = 91;
    public static int AppCompatTheme_spinnerStyle = 92;
    public static int AppCompatTheme_switchStyle = 93;
    public static int AppCompatTheme_textAppearanceLargePopupMenu = 94;
    public static int AppCompatTheme_textAppearanceListItem = 95;
    public static int AppCompatTheme_textAppearanceListItemSecondary = 96;
    public static int AppCompatTheme_textAppearanceListItemSmall = 97;
    public static int AppCompatTheme_textAppearancePopupMenuHeader = 98;
    public static int AppCompatTheme_textAppearanceSearchResultSubtitle = 99;
    public static int AppCompatTheme_textAppearanceSearchResultTitle = 100;
    public static int AppCompatTheme_textAppearanceSmallPopupMenu = 101;
    public static int AppCompatTheme_textColorAlertDialogListItem = 102;
    public static int AppCompatTheme_textColorSearchUrl = 103;
    public static int AppCompatTheme_toolbarNavigationButtonStyle = 104;
    public static int AppCompatTheme_toolbarStyle = 105;
    public static int AppCompatTheme_tooltipForegroundColor = 106;
    public static int AppCompatTheme_tooltipFrameBackground = 107;
    public static int AppCompatTheme_viewInflaterClass = 108;
    public static int AppCompatTheme_windowActionBar = 109;
    public static int AppCompatTheme_windowActionBarOverlay = 110;
    public static int AppCompatTheme_windowActionModeOverlay = 111;
    public static int AppCompatTheme_windowFixedHeightMajor = 112;
    public static int AppCompatTheme_windowFixedHeightMinor = 113;
    public static int AppCompatTheme_windowFixedWidthMajor = 114;
    public static int AppCompatTheme_windowFixedWidthMinor = 115;
    public static int AppCompatTheme_windowMinWidthMajor = 116;
    public static int AppCompatTheme_windowMinWidthMinor = 117;
    public static int AppCompatTheme_windowNoTitle = 118;
    public static int AppCompatTheme_android_windowIsFloating = 119;
    public static int AppCompatTheme_android_windowAnimationStyle = 120;
    public static final int[] BackgroundStyle = {0, 16843534};
    public static int BackgroundStyle_selectableItemBackground = 0;
    public static int BackgroundStyle_android_selectableItemBackground = 1;
    public static final int[] ButtonBarLayout = {0};
    public static int ButtonBarLayout_allowStacking = 0;
    public static final int[] CheckBoxPreference = {0, 0, 0, 16843247, 16843248, 16843249};
    public static int CheckBoxPreference_disableDependentsState = 0;
    public static int CheckBoxPreference_summaryOff = 1;
    public static int CheckBoxPreference_summaryOn = 2;
    public static int CheckBoxPreference_android_summaryOn = 3;
    public static int CheckBoxPreference_android_summaryOff = 4;
    public static int CheckBoxPreference_android_disableDependentsState = 5;
    public static final int[] ColorStateListItem = {0, 16843173, 16843551};
    public static int ColorStateListItem_alpha = 0;
    public static int ColorStateListItem_android_color = 1;
    public static int ColorStateListItem_android_alpha = 2;
    public static final int[] CompoundButton = {0, 0, 16843015};
    public static int CompoundButton_buttonTint = 0;
    public static int CompoundButton_buttonTintMode = 1;
    public static int CompoundButton_android_button = 2;
    public static final int[] CoordinatorLayout = {0, 0};
    public static int CoordinatorLayout_keylines = 0;
    public static int CoordinatorLayout_statusBarBackground = 1;
    public static final int[] CoordinatorLayout_Layout = {0, 0, 0, 0, 0, 0, 16842931};
    public static int CoordinatorLayout_Layout_layout_anchor = 0;
    public static int CoordinatorLayout_Layout_layout_anchorGravity = 1;
    public static int CoordinatorLayout_Layout_layout_behavior = 2;
    public static int CoordinatorLayout_Layout_layout_dodgeInsetEdges = 3;
    public static int CoordinatorLayout_Layout_layout_insetEdge = 4;
    public static int CoordinatorLayout_Layout_layout_keyline = 5;
    public static int CoordinatorLayout_Layout_android_layout_gravity = 6;
    public static final int[] DialogPreference = {0, 0, 0, 0, 0, 0, 16843250, 16843251, 16843252, 16843253, 16843254, 16843255};
    public static int DialogPreference_dialogIcon = 0;
    public static int DialogPreference_dialogLayout = 1;
    public static int DialogPreference_dialogMessage = 2;
    public static int DialogPreference_dialogTitle = 3;
    public static int DialogPreference_negativeButtonText = 4;
    public static int DialogPreference_positiveButtonText = 5;
    public static int DialogPreference_android_dialogTitle = 6;
    public static int DialogPreference_android_dialogMessage = 7;
    public static int DialogPreference_android_dialogIcon = 8;
    public static int DialogPreference_android_positiveButtonText = 9;
    public static int DialogPreference_android_negativeButtonText = 10;
    public static int DialogPreference_android_dialogLayout = 11;
    public static final int[] DrawerArrowToggle = {0, 0, 0, 0, 0, 0, 0, 0};
    public static int DrawerArrowToggle_arrowHeadLength = 0;
    public static int DrawerArrowToggle_arrowShaftLength = 1;
    public static int DrawerArrowToggle_barLength = 2;
    public static int DrawerArrowToggle_color = 3;
    public static int DrawerArrowToggle_drawableSize = 4;
    public static int DrawerArrowToggle_gapBetweenBars = 5;
    public static int DrawerArrowToggle_spinBars = 6;
    public static int DrawerArrowToggle_thickness = 7;
    public static final int[] FontFamily = {0, 0, 0, 0, 0, 0};
    public static int FontFamily_fontProviderAuthority = 0;
    public static int FontFamily_fontProviderCerts = 1;
    public static int FontFamily_fontProviderFetchStrategy = 2;
    public static int FontFamily_fontProviderFetchTimeout = 3;
    public static int FontFamily_fontProviderPackage = 4;
    public static int FontFamily_fontProviderQuery = 5;
    public static final int[] FontFamilyFont = {0, 0, 0, 0, 0, 16844082, 16844083, 16844095, 16844143, 16844144};
    public static int FontFamilyFont_font = 0;
    public static int FontFamilyFont_fontStyle = 1;
    public static int FontFamilyFont_fontVariationSettings = 2;
    public static int FontFamilyFont_fontWeight = 3;
    public static int FontFamilyFont_ttcIndex = 4;
    public static int FontFamilyFont_android_font = 5;
    public static int FontFamilyFont_android_fontWeight = 6;
    public static int FontFamilyFont_android_fontStyle = 7;
    public static int FontFamilyFont_android_ttcIndex = 8;
    public static int FontFamilyFont_android_fontVariationSettings = 9;
    public static final int[] LinearLayoutCompat = {0, 0, 0, 0, 16842927, 16842948, 16843046, 16843047, 16843048};
    public static int LinearLayoutCompat_divider = 0;
    public static int LinearLayoutCompat_dividerPadding = 1;
    public static int LinearLayoutCompat_measureWithLargestChild = 2;
    public static int LinearLayoutCompat_showDividers = 3;
    public static int LinearLayoutCompat_android_gravity = 4;
    public static int LinearLayoutCompat_android_orientation = 5;
    public static int LinearLayoutCompat_android_baselineAligned = 6;
    public static int LinearLayoutCompat_android_baselineAlignedChildIndex = 7;
    public static int LinearLayoutCompat_android_weightSum = 8;
    public static final int[] LinearLayoutCompat_Layout = {16842931, 16842996, 16842997, 16843137};
    public static int LinearLayoutCompat_Layout_android_layout_gravity = 0;
    public static int LinearLayoutCompat_Layout_android_layout_width = 1;
    public static int LinearLayoutCompat_Layout_android_layout_height = 2;
    public static int LinearLayoutCompat_Layout_android_layout_weight = 3;
    public static final int[] ListPopupWindow = {16843436, 16843437};
    public static int ListPopupWindow_android_dropDownHorizontalOffset = 0;
    public static int ListPopupWindow_android_dropDownVerticalOffset = 1;
    public static final int[] ListPreference = {0, 0, 16842930, 16843256};
    public static int ListPreference_entries = 0;
    public static int ListPreference_entryValues = 1;
    public static int ListPreference_android_entries = 2;
    public static int ListPreference_android_entryValues = 3;
    public static final int[] MenuGroup = {16842766, 16842960, 16843156, 16843230, 16843231, 16843232};
    public static int MenuGroup_android_enabled = 0;
    public static int MenuGroup_android_id = 1;
    public static int MenuGroup_android_visible = 2;
    public static int MenuGroup_android_menuCategory = 3;
    public static int MenuGroup_android_orderInCategory = 4;
    public static int MenuGroup_android_checkableBehavior = 5;
    public static final int[] MenuItem = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16842754, 16842766, 16842960, 16843014, 16843156, 16843230, 16843231, 16843233, 16843234, 16843235, 16843236, 16843237, 16843375};
    public static int MenuItem_actionLayout = 0;
    public static int MenuItem_actionProviderClass = 1;
    public static int MenuItem_actionViewClass = 2;
    public static int MenuItem_alphabeticModifiers = 3;
    public static int MenuItem_contentDescription = 4;
    public static int MenuItem_iconTint = 5;
    public static int MenuItem_iconTintMode = 6;
    public static int MenuItem_numericModifiers = 7;
    public static int MenuItem_showAsAction = 8;
    public static int MenuItem_tooltipText = 9;
    public static int MenuItem_android_icon = 10;
    public static int MenuItem_android_enabled = 11;
    public static int MenuItem_android_id = 12;
    public static int MenuItem_android_checked = 13;
    public static int MenuItem_android_visible = 14;
    public static int MenuItem_android_menuCategory = 15;
    public static int MenuItem_android_orderInCategory = 16;
    public static int MenuItem_android_title = 17;
    public static int MenuItem_android_titleCondensed = 18;
    public static int MenuItem_android_alphabeticShortcut = 19;
    public static int MenuItem_android_numericShortcut = 20;
    public static int MenuItem_android_checkable = 21;
    public static int MenuItem_android_onClick = 22;
    public static final int[] MenuView = {0, 0, 16842926, 16843052, 16843053, 16843054, 16843055, 16843056, 16843057};
    public static int MenuView_preserveIconSpacing = 0;
    public static int MenuView_subMenuArrow = 1;
    public static int MenuView_android_windowAnimationStyle = 2;
    public static int MenuView_android_itemTextAppearance = 3;
    public static int MenuView_android_horizontalDivider = 4;
    public static int MenuView_android_verticalDivider = 5;
    public static int MenuView_android_headerBackground = 6;
    public static int MenuView_android_itemBackground = 7;
    public static int MenuView_android_itemIconDisabledAlpha = 8;
    public static final int[] MultiSelectListPreference = {0, 0, 16842930, 16843256};
    public static int MultiSelectListPreference_entries = 0;
    public static int MultiSelectListPreference_entryValues = 1;
    public static int MultiSelectListPreference_android_entries = 2;
    public static int MultiSelectListPreference_android_entryValues = 3;
    public static final int[] PopupWindow = {0, 16843126, 16843465};
    public static int PopupWindow_overlapAnchor = 0;
    public static int PopupWindow_android_popupBackground = 1;
    public static int PopupWindow_android_popupAnimationStyle = 2;
    public static final int[] PopupWindowBackgroundState = {0};
    public static int PopupWindowBackgroundState_state_above_anchor = 0;
    public static final int[] Preference = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16842754, 16842765, 16842766, 16842994, 16843233, 16843238, 16843240, 16843241, 16843242, 16843243, 16843244, 16843245, 16843246, 16843491, 16844124, 16844129};
    public static int Preference_allowDividerAbove = 0;
    public static int Preference_allowDividerBelow = 1;
    public static int Preference_defaultValue = 2;
    public static int Preference_dependency = 3;
    public static int Preference_enabled = 4;
    public static int Preference_fragment = 5;
    public static int Preference_icon = 6;
    public static int Preference_iconSpaceReserved = 7;
    public static int Preference_isPreferenceVisible = 8;
    public static int Preference_key = 9;
    public static int Preference_layout = 10;
    public static int Preference_order = 11;
    public static int Preference_persistent = 12;
    public static int Preference_selectable = 13;
    public static int Preference_shouldDisableView = 14;
    public static int Preference_singleLineTitle = 15;
    public static int Preference_summary = 16;
    public static int Preference_title = 17;
    public static int Preference_widgetLayout = 18;
    public static int Preference_android_icon = 19;
    public static int Preference_android_persistent = 20;
    public static int Preference_android_enabled = 21;
    public static int Preference_android_layout = 22;
    public static int Preference_android_title = 23;
    public static int Preference_android_selectable = 24;
    public static int Preference_android_key = 25;
    public static int Preference_android_summary = 26;
    public static int Preference_android_order = 27;
    public static int Preference_android_widgetLayout = 28;
    public static int Preference_android_dependency = 29;
    public static int Preference_android_defaultValue = 30;
    public static int Preference_android_shouldDisableView = 31;
    public static int Preference_android_fragment = 32;
    public static int Preference_android_singleLineTitle = 33;
    public static int Preference_android_iconSpaceReserved = 34;
    public static final int[] PreferenceFragment = {0, 16842994, 16843049, 16843050};
    public static int PreferenceFragment_allowDividerAfterLastItem = 0;
    public static int PreferenceFragment_android_layout = 1;
    public static int PreferenceFragment_android_divider = 2;
    public static int PreferenceFragment_android_dividerHeight = 3;
    public static final int[] PreferenceFragmentCompat = {0, 16842994, 16843049, 16843050};
    public static int PreferenceFragmentCompat_allowDividerAfterLastItem = 0;
    public static int PreferenceFragmentCompat_android_layout = 1;
    public static int PreferenceFragmentCompat_android_divider = 2;
    public static int PreferenceFragmentCompat_android_dividerHeight = 3;
    public static final int[] PreferenceGroup = {0, 0, 16843239};
    public static int PreferenceGroup_initialExpandedChildrenCount = 0;
    public static int PreferenceGroup_orderingFromXml = 1;
    public static int PreferenceGroup_android_orderingFromXml = 2;
    public static final int[] PreferenceImageView = {0, 0, 16843039, 16843040};
    public static int PreferenceImageView_maxHeight = 0;
    public static int PreferenceImageView_maxWidth = 1;
    public static int PreferenceImageView_android_maxWidth = 2;
    public static int PreferenceImageView_android_maxHeight = 3;
    public static final int[] PreferenceTheme = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    public static int PreferenceTheme_checkBoxPreferenceStyle = 0;
    public static int PreferenceTheme_dialogPreferenceStyle = 1;
    public static int PreferenceTheme_dropdownPreferenceStyle = 2;
    public static int PreferenceTheme_editTextPreferenceStyle = 3;
    public static int PreferenceTheme_preferenceActivityStyle = 4;
    public static int PreferenceTheme_preferenceCategoryStyle = 5;
    public static int PreferenceTheme_preferenceFragmentCompatStyle = 6;
    public static int PreferenceTheme_preferenceFragmentListStyle = 7;
    public static int PreferenceTheme_preferenceFragmentPaddingSide = 8;
    public static int PreferenceTheme_preferenceFragmentStyle = 9;
    public static int PreferenceTheme_preferenceHeaderPanelStyle = 10;
    public static int PreferenceTheme_preferenceInformationStyle = 11;
    public static int PreferenceTheme_preferenceLayoutChild = 12;
    public static int PreferenceTheme_preferenceListStyle = 13;
    public static int PreferenceTheme_preferencePanelStyle = 14;
    public static int PreferenceTheme_preferenceScreenStyle = 15;
    public static int PreferenceTheme_preferenceStyle = 16;
    public static int PreferenceTheme_preferenceTheme = 17;
    public static int PreferenceTheme_ringtonePreferenceStyle = 18;
    public static int PreferenceTheme_seekBarPreferenceStyle = 19;
    public static int PreferenceTheme_switchPreferenceCompatStyle = 20;
    public static int PreferenceTheme_switchPreferenceStyle = 21;
    public static int PreferenceTheme_yesNoPreferenceStyle = 22;
    public static final int[] RecycleListView = {0, 0};
    public static int RecycleListView_paddingBottomNoButtons = 0;
    public static int RecycleListView_paddingTopNoTitle = 1;
    public static final int[] RecyclerView = {0, 0, 0, 0, 0, 0, 0, 0, 0, 16842948, 16842993};
    public static int RecyclerView_fastScrollEnabled = 0;
    public static int RecyclerView_fastScrollHorizontalThumbDrawable = 1;
    public static int RecyclerView_fastScrollHorizontalTrackDrawable = 2;
    public static int RecyclerView_fastScrollVerticalThumbDrawable = 3;
    public static int RecyclerView_fastScrollVerticalTrackDrawable = 4;
    public static int RecyclerView_layoutManager = 5;
    public static int RecyclerView_reverseLayout = 6;
    public static int RecyclerView_spanCount = 7;
    public static int RecyclerView_stackFromEnd = 8;
    public static int RecyclerView_android_orientation = 9;
    public static int RecyclerView_android_descendantFocusability = 10;
    public static final int[] RestrictedPreference = {0, 0};
    public static int RestrictedPreference_useAdminDisabledSummary = 0;
    public static int RestrictedPreference_userRestriction = 1;
    public static final int[] RestrictedSwitchPreference = {0, 0};
    public static int RestrictedSwitchPreference_restrictedSwitchSummary = 0;
    public static int RestrictedSwitchPreference_useAdditionalSummary = 1;
    public static final int[] SearchView = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16842970, 16843039, 16843296, 16843364};
    public static int SearchView_closeIcon = 0;
    public static int SearchView_commitIcon = 1;
    public static int SearchView_defaultQueryHint = 2;
    public static int SearchView_goIcon = 3;
    public static int SearchView_iconifiedByDefault = 4;
    public static int SearchView_layout = 5;
    public static int SearchView_queryBackground = 6;
    public static int SearchView_queryHint = 7;
    public static int SearchView_searchHintIcon = 8;
    public static int SearchView_searchIcon = 9;
    public static int SearchView_submitBackground = 10;
    public static int SearchView_suggestionRowLayout = 11;
    public static int SearchView_voiceIcon = 12;
    public static int SearchView_android_focusable = 13;
    public static int SearchView_android_maxWidth = 14;
    public static int SearchView_android_inputType = 15;
    public static int SearchView_android_imeOptions = 16;
    public static final int[] SeekBarPreference = {0, 0, 0, 0, 16842994, 16843062};
    public static int SeekBarPreference_adjustable = 0;
    public static int SeekBarPreference_min = 1;
    public static int SeekBarPreference_seekBarIncrement = 2;
    public static int SeekBarPreference_showSeekBarValue = 3;
    public static int SeekBarPreference_android_layout = 4;
    public static int SeekBarPreference_android_max = 5;
    public static final int[] Spinner = {0, 16842930, 16843126, 16843131, 16843362};
    public static int Spinner_popupTheme = 0;
    public static int Spinner_android_entries = 1;
    public static int Spinner_android_popupBackground = 2;
    public static int Spinner_android_prompt = 3;
    public static int Spinner_android_dropDownWidth = 4;
    public static final int[] SwitchCompat = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16843044, 16843045, 16843074};
    public static int SwitchCompat_showText = 0;
    public static int SwitchCompat_splitTrack = 1;
    public static int SwitchCompat_switchMinWidth = 2;
    public static int SwitchCompat_switchPadding = 3;
    public static int SwitchCompat_switchTextAppearance = 4;
    public static int SwitchCompat_thumbTextPadding = 5;
    public static int SwitchCompat_thumbTint = 6;
    public static int SwitchCompat_thumbTintMode = 7;
    public static int SwitchCompat_track = 8;
    public static int SwitchCompat_trackTint = 9;
    public static int SwitchCompat_trackTintMode = 10;
    public static int SwitchCompat_android_textOn = 11;
    public static int SwitchCompat_android_textOff = 12;
    public static int SwitchCompat_android_thumb = 13;
    public static final int[] SwitchPreference = {0, 0, 0, 0, 0, 16843247, 16843248, 16843249, 16843627, 16843628};
    public static int SwitchPreference_disableDependentsState = 0;
    public static int SwitchPreference_summaryOff = 1;
    public static int SwitchPreference_summaryOn = 2;
    public static int SwitchPreference_switchTextOff = 3;
    public static int SwitchPreference_switchTextOn = 4;
    public static int SwitchPreference_android_summaryOn = 5;
    public static int SwitchPreference_android_summaryOff = 6;
    public static int SwitchPreference_android_disableDependentsState = 7;
    public static int SwitchPreference_android_switchTextOn = 8;
    public static int SwitchPreference_android_switchTextOff = 9;
    public static final int[] SwitchPreferenceCompat = {0, 0, 0, 0, 0, 16843247, 16843248, 16843249, 16843627, 16843628};
    public static int SwitchPreferenceCompat_disableDependentsState = 0;
    public static int SwitchPreferenceCompat_summaryOff = 1;
    public static int SwitchPreferenceCompat_summaryOn = 2;
    public static int SwitchPreferenceCompat_switchTextOff = 3;
    public static int SwitchPreferenceCompat_switchTextOn = 4;
    public static int SwitchPreferenceCompat_android_summaryOn = 5;
    public static int SwitchPreferenceCompat_android_summaryOff = 6;
    public static int SwitchPreferenceCompat_android_disableDependentsState = 7;
    public static int SwitchPreferenceCompat_android_switchTextOn = 8;
    public static int SwitchPreferenceCompat_android_switchTextOff = 9;
    public static final int[] TextAppearance = {0, 0, 16842901, 16842902, 16842903, 16842904, 16842906, 16842907, 16843105, 16843106, 16843107, 16843108, 16843692};
    public static int TextAppearance_fontFamily = 0;
    public static int TextAppearance_textAllCaps = 1;
    public static int TextAppearance_android_textSize = 2;
    public static int TextAppearance_android_typeface = 3;
    public static int TextAppearance_android_textStyle = 4;
    public static int TextAppearance_android_textColor = 5;
    public static int TextAppearance_android_textColorHint = 6;
    public static int TextAppearance_android_textColorLink = 7;
    public static int TextAppearance_android_shadowColor = 8;
    public static int TextAppearance_android_shadowDx = 9;
    public static int TextAppearance_android_shadowDy = 10;
    public static int TextAppearance_android_shadowRadius = 11;
    public static int TextAppearance_android_fontFamily = 12;
    @Deprecated
    public static final int[] Toolbar = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16842927, 16843072};
    public static int Toolbar_buttonGravity = 0;
    public static int Toolbar_collapseContentDescription = 1;
    public static int Toolbar_collapseIcon = 2;
    public static int Toolbar_contentInsetEnd = 3;
    public static int Toolbar_contentInsetEndWithActions = 4;
    public static int Toolbar_contentInsetLeft = 5;
    public static int Toolbar_contentInsetRight = 6;
    public static int Toolbar_contentInsetStart = 7;
    public static int Toolbar_contentInsetStartWithNavigation = 8;
    public static int Toolbar_logo = 9;
    public static int Toolbar_logoDescription = 10;
    public static int Toolbar_maxButtonHeight = 11;
    public static int Toolbar_navigationContentDescription = 12;
    public static int Toolbar_navigationIcon = 13;
    public static int Toolbar_popupTheme = 14;
    public static int Toolbar_subtitle = 15;
    public static int Toolbar_subtitleTextAppearance = 16;
    public static int Toolbar_subtitleTextColor = 17;
    public static int Toolbar_title = 18;
    public static int Toolbar_titleMargin = 19;
    public static int Toolbar_titleMarginBottom = 20;
    public static int Toolbar_titleMarginEnd = 21;
    public static int Toolbar_titleMarginStart = 22;
    public static int Toolbar_titleMarginTop = 23;
    @Deprecated
    public static int Toolbar_titleMargins = 24;
    public static int Toolbar_titleTextAppearance = 25;
    public static int Toolbar_titleTextColor = 26;
    public static int Toolbar_android_gravity = 27;
    public static int Toolbar_android_minHeight = 28;
    public static final int[] View = {0, 0, 0, 16842752, 16842970};
    public static int View_paddingEnd = 0;
    public static int View_paddingStart = 1;
    public static int View_theme = 2;
    public static int View_android_theme = 3;
    public static int View_android_focusable = 4;
    public static final int[] ViewBackgroundHelper = {0, 0, 16842964};
    public static int ViewBackgroundHelper_backgroundTint = 0;
    public static int ViewBackgroundHelper_backgroundTintMode = 1;
    public static int ViewBackgroundHelper_android_background = 2;
    public static final int[] ViewStubCompat = {16842960, 16842994, 16842995};
    public static int ViewStubCompat_android_id = 0;
    public static int ViewStubCompat_android_layout = 1;
    public static int ViewStubCompat_android_inflatedId = 2;
    public static final int[] WifiEncryptionState = {0};
    public static int WifiEncryptionState_state_encrypted = 0;
    public static final int[] WifiEncryptionStateOwe = {0};
    public static int WifiEncryptionStateOwe_state_encrypted_owe = 0;
    public static final int[] WifiEncryptionStateSae = {0};
    public static int WifiEncryptionStateSae_state_encrypted_sae = 0;
    public static final int[] WifiMeteredState = {0};
    public static int WifiMeteredState_state_metered = 0;
    public static final int[] WifiSavedState = {0};
    public static int WifiSavedState_state_saved = 0;
}
