package com.ici.app.base;

/* loaded from: classes.dex */
public final class R$attr {
    public static final int actionBarDivider = 2130903040;
    public static final int actionBarItemBackground = 2130903041;
    public static final int actionBarPopupTheme = 2130903042;
    public static final int actionBarSize = 2130903043;
    public static final int actionBarSplitStyle = 2130903044;
    public static final int actionBarStyle = 2130903045;
    public static final int actionBarTabBarStyle = 2130903046;
    public static final int actionBarTabStyle = 2130903047;
    public static final int actionBarTabTextStyle = 2130903048;
    public static final int actionBarTheme = 2130903049;
    public static final int actionBarWidgetTheme = 2130903050;
    public static final int actionButtonStyle = 2130903051;
    public static final int actionDropDownStyle = 2130903052;
    public static final int actionLayout = 2130903053;
    public static final int actionMenuTextAppearance = 2130903054;
    public static final int actionMenuTextColor = 2130903055;
    public static final int actionModeBackground = 2130903056;
    public static final int actionModeCloseButtonStyle = 2130903057;
    public static final int actionModeCloseDrawable = 2130903058;
    public static final int actionModeCopyDrawable = 2130903059;
    public static final int actionModeCutDrawable = 2130903060;
    public static final int actionModeFindDrawable = 2130903061;
    public static final int actionModePasteDrawable = 2130903062;
    public static final int actionModePopupWindowStyle = **********;
    public static final int actionModeSelectAllDrawable = **********;
    public static final int actionModeShareDrawable = **********;
    public static final int actionModeSplitBackground = **********;
    public static final int actionModeStyle = **********;
    public static final int actionModeWebSearchDrawable = **********;
    public static final int actionOverflowButtonStyle = **********;
    public static final int actionOverflowMenuStyle = **********;
    public static final int actionProviderClass = **********;
    public static final int actionViewClass = **********;
    public static final int activityChooserViewStyle = **********;
    public static final int alertDialogButtonGroupStyle = **********;
    public static final int alertDialogCenterButtons = **********;
    public static final int alertDialogStyle = **********;
    public static final int alertDialogTheme = **********;
    public static final int allowStacking = **********;
    public static final int alpha = **********;
    public static final int alphabeticModifiers = **********;
    public static final int arrowHeadLength = **********;
    public static final int arrowShaftLength = **********;
    public static final int autoCompleteTextViewStyle = **********;
    public static final int autoSizeMaxTextSize = **********;
    public static final int autoSizeMinTextSize = **********;
    public static final int autoSizePresetSizes = **********;
    public static final int autoSizeStepGranularity = 2130903087;
    public static final int autoSizeTextType = 2130903088;
    public static final int background = 2130903089;
    public static final int backgroundSplit = 2130903090;
    public static final int backgroundStacked = 2130903091;
    public static final int backgroundTint = 2130903092;
    public static final int backgroundTintMode = 2130903093;
    public static final int barLength = 2130903094;
    public static final int barrierAllowsGoneWidgets = 2130903095;
    public static final int barrierDirection = 2130903096;
    public static final int borderlessButtonStyle = 2130903097;
    public static final int buttonBarButtonStyle = 2130903098;
    public static final int buttonBarNegativeButtonStyle = 2130903099;
    public static final int buttonBarNeutralButtonStyle = 2130903100;
    public static final int buttonBarPositiveButtonStyle = 2130903101;
    public static final int buttonBarStyle = 2130903102;
    public static final int buttonCompat = 2130903103;
    public static final int buttonGravity = 2130903104;
    public static final int buttonIconDimen = 2130903105;
    public static final int buttonPanelSideLayout = 2130903106;
    public static final int buttonStyle = 2130903107;
    public static final int buttonStyleSmall = 2130903108;
    public static final int buttonTint = 2130903109;
    public static final int buttonTintMode = 2130903110;
    public static final int chainUseRtl = 2130903111;
    public static final int checkboxStyle = 2130903112;
    public static final int checkedTextViewStyle = 2130903113;
    public static final int closeIcon = 2130903114;
    public static final int closeItemLayout = 2130903115;
    public static final int collapseContentDescription = 2130903116;
    public static final int collapseIcon = 2130903117;
    public static final int color = 2130903118;
    public static final int colorAccent = 2130903119;
    public static final int colorBackgroundFloating = 2130903120;
    public static final int colorButtonNormal = 2130903121;
    public static final int colorControlActivated = 2130903122;
    public static final int colorControlHighlight = 2130903123;
    public static final int colorControlNormal = 2130903124;
    public static final int colorError = 2130903125;
    public static final int colorPrimary = 2130903126;
    public static final int colorPrimaryDark = 2130903127;
    public static final int colorSwitchThumbNormal = 2130903128;
    public static final int commitIcon = 2130903129;
    public static final int constraintSet = 2130903130;
    public static final int constraint_referenced_ids = 2130903131;
    public static final int content = 2130903132;
    public static final int contentDescription = 2130903133;
    public static final int contentInsetEnd = 2130903134;
    public static final int contentInsetEndWithActions = 2130903135;
    public static final int contentInsetLeft = 2130903136;
    public static final int contentInsetRight = 2130903137;
    public static final int contentInsetStart = 2130903138;
    public static final int contentInsetStartWithNavigation = 2130903139;
    public static final int controlBackground = 2130903140;
    public static final int customNavigationLayout = 2130903141;
    public static final int defaultQueryHint = 2130903142;
    public static final int dialogCornerRadius = 2130903143;
    public static final int dialogPreferredPadding = 2130903144;
    public static final int dialogTheme = 2130903145;
    public static final int displayOptions = 2130903146;
    public static final int divider = 2130903147;
    public static final int dividerHorizontal = 2130903148;
    public static final int dividerPadding = 2130903149;
    public static final int dividerVertical = 2130903150;
    public static final int drawableBottomCompat = 2130903151;
    public static final int drawableEndCompat = 2130903152;
    public static final int drawableLeftCompat = 2130903153;
    public static final int drawableRightCompat = 2130903154;
    public static final int drawableSize = 2130903155;
    public static final int drawableStartCompat = 2130903156;
    public static final int drawableTint = 2130903157;
    public static final int drawableTintMode = 2130903158;
    public static final int drawableTopCompat = 2130903159;
    public static final int drawerArrowStyle = 2130903160;
    public static final int dropDownListViewStyle = 2130903161;
    public static final int dropdownListPreferredItemHeight = 2130903162;
    public static final int editTextBackground = **********;
    public static final int editTextColor = **********;
    public static final int editTextStyle = **********;
    public static final int elevation = **********;
    public static final int emptyVisibility = **********;
    public static final int expandActivityOverflowButtonDrawable = **********;
    public static final int firstBaselineToTopHeight = **********;
    public static final int font = **********;
    public static final int fontFamily = **********;
    public static final int fontProviderAuthority = **********;
    public static final int fontProviderCerts = **********;
    public static final int fontProviderFetchStrategy = **********;
    public static final int fontProviderFetchTimeout = **********;
    public static final int fontProviderPackage = **********;
    public static final int fontProviderQuery = **********;
    public static final int fontStyle = **********;
    public static final int fontVariationSettings = **********;
    public static final int fontWeight = **********;
    public static final int gapBetweenBars = **********;
    public static final int goIcon = **********;
    public static final int height = **********;
    public static final int hideOnContentScroll = **********;
    public static final int homeAsUpIndicator = **********;
    public static final int homeLayout = **********;
    public static final int icon = **********;
    public static final int iconTint = **********;
    public static final int iconTintMode = **********;
    public static final int iconifiedByDefault = **********;
    public static final int imageButtonStyle = **********;
    public static final int indeterminateProgressStyle = **********;
    public static final int initialActivityCount = **********;
    public static final int isLightTheme = **********;
    public static final int itemPadding = 2130903200;
    public static final int lastBaselineToBottomHeight = 2130903201;
    public static final int layout = 2130903202;
    public static final int layout_constrainedHeight = 2130903204;
    public static final int layout_constrainedWidth = 2130903205;
    public static final int layout_constraintBaseline_creator = 2130903206;
    public static final int layout_constraintBaseline_toBaselineOf = 2130903207;
    public static final int layout_constraintBottom_creator = 2130903208;
    public static final int layout_constraintBottom_toBottomOf = 2130903209;
    public static final int layout_constraintBottom_toTopOf = 2130903210;
    public static final int layout_constraintCircle = 2130903211;
    public static final int layout_constraintCircleAngle = 2130903212;
    public static final int layout_constraintCircleRadius = 2130903213;
    public static final int layout_constraintDimensionRatio = 2130903214;
    public static final int layout_constraintEnd_toEndOf = 2130903215;
    public static final int layout_constraintEnd_toStartOf = 2130903216;
    public static final int layout_constraintGuide_begin = 2130903217;
    public static final int layout_constraintGuide_end = 2130903218;
    public static final int layout_constraintGuide_percent = 2130903219;
    public static final int layout_constraintHeight_default = 2130903220;
    public static final int layout_constraintHeight_max = 2130903221;
    public static final int layout_constraintHeight_min = 2130903222;
    public static final int layout_constraintHeight_percent = 2130903223;
    public static final int layout_constraintHorizontal_bias = 2130903224;
    public static final int layout_constraintHorizontal_chainStyle = 2130903225;
    public static final int layout_constraintHorizontal_weight = 2130903226;
    public static final int layout_constraintLeft_creator = 2130903227;
    public static final int layout_constraintLeft_toLeftOf = 2130903228;
    public static final int layout_constraintLeft_toRightOf = 2130903229;
    public static final int layout_constraintRight_creator = 2130903230;
    public static final int layout_constraintRight_toLeftOf = 2130903231;
    public static final int layout_constraintRight_toRightOf = 2130903232;
    public static final int layout_constraintStart_toEndOf = 2130903233;
    public static final int layout_constraintStart_toStartOf = 2130903234;
    public static final int layout_constraintTop_creator = 2130903235;
    public static final int layout_constraintTop_toBottomOf = 2130903236;
    public static final int layout_constraintTop_toTopOf = 2130903237;
    public static final int layout_constraintVertical_bias = 2130903238;
    public static final int layout_constraintVertical_chainStyle = 2130903239;
    public static final int layout_constraintVertical_weight = 2130903240;
    public static final int layout_constraintWidth_default = 2130903241;
    public static final int layout_constraintWidth_max = 2130903242;
    public static final int layout_constraintWidth_min = 2130903243;
    public static final int layout_constraintWidth_percent = 2130903244;
    public static final int layout_editor_absoluteX = 2130903245;
    public static final int layout_editor_absoluteY = 2130903246;
    public static final int layout_goneMarginBottom = 2130903247;
    public static final int layout_goneMarginEnd = 2130903248;
    public static final int layout_goneMarginLeft = 2130903249;
    public static final int layout_goneMarginRight = 2130903250;
    public static final int layout_goneMarginStart = 2130903251;
    public static final int layout_goneMarginTop = 2130903252;
    public static final int layout_optimizationLevel = 2130903253;
    public static final int lineHeight = 2130903254;
    public static final int listChoiceBackgroundIndicator = 2130903255;
    public static final int listChoiceIndicatorMultipleAnimated = 2130903256;
    public static final int listChoiceIndicatorSingleAnimated = 2130903257;
    public static final int listDividerAlertDialog = 2130903258;
    public static final int listItemLayout = 2130903259;
    public static final int listLayout = 2130903260;
    public static final int listMenuViewStyle = 2130903261;
    public static final int listPopupWindowStyle = 2130903262;
    public static final int listPreferredItemHeight = 2130903263;
    public static final int listPreferredItemHeightLarge = 2130903264;
    public static final int listPreferredItemHeightSmall = 2130903265;
    public static final int listPreferredItemPaddingEnd = 2130903266;
    public static final int listPreferredItemPaddingLeft = 2130903267;
    public static final int listPreferredItemPaddingRight = 2130903268;
    public static final int listPreferredItemPaddingStart = 2130903269;
    public static final int logo = 2130903270;
    public static final int logoDescription = 2130903271;
    public static final int maxButtonHeight = 2130903272;
    public static final int measureWithLargestChild = 2130903273;
    public static final int menu = 2130903274;
    public static final int multiChoiceItemLayout = 2130903275;
    public static final int navigationContentDescription = 2130903276;
    public static final int navigationIcon = 2130903277;
    public static final int navigationMode = 2130903278;
    public static final int numericModifiers = 2130903279;
    public static final int overlapAnchor = 2130903280;
    public static final int paddingBottomNoButtons = 2130903281;
    public static final int paddingEnd = 2130903282;
    public static final int paddingStart = 2130903283;
    public static final int paddingTopNoTitle = 2130903284;
    public static final int panelBackground = 2130903285;
    public static final int panelMenuListTheme = 2130903286;
    public static final int panelMenuListWidth = 2130903287;
    public static final int popupMenuStyle = 2130903296;
    public static final int popupTheme = 2130903297;
    public static final int popupWindowStyle = 2130903298;
    public static final int preserveIconSpacing = 2130903299;
    public static final int progressBarPadding = 2130903300;
    public static final int progressBarStyle = 2130903301;
    public static final int queryBackground = 2130903302;
    public static final int queryHint = 2130903303;
    public static final int radioButtonStyle = 2130903304;
    public static final int ratingBarStyle = 2130903305;
    public static final int ratingBarStyleIndicator = 2130903306;
    public static final int ratingBarStyleSmall = 2130903307;
    public static final int searchHintIcon = 2130903310;
    public static final int searchIcon = 2130903311;
    public static final int searchViewStyle = 2130903312;
    public static final int seekBarStyle = 2130903313;
    public static final int selectableItemBackground = 2130903314;
    public static final int selectableItemBackgroundBorderless = 2130903315;
    public static final int showAsAction = 2130903316;
    public static final int showDividers = 2130903317;
    public static final int showText = 2130903318;
    public static final int showTitle = 2130903319;
    public static final int singleChoiceItemLayout = 2130903320;
    public static final int spinBars = 2130903338;
    public static final int spinnerDropDownItemStyle = 2130903339;
    public static final int spinnerStyle = 2130903340;
    public static final int splitTrack = 2130903341;
    public static final int srcCompat = 2130903342;
    public static final int state_above_anchor = 2130903344;
    public static final int subMenuArrow = 2130903345;
    public static final int submitBackground = 2130903346;
    public static final int subtitle = 2130903347;
    public static final int subtitleTextAppearance = 2130903348;
    public static final int subtitleTextColor = 2130903349;
    public static final int subtitleTextStyle = 2130903350;
    public static final int suggestionRowLayout = 2130903351;
    public static final int switchMinWidth = 2130903352;
    public static final int switchPadding = 2130903353;
    public static final int switchStyle = 2130903354;
    public static final int switchTextAppearance = 2130903355;
    public static final int textAllCaps = 2130903356;
    public static final int textAppearanceLargePopupMenu = 2130903357;
    public static final int textAppearanceListItem = 2130903358;
    public static final int textAppearanceListItemSecondary = 2130903359;
    public static final int textAppearanceListItemSmall = 2130903360;
    public static final int textAppearancePopupMenuHeader = 2130903361;
    public static final int textAppearanceSearchResultSubtitle = 2130903362;
    public static final int textAppearanceSearchResultTitle = 2130903363;
    public static final int textAppearanceSmallPopupMenu = 2130903364;
    public static final int textColorAlertDialogListItem = 2130903365;
    public static final int textColorSearchUrl = 2130903366;
    public static final int textLocale = 2130903367;
    public static final int theme = 2130903368;
    public static final int thickness = 2130903369;
    public static final int thumbTextPadding = 2130903370;
    public static final int thumbTint = 2130903371;
    public static final int thumbTintMode = 2130903372;
    public static final int tickMark = 2130903373;
    public static final int tickMarkTint = 2130903374;
    public static final int tickMarkTintMode = 2130903375;
    public static final int tint = 2130903376;
    public static final int tintMode = 2130903377;
    public static final int title = 2130903378;
    public static final int titleMargin = 2130903379;
    public static final int titleMarginBottom = 2130903380;
    public static final int titleMarginEnd = 2130903381;
    public static final int titleMarginStart = 2130903382;
    public static final int titleMarginTop = 2130903383;
    public static final int titleMargins = 2130903384;
    public static final int titleTextAppearance = 2130903385;
    public static final int titleTextColor = 2130903386;
    public static final int titleTextStyle = 2130903387;
    public static final int toolbarNavigationButtonStyle = 2130903388;
    public static final int toolbarStyle = 2130903389;
    public static final int tooltipForegroundColor = 2130903390;
    public static final int tooltipFrameBackground = 2130903391;
    public static final int tooltipText = 2130903392;
    public static final int track = 2130903393;
    public static final int trackTint = 2130903394;
    public static final int trackTintMode = 2130903395;
    public static final int ttcIndex = 2130903396;
    public static final int viewInflaterClass = 2130903398;
    public static final int voiceIcon = 2130903399;
    public static final int windowActionBar = 2130903400;
    public static final int windowActionBarOverlay = 2130903401;
    public static final int windowActionModeOverlay = 2130903402;
    public static final int windowFixedHeightMajor = 2130903403;
    public static final int windowFixedHeightMinor = 2130903404;
    public static final int windowFixedWidthMajor = 2130903405;
    public static final int windowFixedWidthMinor = 2130903406;
    public static final int windowMinWidthMajor = 2130903407;
    public static final int windowMinWidthMinor = 2130903408;
    public static final int windowNoTitle = 2130903409;
}
