package com.ici.app.bean;

import androidx.fragment.app.Fragment;
import java.io.Serializable;

/* loaded from: classes.dex */
public class FragmentInfo implements Serializable, Comparable {
    public static final long serialVersionUID = 5659302358558105374L;
    public int contentID;
    public Fragment fragment;
    public int id;

    /* loaded from: classes.dex */
    public static class b {

        /* renamed from: a  reason: collision with root package name */
        public int f504a;

        /* renamed from: b  reason: collision with root package name */
        public int f505b;
        public Fragment c;
    }

    @Override // java.lang.Comparable
    public int compareTo(Object obj) {
        int i = ((FragmentInfo) obj).id;
        int i2 = this.id;
        if (i < i2) {
            return -1;
        }
        return i > i2 ? 1 : 0;
    }

    public int getContentID() {
        return this.contentID;
    }

    public Fragment getFragment() {
        return this.fragment;
    }

    public int getId() {
        return this.id;
    }

    public void setId(int i) {
        this.id = i;
    }

    public String toString() {
        StringBuilder e = b.a.b.a.a.e("FragmentInfo{contentID=");
        e.append(this.contentID);
        e.append(", id=");
        e.append(this.id);
        e.append(", fragment=");
        e.append(this.fragment);
        e.append('}');
        return e.toString();
    }

    public FragmentInfo(b bVar) {
        this.contentID = bVar.f504a;
        this.id = bVar.f505b;
        this.fragment = bVar.c;
    }
}
