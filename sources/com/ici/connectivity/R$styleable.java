package com.ici.connectivity;

/* loaded from: classes.dex */
public final class R$styleable {
    public static final int ActionBarLayout_android_layout_gravity = 0;
    public static final int ActionBar_background = 0;
    public static final int ActionBar_backgroundSplit = 1;
    public static final int ActionBar_backgroundStacked = 2;
    public static final int ActionBar_contentInsetEnd = 3;
    public static final int ActionBar_contentInsetEndWithActions = 4;
    public static final int ActionBar_contentInsetLeft = 5;
    public static final int ActionBar_contentInsetRight = 6;
    public static final int ActionBar_contentInsetStart = 7;
    public static final int ActionBar_contentInsetStartWithNavigation = 8;
    public static final int ActionBar_customNavigationLayout = 9;
    public static final int ActionBar_displayOptions = 10;
    public static final int ActionBar_divider = 11;
    public static final int ActionBar_elevation = 12;
    public static final int ActionBar_height = 13;
    public static final int ActionBar_hideOnContentScroll = 14;
    public static final int ActionBar_homeAsUpIndicator = 15;
    public static final int ActionBar_homeLayout = 16;
    public static final int ActionBar_icon = 17;
    public static final int ActionBar_indeterminateProgressStyle = 18;
    public static final int ActionBar_itemPadding = 19;
    public static final int ActionBar_logo = 20;
    public static final int ActionBar_navigationMode = 21;
    public static final int ActionBar_popupTheme = 22;
    public static final int ActionBar_progressBarPadding = 23;
    public static final int ActionBar_progressBarStyle = 24;
    public static final int ActionBar_subtitle = 25;
    public static final int ActionBar_subtitleTextStyle = 26;
    public static final int ActionBar_title = 27;
    public static final int ActionBar_titleTextStyle = 28;
    public static final int ActionMenuItemView_android_minWidth = 0;
    public static final int ActionMode_background = 0;
    public static final int ActionMode_backgroundSplit = 1;
    public static final int ActionMode_closeItemLayout = 2;
    public static final int ActionMode_height = 3;
    public static final int ActionMode_subtitleTextStyle = 4;
    public static final int ActionMode_titleTextStyle = 5;
    public static final int ActivityChooserView_expandActivityOverflowButtonDrawable = 0;
    public static final int ActivityChooserView_initialActivityCount = 1;
    public static final int AlertDialog_android_layout = 0;
    public static final int AlertDialog_buttonIconDimen = 1;
    public static final int AlertDialog_buttonPanelSideLayout = 2;
    public static final int AlertDialog_listItemLayout = 3;
    public static final int AlertDialog_listLayout = 4;
    public static final int AlertDialog_multiChoiceItemLayout = 5;
    public static final int AlertDialog_showTitle = 6;
    public static final int AlertDialog_singleChoiceItemLayout = 7;
    public static final int AnimatedStateListDrawableCompat_android_constantSize = 3;
    public static final int AnimatedStateListDrawableCompat_android_dither = 0;
    public static final int AnimatedStateListDrawableCompat_android_enterFadeDuration = 4;
    public static final int AnimatedStateListDrawableCompat_android_exitFadeDuration = 5;
    public static final int AnimatedStateListDrawableCompat_android_variablePadding = 2;
    public static final int AnimatedStateListDrawableCompat_android_visible = 1;
    public static final int AnimatedStateListDrawableItem_android_drawable = 1;
    public static final int AnimatedStateListDrawableItem_android_id = 0;
    public static final int AnimatedStateListDrawableTransition_android_drawable = 0;
    public static final int AnimatedStateListDrawableTransition_android_fromId = 2;
    public static final int AnimatedStateListDrawableTransition_android_reversible = 3;
    public static final int AnimatedStateListDrawableTransition_android_toId = 1;
    public static final int AppCompatImageView_android_src = 0;
    public static final int AppCompatImageView_srcCompat = 1;
    public static final int AppCompatImageView_tint = 2;
    public static final int AppCompatImageView_tintMode = 3;
    public static final int AppCompatSeekBar_android_thumb = 0;
    public static final int AppCompatSeekBar_tickMark = 1;
    public static final int AppCompatSeekBar_tickMarkTint = 2;
    public static final int AppCompatSeekBar_tickMarkTintMode = 3;
    public static final int AppCompatTextHelper_android_drawableBottom = 2;
    public static final int AppCompatTextHelper_android_drawableEnd = 6;
    public static final int AppCompatTextHelper_android_drawableLeft = 3;
    public static final int AppCompatTextHelper_android_drawableRight = 4;
    public static final int AppCompatTextHelper_android_drawableStart = 5;
    public static final int AppCompatTextHelper_android_drawableTop = 1;
    public static final int AppCompatTextHelper_android_textAppearance = 0;
    public static final int AppCompatTextView_android_textAppearance = 0;
    public static final int AppCompatTextView_autoSizeMaxTextSize = 1;
    public static final int AppCompatTextView_autoSizeMinTextSize = 2;
    public static final int AppCompatTextView_autoSizePresetSizes = 3;
    public static final int AppCompatTextView_autoSizeStepGranularity = 4;
    public static final int AppCompatTextView_autoSizeTextType = 5;
    public static final int AppCompatTextView_drawableBottomCompat = 6;
    public static final int AppCompatTextView_drawableEndCompat = 7;
    public static final int AppCompatTextView_drawableLeftCompat = 8;
    public static final int AppCompatTextView_drawableRightCompat = 9;
    public static final int AppCompatTextView_drawableStartCompat = 10;
    public static final int AppCompatTextView_drawableTint = 11;
    public static final int AppCompatTextView_drawableTintMode = 12;
    public static final int AppCompatTextView_drawableTopCompat = 13;
    public static final int AppCompatTextView_firstBaselineToTopHeight = 14;
    public static final int AppCompatTextView_fontFamily = 15;
    public static final int AppCompatTextView_fontVariationSettings = 16;
    public static final int AppCompatTextView_lastBaselineToBottomHeight = 17;
    public static final int AppCompatTextView_lineHeight = 18;
    public static final int AppCompatTextView_textAllCaps = 19;
    public static final int AppCompatTextView_textLocale = 20;
    public static final int AppCompatTheme_actionBarDivider = 2;
    public static final int AppCompatTheme_actionBarItemBackground = 3;
    public static final int AppCompatTheme_actionBarPopupTheme = 4;
    public static final int AppCompatTheme_actionBarSize = 5;
    public static final int AppCompatTheme_actionBarSplitStyle = 6;
    public static final int AppCompatTheme_actionBarStyle = 7;
    public static final int AppCompatTheme_actionBarTabBarStyle = 8;
    public static final int AppCompatTheme_actionBarTabStyle = 9;
    public static final int AppCompatTheme_actionBarTabTextStyle = 10;
    public static final int AppCompatTheme_actionBarTheme = 11;
    public static final int AppCompatTheme_actionBarWidgetTheme = 12;
    public static final int AppCompatTheme_actionButtonStyle = 13;
    public static final int AppCompatTheme_actionDropDownStyle = 14;
    public static final int AppCompatTheme_actionMenuTextAppearance = 15;
    public static final int AppCompatTheme_actionMenuTextColor = 16;
    public static final int AppCompatTheme_actionModeBackground = 17;
    public static final int AppCompatTheme_actionModeCloseButtonStyle = 18;
    public static final int AppCompatTheme_actionModeCloseDrawable = 19;
    public static final int AppCompatTheme_actionModeCopyDrawable = 20;
    public static final int AppCompatTheme_actionModeCutDrawable = 21;
    public static final int AppCompatTheme_actionModeFindDrawable = 22;
    public static final int AppCompatTheme_actionModePasteDrawable = 23;
    public static final int AppCompatTheme_actionModePopupWindowStyle = 24;
    public static final int AppCompatTheme_actionModeSelectAllDrawable = 25;
    public static final int AppCompatTheme_actionModeShareDrawable = 26;
    public static final int AppCompatTheme_actionModeSplitBackground = 27;
    public static final int AppCompatTheme_actionModeStyle = 28;
    public static final int AppCompatTheme_actionModeWebSearchDrawable = 29;
    public static final int AppCompatTheme_actionOverflowButtonStyle = 30;
    public static final int AppCompatTheme_actionOverflowMenuStyle = 31;
    public static final int AppCompatTheme_activityChooserViewStyle = 32;
    public static final int AppCompatTheme_alertDialogButtonGroupStyle = 33;
    public static final int AppCompatTheme_alertDialogCenterButtons = 34;
    public static final int AppCompatTheme_alertDialogStyle = 35;
    public static final int AppCompatTheme_alertDialogTheme = 36;
    public static final int AppCompatTheme_android_windowAnimationStyle = 1;
    public static final int AppCompatTheme_android_windowIsFloating = 0;
    public static final int AppCompatTheme_autoCompleteTextViewStyle = 37;
    public static final int AppCompatTheme_borderlessButtonStyle = 38;
    public static final int AppCompatTheme_buttonBarButtonStyle = 39;
    public static final int AppCompatTheme_buttonBarNegativeButtonStyle = 40;
    public static final int AppCompatTheme_buttonBarNeutralButtonStyle = 41;
    public static final int AppCompatTheme_buttonBarPositiveButtonStyle = 42;
    public static final int AppCompatTheme_buttonBarStyle = 43;
    public static final int AppCompatTheme_buttonStyle = 44;
    public static final int AppCompatTheme_buttonStyleSmall = 45;
    public static final int AppCompatTheme_checkboxStyle = 46;
    public static final int AppCompatTheme_checkedTextViewStyle = 47;
    public static final int AppCompatTheme_colorAccent = 48;
    public static final int AppCompatTheme_colorBackgroundFloating = 49;
    public static final int AppCompatTheme_colorButtonNormal = 50;
    public static final int AppCompatTheme_colorControlActivated = 51;
    public static final int AppCompatTheme_colorControlHighlight = 52;
    public static final int AppCompatTheme_colorControlNormal = 53;
    public static final int AppCompatTheme_colorError = 54;
    public static final int AppCompatTheme_colorPrimary = 55;
    public static final int AppCompatTheme_colorPrimaryDark = 56;
    public static final int AppCompatTheme_colorSwitchThumbNormal = 57;
    public static final int AppCompatTheme_controlBackground = 58;
    public static final int AppCompatTheme_dialogCornerRadius = 59;
    public static final int AppCompatTheme_dialogPreferredPadding = 60;
    public static final int AppCompatTheme_dialogTheme = 61;
    public static final int AppCompatTheme_dividerHorizontal = 62;
    public static final int AppCompatTheme_dividerVertical = 63;
    public static final int AppCompatTheme_dropDownListViewStyle = 64;
    public static final int AppCompatTheme_dropdownListPreferredItemHeight = 65;
    public static final int AppCompatTheme_editTextBackground = 66;
    public static final int AppCompatTheme_editTextColor = 67;
    public static final int AppCompatTheme_editTextStyle = 68;
    public static final int AppCompatTheme_homeAsUpIndicator = 69;
    public static final int AppCompatTheme_imageButtonStyle = 70;
    public static final int AppCompatTheme_listChoiceBackgroundIndicator = 71;
    public static final int AppCompatTheme_listChoiceIndicatorMultipleAnimated = 72;
    public static final int AppCompatTheme_listChoiceIndicatorSingleAnimated = 73;
    public static final int AppCompatTheme_listDividerAlertDialog = 74;
    public static final int AppCompatTheme_listMenuViewStyle = 75;
    public static final int AppCompatTheme_listPopupWindowStyle = 76;
    public static final int AppCompatTheme_listPreferredItemHeight = 77;
    public static final int AppCompatTheme_listPreferredItemHeightLarge = 78;
    public static final int AppCompatTheme_listPreferredItemHeightSmall = 79;
    public static final int AppCompatTheme_listPreferredItemPaddingEnd = 80;
    public static final int AppCompatTheme_listPreferredItemPaddingLeft = 81;
    public static final int AppCompatTheme_listPreferredItemPaddingRight = 82;
    public static final int AppCompatTheme_listPreferredItemPaddingStart = 83;
    public static final int AppCompatTheme_panelBackground = 84;
    public static final int AppCompatTheme_panelMenuListTheme = 85;
    public static final int AppCompatTheme_panelMenuListWidth = 86;
    public static final int AppCompatTheme_popupMenuStyle = 87;
    public static final int AppCompatTheme_popupWindowStyle = 88;
    public static final int AppCompatTheme_radioButtonStyle = 89;
    public static final int AppCompatTheme_ratingBarStyle = 90;
    public static final int AppCompatTheme_ratingBarStyleIndicator = 91;
    public static final int AppCompatTheme_ratingBarStyleSmall = 92;
    public static final int AppCompatTheme_searchViewStyle = 93;
    public static final int AppCompatTheme_seekBarStyle = 94;
    public static final int AppCompatTheme_selectableItemBackground = 95;
    public static final int AppCompatTheme_selectableItemBackgroundBorderless = 96;
    public static final int AppCompatTheme_spinnerDropDownItemStyle = 97;
    public static final int AppCompatTheme_spinnerStyle = 98;
    public static final int AppCompatTheme_switchStyle = 99;
    public static final int AppCompatTheme_textAppearanceLargePopupMenu = 100;
    public static final int AppCompatTheme_textAppearanceListItem = 101;
    public static final int AppCompatTheme_textAppearanceListItemSecondary = 102;
    public static final int AppCompatTheme_textAppearanceListItemSmall = 103;
    public static final int AppCompatTheme_textAppearancePopupMenuHeader = 104;
    public static final int AppCompatTheme_textAppearanceSearchResultSubtitle = 105;
    public static final int AppCompatTheme_textAppearanceSearchResultTitle = 106;
    public static final int AppCompatTheme_textAppearanceSmallPopupMenu = 107;
    public static final int AppCompatTheme_textColorAlertDialogListItem = 108;
    public static final int AppCompatTheme_textColorSearchUrl = 109;
    public static final int AppCompatTheme_toolbarNavigationButtonStyle = 110;
    public static final int AppCompatTheme_toolbarStyle = 111;
    public static final int AppCompatTheme_tooltipForegroundColor = 112;
    public static final int AppCompatTheme_tooltipFrameBackground = 113;
    public static final int AppCompatTheme_viewInflaterClass = 114;
    public static final int AppCompatTheme_windowActionBar = 115;
    public static final int AppCompatTheme_windowActionBarOverlay = 116;
    public static final int AppCompatTheme_windowActionModeOverlay = 117;
    public static final int AppCompatTheme_windowFixedHeightMajor = 118;
    public static final int AppCompatTheme_windowFixedHeightMinor = 119;
    public static final int AppCompatTheme_windowFixedWidthMajor = 120;
    public static final int AppCompatTheme_windowFixedWidthMinor = 121;
    public static final int AppCompatTheme_windowMinWidthMajor = 122;
    public static final int AppCompatTheme_windowMinWidthMinor = 123;
    public static final int AppCompatTheme_windowNoTitle = 124;
    public static final int ButtonBarLayout_allowStacking = 0;
    public static final int ColorStateListItem_alpha = 2;
    public static final int ColorStateListItem_android_alpha = 1;
    public static final int ColorStateListItem_android_color = 0;
    public static final int CompoundButton_android_button = 0;
    public static final int CompoundButton_buttonCompat = 1;
    public static final int CompoundButton_buttonTint = 2;
    public static final int CompoundButton_buttonTintMode = 3;
    public static final int ConstraintLayout_Layout_android_maxHeight = 2;
    public static final int ConstraintLayout_Layout_android_maxWidth = 1;
    public static final int ConstraintLayout_Layout_android_minHeight = 4;
    public static final int ConstraintLayout_Layout_android_minWidth = 3;
    public static final int ConstraintLayout_Layout_android_orientation = 0;
    public static final int ConstraintLayout_Layout_barrierAllowsGoneWidgets = 5;
    public static final int ConstraintLayout_Layout_barrierDirection = 6;
    public static final int ConstraintLayout_Layout_chainUseRtl = 7;
    public static final int ConstraintLayout_Layout_constraintSet = 8;
    public static final int ConstraintLayout_Layout_constraint_referenced_ids = 9;
    public static final int ConstraintLayout_Layout_layout_constrainedHeight = 10;
    public static final int ConstraintLayout_Layout_layout_constrainedWidth = 11;
    public static final int ConstraintLayout_Layout_layout_constraintBaseline_creator = 12;
    public static final int ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf = 13;
    public static final int ConstraintLayout_Layout_layout_constraintBottom_creator = 14;
    public static final int ConstraintLayout_Layout_layout_constraintBottom_toBottomOf = 15;
    public static final int ConstraintLayout_Layout_layout_constraintBottom_toTopOf = 16;
    public static final int ConstraintLayout_Layout_layout_constraintCircle = 17;
    public static final int ConstraintLayout_Layout_layout_constraintCircleAngle = 18;
    public static final int ConstraintLayout_Layout_layout_constraintCircleRadius = 19;
    public static final int ConstraintLayout_Layout_layout_constraintDimensionRatio = 20;
    public static final int ConstraintLayout_Layout_layout_constraintEnd_toEndOf = 21;
    public static final int ConstraintLayout_Layout_layout_constraintEnd_toStartOf = 22;
    public static final int ConstraintLayout_Layout_layout_constraintGuide_begin = 23;
    public static final int ConstraintLayout_Layout_layout_constraintGuide_end = 24;
    public static final int ConstraintLayout_Layout_layout_constraintGuide_percent = 25;
    public static final int ConstraintLayout_Layout_layout_constraintHeight_default = 26;
    public static final int ConstraintLayout_Layout_layout_constraintHeight_max = 27;
    public static final int ConstraintLayout_Layout_layout_constraintHeight_min = 28;
    public static final int ConstraintLayout_Layout_layout_constraintHeight_percent = 29;
    public static final int ConstraintLayout_Layout_layout_constraintHorizontal_bias = 30;
    public static final int ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle = 31;
    public static final int ConstraintLayout_Layout_layout_constraintHorizontal_weight = 32;
    public static final int ConstraintLayout_Layout_layout_constraintLeft_creator = 33;
    public static final int ConstraintLayout_Layout_layout_constraintLeft_toLeftOf = 34;
    public static final int ConstraintLayout_Layout_layout_constraintLeft_toRightOf = 35;
    public static final int ConstraintLayout_Layout_layout_constraintRight_creator = 36;
    public static final int ConstraintLayout_Layout_layout_constraintRight_toLeftOf = 37;
    public static final int ConstraintLayout_Layout_layout_constraintRight_toRightOf = 38;
    public static final int ConstraintLayout_Layout_layout_constraintStart_toEndOf = 39;
    public static final int ConstraintLayout_Layout_layout_constraintStart_toStartOf = 40;
    public static final int ConstraintLayout_Layout_layout_constraintTop_creator = 41;
    public static final int ConstraintLayout_Layout_layout_constraintTop_toBottomOf = 42;
    public static final int ConstraintLayout_Layout_layout_constraintTop_toTopOf = 43;
    public static final int ConstraintLayout_Layout_layout_constraintVertical_bias = 44;
    public static final int ConstraintLayout_Layout_layout_constraintVertical_chainStyle = 45;
    public static final int ConstraintLayout_Layout_layout_constraintVertical_weight = 46;
    public static final int ConstraintLayout_Layout_layout_constraintWidth_default = 47;
    public static final int ConstraintLayout_Layout_layout_constraintWidth_max = 48;
    public static final int ConstraintLayout_Layout_layout_constraintWidth_min = 49;
    public static final int ConstraintLayout_Layout_layout_constraintWidth_percent = 50;
    public static final int ConstraintLayout_Layout_layout_editor_absoluteX = 51;
    public static final int ConstraintLayout_Layout_layout_editor_absoluteY = 52;
    public static final int ConstraintLayout_Layout_layout_goneMarginBottom = 53;
    public static final int ConstraintLayout_Layout_layout_goneMarginEnd = 54;
    public static final int ConstraintLayout_Layout_layout_goneMarginLeft = 55;
    public static final int ConstraintLayout_Layout_layout_goneMarginRight = 56;
    public static final int ConstraintLayout_Layout_layout_goneMarginStart = 57;
    public static final int ConstraintLayout_Layout_layout_goneMarginTop = 58;
    public static final int ConstraintLayout_Layout_layout_optimizationLevel = 59;
    public static final int ConstraintLayout_placeholder_content = 0;
    public static final int ConstraintLayout_placeholder_emptyVisibility = 1;
    public static final int ConstraintSet_android_alpha = 13;
    public static final int ConstraintSet_android_elevation = 26;
    public static final int ConstraintSet_android_id = 1;
    public static final int ConstraintSet_android_layout_height = 4;
    public static final int ConstraintSet_android_layout_marginBottom = 8;
    public static final int ConstraintSet_android_layout_marginEnd = 24;
    public static final int ConstraintSet_android_layout_marginLeft = 5;
    public static final int ConstraintSet_android_layout_marginRight = 7;
    public static final int ConstraintSet_android_layout_marginStart = 23;
    public static final int ConstraintSet_android_layout_marginTop = 6;
    public static final int ConstraintSet_android_layout_width = 3;
    public static final int ConstraintSet_android_maxHeight = 10;
    public static final int ConstraintSet_android_maxWidth = 9;
    public static final int ConstraintSet_android_minHeight = 12;
    public static final int ConstraintSet_android_minWidth = 11;
    public static final int ConstraintSet_android_orientation = 0;
    public static final int ConstraintSet_android_rotation = 20;
    public static final int ConstraintSet_android_rotationX = 21;
    public static final int ConstraintSet_android_rotationY = 22;
    public static final int ConstraintSet_android_scaleX = 18;
    public static final int ConstraintSet_android_scaleY = 19;
    public static final int ConstraintSet_android_transformPivotX = 14;
    public static final int ConstraintSet_android_transformPivotY = 15;
    public static final int ConstraintSet_android_translationX = 16;
    public static final int ConstraintSet_android_translationY = 17;
    public static final int ConstraintSet_android_translationZ = 25;
    public static final int ConstraintSet_android_visibility = 2;
    public static final int ConstraintSet_barrierAllowsGoneWidgets = 27;
    public static final int ConstraintSet_barrierDirection = 28;
    public static final int ConstraintSet_chainUseRtl = 29;
    public static final int ConstraintSet_constraint_referenced_ids = 30;
    public static final int ConstraintSet_layout_constrainedHeight = 31;
    public static final int ConstraintSet_layout_constrainedWidth = 32;
    public static final int ConstraintSet_layout_constraintBaseline_creator = 33;
    public static final int ConstraintSet_layout_constraintBaseline_toBaselineOf = 34;
    public static final int ConstraintSet_layout_constraintBottom_creator = 35;
    public static final int ConstraintSet_layout_constraintBottom_toBottomOf = 36;
    public static final int ConstraintSet_layout_constraintBottom_toTopOf = 37;
    public static final int ConstraintSet_layout_constraintCircle = 38;
    public static final int ConstraintSet_layout_constraintCircleAngle = 39;
    public static final int ConstraintSet_layout_constraintCircleRadius = 40;
    public static final int ConstraintSet_layout_constraintDimensionRatio = 41;
    public static final int ConstraintSet_layout_constraintEnd_toEndOf = 42;
    public static final int ConstraintSet_layout_constraintEnd_toStartOf = 43;
    public static final int ConstraintSet_layout_constraintGuide_begin = 44;
    public static final int ConstraintSet_layout_constraintGuide_end = 45;
    public static final int ConstraintSet_layout_constraintGuide_percent = 46;
    public static final int ConstraintSet_layout_constraintHeight_default = 47;
    public static final int ConstraintSet_layout_constraintHeight_max = 48;
    public static final int ConstraintSet_layout_constraintHeight_min = 49;
    public static final int ConstraintSet_layout_constraintHeight_percent = 50;
    public static final int ConstraintSet_layout_constraintHorizontal_bias = 51;
    public static final int ConstraintSet_layout_constraintHorizontal_chainStyle = 52;
    public static final int ConstraintSet_layout_constraintHorizontal_weight = 53;
    public static final int ConstraintSet_layout_constraintLeft_creator = 54;
    public static final int ConstraintSet_layout_constraintLeft_toLeftOf = 55;
    public static final int ConstraintSet_layout_constraintLeft_toRightOf = 56;
    public static final int ConstraintSet_layout_constraintRight_creator = 57;
    public static final int ConstraintSet_layout_constraintRight_toLeftOf = 58;
    public static final int ConstraintSet_layout_constraintRight_toRightOf = 59;
    public static final int ConstraintSet_layout_constraintStart_toEndOf = 60;
    public static final int ConstraintSet_layout_constraintStart_toStartOf = 61;
    public static final int ConstraintSet_layout_constraintTop_creator = 62;
    public static final int ConstraintSet_layout_constraintTop_toBottomOf = 63;
    public static final int ConstraintSet_layout_constraintTop_toTopOf = 64;
    public static final int ConstraintSet_layout_constraintVertical_bias = 65;
    public static final int ConstraintSet_layout_constraintVertical_chainStyle = 66;
    public static final int ConstraintSet_layout_constraintVertical_weight = 67;
    public static final int ConstraintSet_layout_constraintWidth_default = 68;
    public static final int ConstraintSet_layout_constraintWidth_max = 69;
    public static final int ConstraintSet_layout_constraintWidth_min = 70;
    public static final int ConstraintSet_layout_constraintWidth_percent = 71;
    public static final int ConstraintSet_layout_editor_absoluteX = 72;
    public static final int ConstraintSet_layout_editor_absoluteY = 73;
    public static final int ConstraintSet_layout_goneMarginBottom = 74;
    public static final int ConstraintSet_layout_goneMarginEnd = 75;
    public static final int ConstraintSet_layout_goneMarginLeft = 76;
    public static final int ConstraintSet_layout_goneMarginRight = 77;
    public static final int ConstraintSet_layout_goneMarginStart = 78;
    public static final int ConstraintSet_layout_goneMarginTop = 79;
    public static final int CustomTextView_typeface = 0;
    public static final int DrawerArrowToggle_arrowHeadLength = 0;
    public static final int DrawerArrowToggle_arrowShaftLength = 1;
    public static final int DrawerArrowToggle_barLength = 2;
    public static final int DrawerArrowToggle_color = 3;
    public static final int DrawerArrowToggle_drawableSize = 4;
    public static final int DrawerArrowToggle_gapBetweenBars = 5;
    public static final int DrawerArrowToggle_spinBars = 6;
    public static final int DrawerArrowToggle_thickness = 7;
    public static final int FontFamilyFont_android_font = 0;
    public static final int FontFamilyFont_android_fontStyle = 2;
    public static final int FontFamilyFont_android_fontVariationSettings = 4;
    public static final int FontFamilyFont_android_fontWeight = 1;
    public static final int FontFamilyFont_android_ttcIndex = 3;
    public static final int FontFamilyFont_font = 5;
    public static final int FontFamilyFont_fontStyle = 6;
    public static final int FontFamilyFont_fontVariationSettings = 7;
    public static final int FontFamilyFont_fontWeight = 8;
    public static final int FontFamilyFont_ttcIndex = 9;
    public static final int FontFamily_fontProviderAuthority = 0;
    public static final int FontFamily_fontProviderCerts = 1;
    public static final int FontFamily_fontProviderFetchStrategy = 2;
    public static final int FontFamily_fontProviderFetchTimeout = 3;
    public static final int FontFamily_fontProviderPackage = 4;
    public static final int FontFamily_fontProviderQuery = 5;
    public static final int GradientColorItem_android_color = 0;
    public static final int GradientColorItem_android_offset = 1;
    public static final int GradientColor_android_centerColor = 7;
    public static final int GradientColor_android_centerX = 3;
    public static final int GradientColor_android_centerY = 4;
    public static final int GradientColor_android_endColor = 1;
    public static final int GradientColor_android_endX = 10;
    public static final int GradientColor_android_endY = 11;
    public static final int GradientColor_android_gradientRadius = 5;
    public static final int GradientColor_android_startColor = 0;
    public static final int GradientColor_android_startX = 8;
    public static final int GradientColor_android_startY = 9;
    public static final int GradientColor_android_tileMode = 6;
    public static final int GradientColor_android_type = 2;
    public static final int LinearConstraintLayout_android_orientation = 0;
    public static final int LinearLayoutCompat_Layout_android_layout_gravity = 0;
    public static final int LinearLayoutCompat_Layout_android_layout_height = 2;
    public static final int LinearLayoutCompat_Layout_android_layout_weight = 3;
    public static final int LinearLayoutCompat_Layout_android_layout_width = 1;
    public static final int LinearLayoutCompat_android_baselineAligned = 2;
    public static final int LinearLayoutCompat_android_baselineAlignedChildIndex = 3;
    public static final int LinearLayoutCompat_android_gravity = 0;
    public static final int LinearLayoutCompat_android_orientation = 1;
    public static final int LinearLayoutCompat_android_weightSum = 4;
    public static final int LinearLayoutCompat_divider = 5;
    public static final int LinearLayoutCompat_dividerPadding = 6;
    public static final int LinearLayoutCompat_measureWithLargestChild = 7;
    public static final int LinearLayoutCompat_showDividers = 8;
    public static final int ListPopupWindow_android_dropDownHorizontalOffset = 0;
    public static final int ListPopupWindow_android_dropDownVerticalOffset = 1;
    public static final int MenuGroup_android_checkableBehavior = 5;
    public static final int MenuGroup_android_enabled = 0;
    public static final int MenuGroup_android_id = 1;
    public static final int MenuGroup_android_menuCategory = 3;
    public static final int MenuGroup_android_orderInCategory = 4;
    public static final int MenuGroup_android_visible = 2;
    public static final int MenuItem_actionLayout = 13;
    public static final int MenuItem_actionProviderClass = 14;
    public static final int MenuItem_actionViewClass = 15;
    public static final int MenuItem_alphabeticModifiers = 16;
    public static final int MenuItem_android_alphabeticShortcut = 9;
    public static final int MenuItem_android_checkable = 11;
    public static final int MenuItem_android_checked = 3;
    public static final int MenuItem_android_enabled = 1;
    public static final int MenuItem_android_icon = 0;
    public static final int MenuItem_android_id = 2;
    public static final int MenuItem_android_menuCategory = 5;
    public static final int MenuItem_android_numericShortcut = 10;
    public static final int MenuItem_android_onClick = 12;
    public static final int MenuItem_android_orderInCategory = 6;
    public static final int MenuItem_android_title = 7;
    public static final int MenuItem_android_titleCondensed = 8;
    public static final int MenuItem_android_visible = 4;
    public static final int MenuItem_contentDescription = 17;
    public static final int MenuItem_iconTint = 18;
    public static final int MenuItem_iconTintMode = 19;
    public static final int MenuItem_numericModifiers = 20;
    public static final int MenuItem_showAsAction = 21;
    public static final int MenuItem_tooltipText = 22;
    public static final int MenuView_android_headerBackground = 4;
    public static final int MenuView_android_horizontalDivider = 2;
    public static final int MenuView_android_itemBackground = 5;
    public static final int MenuView_android_itemIconDisabledAlpha = 6;
    public static final int MenuView_android_itemTextAppearance = 1;
    public static final int MenuView_android_verticalDivider = 3;
    public static final int MenuView_android_windowAnimationStyle = 0;
    public static final int MenuView_preserveIconSpacing = 7;
    public static final int MenuView_subMenuArrow = 8;
    public static final int PicturePlayerView_picture_antiAlias = 0;
    public static final int PicturePlayerView_picture_cacheFrameNumber = 1;
    public static final int PicturePlayerView_picture_dither = 2;
    public static final int PicturePlayerView_picture_filterBitmap = 3;
    public static final int PicturePlayerView_picture_loop = 4;
    public static final int PicturePlayerView_picture_opaque = 5;
    public static final int PicturePlayerView_picture_scaleType = 6;
    public static final int PicturePlayerView_picture_source = 7;
    public static final int PopupWindowBackgroundState_state_above_anchor = 0;
    public static final int PopupWindow_android_popupAnimationStyle = 1;
    public static final int PopupWindow_android_popupBackground = 0;
    public static final int PopupWindow_overlapAnchor = 2;
    public static final int RecycleListView_paddingBottomNoButtons = 0;
    public static final int RecycleListView_paddingTopNoTitle = 1;
    public static final int RecyclerView_android_clipToPadding = 1;
    public static final int RecyclerView_android_descendantFocusability = 2;
    public static final int RecyclerView_android_orientation = 0;
    public static final int RecyclerView_fastScrollEnabled = 3;
    public static final int RecyclerView_fastScrollHorizontalThumbDrawable = 4;
    public static final int RecyclerView_fastScrollHorizontalTrackDrawable = 5;
    public static final int RecyclerView_fastScrollVerticalThumbDrawable = 6;
    public static final int RecyclerView_fastScrollVerticalTrackDrawable = 7;
    public static final int RecyclerView_layoutManager = 8;
    public static final int RecyclerView_reverseLayout = 9;
    public static final int RecyclerView_spanCount = 10;
    public static final int RecyclerView_stackFromEnd = 11;
    public static final int SearchView_android_focusable = 0;
    public static final int SearchView_android_imeOptions = 3;
    public static final int SearchView_android_inputType = 2;
    public static final int SearchView_android_maxWidth = 1;
    public static final int SearchView_closeIcon = 4;
    public static final int SearchView_commitIcon = 5;
    public static final int SearchView_defaultQueryHint = 6;
    public static final int SearchView_goIcon = 7;
    public static final int SearchView_iconifiedByDefault = 8;
    public static final int SearchView_layout = 9;
    public static final int SearchView_queryBackground = 10;
    public static final int SearchView_queryHint = 11;
    public static final int SearchView_searchHintIcon = 12;
    public static final int SearchView_searchIcon = 13;
    public static final int SearchView_submitBackground = 14;
    public static final int SearchView_suggestionRowLayout = 15;
    public static final int SearchView_voiceIcon = 16;
    public static final int SmoothProgressBar_spbStyle = 0;
    public static final int SmoothProgressBar_spb_background = 1;
    public static final int SmoothProgressBar_spb_color = 2;
    public static final int SmoothProgressBar_spb_colors = 3;
    public static final int SmoothProgressBar_spb_generate_background_with_colors = 4;
    public static final int SmoothProgressBar_spb_gradients = 5;
    public static final int SmoothProgressBar_spb_interpolator = 6;
    public static final int SmoothProgressBar_spb_mirror_mode = 7;
    public static final int SmoothProgressBar_spb_progressiveStart_activated = 8;
    public static final int SmoothProgressBar_spb_progressiveStart_speed = 9;
    public static final int SmoothProgressBar_spb_progressiveStop_speed = 10;
    public static final int SmoothProgressBar_spb_reversed = 11;
    public static final int SmoothProgressBar_spb_sections_count = 12;
    public static final int SmoothProgressBar_spb_speed = 13;
    public static final int SmoothProgressBar_spb_stroke_separator_length = 14;
    public static final int SmoothProgressBar_spb_stroke_width = 15;
    public static final int Spinner_android_dropDownWidth = 3;
    public static final int Spinner_android_entries = 0;
    public static final int Spinner_android_popupBackground = 1;
    public static final int Spinner_android_prompt = 2;
    public static final int Spinner_popupTheme = 4;
    public static final int StateListDrawableItem_android_drawable = 0;
    public static final int StateListDrawable_android_constantSize = 3;
    public static final int StateListDrawable_android_dither = 0;
    public static final int StateListDrawable_android_enterFadeDuration = 4;
    public static final int StateListDrawable_android_exitFadeDuration = 5;
    public static final int StateListDrawable_android_variablePadding = 2;
    public static final int StateListDrawable_android_visible = 1;
    public static final int SwitchCompat_android_textOff = 1;
    public static final int SwitchCompat_android_textOn = 0;
    public static final int SwitchCompat_android_thumb = 2;
    public static final int SwitchCompat_showText = 3;
    public static final int SwitchCompat_splitTrack = 4;
    public static final int SwitchCompat_switchMinWidth = 5;
    public static final int SwitchCompat_switchPadding = 6;
    public static final int SwitchCompat_switchTextAppearance = 7;
    public static final int SwitchCompat_thumbTextPadding = 8;
    public static final int SwitchCompat_thumbTint = 9;
    public static final int SwitchCompat_thumbTintMode = 10;
    public static final int SwitchCompat_track = 11;
    public static final int SwitchCompat_trackTint = 12;
    public static final int SwitchCompat_trackTintMode = 13;
    public static final int TextAppearance_android_fontFamily = 10;
    public static final int TextAppearance_android_shadowColor = 6;
    public static final int TextAppearance_android_shadowDx = 7;
    public static final int TextAppearance_android_shadowDy = 8;
    public static final int TextAppearance_android_shadowRadius = 9;
    public static final int TextAppearance_android_textColor = 3;
    public static final int TextAppearance_android_textColorHint = 4;
    public static final int TextAppearance_android_textColorLink = 5;
    public static final int TextAppearance_android_textFontWeight = 11;
    public static final int TextAppearance_android_textSize = 0;
    public static final int TextAppearance_android_textStyle = 2;
    public static final int TextAppearance_android_typeface = 1;
    public static final int TextAppearance_fontFamily = 12;
    public static final int TextAppearance_fontVariationSettings = 13;
    public static final int TextAppearance_textAllCaps = 14;
    public static final int TextAppearance_textLocale = 15;
    public static final int Toolbar_android_gravity = 0;
    public static final int Toolbar_android_minHeight = 1;
    public static final int Toolbar_buttonGravity = 2;
    public static final int Toolbar_collapseContentDescription = 3;
    public static final int Toolbar_collapseIcon = 4;
    public static final int Toolbar_contentInsetEnd = 5;
    public static final int Toolbar_contentInsetEndWithActions = 6;
    public static final int Toolbar_contentInsetLeft = 7;
    public static final int Toolbar_contentInsetRight = 8;
    public static final int Toolbar_contentInsetStart = 9;
    public static final int Toolbar_contentInsetStartWithNavigation = 10;
    public static final int Toolbar_logo = 11;
    public static final int Toolbar_logoDescription = 12;
    public static final int Toolbar_maxButtonHeight = 13;
    public static final int Toolbar_menu = 14;
    public static final int Toolbar_navigationContentDescription = 15;
    public static final int Toolbar_navigationIcon = 16;
    public static final int Toolbar_popupTheme = 17;
    public static final int Toolbar_subtitle = 18;
    public static final int Toolbar_subtitleTextAppearance = 19;
    public static final int Toolbar_subtitleTextColor = 20;
    public static final int Toolbar_title = 21;
    public static final int Toolbar_titleMargin = 22;
    public static final int Toolbar_titleMarginBottom = 23;
    public static final int Toolbar_titleMarginEnd = 24;
    public static final int Toolbar_titleMarginStart = 25;
    public static final int Toolbar_titleMarginTop = 26;
    public static final int Toolbar_titleMargins = 27;
    public static final int Toolbar_titleTextAppearance = 28;
    public static final int Toolbar_titleTextColor = 29;
    public static final int ViewBackgroundHelper_android_background = 0;
    public static final int ViewBackgroundHelper_backgroundTint = 1;
    public static final int ViewBackgroundHelper_backgroundTintMode = 2;
    public static final int ViewStubCompat_android_id = 0;
    public static final int ViewStubCompat_android_inflatedId = 2;
    public static final int ViewStubCompat_android_layout = 1;
    public static final int View_android_focusable = 1;
    public static final int View_android_theme = 0;
    public static final int View_paddingEnd = 2;
    public static final int View_paddingStart = 3;
    public static final int View_theme = 4;
    public static final int[] ActionBar = {R.attr.background, R.attr.backgroundSplit, R.attr.backgroundStacked, R.attr.contentInsetEnd, R.attr.contentInsetEndWithActions, R.attr.contentInsetLeft, R.attr.contentInsetRight, R.attr.contentInsetStart, R.attr.contentInsetStartWithNavigation, R.attr.customNavigationLayout, R.attr.displayOptions, R.attr.divider, R.attr.elevation, R.attr.height, R.attr.hideOnContentScroll, R.attr.homeAsUpIndicator, R.attr.homeLayout, R.attr.icon, R.attr.indeterminateProgressStyle, R.attr.itemPadding, R.attr.logo, R.attr.navigationMode, R.attr.popupTheme, R.attr.progressBarPadding, R.attr.progressBarStyle, R.attr.subtitle, R.attr.subtitleTextStyle, R.attr.title, R.attr.titleTextStyle};
    public static final int[] ActionBarLayout = {16842931};
    public static final int[] ActionMenuItemView = {16843071};
    public static final int[] ActionMenuView = new int[0];
    public static final int[] ActionMode = {R.attr.background, R.attr.backgroundSplit, R.attr.closeItemLayout, R.attr.height, R.attr.subtitleTextStyle, R.attr.titleTextStyle};
    public static final int[] ActivityChooserView = {R.attr.expandActivityOverflowButtonDrawable, R.attr.initialActivityCount};
    public static final int[] AlertDialog = {16842994, R.attr.buttonIconDimen, R.attr.buttonPanelSideLayout, R.attr.listItemLayout, R.attr.listLayout, R.attr.multiChoiceItemLayout, R.attr.showTitle, R.attr.singleChoiceItemLayout};
    public static final int[] AnimatedStateListDrawableCompat = {16843036, 16843156, 16843157, 16843158, 16843532, 16843533};
    public static final int[] AnimatedStateListDrawableItem = {16842960, 16843161};
    public static final int[] AnimatedStateListDrawableTransition = {16843161, 16843849, 16843850, 16843851};
    public static final int[] AppCompatImageView = {16843033, R.attr.srcCompat, R.attr.tint, R.attr.tintMode};
    public static final int[] AppCompatSeekBar = {16843074, R.attr.tickMark, R.attr.tickMarkTint, R.attr.tickMarkTintMode};
    public static final int[] AppCompatTextHelper = {16842804, 16843117, 16843118, 16843119, 16843120, 16843666, 16843667};
    public static final int[] AppCompatTextView = {16842804, R.attr.autoSizeMaxTextSize, R.attr.autoSizeMinTextSize, R.attr.autoSizePresetSizes, R.attr.autoSizeStepGranularity, R.attr.autoSizeTextType, R.attr.drawableBottomCompat, R.attr.drawableEndCompat, R.attr.drawableLeftCompat, R.attr.drawableRightCompat, R.attr.drawableStartCompat, R.attr.drawableTint, R.attr.drawableTintMode, R.attr.drawableTopCompat, R.attr.firstBaselineToTopHeight, R.attr.fontFamily, R.attr.fontVariationSettings, R.attr.lastBaselineToBottomHeight, R.attr.lineHeight, R.attr.textAllCaps, R.attr.textLocale};
    public static final int[] AppCompatTheme = {16842839, 16842926, R.attr.actionBarDivider, R.attr.actionBarItemBackground, R.attr.actionBarPopupTheme, R.attr.actionBarSize, R.attr.actionBarSplitStyle, R.attr.actionBarStyle, R.attr.actionBarTabBarStyle, R.attr.actionBarTabStyle, R.attr.actionBarTabTextStyle, R.attr.actionBarTheme, R.attr.actionBarWidgetTheme, R.attr.actionButtonStyle, R.attr.actionDropDownStyle, R.attr.actionMenuTextAppearance, R.attr.actionMenuTextColor, R.attr.actionModeBackground, R.attr.actionModeCloseButtonStyle, R.attr.actionModeCloseDrawable, R.attr.actionModeCopyDrawable, R.attr.actionModeCutDrawable, R.attr.actionModeFindDrawable, R.attr.actionModePasteDrawable, R.attr.actionModePopupWindowStyle, R.attr.actionModeSelectAllDrawable, R.attr.actionModeShareDrawable, R.attr.actionModeSplitBackground, R.attr.actionModeStyle, R.attr.actionModeWebSearchDrawable, R.attr.actionOverflowButtonStyle, R.attr.actionOverflowMenuStyle, R.attr.activityChooserViewStyle, R.attr.alertDialogButtonGroupStyle, R.attr.alertDialogCenterButtons, R.attr.alertDialogStyle, R.attr.alertDialogTheme, R.attr.autoCompleteTextViewStyle, R.attr.borderlessButtonStyle, R.attr.buttonBarButtonStyle, R.attr.buttonBarNegativeButtonStyle, R.attr.buttonBarNeutralButtonStyle, R.attr.buttonBarPositiveButtonStyle, R.attr.buttonBarStyle, R.attr.buttonStyle, R.attr.buttonStyleSmall, R.attr.checkboxStyle, R.attr.checkedTextViewStyle, R.attr.colorAccent, R.attr.colorBackgroundFloating, R.attr.colorButtonNormal, R.attr.colorControlActivated, R.attr.colorControlHighlight, R.attr.colorControlNormal, R.attr.colorError, R.attr.colorPrimary, R.attr.colorPrimaryDark, R.attr.colorSwitchThumbNormal, R.attr.controlBackground, R.attr.dialogCornerRadius, R.attr.dialogPreferredPadding, R.attr.dialogTheme, R.attr.dividerHorizontal, R.attr.dividerVertical, R.attr.dropDownListViewStyle, R.attr.dropdownListPreferredItemHeight, R.attr.editTextBackground, R.attr.editTextColor, R.attr.editTextStyle, R.attr.homeAsUpIndicator, R.attr.imageButtonStyle, R.attr.listChoiceBackgroundIndicator, R.attr.listChoiceIndicatorMultipleAnimated, R.attr.listChoiceIndicatorSingleAnimated, R.attr.listDividerAlertDialog, R.attr.listMenuViewStyle, R.attr.listPopupWindowStyle, R.attr.listPreferredItemHeight, R.attr.listPreferredItemHeightLarge, R.attr.listPreferredItemHeightSmall, R.attr.listPreferredItemPaddingEnd, R.attr.listPreferredItemPaddingLeft, R.attr.listPreferredItemPaddingRight, R.attr.listPreferredItemPaddingStart, R.attr.panelBackground, R.attr.panelMenuListTheme, R.attr.panelMenuListWidth, R.attr.popupMenuStyle, R.attr.popupWindowStyle, R.attr.radioButtonStyle, R.attr.ratingBarStyle, R.attr.ratingBarStyleIndicator, R.attr.ratingBarStyleSmall, R.attr.searchViewStyle, R.attr.seekBarStyle, R.attr.selectableItemBackground, R.attr.selectableItemBackgroundBorderless, R.attr.spinnerDropDownItemStyle, R.attr.spinnerStyle, R.attr.switchStyle, R.attr.textAppearanceLargePopupMenu, R.attr.textAppearanceListItem, R.attr.textAppearanceListItemSecondary, R.attr.textAppearanceListItemSmall, R.attr.textAppearancePopupMenuHeader, R.attr.textAppearanceSearchResultSubtitle, R.attr.textAppearanceSearchResultTitle, R.attr.textAppearanceSmallPopupMenu, R.attr.textColorAlertDialogListItem, R.attr.textColorSearchUrl, R.attr.toolbarNavigationButtonStyle, R.attr.toolbarStyle, R.attr.tooltipForegroundColor, R.attr.tooltipFrameBackground, R.attr.viewInflaterClass, R.attr.windowActionBar, R.attr.windowActionBarOverlay, R.attr.windowActionModeOverlay, R.attr.windowFixedHeightMajor, R.attr.windowFixedHeightMinor, R.attr.windowFixedWidthMajor, R.attr.windowFixedWidthMinor, R.attr.windowMinWidthMajor, R.attr.windowMinWidthMinor, R.attr.windowNoTitle};
    public static final int[] ButtonBarLayout = {R.attr.allowStacking};
    public static final int[] ColorStateListItem = {16843173, 16843551, R.attr.alpha};
    public static final int[] CompoundButton = {16843015, R.attr.buttonCompat, R.attr.buttonTint, R.attr.buttonTintMode};
    public static final int[] ConstraintLayout_Layout = {16842948, 16843039, 16843040, 16843071, 16843072, R.attr.barrierAllowsGoneWidgets, R.attr.barrierDirection, R.attr.chainUseRtl, R.attr.constraintSet, R.attr.constraint_referenced_ids, R.attr.layout_constrainedHeight, R.attr.layout_constrainedWidth, R.attr.layout_constraintBaseline_creator, R.attr.layout_constraintBaseline_toBaselineOf, R.attr.layout_constraintBottom_creator, R.attr.layout_constraintBottom_toBottomOf, R.attr.layout_constraintBottom_toTopOf, R.attr.layout_constraintCircle, R.attr.layout_constraintCircleAngle, R.attr.layout_constraintCircleRadius, R.attr.layout_constraintDimensionRatio, R.attr.layout_constraintEnd_toEndOf, R.attr.layout_constraintEnd_toStartOf, R.attr.layout_constraintGuide_begin, R.attr.layout_constraintGuide_end, R.attr.layout_constraintGuide_percent, R.attr.layout_constraintHeight_default, R.attr.layout_constraintHeight_max, R.attr.layout_constraintHeight_min, R.attr.layout_constraintHeight_percent, R.attr.layout_constraintHorizontal_bias, R.attr.layout_constraintHorizontal_chainStyle, R.attr.layout_constraintHorizontal_weight, R.attr.layout_constraintLeft_creator, R.attr.layout_constraintLeft_toLeftOf, R.attr.layout_constraintLeft_toRightOf, R.attr.layout_constraintRight_creator, R.attr.layout_constraintRight_toLeftOf, R.attr.layout_constraintRight_toRightOf, R.attr.layout_constraintStart_toEndOf, R.attr.layout_constraintStart_toStartOf, R.attr.layout_constraintTop_creator, R.attr.layout_constraintTop_toBottomOf, R.attr.layout_constraintTop_toTopOf, R.attr.layout_constraintVertical_bias, R.attr.layout_constraintVertical_chainStyle, R.attr.layout_constraintVertical_weight, R.attr.layout_constraintWidth_default, R.attr.layout_constraintWidth_max, R.attr.layout_constraintWidth_min, R.attr.layout_constraintWidth_percent, R.attr.layout_editor_absoluteX, R.attr.layout_editor_absoluteY, R.attr.layout_goneMarginBottom, R.attr.layout_goneMarginEnd, R.attr.layout_goneMarginLeft, R.attr.layout_goneMarginRight, R.attr.layout_goneMarginStart, R.attr.layout_goneMarginTop, R.attr.layout_optimizationLevel};
    public static final int[] ConstraintLayout_placeholder = {R.attr.content, R.attr.emptyVisibility};
    public static final int[] ConstraintSet = {16842948, 16842960, 16842972, 16842996, 16842997, 16842999, 16843000, 16843001, 16843002, 16843039, 16843040, 16843071, 16843072, 16843551, 16843552, 16843553, 16843554, 16843555, 16843556, 16843557, 16843558, 16843559, 16843560, 16843701, 16843702, 16843770, 16843840, R.attr.barrierAllowsGoneWidgets, R.attr.barrierDirection, R.attr.chainUseRtl, R.attr.constraint_referenced_ids, R.attr.layout_constrainedHeight, R.attr.layout_constrainedWidth, R.attr.layout_constraintBaseline_creator, R.attr.layout_constraintBaseline_toBaselineOf, R.attr.layout_constraintBottom_creator, R.attr.layout_constraintBottom_toBottomOf, R.attr.layout_constraintBottom_toTopOf, R.attr.layout_constraintCircle, R.attr.layout_constraintCircleAngle, R.attr.layout_constraintCircleRadius, R.attr.layout_constraintDimensionRatio, R.attr.layout_constraintEnd_toEndOf, R.attr.layout_constraintEnd_toStartOf, R.attr.layout_constraintGuide_begin, R.attr.layout_constraintGuide_end, R.attr.layout_constraintGuide_percent, R.attr.layout_constraintHeight_default, R.attr.layout_constraintHeight_max, R.attr.layout_constraintHeight_min, R.attr.layout_constraintHeight_percent, R.attr.layout_constraintHorizontal_bias, R.attr.layout_constraintHorizontal_chainStyle, R.attr.layout_constraintHorizontal_weight, R.attr.layout_constraintLeft_creator, R.attr.layout_constraintLeft_toLeftOf, R.attr.layout_constraintLeft_toRightOf, R.attr.layout_constraintRight_creator, R.attr.layout_constraintRight_toLeftOf, R.attr.layout_constraintRight_toRightOf, R.attr.layout_constraintStart_toEndOf, R.attr.layout_constraintStart_toStartOf, R.attr.layout_constraintTop_creator, R.attr.layout_constraintTop_toBottomOf, R.attr.layout_constraintTop_toTopOf, R.attr.layout_constraintVertical_bias, R.attr.layout_constraintVertical_chainStyle, R.attr.layout_constraintVertical_weight, R.attr.layout_constraintWidth_default, R.attr.layout_constraintWidth_max, R.attr.layout_constraintWidth_min, R.attr.layout_constraintWidth_percent, R.attr.layout_editor_absoluteX, R.attr.layout_editor_absoluteY, R.attr.layout_goneMarginBottom, R.attr.layout_goneMarginEnd, R.attr.layout_goneMarginLeft, R.attr.layout_goneMarginRight, R.attr.layout_goneMarginStart, R.attr.layout_goneMarginTop};
    public static final int[] CustomTextView = {R.attr.typeface};
    public static final int[] DrawerArrowToggle = {R.attr.arrowHeadLength, R.attr.arrowShaftLength, R.attr.barLength, R.attr.color, R.attr.drawableSize, R.attr.gapBetweenBars, R.attr.spinBars, R.attr.thickness};
    public static final int[] FontFamily = {R.attr.fontProviderAuthority, R.attr.fontProviderCerts, R.attr.fontProviderFetchStrategy, R.attr.fontProviderFetchTimeout, R.attr.fontProviderPackage, R.attr.fontProviderQuery};
    public static final int[] FontFamilyFont = {16844082, 16844083, 16844095, 16844143, 16844144, R.attr.font, R.attr.fontStyle, R.attr.fontVariationSettings, R.attr.fontWeight, R.attr.ttcIndex};
    public static final int[] GradientColor = {16843165, 16843166, 16843169, 16843170, 16843171, 16843172, 16843265, 16843275, 16844048, 16844049, 16844050, 16844051};
    public static final int[] GradientColorItem = {16843173, 16844052};
    public static final int[] LinearConstraintLayout = {16842948};
    public static final int[] LinearLayoutCompat = {16842927, 16842948, 16843046, 16843047, 16843048, R.attr.divider, R.attr.dividerPadding, R.attr.measureWithLargestChild, R.attr.showDividers};
    public static final int[] LinearLayoutCompat_Layout = {16842931, 16842996, 16842997, 16843137};
    public static final int[] ListPopupWindow = {16843436, 16843437};
    public static final int[] MenuGroup = {16842766, 16842960, 16843156, 16843230, 16843231, 16843232};
    public static final int[] MenuItem = {16842754, 16842766, 16842960, 16843014, 16843156, 16843230, 16843231, 16843233, 16843234, 16843235, 16843236, 16843237, 16843375, R.attr.actionLayout, R.attr.actionProviderClass, R.attr.actionViewClass, R.attr.alphabeticModifiers, R.attr.contentDescription, R.attr.iconTint, R.attr.iconTintMode, R.attr.numericModifiers, R.attr.showAsAction, R.attr.tooltipText};
    public static final int[] MenuView = {16842926, 16843052, 16843053, 16843054, 16843055, 16843056, 16843057, R.attr.preserveIconSpacing, R.attr.subMenuArrow};
    public static final int[] PicturePlayerView = {R.attr.picture_antiAlias, R.attr.picture_cacheFrameNumber, R.attr.picture_dither, R.attr.picture_filterBitmap, R.attr.picture_loop, R.attr.picture_opaque, R.attr.picture_scaleType, R.attr.picture_source};
    public static final int[] PopupWindow = {16843126, 16843465, R.attr.overlapAnchor};
    public static final int[] PopupWindowBackgroundState = {R.attr.state_above_anchor};
    public static final int[] RecycleListView = {R.attr.paddingBottomNoButtons, R.attr.paddingTopNoTitle};
    public static final int[] RecyclerView = {16842948, 16842987, 16842993, R.attr.fastScrollEnabled, R.attr.fastScrollHorizontalThumbDrawable, R.attr.fastScrollHorizontalTrackDrawable, R.attr.fastScrollVerticalThumbDrawable, R.attr.fastScrollVerticalTrackDrawable, R.attr.layoutManager, R.attr.reverseLayout, R.attr.spanCount, R.attr.stackFromEnd};
    public static final int[] SearchView = {16842970, 16843039, 16843296, 16843364, R.attr.closeIcon, R.attr.commitIcon, R.attr.defaultQueryHint, R.attr.goIcon, R.attr.iconifiedByDefault, R.attr.layout, R.attr.queryBackground, R.attr.queryHint, R.attr.searchHintIcon, R.attr.searchIcon, R.attr.submitBackground, R.attr.suggestionRowLayout, R.attr.voiceIcon};
    public static final int[] SmoothProgressBar = {R.attr.spbStyle, R.attr.spb_background, R.attr.spb_color, R.attr.spb_colors, R.attr.spb_generate_background_with_colors, R.attr.spb_gradients, R.attr.spb_interpolator, R.attr.spb_mirror_mode, R.attr.spb_progressiveStart_activated, R.attr.spb_progressiveStart_speed, R.attr.spb_progressiveStop_speed, R.attr.spb_reversed, R.attr.spb_sections_count, R.attr.spb_speed, R.attr.spb_stroke_separator_length, R.attr.spb_stroke_width};
    public static final int[] Spinner = {16842930, 16843126, 16843131, 16843362, R.attr.popupTheme};
    public static final int[] StateListDrawable = {16843036, 16843156, 16843157, 16843158, 16843532, 16843533};
    public static final int[] StateListDrawableItem = {16843161};
    public static final int[] SwitchCompat = {16843044, 16843045, 16843074, R.attr.showText, R.attr.splitTrack, R.attr.switchMinWidth, R.attr.switchPadding, R.attr.switchTextAppearance, R.attr.thumbTextPadding, R.attr.thumbTint, R.attr.thumbTintMode, R.attr.track, R.attr.trackTint, R.attr.trackTintMode};
    public static final int[] TextAppearance = {16842901, 16842902, 16842903, 16842904, 16842906, 16842907, 16843105, 16843106, 16843107, 16843108, 16843692, 16844165, R.attr.fontFamily, R.attr.fontVariationSettings, R.attr.textAllCaps, R.attr.textLocale};
    public static final int[] Toolbar = {16842927, 16843072, R.attr.buttonGravity, R.attr.collapseContentDescription, R.attr.collapseIcon, R.attr.contentInsetEnd, R.attr.contentInsetEndWithActions, R.attr.contentInsetLeft, R.attr.contentInsetRight, R.attr.contentInsetStart, R.attr.contentInsetStartWithNavigation, R.attr.logo, R.attr.logoDescription, R.attr.maxButtonHeight, R.attr.menu, R.attr.navigationContentDescription, R.attr.navigationIcon, R.attr.popupTheme, R.attr.subtitle, R.attr.subtitleTextAppearance, R.attr.subtitleTextColor, R.attr.title, R.attr.titleMargin, R.attr.titleMarginBottom, R.attr.titleMarginEnd, R.attr.titleMarginStart, R.attr.titleMarginTop, R.attr.titleMargins, R.attr.titleTextAppearance, R.attr.titleTextColor};
    public static final int[] View = {16842752, 16842970, R.attr.paddingEnd, R.attr.paddingStart, R.attr.theme};
    public static final int[] ViewBackgroundHelper = {16842964, R.attr.backgroundTint, R.attr.backgroundTintMode};
    public static final int[] ViewStubCompat = {16842960, 16842994, 16842995};
}
