package com.ici.connectivity.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import b.a.b.a.a;
import b.d.c.f.x;

/* loaded from: classes.dex */
public class BatteryLevelView extends View {

    /* renamed from: b  reason: collision with root package name */
    public Paint f525b;
    public float c;
    public float d;

    public BatteryLevelView(Context context) {
        this(context, null);
    }

    public final void a() {
        Paint paint = new Paint();
        this.f525b = paint;
        paint.setColor(Color.parseColor("#FFB954"));
        this.f525b.setStyle(Paint.Style.FILL);
        this.f525b.setStrokeWidth(3.0f);
        this.c = 0.0f;
    }

    @Override // android.view.View
    public void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        canvas.drawRoundRect(3.0f, 3.0f, this.c, this.d, 1.5f, 1.5f, this.f525b);
    }

    public void setBatteryLevel(int i) {
        if (i < 0) {
            Log.i("BatteryLevelView", " 更新电量数值异常: " + i);
            setVisibility(8);
            return;
        }
        if (getVisibility() != 0) {
            setVisibility(0);
            Log.i("BatteryLevelView", " set VISIBLE ");
        }
        a.i("更新电量最终实际值 : ", i, "BatteryLevelView");
        if (this.f525b == null) {
            a();
        }
        x.a().e(i);
        if (i == 100) {
            this.f525b.setColor(Color.parseColor("#86F015"));
            this.c = 34.0f;
        } else if (i >= 80 && i < 100) {
            this.f525b.setColor(Color.parseColor("#86F015"));
            this.c = 30.0f;
        } else if (i >= 60 && i <= 79) {
            this.f525b.setColor(Color.parseColor("#86F015"));
            this.c = 24.0f;
        } else if (i >= 40 && i <= 59) {
            this.f525b.setColor(Color.parseColor("#86F015"));
            this.c = 18.0f;
        } else if (i >= 20 && i <= 39) {
            this.f525b.setColor(Color.parseColor("#86F015"));
            this.c = 12.0f;
        } else {
            this.f525b.setColor(Color.parseColor("#FFB954"));
            this.c = 6.0f;
        }
        invalidate();
    }

    public BatteryLevelView(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, 0);
    }

    public BatteryLevelView(Context context, AttributeSet attributeSet, int i) {
        super(context, attributeSet, i);
        this.c = 1.0f;
        this.d = 17.0f;
        a();
    }
}
