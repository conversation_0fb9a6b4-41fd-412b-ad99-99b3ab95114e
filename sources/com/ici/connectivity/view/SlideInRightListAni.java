package com.ici.connectivity.view;

import android.view.View;
import android.view.animation.Interpolator;
import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.RecyclerView;
import jp.wasabeef.recyclerview.animators.BaseItemAnimator;

/* loaded from: classes.dex */
public class SlideInRightList<PERSON>ni extends BaseItemAnimator {
    public SlideInRightListAni() {
    }

    @Override // jp.wasabeef.recyclerview.animators.BaseItemAnimator
    public void animateAddImpl(RecyclerView.ViewHolder viewHolder) {
        ViewCompat.animate(viewHolder.itemView).translationX(0.0f).setDuration(getAddDuration()).setInterpolator(this.mInterpolator).setListener(new BaseItemAnimator.DefaultAddVpaListener(viewHolder)).setStartDelay(getAddDelay(viewHolder)).start();
    }

    @Override // jp.wasabeef.recyclerview.animators.BaseItemAnimator
    public void animateRemoveImpl(RecyclerView.ViewHolder viewHolder) {
        ViewCompat.animate(viewHolder.itemView).translationX(0.0f).setDuration(getRemoveDuration()).setInterpolator(this.mInterpolator).setListener(new BaseItemAnimator.DefaultRemoveVpaListener(viewHolder)).setStartDelay(getRemoveDelay(viewHolder)).start();
    }

    @Override // jp.wasabeef.recyclerview.animators.BaseItemAnimator
    public void preAnimateAddImpl(RecyclerView.ViewHolder viewHolder) {
        View view = viewHolder.itemView;
        ViewCompat.setTranslationX(view, view.getWidth());
    }

    public SlideInRightListAni(Interpolator interpolator) {
        this.mInterpolator = interpolator;
    }
}
