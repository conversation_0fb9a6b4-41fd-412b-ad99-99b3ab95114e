package com.ici.connectivity;

/* JADX INFO: This class is generated by JADX */
public final class R {

    /* loaded from: classes.dex */
    public final class anim {
        public static final int abc_fade_in = 0x7f010000;
        public static final int abc_fade_out = 0x7f010001;
        public static final int abc_grow_fade_in_from_bottom = 0x7f010002;
        public static final int abc_popup_enter = 0x7f010003;
        public static final int abc_popup_exit = 0x7f010004;
        public static final int abc_shrink_fade_out_from_bottom = 0x7f010005;
        public static final int abc_slide_in_bottom = 0x7f010006;
        public static final int abc_slide_in_top = 0x7f010007;
        public static final int abc_slide_out_bottom = 0x7f010008;
        public static final int abc_slide_out_top = 0x7f010009;
        public static final int abc_tooltip_enter = 0x7f01000a;
        public static final int abc_tooltip_exit = 0x7f01000b;
        public static final int anim_loading = 0x7f01000c;
        public static final int btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000d;
        public static final int btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000e;
        public static final int btn_checkbox_to_checked_icon_null_animation = 0x7f01000f;
        public static final int btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f010010;
        public static final int btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010011;
        public static final int btn_checkbox_to_unchecked_icon_null_animation = 0x7f010012;
        public static final int btn_radio_to_off_mtrl_dot_group_animation = 0x7f010013;
        public static final int btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010014;
        public static final int btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010015;
        public static final int btn_radio_to_on_mtrl_dot_group_animation = 0x7f010016;
        public static final int btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010017;
        public static final int btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010018;
        public static final int ici28_vector_cluster_media_progress_indeterminate_thumb1 = 0x7f010019;
        public static final int ici28_vector_cluster_media_progress_indeterminate_thumb2 = 0x7f01001a;

        public anim(
        /*  JADX ERROR: Method generation error
            jadx.core.utils.exceptions.JadxRuntimeException: Method arg registers not loaded: com.ici.connectivity.R.anim.<init>():void, class status: PROCESS_COMPLETE
            	at jadx.core.dex.nodes.MethodNode.getArgRegs(MethodNode.java:251)
            	at jadx.core.codegen.MethodGen.addDefinition(MethodGen.java:154)
            	at jadx.core.codegen.ClassGen.addMethodCode(ClassGen.java:372)
            	at jadx.core.codegen.ClassGen.addMethod(ClassGen.java:306)
            	at jadx.core.codegen.ClassGen.lambda$addInnerClsAndMethods$2(ClassGen.java:272)
            	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
            	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
            	at java.base/java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:395)
            	at java.base/java.util.stream.Sink$ChainedReference.end(Sink.java:258)
            */
    }

    /* loaded from: classes.dex */
    public final class array {
        public static final int progress_colors = 0x7f020000;

        public array(
        /*  JADX ERROR: Method generation error
            jadx.core.utils.exceptions.JadxRuntimeException: Method arg registers not loaded: com.ici.connectivity.R.array.<init>():void, class status: PROCESS_COMPLETE
            	at jadx.core.dex.nodes.MethodNode.getArgRegs(MethodNode.java:251)
            	at jadx.core.codegen.MethodGen.addDefinition(MethodGen.java:154)
            	at jadx.core.codegen.ClassGen.addMethodCode(ClassGen.java:372)
            	at jadx.core.codegen.ClassGen.addMethod(ClassGen.java:306)
            	at jadx.core.codegen.ClassGen.lambda$addInnerClsAndMethods$2(ClassGen.java:272)
            	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
            	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
            	at java.base/java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:395)
            	at java.base/java.util.stream.Sink$ChainedReference.end(Sink.java:258)
            */
    }

    /* loaded from: classes.dex */
    public final class attr {
        public static final int actionBarDivider = 0x7f030000;
        public static final int actionBarItemBackground = 0x7f030001;
        public static final int actionBarPopupTheme = 0x7f030002;
        public static final int actionBarSize = 0x7f030003;
        public static final int actionBarSplitStyle = 0x7f030004;
        public static final int actionBarStyle = 0x7f030005;
        public static final int actionBarTabBarStyle = 0x7f030006;
        public static final int actionBarTabStyle = 0x7f030007;
        public static final int actionBarTabTextStyle = 0x7f030008;
        public static final int actionBarTheme = 0x7f030009;
        public static final int actionBarWidgetTheme = 0x7f03000a;
        public static final int actionButtonStyle = 0x7f03000b;
        public static final int actionDropDownStyle = 0x7f03000c;
        public static final int actionLayout = 0x7f03000d;
        public static final int actionMenuTextAppearance = 0x7f03000e;
        public static final int actionMenuTextColor = 0x7f03000f;
        public static final int actionModeBackground = 0x7f030010;
        public static final int actionModeCloseButtonStyle = 0x7f030011;
        public static final int actionModeCloseDrawable = 0x7f030012;
        public static final int actionModeCopyDrawable = 0x7f030013;
        public static final int actionModeCutDrawable = 0x7f030014;
        public static final int actionModeFindDrawable = 0x7f030015;
        public static final int actionModePasteDrawable = 0x7f030016;
        public static final int actionModePopupWindowStyle = 0x7f030017;
        public static final int actionModeSelectAllDrawable = 0x7f030018;
        public static final int actionModeShareDrawable = 0x7f030019;
        public static final int actionModeSplitBackground = 0x7f03001a;
        public static final int actionModeStyle = 0x7f03001b;
        public static final int actionModeWebSearchDrawable = 0x7f03001c;
        public static final int actionOverflowButtonStyle = 0x7f03001d;
        public static final int actionOverflowMenuStyle = 0x7f03001e;
        public static final int actionProviderClass = 0x7f03001f;
        public static final int actionViewClass = 0x7f030020;
        public static final int activityChooserViewStyle = 0x7f030021;
        public static final int alertDialogButtonGroupStyle = 0x7f030022;
        public static final int alertDialogCenterButtons = 0x7f030023;
        public static final int alertDialogStyle = 0x7f030024;
        public static final int alertDialogTheme = 0x7f030025;
        public static final int allowStacking = 0x7f030026;
        public static final int alpha = 0x7f030027;
        public static final int alphabeticModifiers = 0x7f030028;
        public static final int arrowHeadLength = 0x7f030029;
        public static final int arrowShaftLength = 0x7f03002a;
        public static final int autoCompleteTextViewStyle = 0x7f03002b;
        public static final int autoSizeMaxTextSize = 0x7f03002c;
        public static final int autoSizeMinTextSize = 0x7f03002d;
        public static final int autoSizePresetSizes = 0x7f03002e;
        public static final int autoSizeStepGranularity = 0x7f03002f;
        public static final int autoSizeTextType = 0x7f030030;
        public static final int background = 0x7f030031;
        public static final int backgroundSplit = 0x7f030032;
        public static final int backgroundStacked = 0x7f030033;
        public static final int backgroundTint = 0x7f030034;
        public static final int backgroundTintMode = 0x7f030035;
        public static final int barLength = 0x7f030036;
        public static final int barrierAllowsGoneWidgets = 0x7f030037;
        public static final int barrierDirection = 0x7f030038;
        public static final int borderlessButtonStyle = 0x7f030039;
        public static final int buttonBarButtonStyle = 0x7f03003a;
        public static final int buttonBarNegativeButtonStyle = 0x7f03003b;
        public static final int buttonBarNeutralButtonStyle = 0x7f03003c;
        public static final int buttonBarPositiveButtonStyle = 0x7f03003d;
        public static final int buttonBarStyle = 0x7f03003e;
        public static final int buttonCompat = 0x7f03003f;
        public static final int buttonGravity = 0x7f030040;
        public static final int buttonIconDimen = 0x7f030041;
        public static final int buttonPanelSideLayout = 0x7f030042;
        public static final int buttonStyle = 0x7f030043;
        public static final int buttonStyleSmall = 0x7f030044;
        public static final int buttonTint = 0x7f030045;
        public static final int buttonTintMode = 0x7f030046;
        public static final int chainUseRtl = 0x7f030047;
        public static final int checkboxStyle = 0x7f030048;
        public static final int checkedTextViewStyle = 0x7f030049;
        public static final int closeIcon = 0x7f03004a;
        public static final int closeItemLayout = 0x7f03004b;
        public static final int collapseContentDescription = 0x7f03004c;
        public static final int collapseIcon = 0x7f03004d;
        public static final int color = 0x7f03004e;
        public static final int colorAccent = 0x7f03004f;
        public static final int colorBackgroundFloating = 0x7f030050;
        public static final int colorButtonNormal = 0x7f030051;
        public static final int colorControlActivated = 0x7f030052;
        public static final int colorControlHighlight = 0x7f030053;
        public static final int colorControlNormal = 0x7f030054;
        public static final int colorError = 0x7f030055;
        public static final int colorPrimary = 0x7f030056;
        public static final int colorPrimaryDark = 0x7f030057;
        public static final int colorSwitchThumbNormal = 0x7f030058;
        public static final int commitIcon = 0x7f030059;
        public static final int constraintSet = 0x7f03005a;
        public static final int constraint_referenced_ids = 0x7f03005b;
        public static final int content = 0x7f03005c;
        public static final int contentDescription = 0x7f03005d;
        public static final int contentInsetEnd = 0x7f03005e;
        public static final int contentInsetEndWithActions = 0x7f03005f;
        public static final int contentInsetLeft = 0x7f030060;
        public static final int contentInsetRight = 0x7f030061;
        public static final int contentInsetStart = 0x7f030062;
        public static final int contentInsetStartWithNavigation = 0x7f030063;
        public static final int controlBackground = 0x7f030064;
        public static final int customNavigationLayout = 0x7f030065;
        public static final int defaultQueryHint = 0x7f030066;
        public static final int dialogCornerRadius = 0x7f030067;
        public static final int dialogPreferredPadding = 0x7f030068;
        public static final int dialogTheme = 0x7f030069;
        public static final int displayOptions = 0x7f03006a;
        public static final int divider = 0x7f03006b;
        public static final int dividerHorizontal = 0x7f03006c;
        public static final int dividerPadding = 0x7f03006d;
        public static final int dividerVertical = 0x7f03006e;
        public static final int drawableBottomCompat = 0x7f03006f;
        public static final int drawableEndCompat = 0x7f030070;
        public static final int drawableLeftCompat = 0x7f030071;
        public static final int drawableRightCompat = 0x7f030072;
        public static final int drawableSize = 0x7f030073;
        public static final int drawableStartCompat = 0x7f030074;
        public static final int drawableTint = 0x7f030075;
        public static final int drawableTintMode = 0x7f030076;
        public static final int drawableTopCompat = 0x7f030077;
        public static final int drawerArrowStyle = 0x7f030078;
        public static final int dropDownListViewStyle = 0x7f030079;
        public static final int dropdownListPreferredItemHeight = 0x7f03007a;
        public static final int editTextBackground = 0x7f03007b;
        public static final int editTextColor = 0x7f03007c;
        public static final int editTextStyle = 0x7f03007d;
        public static final int elevation = 0x7f03007e;
        public static final int emptyVisibility = 0x7f03007f;
        public static final int expandActivityOverflowButtonDrawable = 0x7f030080;
        public static final int fastScrollEnabled = 0x7f030081;
        public static final int fastScrollHorizontalThumbDrawable = 0x7f030082;
        public static final int fastScrollHorizontalTrackDrawable = 0x7f030083;
        public static final int fastScrollVerticalThumbDrawable = 0x7f030084;
        public static final int fastScrollVerticalTrackDrawable = 0x7f030085;
        public static final int firstBaselineToTopHeight = 0x7f030086;
        public static final int font = 0x7f030087;
        public static final int fontFamily = 0x7f030088;
        public static final int fontProviderAuthority = 0x7f030089;
        public static final int fontProviderCerts = 0x7f03008a;
        public static final int fontProviderFetchStrategy = 0x7f03008b;
        public static final int fontProviderFetchTimeout = 0x7f03008c;
        public static final int fontProviderPackage = 0x7f03008d;
        public static final int fontProviderQuery = 0x7f03008e;
        public static final int fontStyle = 0x7f03008f;
        public static final int fontVariationSettings = 0x7f030090;
        public static final int fontWeight = 0x7f030091;
        public static final int gapBetweenBars = 0x7f030092;
        public static final int goIcon = 0x7f030093;
        public static final int height = 0x7f030094;
        public static final int hideOnContentScroll = 0x7f030095;
        public static final int homeAsUpIndicator = 0x7f030096;
        public static final int homeLayout = 0x7f030097;
        public static final int icon = 0x7f030098;
        public static final int iconTint = 0x7f030099;
        public static final int iconTintMode = 0x7f03009a;
        public static final int iconifiedByDefault = 0x7f03009b;
        public static final int imageButtonStyle = 0x7f03009c;
        public static final int indeterminateProgressStyle = 0x7f03009d;
        public static final int initialActivityCount = 0x7f03009e;
        public static final int isLightTheme = 0x7f03009f;
        public static final int itemPadding = 0x7f0300a0;
        public static final int lastBaselineToBottomHeight = 0x7f0300a1;
        public static final int layout = 0x7f0300a2;
        public static final int layoutManager = 0x7f0300a3;
        public static final int layout_constrainedHeight = 0x7f0300a4;
        public static final int layout_constrainedWidth = 0x7f0300a5;
        public static final int layout_constraintBaseline_creator = 0x7f0300a6;
        public static final int layout_constraintBaseline_toBaselineOf = 0x7f0300a7;
        public static final int layout_constraintBottom_creator = 0x7f0300a8;
        public static final int layout_constraintBottom_toBottomOf = 0x7f0300a9;
        public static final int layout_constraintBottom_toTopOf = 0x7f0300aa;
        public static final int layout_constraintCircle = 0x7f0300ab;
        public static final int layout_constraintCircleAngle = 0x7f0300ac;
        public static final int layout_constraintCircleRadius = 0x7f0300ad;
        public static final int layout_constraintDimensionRatio = 0x7f0300ae;
        public static final int layout_constraintEnd_toEndOf = 0x7f0300af;
        public static final int layout_constraintEnd_toStartOf = 0x7f0300b0;
        public static final int layout_constraintGuide_begin = 0x7f0300b1;
        public static final int layout_constraintGuide_end = 0x7f0300b2;
        public static final int layout_constraintGuide_percent = 0x7f0300b3;
        public static final int layout_constraintHeight_default = 0x7f0300b4;
        public static final int layout_constraintHeight_max = 0x7f0300b5;
        public static final int layout_constraintHeight_min = 0x7f0300b6;
        public static final int layout_constraintHeight_percent = 0x7f0300b7;
        public static final int layout_constraintHorizontal_bias = 0x7f0300b8;
        public static final int layout_constraintHorizontal_chainStyle = 0x7f0300b9;
        public static final int layout_constraintHorizontal_weight = 0x7f0300ba;
        public static final int layout_constraintLeft_creator = 0x7f0300bb;
        public static final int layout_constraintLeft_toLeftOf = 0x7f0300bc;
        public static final int layout_constraintLeft_toRightOf = 0x7f0300bd;
        public static final int layout_constraintRight_creator = 0x7f0300be;
        public static final int layout_constraintRight_toLeftOf = 0x7f0300bf;
        public static final int layout_constraintRight_toRightOf = 0x7f0300c0;
        public static final int layout_constraintStart_toEndOf = 0x7f0300c1;
        public static final int layout_constraintStart_toStartOf = 0x7f0300c2;
        public static final int layout_constraintTop_creator = 0x7f0300c3;
        public static final int layout_constraintTop_toBottomOf = 0x7f0300c4;
        public static final int layout_constraintTop_toTopOf = 0x7f0300c5;
        public static final int layout_constraintVertical_bias = 0x7f0300c6;
        public static final int layout_constraintVertical_chainStyle = 0x7f0300c7;
        public static final int layout_constraintVertical_weight = 0x7f0300c8;
        public static final int layout_constraintWidth_default = 0x7f0300c9;
        public static final int layout_constraintWidth_max = 0x7f0300ca;
        public static final int layout_constraintWidth_min = 0x7f0300cb;
        public static final int layout_constraintWidth_percent = 0x7f0300cc;
        public static final int layout_editor_absoluteX = 0x7f0300cd;
        public static final int layout_editor_absoluteY = 0x7f0300ce;
        public static final int layout_goneMarginBottom = 0x7f0300cf;
        public static final int layout_goneMarginEnd = 0x7f0300d0;
        public static final int layout_goneMarginLeft = 0x7f0300d1;
        public static final int layout_goneMarginRight = 0x7f0300d2;
        public static final int layout_goneMarginStart = 0x7f0300d3;
        public static final int layout_goneMarginTop = 0x7f0300d4;
        public static final int layout_optimizationLevel = 0x7f0300d5;
        public static final int lineHeight = 0x7f0300d6;
        public static final int listChoiceBackgroundIndicator = 0x7f0300d7;
        public static final int listChoiceIndicatorMultipleAnimated = 0x7f0300d8;
        public static final int listChoiceIndicatorSingleAnimated = 0x7f0300d9;
        public static final int listDividerAlertDialog = 0x7f0300da;
        public static final int listItemLayout = 0x7f0300db;
        public static final int listLayout = 0x7f0300dc;
        public static final int listMenuViewStyle = 0x7f0300dd;
        public static final int listPopupWindowStyle = 0x7f0300de;
        public static final int listPreferredItemHeight = 0x7f0300df;
        public static final int listPreferredItemHeightLarge = 0x7f0300e0;
        public static final int listPreferredItemHeightSmall = 0x7f0300e1;
        public static final int listPreferredItemPaddingEnd = 0x7f0300e2;
        public static final int listPreferredItemPaddingLeft = 0x7f0300e3;
        public static final int listPreferredItemPaddingRight = 0x7f0300e4;
        public static final int listPreferredItemPaddingStart = 0x7f0300e5;
        public static final int logo = 0x7f0300e6;
        public static final int logoDescription = 0x7f0300e7;
        public static final int maxButtonHeight = 0x7f0300e8;
        public static final int measureWithLargestChild = 0x7f0300e9;
        public static final int menu = 0x7f0300ea;
        public static final int multiChoiceItemLayout = 0x7f0300eb;
        public static final int navigationContentDescription = 0x7f0300ec;
        public static final int navigationIcon = 0x7f0300ed;
        public static final int navigationMode = 0x7f0300ee;
        public static final int numericModifiers = 0x7f0300ef;
        public static final int overlapAnchor = 0x7f0300f0;
        public static final int paddingBottomNoButtons = 0x7f0300f1;
        public static final int paddingEnd = 0x7f0300f2;
        public static final int paddingStart = 0x7f0300f3;
        public static final int paddingTopNoTitle = 0x7f0300f4;
        public static final int panelBackground = 0x7f0300f5;
        public static final int panelMenuListTheme = 0x7f0300f6;
        public static final int panelMenuListWidth = 0x7f0300f7;
        public static final int picture_antiAlias = 0x7f0300f8;
        public static final int picture_cacheFrameNumber = 0x7f0300f9;
        public static final int picture_dither = 0x7f0300fa;
        public static final int picture_filterBitmap = 0x7f0300fb;
        public static final int picture_loop = 0x7f0300fc;
        public static final int picture_opaque = 0x7f0300fd;
        public static final int picture_scaleType = 0x7f0300fe;
        public static final int picture_source = 0x7f0300ff;
        public static final int popupMenuStyle = 0x7f030100;
        public static final int popupTheme = 0x7f030101;
        public static final int popupWindowStyle = 0x7f030102;
        public static final int preserveIconSpacing = 0x7f030103;
        public static final int progressBarPadding = 0x7f030104;
        public static final int progressBarStyle = 0x7f030105;
        public static final int queryBackground = 0x7f030106;
        public static final int queryHint = 0x7f030107;
        public static final int radioButtonStyle = 0x7f030108;
        public static final int ratingBarStyle = 0x7f030109;
        public static final int ratingBarStyleIndicator = 0x7f03010a;
        public static final int ratingBarStyleSmall = 0x7f03010b;
        public static final int recyclerViewStyle = 0x7f03010c;
        public static final int reverseLayout = 0x7f03010d;
        public static final int searchHintIcon = 0x7f03010e;
        public static final int searchIcon = 0x7f03010f;
        public static final int searchViewStyle = 0x7f030110;
        public static final int seekBarStyle = 0x7f030111;
        public static final int selectableItemBackground = 0x7f030112;
        public static final int selectableItemBackgroundBorderless = 0x7f030113;
        public static final int showAsAction = 0x7f030114;
        public static final int showDividers = 0x7f030115;
        public static final int showText = 0x7f030116;
        public static final int showTitle = 0x7f030117;
        public static final int singleChoiceItemLayout = 0x7f030118;
        public static final int spanCount = 0x7f030119;
        public static final int spbStyle = 0x7f03011a;
        public static final int spb_background = 0x7f03011b;
        public static final int spb_color = 0x7f03011c;
        public static final int spb_colors = 0x7f03011d;
        public static final int spb_generate_background_with_colors = 0x7f03011e;
        public static final int spb_gradients = 0x7f03011f;
        public static final int spb_interpolator = 0x7f030120;
        public static final int spb_mirror_mode = 0x7f030121;
        public static final int spb_progressiveStart_activated = 0x7f030122;
        public static final int spb_progressiveStart_speed = 0x7f030123;
        public static final int spb_progressiveStop_speed = 0x7f030124;
        public static final int spb_reversed = 0x7f030125;
        public static final int spb_sections_count = 0x7f030126;
        public static final int spb_speed = 0x7f030127;
        public static final int spb_stroke_separator_length = 0x7f030128;
        public static final int spb_stroke_width = 0x7f030129;
        public static final int spinBars = 0x7f03012a;
        public static final int spinnerDropDownItemStyle = 0x7f03012b;
        public static final int spinnerStyle = 0x7f03012c;
        public static final int splitTrack = 0x7f03012d;
        public static final int srcCompat = 0x7f03012e;
        public static final int stackFromEnd = 0x7f03012f;
        public static final int state_above_anchor = 0x7f030130;
        public static final int subMenuArrow = 0x7f030131;
        public static final int submitBackground = 0x7f030132;
        public static final int subtitle = 0x7f030133;
        public static final int subtitleTextAppearance = 0x7f030134;
        public static final int subtitleTextColor = 0x7f030135;
        public static final int subtitleTextStyle = 0x7f030136;
        public static final int suggestionRowLayout = 0x7f030137;
        public static final int switchMinWidth = 0x7f030138;
        public static final int switchPadding = 0x7f030139;
        public static final int switchStyle = 0x7f03013a;
        public static final int switchTextAppearance = 0x7f03013b;
        public static final int textAllCaps = 0x7f03013c;
        public static final int textAppearanceLargePopupMenu = 0x7f03013d;
        public static final int textAppearanceListItem = 0x7f03013e;
        public static final int textAppearanceListItemSecondary = 0x7f03013f;
        public static final int textAppearanceListItemSmall = 0x7f030140;
        public static final int textAppearancePopupMenuHeader = 0x7f030141;
        public static final int textAppearanceSearchResultSubtitle = 0x7f030142;
        public static final int textAppearanceSearchResultTitle = 0x7f030143;
        public static final int textAppearanceSmallPopupMenu = 0x7f030144;
        public static final int textColorAlertDialogListItem = 0x7f030145;
        public static final int textColorSearchUrl = 0x7f030146;
        public static final int textLocale = 0x7f030147;
        public static final int theme = 0x7f030148;
        public static final int thickness = 0x7f030149;
        public static final int thumbTextPadding = 0x7f03014a;
        public static final int thumbTint = 0x7f03014b;
        public static final int thumbTintMode = 0x7f03014c;
        public static final int tickMark = 0x7f03014d;
        public static final int tickMarkTint = 0x7f03014e;
        public static final int tickMarkTintMode = 0x7f03014f;
        public static final int tint = 0x7f030150;
        public static final int tintMode = 0x7f030151;
        public static final int title = 0x7f030152;
        public static final int titleMargin = 0x7f030153;
        public static final int titleMarginBottom = 0x7f030154;
        public static final int titleMarginEnd = 0x7f030155;
        public static final int titleMarginStart = 0x7f030156;
        public static final int titleMarginTop = 0x7f030157;
        public static final int titleMargins = 0x7f030158;
        public static final int titleTextAppearance = 0x7f030159;
        public static final int titleTextColor = 0x7f03015a;
        public static final int titleTextStyle = 0x7f03015b;
        public static final int toolbarNavigationButtonStyle = 0x7f03015c;
        public static final int toolbarStyle = 0x7f03015d;
        public static final int tooltipForegroundColor = 0x7f03015e;
        public static final int tooltipFrameBackground = 0x7f03015f;
        public static final int tooltipText = 0x7f030160;
        public static final int track = 0x7f030161;
        public static final int trackTint = 0x7f030162;
        public static final int trackTintMode = 0x7f030163;
        public static final int ttcIndex = 0x7f030164;
        public static final int typeface = 0x7f030165;
        public static final int viewInflaterClass = 0x7f030166;
        public static final int voiceIcon = 0x7f030167;
        public static final int windowActionBar = 0x7f030168;
        public static final int windowActionBarOverlay = 0x7f030169;
        public static final int windowActionModeOverlay = 0x7f03016a;
        public static final int windowFixedHeightMajor = 0x7f03016b;
        public static final int windowFixedHeightMinor = 0x7f03016c;
        public static final int windowFixedWidthMajor = 0x7f03016d;
        public static final int windowFixedWidthMinor = 0x7f03016e;
        public static final int windowMinWidthMajor = 0x7f03016f;
        public static final int windowMinWidthMinor = 0x7f030170;
        public static final int windowNoTitle = 0x7f030171;

        public attr(
        /*  JADX ERROR: Method generation error
            jadx.core.utils.exceptions.JadxRuntimeException: Method arg registers not loaded: com.ici.connectivity.R.attr.<init>():void, class status: PROCESS_COMPLETE
            	at jadx.core.dex.nodes.MethodNode.getArgRegs(MethodNode.java:251)
            	at jadx.core.codegen.MethodGen.addDefinition(MethodGen.java:154)
            	at jadx.core.codegen.ClassGen.addMethodCode(ClassGen.java:372)
            	at jadx.core.codegen.ClassGen.addMethod(ClassGen.java:306)
            	at jadx.core.codegen.ClassGen.lambda$addInnerClsAndMethods$2(ClassGen.java:272)
            	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
            	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
            	at java.base/java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:395)
            	at java.base/java.util.stream.Sink$ChainedReference.end(Sink.java:258)
            */
    }

    /* loaded from: classes.dex */
    public final class bool {
        public static final int abc_action_bar_embed_tabs = 0x7f040000;
        public static final int abc_allow_stacked_button_bar = 0x7f040001;
        public static final int abc_config_actionMenuItemAllCaps = 0x7f040002;
        public static final int spb_default_mirror_mode = 0x7f040003;
        public static final int spb_default_progressiveStart_activated = 0x7f040004;
        public static final int spb_default_reversed = 0x7f040005;

        public bool(
        /*  JADX ERROR: Method generation error
            jadx.core.utils.exceptions.JadxRuntimeException: Method arg registers not loaded: com.ici.connectivity.R.bool.<init>():void, class status: PROCESS_COMPLETE
            	at jadx.core.dex.nodes.MethodNode.getArgRegs(MethodNode.java:251)
            	at jadx.core.codegen.MethodGen.addDefinition(MethodGen.java:154)
            	at jadx.core.codegen.ClassGen.addMethodCode(ClassGen.java:372)
            	at jadx.core.codegen.ClassGen.addMethod(ClassGen.java:306)
            	at jadx.core.codegen.ClassGen.lambda$addInnerClsAndMethods$2(ClassGen.java:272)
            	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
            	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
            	at java.base/java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:395)
            	at java.base/java.util.stream.Sink$ChainedReference.end(Sink.java:258)
            */
    }

    /* loaded from: classes.dex */
    public final class color {
        public static final int BEFF7A = 0x7f050000;
        public static final int abc_background_cache_hint_selector_material_dark = 0x7f050001;
        public static final int abc_background_cache_hint_selector_material_light = 0x7f050002;
        public static final int abc_btn_colored_borderless_text_material = 0x7f050003;
        public static final int abc_btn_colored_text_material = 0x7f050004;
        public static final int abc_color_highlight_material = 0x7f050005;
        public static final int abc_hint_foreground_material_dark = 0x7f050006;
        public static final int abc_hint_foreground_material_light = 0x7f050007;
        public static final int abc_input_method_navigation_guard = 0x7f050008;
        public static final int abc_primary_text_disable_only_material_dark = 0x7f050009;
        public static final int abc_primary_text_disable_only_material_light = 0x7f05000a;
        public static final int abc_primary_text_material_dark = 0x7f05000b;
        public static final int abc_primary_text_material_light = 0x7f05000c;
        public static final int abc_search_url_text = 0x7f05000d;
        public static final int abc_search_url_text_normal = 0x7f05000e;
        public static final int abc_search_url_text_pressed = 0x7f05000f;
        public static final int abc_search_url_text_selected = 0x7f050010;
        public static final int abc_secondary_text_material_dark = 0x7f050011;
        public static final int abc_secondary_text_material_light = 0x7f050012;
        public static final int abc_tint_btn_checkable = 0x7f050013;
        public static final int abc_tint_default = 0x7f050014;
        public static final int abc_tint_edittext = 0x7f050015;
        public static final int abc_tint_seek_thumb = 0x7f050016;
        public static final int abc_tint_spinner = 0x7f050017;
        public static final int abc_tint_switch_track = 0x7f050018;
        public static final int accent_material_dark = 0x7f050019;
        public static final int accent_material_light = 0x7f05001a;
        public static final int background_floating_material_dark = 0x7f05001b;
        public static final int background_floating_material_light = 0x7f05001c;
        public static final int background_material_dark = 0x7f05001d;
        public static final int background_material_light = 0x7f05001e;
        public static final int bright_foreground_disabled_material_dark = 0x7f05001f;
        public static final int bright_foreground_disabled_material_light = 0x7f050020;
        public static final int bright_foreground_inverse_material_dark = 0x7f050021;
        public static final int bright_foreground_inverse_material_light = 0x7f050022;
        public static final int bright_foreground_material_dark = 0x7f050023;
        public static final int bright_foreground_material_light = 0x7f050024;
        public static final int button_material_dark = 0x7f050025;
        public static final int button_material_light = 0x7f050026;
        public static final int cl_ici_color_help_text = 0x7f050027;
        public static final int cl_ici_color_prompt_btn_text = 0x7f050028;
        public static final int cl_ici_color_prompt_text = 0x7f050029;
        public static final int cl_ici_color_prompt_view_bg = 0x7f05002a;
        public static final int colorAccent = 0x7f05002b;
        public static final int colorBlack = 0x7f05002c;
        public static final int colorBlack_t8 = 0x7f05002d;
        public static final int colorBlue = 0x7f05002e;
        public static final int colorBlueSky = 0x7f05002f;
        public static final int colorBlueSky_t = 0x7f050030;
        public static final int colorBlue_t = 0x7f050031;
        public static final int colorBrown = 0x7f050032;
        public static final int colorBrownDark = 0x7f050033;
        public static final int colorGreen = 0x7f050034;
        public static final int colorGreenDaisy = 0x7f050035;
        public static final int colorGreenTeg = 0x7f050036;
        public static final int colorGrey = 0x7f050037;
        public static final int colorGrey_t8 = 0x7f050038;
        public static final int colorGrey_tc = 0x7f050039;
        public static final int colorOrange = 0x7f05003a;
        public static final int colorPrimary = 0x7f05003b;
        public static final int colorPrimaryDark = 0x7f05003c;
        public static final int colorPurple = 0x7f05003d;
        public static final int colorRed = 0x7f05003e;
        public static final int colorWhite = 0x7f05003f;
        public static final int colorWhite_t8 = 0x7f050040;
        public static final int colorWhite_tA = 0x7f050041;
        public static final int colorWhite_tD = 0x7f050042;
        public static final int colorYellow = 0x7f050043;
        public static final int color_000000 = 0x7f050044;
        public static final int color_108aeb = 0x7f050045;
        public static final int color_191C28 = 0x7f050046;
        public static final int color_1C212F = 0x7f050047;
        public static final int color_202434 = 0x7f050048;
        public static final int color_24262c = 0x7f050049;
        public static final int color_24365D = 0x7f05004a;
        public static final int color_272A37 = 0x7f05004b;
        public static final int color_2B3142 = 0x7f05004c;
        public static final int color_33979797 = 0x7f05004d;
        public static final int color_33ffffff = 0x7f05004e;
        public static final int color_3F485B = 0x7f05004f;
        public static final int color_454D60 = 0x7f050050;
        public static final int color_4D77859C = 0x7f050051;
        public static final int color_4DBAD2F3 = 0x7f050052;
        public static final int color_4Dffffff = 0x7f050053;
        public static final int color_53576A = 0x7f050054;
        public static final int color_5B6674 = 0x7f050055;
        public static final int color_6D7B92 = 0x7f050056;
        public static final int color_748CC5 = 0x7f050057;
        public static final int color_80000000 = 0x7f050058;
        public static final int color_80BAD2F3 = 0x7f050059;
        public static final int color_80ffffff = 0x7f05005a;
        public static final int color_8E97AB = 0x7f05005b;
        public static final int color_979797 = 0x7f05005c;
        public static final int color_99ffffff = 0x7f05005d;
        public static final int color_AAB1BE = 0x7f05005e;
        public static final int color_ADB7CE = 0x7f05005f;
        public static final int color_AFB4BF = 0x7f050060;
        public static final int color_B0BEE1 = 0x7f050061;
        public static final int color_B3454D60 = 0x7f050062;
        public static final int color_BAD2F3 = 0x7f050063;
        public static final int color_DBA06F = 0x7f050064;
        public static final int color_DBEBFF = 0x7f050065;
        public static final int color_DDA574 = 0x7f050066;
        public static final int color_E5B586 = 0x7f050067;
        public static final int color_F2F2F2 = 0x7f050068;
        public static final int color_FDE6BB = 0x7f050069;
        public static final int color_FFAD65 = 0x7f05006a;
        public static final int color_FFE7AE = 0x7f05006b;
        public static final int color_FFEABF = 0x7f05006c;
        public static final int color_FFFFFF = 0x7f05006d;
        public static final int color_ffffff = 0x7f05006e;
        public static final int color_pressed = 0x7f05006f;
        public static final int color_toast = 0x7f050070;
        public static final int color_tran = 0x7f050071;
        public static final int color_trans = 0x7f050072;
        public static final int dim_foreground_disabled_material_dark = 0x7f050073;
        public static final int dim_foreground_disabled_material_light = 0x7f050074;
        public static final int dim_foreground_material_dark = 0x7f050075;
        public static final int dim_foreground_material_light = 0x7f050076;
        public static final int error_color_material_dark = 0x7f050077;
        public static final int error_color_material_light = 0x7f050078;
        public static final int foreground_material_dark = 0x7f050079;
        public static final int foreground_material_light = 0x7f05007a;
        public static final int highlighted_text_material_dark = 0x7f05007b;
        public static final int highlighted_text_material_light = 0x7f05007c;
        public static final int letter_tile_default_color = 0x7f05007d;
        public static final int letter_tile_font_color = 0x7f05007e;
        public static final int material_blue_grey_800 = 0x7f05007f;
        public static final int material_blue_grey_900 = 0x7f050080;
        public static final int material_blue_grey_950 = 0x7f050081;
        public static final int material_deep_teal_200 = 0x7f050082;
        public static final int material_deep_teal_500 = 0x7f050083;
        public static final int material_grey_100 = 0x7f050084;
        public static final int material_grey_300 = 0x7f050085;
        public static final int material_grey_50 = 0x7f050086;
        public static final int material_grey_600 = 0x7f050087;
        public static final int material_grey_800 = 0x7f050088;
        public static final int material_grey_850 = 0x7f050089;
        public static final int material_grey_900 = 0x7f05008a;
        public static final int notification_action_color_filter = 0x7f05008b;
        public static final int notification_icon_bg_color = 0x7f05008c;
        public static final int paring_pb_color = 0x7f05008d;
        public static final int primary_dark_material_dark = 0x7f05008e;
        public static final int primary_dark_material_light = 0x7f05008f;
        public static final int primary_material_dark = 0x7f050090;
        public static final int primary_material_light = 0x7f050091;
        public static final int primary_text_default_material_dark = 0x7f050092;
        public static final int primary_text_default_material_light = 0x7f050093;
        public static final int primary_text_disabled_material_dark = 0x7f050094;
        public static final int primary_text_disabled_material_light = 0x7f050095;
        public static final int ripple_material_dark = 0x7f050096;
        public static final int ripple_material_light = 0x7f050097;
        public static final int secondary_text_default_material_dark = 0x7f050098;
        public static final int secondary_text_default_material_light = 0x7f050099;
        public static final int secondary_text_disabled_material_dark = 0x7f05009a;
        public static final int secondary_text_disabled_material_light = 0x7f05009b;
        public static final int spb_default_color = 0x7f05009c;
        public static final int switch_thumb_disabled_material_dark = 0x7f05009d;
        public static final int switch_thumb_disabled_material_light = 0x7f05009e;
        public static final int switch_thumb_material_dark = 0x7f05009f;
        public static final int switch_thumb_material_light = 0x7f0500a0;
        public static final int switch_thumb_normal_material_dark = 0x7f0500a1;
        public static final int switch_thumb_normal_material_light = 0x7f0500a2;
        public static final int tooltip_background_dark = 0x7f0500a3;
        public static final int tooltip_background_light = 0x7f0500a4;

        public color(
        /*  JADX ERROR: Method generation error
            jadx.core.utils.exceptions.JadxRuntimeException: Method arg registers not loaded: com.ici.connectivity.R.color.<init>():void, class status: PROCESS_COMPLETE
            	at jadx.core.dex.nodes.MethodNode.getArgRegs(MethodNode.java:251)
            	at jadx.core.codegen.MethodGen.addDefinition(MethodGen.java:154)
            	at jadx.core.codegen.ClassGen.addMethodCode(ClassGen.java:372)
            	at jadx.core.codegen.ClassGen.addMethod(ClassGen.java:306)
            	at jadx.core.codegen.ClassGen.lambda$addInnerClsAndMethods$2(ClassGen.java:272)
            	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
            	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
            	at java.base/java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:395)
            	at java.base/java.util.stream.Sink$ChainedReference.end(Sink.java:258)
            */
    }

    /* loaded from: classes.dex */
    public final class dimen {
        public static final int abc_action_bar_content_inset_material = 0x7f060000;
        public static final int abc_action_bar_content_inset_with_nav = 0x7f060001;
        public static final int abc_action_bar_default_height_material = 0x7f060002;
        public static final int abc_action_bar_default_padding_end_material = 0x7f060003;
        public static final int abc_action_bar_default_padding_start_material = 0x7f060004;
        public static final int abc_action_bar_elevation_material = 0x7f060005;
        public static final int abc_action_bar_icon_vertical_padding_material = 0x7f060006;
        public static final int abc_action_bar_overflow_padding_end_material = 0x7f060007;
        public static final int abc_action_bar_overflow_padding_start_material = 0x7f060008;
        public static final int abc_action_bar_stacked_max_height = 0x7f060009;
        public static final int abc_action_bar_stacked_tab_max_width = 0x7f06000a;
        public static final int abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b;
        public static final int abc_action_bar_subtitle_top_margin_material = 0x7f06000c;
        public static final int abc_action_button_min_height_material = 0x7f06000d;
        public static final int abc_action_button_min_width_material = 0x7f06000e;
        public static final int abc_action_button_min_width_overflow_material = 0x7f06000f;
        public static final int abc_alert_dialog_button_bar_height = 0x7f060010;
        public static final int abc_alert_dialog_button_dimen = 0x7f060011;
        public static final int abc_button_inset_horizontal_material = 0x7f060012;
        public static final int abc_button_inset_vertical_material = 0x7f060013;
        public static final int abc_button_padding_horizontal_material = 0x7f060014;
        public static final int abc_button_padding_vertical_material = 0x7f060015;
        public static final int abc_cascading_menus_min_smallest_width = 0x7f060016;
        public static final int abc_config_prefDialogWidth = 0x7f060017;
        public static final int abc_control_corner_material = 0x7f060018;
        public static final int abc_control_inset_material = 0x7f060019;
        public static final int abc_control_padding_material = 0x7f06001a;
        public static final int abc_dialog_corner_radius_material = 0x7f06001b;
        public static final int abc_dialog_fixed_height_major = 0x7f06001c;
        public static final int abc_dialog_fixed_height_minor = 0x7f06001d;
        public static final int abc_dialog_fixed_width_major = 0x7f06001e;
        public static final int abc_dialog_fixed_width_minor = 0x7f06001f;
        public static final int abc_dialog_list_padding_bottom_no_buttons = 0x7f060020;
        public static final int abc_dialog_list_padding_top_no_title = 0x7f060021;
        public static final int abc_dialog_min_width_major = 0x7f060022;
        public static final int abc_dialog_min_width_minor = 0x7f060023;
        public static final int abc_dialog_padding_material = 0x7f060024;
        public static final int abc_dialog_padding_top_material = 0x7f060025;
        public static final int abc_dialog_title_divider_material = 0x7f060026;
        public static final int abc_disabled_alpha_material_dark = 0x7f060027;
        public static final int abc_disabled_alpha_material_light = 0x7f060028;
        public static final int abc_dropdownitem_icon_width = 0x7f060029;
        public static final int abc_dropdownitem_text_padding_left = 0x7f06002a;
        public static final int abc_dropdownitem_text_padding_right = 0x7f06002b;
        public static final int abc_edit_text_inset_bottom_material = 0x7f06002c;
        public static final int abc_edit_text_inset_horizontal_material = 0x7f06002d;
        public static final int abc_edit_text_inset_top_material = 0x7f06002e;
        public static final int abc_floating_window_z = 0x7f06002f;
        public static final int abc_list_item_height_large_material = 0x7f060030;
        public static final int abc_list_item_height_material = 0x7f060031;
        public static final int abc_list_item_height_small_material = 0x7f060032;
        public static final int abc_list_item_padding_horizontal_material = 0x7f060033;
        public static final int abc_panel_menu_list_width = 0x7f060034;
        public static final int abc_progress_bar_height_material = 0x7f060035;
        public static final int abc_search_view_preferred_height = 0x7f060036;
        public static final int abc_search_view_preferred_width = 0x7f060037;
        public static final int abc_seekbar_track_background_height_material = 0x7f060038;
        public static final int abc_seekbar_track_progress_height_material = 0x7f060039;
        public static final int abc_select_dialog_padding_start_material = 0x7f06003a;
        public static final int abc_switch_padding = 0x7f06003b;
        public static final int abc_text_size_body_1_material = 0x7f06003c;
        public static final int abc_text_size_body_2_material = 0x7f06003d;
        public static final int abc_text_size_button_material = 0x7f06003e;
        public static final int abc_text_size_caption_material = 0x7f06003f;
        public static final int abc_text_size_display_1_material = 0x7f060040;
        public static final int abc_text_size_display_2_material = 0x7f060041;
        public static final int abc_text_size_display_3_material = 0x7f060042;
        public static final int abc_text_size_display_4_material = 0x7f060043;
        public static final int abc_text_size_headline_material = 0x7f060044;
        public static final int abc_text_size_large_material = 0x7f060045;
        public static final int abc_text_size_medium_material = 0x7f060046;
        public static final int abc_text_size_menu_header_material = 0x7f060047;
        public static final int abc_text_size_menu_material = 0x7f060048;
        public static final int abc_text_size_small_material = 0x7f060049;
        public static final int abc_text_size_subhead_material = 0x7f06004a;
        public static final int abc_text_size_subtitle_material_toolbar = 0x7f06004b;
        public static final int abc_text_size_title_material = 0x7f06004c;
        public static final int abc_text_size_title_material_toolbar = 0x7f06004d;
        public static final int activity_horizontal_margin = 0x7f06004e;
        public static final int activity_vertical_margin = 0x7f06004f;
        public static final int button_translation_z = 0x7f060050;
        public static final int cl_ici_dimen_help_item_height = 0x7f060051;
        public static final int cl_ici_dimen_help_text_line_width = 0x7f060052;
        public static final int cl_ici_dimen_help_txt_size = 0x7f060053;
        public static final int cl_ici_dimen_launcher_navi_bar_width = 0x7f060054;
        public static final int cl_ici_dimen_launcher_navi_bar_width_dvi = 0x7f060055;
        public static final int cl_ici_dimen_prompt_btn_text_size = 0x7f060056;
        public static final int cl_ici_dimen_prompt_view_summary_txt_size = 0x7f060057;
        public static final int cl_ici_dimen_prompt_view_title_txt_size = 0x7f060058;
        public static final int common_navigator_height = 0x7f060059;
        public static final int compat_button_inset_horizontal_material = 0x7f06005a;
        public static final int compat_button_inset_vertical_material = 0x7f06005b;
        public static final int compat_button_padding_horizontal_material = 0x7f06005c;
        public static final int compat_button_padding_vertical_material = 0x7f06005d;
        public static final int compat_control_corner_material = 0x7f06005e;
        public static final int compat_notification_large_icon_max_height = 0x7f06005f;
        public static final int compat_notification_large_icon_max_width = 0x7f060060;
        public static final int dimen_ici28_launcher_apptray_width = 0x7f060061;
        public static final int disabled_alpha_material_dark = 0x7f060062;
        public static final int disabled_alpha_material_light = 0x7f060063;
        public static final int dp1 = 0x7f060064;
        public static final int dp10 = 0x7f060065;
        public static final int dp100 = 0x7f060066;
        public static final int dp1000 = 0x7f060067;
        public static final int dp101 = 0x7f060068;
        public static final int dp102 = 0x7f060069;
        public static final int dp103 = 0x7f06006a;
        public static final int dp104 = 0x7f06006b;
        public static final int dp105 = 0x7f06006c;
        public static final int dp106 = 0x7f06006d;
        public static final int dp107 = 0x7f06006e;
        public static final int dp108 = 0x7f06006f;
        public static final int dp1080 = 0x7f060070;
        public static final int dp109 = 0x7f060071;
        public static final int dp11 = 0x7f060072;
        public static final int dp110 = 0x7f060073;
        public static final int dp111 = 0x7f060074;
        public static final int dp112 = 0x7f060075;
        public static final int dp113 = 0x7f060076;
        public static final int dp114 = 0x7f060077;
        public static final int dp115 = 0x7f060078;
        public static final int dp116 = 0x7f060079;
        public static final int dp117 = 0x7f06007a;
        public static final int dp118 = 0x7f06007b;
        public static final int dp119 = 0x7f06007c;
        public static final int dp12 = 0x7f06007d;
        public static final int dp120 = 0x7f06007e;
        public static final int dp121 = 0x7f06007f;
        public static final int dp122 = 0x7f060080;
        public static final int dp123 = 0x7f060081;
        public static final int dp124 = 0x7f060082;
        public static final int dp125 = 0x7f060083;
        public static final int dp126 = 0x7f060084;
        public static final int dp127 = 0x7f060085;
        public static final int dp128 = 0x7f060086;
        public static final int dp129 = 0x7f060087;
        public static final int dp13 = 0x7f060088;
        public static final int dp130 = 0x7f060089;
        public static final int dp131 = 0x7f06008a;
        public static final int dp132 = 0x7f06008b;
        public static final int dp133 = 0x7f06008c;
        public static final int dp134 = 0x7f06008d;
        public static final int dp135 = 0x7f06008e;
        public static final int dp136 = 0x7f06008f;
        public static final int dp137 = 0x7f060090;
        public static final int dp138 = 0x7f060091;
        public static final int dp139 = 0x7f060092;
        public static final int dp14 = 0x7f060093;
        public static final int dp140 = 0x7f060094;
        public static final int dp141 = 0x7f060095;
        public static final int dp142 = 0x7f060096;
        public static final int dp143 = 0x7f060097;
        public static final int dp144 = 0x7f060098;
        public static final int dp145 = 0x7f060099;
        public static final int dp146 = 0x7f06009a;
        public static final int dp147 = 0x7f06009b;
        public static final int dp148 = 0x7f06009c;
        public static final int dp149 = 0x7f06009d;
        public static final int dp15 = 0x7f06009e;
        public static final int dp150 = 0x7f06009f;
        public static final int dp151 = 0x7f0600a0;
        public static final int dp152 = 0x7f0600a1;
        public static final int dp153 = 0x7f0600a2;
        public static final int dp154 = 0x7f0600a3;
        public static final int dp155 = 0x7f0600a4;
        public static final int dp156 = 0x7f0600a5;
        public static final int dp157 = 0x7f0600a6;
        public static final int dp158 = 0x7f0600a7;
        public static final int dp159 = 0x7f0600a8;
        public static final int dp16 = 0x7f0600a9;
        public static final int dp160 = 0x7f0600aa;
        public static final int dp161 = 0x7f0600ab;
        public static final int dp162 = 0x7f0600ac;
        public static final int dp163 = 0x7f0600ad;
        public static final int dp164 = 0x7f0600ae;
        public static final int dp165 = 0x7f0600af;
        public static final int dp166 = 0x7f0600b0;
        public static final int dp167 = 0x7f0600b1;
        public static final int dp168 = 0x7f0600b2;
        public static final int dp169 = 0x7f0600b3;
        public static final int dp17 = 0x7f0600b4;
        public static final int dp170 = 0x7f0600b5;
        public static final int dp171 = 0x7f0600b6;
        public static final int dp172 = 0x7f0600b7;
        public static final int dp173 = 0x7f0600b8;
        public static final int dp174 = 0x7f0600b9;
        public static final int dp175 = 0x7f0600ba;
        public static final int dp176 = 0x7f0600bb;
        public static final int dp177 = 0x7f0600bc;
        public static final int dp178 = 0x7f0600bd;
        public static final int dp179 = 0x7f0600be;
        public static final int dp18 = 0x7f0600bf;
        public static final int dp180 = 0x7f0600c0;
        public static final int dp181 = 0x7f0600c1;
        public static final int dp182 = 0x7f0600c2;
        public static final int dp183 = 0x7f0600c3;
        public static final int dp184 = 0x7f0600c4;
        public static final int dp185 = 0x7f0600c5;
        public static final int dp186 = 0x7f0600c6;
        public static final int dp187 = 0x7f0600c7;
        public static final int dp188 = 0x7f0600c8;
        public static final int dp189 = 0x7f0600c9;
        public static final int dp19 = 0x7f0600ca;
        public static final int dp190 = 0x7f0600cb;
        public static final int dp191 = 0x7f0600cc;
        public static final int dp192 = 0x7f0600cd;
        public static final int dp193 = 0x7f0600ce;
        public static final int dp194 = 0x7f0600cf;
        public static final int dp195 = 0x7f0600d0;
        public static final int dp196 = 0x7f0600d1;
        public static final int dp197 = 0x7f0600d2;
        public static final int dp198 = 0x7f0600d3;
        public static final int dp199 = 0x7f0600d4;
        public static final int dp2 = 0x7f0600d5;
        public static final int dp20 = 0x7f0600d6;
        public static final int dp200 = 0x7f0600d7;
        public static final int dp201 = 0x7f0600d8;
        public static final int dp202 = 0x7f0600d9;
        public static final int dp203 = 0x7f0600da;
        public static final int dp204 = 0x7f0600db;
        public static final int dp205 = 0x7f0600dc;
        public static final int dp206 = 0x7f0600dd;
        public static final int dp207 = 0x7f0600de;
        public static final int dp208 = 0x7f0600df;
        public static final int dp209 = 0x7f0600e0;
        public static final int dp21 = 0x7f0600e1;
        public static final int dp210 = 0x7f0600e2;
        public static final int dp211 = 0x7f0600e3;
        public static final int dp212 = 0x7f0600e4;
        public static final int dp213 = 0x7f0600e5;
        public static final int dp214 = 0x7f0600e6;
        public static final int dp215 = 0x7f0600e7;
        public static final int dp216 = 0x7f0600e8;
        public static final int dp217 = 0x7f0600e9;
        public static final int dp218 = 0x7f0600ea;
        public static final int dp219 = 0x7f0600eb;
        public static final int dp22 = 0x7f0600ec;
        public static final int dp220 = 0x7f0600ed;
        public static final int dp221 = 0x7f0600ee;
        public static final int dp222 = 0x7f0600ef;
        public static final int dp223 = 0x7f0600f0;
        public static final int dp224 = 0x7f0600f1;
        public static final int dp225 = 0x7f0600f2;
        public static final int dp226 = 0x7f0600f3;
        public static final int dp227 = 0x7f0600f4;
        public static final int dp228 = 0x7f0600f5;
        public static final int dp229 = 0x7f0600f6;
        public static final int dp23 = 0x7f0600f7;
        public static final int dp230 = 0x7f0600f8;
        public static final int dp231 = 0x7f0600f9;
        public static final int dp232 = 0x7f0600fa;
        public static final int dp233 = 0x7f0600fb;
        public static final int dp234 = 0x7f0600fc;
        public static final int dp235 = 0x7f0600fd;
        public static final int dp236 = 0x7f0600fe;
        public static final int dp237 = 0x7f0600ff;
        public static final int dp238 = 0x7f060100;
        public static final int dp239 = 0x7f060101;
        public static final int dp24 = 0x7f060102;
        public static final int dp240 = 0x7f060103;
        public static final int dp241 = 0x7f060104;
        public static final int dp242 = 0x7f060105;
        public static final int dp243 = 0x7f060106;
        public static final int dp244 = 0x7f060107;
        public static final int dp245 = 0x7f060108;
        public static final int dp246 = 0x7f060109;
        public static final int dp247 = 0x7f06010a;
        public static final int dp248 = 0x7f06010b;
        public static final int dp249 = 0x7f06010c;
        public static final int dp25 = 0x7f06010d;
        public static final int dp250 = 0x7f06010e;
        public static final int dp251 = 0x7f06010f;
        public static final int dp252 = 0x7f060110;
        public static final int dp253 = 0x7f060111;
        public static final int dp254 = 0x7f060112;
        public static final int dp255 = 0x7f060113;
        public static final int dp256 = 0x7f060114;
        public static final int dp257 = 0x7f060115;
        public static final int dp258 = 0x7f060116;
        public static final int dp259 = 0x7f060117;
        public static final int dp26 = 0x7f060118;
        public static final int dp260 = 0x7f060119;
        public static final int dp261 = 0x7f06011a;
        public static final int dp262 = 0x7f06011b;
        public static final int dp263 = 0x7f06011c;
        public static final int dp264 = 0x7f06011d;
        public static final int dp265 = 0x7f06011e;
        public static final int dp266 = 0x7f06011f;
        public static final int dp267 = 0x7f060120;
        public static final int dp268 = 0x7f060121;
        public static final int dp269 = 0x7f060122;
        public static final int dp27 = 0x7f060123;
        public static final int dp270 = 0x7f060124;
        public static final int dp271 = 0x7f060125;
        public static final int dp272 = 0x7f060126;
        public static final int dp273 = 0x7f060127;
        public static final int dp274 = 0x7f060128;
        public static final int dp275 = 0x7f060129;
        public static final int dp276 = 0x7f06012a;
        public static final int dp277 = 0x7f06012b;
        public static final int dp278 = 0x7f06012c;
        public static final int dp279 = 0x7f06012d;
        public static final int dp28 = 0x7f06012e;
        public static final int dp280 = 0x7f06012f;
        public static final int dp281 = 0x7f060130;
        public static final int dp282 = 0x7f060131;
        public static final int dp283 = 0x7f060132;
        public static final int dp284 = 0x7f060133;
        public static final int dp285 = 0x7f060134;
        public static final int dp286 = 0x7f060135;
        public static final int dp287 = 0x7f060136;
        public static final int dp288 = 0x7f060137;
        public static final int dp289 = 0x7f060138;
        public static final int dp29 = 0x7f060139;
        public static final int dp290 = 0x7f06013a;
        public static final int dp291 = 0x7f06013b;
        public static final int dp292 = 0x7f06013c;
        public static final int dp293 = 0x7f06013d;
        public static final int dp294 = 0x7f06013e;
        public static final int dp295 = 0x7f06013f;
        public static final int dp296 = 0x7f060140;
        public static final int dp297 = 0x7f060141;
        public static final int dp298 = 0x7f060142;
        public static final int dp299 = 0x7f060143;
        public static final int dp3 = 0x7f060144;
        public static final int dp30 = 0x7f060145;
        public static final int dp300 = 0x7f060146;
        public static final int dp301 = 0x7f060147;
        public static final int dp302 = 0x7f060148;
        public static final int dp303 = 0x7f060149;
        public static final int dp304 = 0x7f06014a;
        public static final int dp305 = 0x7f06014b;
        public static final int dp306 = 0x7f06014c;
        public static final int dp307 = 0x7f06014d;
        public static final int dp308 = 0x7f06014e;
        public static final int dp309 = 0x7f06014f;
        public static final int dp31 = 0x7f060150;
        public static final int dp310 = 0x7f060151;
        public static final int dp311 = 0x7f060152;
        public static final int dp312 = 0x7f060153;
        public static final int dp313 = 0x7f060154;
        public static final int dp314 = 0x7f060155;
        public static final int dp315 = 0x7f060156;
        public static final int dp316 = 0x7f060157;
        public static final int dp317 = 0x7f060158;
        public static final int dp318 = 0x7f060159;
        public static final int dp319 = 0x7f06015a;
        public static final int dp32 = 0x7f06015b;
        public static final int dp320 = 0x7f06015c;
        public static final int dp321 = 0x7f06015d;
        public static final int dp322 = 0x7f06015e;
        public static final int dp323 = 0x7f06015f;
        public static final int dp324 = 0x7f060160;
        public static final int dp325 = 0x7f060161;
        public static final int dp326 = 0x7f060162;
        public static final int dp327 = 0x7f060163;
        public static final int dp328 = 0x7f060164;
        public static final int dp329 = 0x7f060165;
        public static final int dp33 = 0x7f060166;
        public static final int dp330 = 0x7f060167;
        public static final int dp331 = 0x7f060168;
        public static final int dp332 = 0x7f060169;
        public static final int dp333 = 0x7f06016a;
        public static final int dp334 = 0x7f06016b;
        public static final int dp335 = 0x7f06016c;
        public static final int dp336 = 0x7f06016d;
        public static final int dp337 = 0x7f06016e;
        public static final int dp338 = 0x7f06016f;
        public static final int dp339 = 0x7f060170;
        public static final int dp34 = 0x7f060171;
        public static final int dp340 = 0x7f060172;
        public static final int dp341 = 0x7f060173;
        public static final int dp342 = 0x7f060174;
        public static final int dp343 = 0x7f060175;
        public static final int dp344 = 0x7f060176;
        public static final int dp345 = 0x7f060177;
        public static final int dp346 = 0x7f060178;
        public static final int dp347 = 0x7f060179;
        public static final int dp348 = 0x7f06017a;
        public static final int dp349 = 0x7f06017b;
        public static final int dp35 = 0x7f06017c;
        public static final int dp350 = 0x7f06017d;
        public static final int dp351 = 0x7f06017e;
        public static final int dp352 = 0x7f06017f;
        public static final int dp353 = 0x7f060180;
        public static final int dp354 = 0x7f060181;
        public static final int dp355 = 0x7f060182;
        public static final int dp356 = 0x7f060183;
        public static final int dp357 = 0x7f060184;
        public static final int dp358 = 0x7f060185;
        public static final int dp359 = 0x7f060186;
        public static final int dp36 = 0x7f060187;
        public static final int dp360 = 0x7f060188;
        public static final int dp361 = 0x7f060189;
        public static final int dp362 = 0x7f06018a;
        public static final int dp363 = 0x7f06018b;
        public static final int dp364 = 0x7f06018c;
        public static final int dp365 = 0x7f06018d;
        public static final int dp366 = 0x7f06018e;
        public static final int dp367 = 0x7f06018f;
        public static final int dp368 = 0x7f060190;
        public static final int dp369 = 0x7f060191;
        public static final int dp37 = 0x7f060192;
        public static final int dp370 = 0x7f060193;
        public static final int dp371 = 0x7f060194;
        public static final int dp372 = 0x7f060195;
        public static final int dp373 = 0x7f060196;
        public static final int dp374 = 0x7f060197;
        public static final int dp375 = 0x7f060198;
        public static final int dp376 = 0x7f060199;
        public static final int dp377 = 0x7f06019a;
        public static final int dp378 = 0x7f06019b;
        public static final int dp379 = 0x7f06019c;
        public static final int dp38 = 0x7f06019d;
        public static final int dp380 = 0x7f06019e;
        public static final int dp381 = 0x7f06019f;
        public static final int dp382 = 0x7f0601a0;
        public static final int dp383 = 0x7f0601a1;
        public static final int dp384 = 0x7f0601a2;
        public static final int dp385 = 0x7f0601a3;
        public static final int dp386 = 0x7f0601a4;
        public static final int dp387 = 0x7f0601a5;
        public static final int dp388 = 0x7f0601a6;
        public static final int dp389 = 0x7f0601a7;
        public static final int dp39 = 0x7f0601a8;
        public static final int dp390 = 0x7f0601a9;
        public static final int dp391 = 0x7f0601aa;
        public static final int dp392 = 0x7f0601ab;
        public static final int dp393 = 0x7f0601ac;
        public static final int dp394 = 0x7f0601ad;
        public static final int dp395 = 0x7f0601ae;
        public static final int dp396 = 0x7f0601af;
        public static final int dp397 = 0x7f0601b0;
        public static final int dp398 = 0x7f0601b1;
        public static final int dp399 = 0x7f0601b2;
        public static final int dp4 = 0x7f0601b3;
        public static final int dp40 = 0x7f0601b4;
        public static final int dp400 = 0x7f0601b5;
        public static final int dp401 = 0x7f0601b6;
        public static final int dp402 = 0x7f0601b7;
        public static final int dp403 = 0x7f0601b8;
        public static final int dp404 = 0x7f0601b9;
        public static final int dp405 = 0x7f0601ba;
        public static final int dp406 = 0x7f0601bb;
        public static final int dp407 = 0x7f0601bc;
        public static final int dp408 = 0x7f0601bd;
        public static final int dp409 = 0x7f0601be;
        public static final int dp41 = 0x7f0601bf;
        public static final int dp410 = 0x7f0601c0;
        public static final int dp411 = 0x7f0601c1;
        public static final int dp412 = 0x7f0601c2;
        public static final int dp413 = 0x7f0601c3;
        public static final int dp414 = 0x7f0601c4;
        public static final int dp415 = 0x7f0601c5;
        public static final int dp416 = 0x7f0601c6;
        public static final int dp417 = 0x7f0601c7;
        public static final int dp418 = 0x7f0601c8;
        public static final int dp419 = 0x7f0601c9;
        public static final int dp42 = 0x7f0601ca;
        public static final int dp420 = 0x7f0601cb;
        public static final int dp421 = 0x7f0601cc;
        public static final int dp422 = 0x7f0601cd;
        public static final int dp423 = 0x7f0601ce;
        public static final int dp424 = 0x7f0601cf;
        public static final int dp425 = 0x7f0601d0;
        public static final int dp426 = 0x7f0601d1;
        public static final int dp427 = 0x7f0601d2;
        public static final int dp428 = 0x7f0601d3;
        public static final int dp429 = 0x7f0601d4;
        public static final int dp43 = 0x7f0601d5;
        public static final int dp430 = 0x7f0601d6;
        public static final int dp431 = 0x7f0601d7;
        public static final int dp432 = 0x7f0601d8;
        public static final int dp433 = 0x7f0601d9;
        public static final int dp434 = 0x7f0601da;
        public static final int dp435 = 0x7f0601db;
        public static final int dp436 = 0x7f0601dc;
        public static final int dp437 = 0x7f0601dd;
        public static final int dp438 = 0x7f0601de;
        public static final int dp439 = 0x7f0601df;
        public static final int dp44 = 0x7f0601e0;
        public static final int dp440 = 0x7f0601e1;
        public static final int dp441 = 0x7f0601e2;
        public static final int dp442 = 0x7f0601e3;
        public static final int dp443 = 0x7f0601e4;
        public static final int dp444 = 0x7f0601e5;
        public static final int dp445 = 0x7f0601e6;
        public static final int dp446 = 0x7f0601e7;
        public static final int dp447 = 0x7f0601e8;
        public static final int dp448 = 0x7f0601e9;
        public static final int dp449 = 0x7f0601ea;
        public static final int dp45 = 0x7f0601eb;
        public static final int dp450 = 0x7f0601ec;
        public static final int dp451 = 0x7f0601ed;
        public static final int dp452 = 0x7f0601ee;
        public static final int dp453 = 0x7f0601ef;
        public static final int dp454 = 0x7f0601f0;
        public static final int dp455 = 0x7f0601f1;
        public static final int dp456 = 0x7f0601f2;
        public static final int dp457 = 0x7f0601f3;
        public static final int dp458 = 0x7f0601f4;
        public static final int dp459 = 0x7f0601f5;
        public static final int dp46 = 0x7f0601f6;
        public static final int dp460 = 0x7f0601f7;
        public static final int dp461 = 0x7f0601f8;
        public static final int dp462 = 0x7f0601f9;
        public static final int dp463 = 0x7f0601fa;
        public static final int dp464 = 0x7f0601fb;
        public static final int dp465 = 0x7f0601fc;
        public static final int dp466 = 0x7f0601fd;
        public static final int dp467 = 0x7f0601fe;
        public static final int dp468 = 0x7f0601ff;
        public static final int dp469 = 0x7f060200;
        public static final int dp47 = 0x7f060201;
        public static final int dp470 = 0x7f060202;
        public static final int dp471 = 0x7f060203;
        public static final int dp472 = 0x7f060204;
        public static final int dp473 = 0x7f060205;
        public static final int dp474 = 0x7f060206;
        public static final int dp475 = 0x7f060207;
        public static final int dp476 = 0x7f060208;
        public static final int dp477 = 0x7f060209;
        public static final int dp478 = 0x7f06020a;
        public static final int dp479 = 0x7f06020b;
        public static final int dp48 = 0x7f06020c;
        public static final int dp480 = 0x7f06020d;
        public static final int dp481 = 0x7f06020e;
        public static final int dp482 = 0x7f06020f;
        public static final int dp483 = 0x7f060210;
        public static final int dp484 = 0x7f060211;
        public static final int dp485 = 0x7f060212;
        public static final int dp486 = 0x7f060213;
        public static final int dp487 = 0x7f060214;
        public static final int dp488 = 0x7f060215;
        public static final int dp489 = 0x7f060216;
        public static final int dp49 = 0x7f060217;
        public static final int dp490 = 0x7f060218;
        public static final int dp491 = 0x7f060219;
        public static final int dp492 = 0x7f06021a;
        public static final int dp493 = 0x7f06021b;
        public static final int dp494 = 0x7f06021c;
        public static final int dp495 = 0x7f06021d;
        public static final int dp496 = 0x7f06021e;
        public static final int dp497 = 0x7f06021f;
        public static final int dp498 = 0x7f060220;
        public static final int dp499 = 0x7f060221;
        public static final int dp5 = 0x7f060222;
        public static final int dp50 = 0x7f060223;
        public static final int dp500 = 0x7f060224;
        public static final int dp501 = 0x7f060225;
        public static final int dp502 = 0x7f060226;
        public static final int dp503 = 0x7f060227;
        public static final int dp504 = 0x7f060228;
        public static final int dp505 = 0x7f060229;
        public static final int dp506 = 0x7f06022a;
        public static final int dp507 = 0x7f06022b;
        public static final int dp508 = 0x7f06022c;
        public static final int dp509 = 0x7f06022d;
        public static final int dp51 = 0x7f06022e;
        public static final int dp510 = 0x7f06022f;
        public static final int dp511 = 0x7f060230;
        public static final int dp512 = 0x7f060231;
        public static final int dp513 = 0x7f060232;
        public static final int dp514 = 0x7f060233;
        public static final int dp515 = 0x7f060234;
        public static final int dp516 = 0x7f060235;
        public static final int dp517 = 0x7f060236;
        public static final int dp518 = 0x7f060237;
        public static final int dp519 = 0x7f060238;
        public static final int dp52 = 0x7f060239;
        public static final int dp520 = 0x7f06023a;
        public static final int dp521 = 0x7f06023b;
        public static final int dp522 = 0x7f06023c;
        public static final int dp523 = 0x7f06023d;
        public static final int dp524 = 0x7f06023e;
        public static final int dp525 = 0x7f06023f;
        public static final int dp526 = 0x7f060240;
        public static final int dp527 = 0x7f060241;
        public static final int dp528 = 0x7f060242;
        public static final int dp529 = 0x7f060243;
        public static final int dp53 = 0x7f060244;
        public static final int dp530 = 0x7f060245;
        public static final int dp531 = 0x7f060246;
        public static final int dp532 = 0x7f060247;
        public static final int dp533 = 0x7f060248;
        public static final int dp534 = 0x7f060249;
        public static final int dp535 = 0x7f06024a;
        public static final int dp536 = 0x7f06024b;
        public static final int dp537 = 0x7f06024c;
        public static final int dp538 = 0x7f06024d;
        public static final int dp539 = 0x7f06024e;
        public static final int dp54 = 0x7f06024f;
        public static final int dp540 = 0x7f060250;
        public static final int dp541 = 0x7f060251;
        public static final int dp542 = 0x7f060252;
        public static final int dp543 = 0x7f060253;
        public static final int dp544 = 0x7f060254;
        public static final int dp545 = 0x7f060255;
        public static final int dp546 = 0x7f060256;
        public static final int dp547 = 0x7f060257;
        public static final int dp548 = 0x7f060258;
        public static final int dp549 = 0x7f060259;
        public static final int dp55 = 0x7f06025a;
        public static final int dp550 = 0x7f06025b;
        public static final int dp551 = 0x7f06025c;
        public static final int dp552 = 0x7f06025d;
        public static final int dp553 = 0x7f06025e;
        public static final int dp554 = 0x7f06025f;
        public static final int dp555 = 0x7f060260;
        public static final int dp556 = 0x7f060261;
        public static final int dp557 = 0x7f060262;
        public static final int dp558 = 0x7f060263;
        public static final int dp559 = 0x7f060264;
        public static final int dp56 = 0x7f060265;
        public static final int dp560 = 0x7f060266;
        public static final int dp561 = 0x7f060267;
        public static final int dp562 = 0x7f060268;
        public static final int dp563 = 0x7f060269;
        public static final int dp564 = 0x7f06026a;
        public static final int dp565 = 0x7f06026b;
        public static final int dp566 = 0x7f06026c;
        public static final int dp567 = 0x7f06026d;
        public static final int dp568 = 0x7f06026e;
        public static final int dp569 = 0x7f06026f;
        public static final int dp57 = 0x7f060270;
        public static final int dp570 = 0x7f060271;
        public static final int dp571 = 0x7f060272;
        public static final int dp572 = 0x7f060273;
        public static final int dp573 = 0x7f060274;
        public static final int dp574 = 0x7f060275;
        public static final int dp575 = 0x7f060276;
        public static final int dp576 = 0x7f060277;
        public static final int dp577 = 0x7f060278;
        public static final int dp578 = 0x7f060279;
        public static final int dp579 = 0x7f06027a;
        public static final int dp58 = 0x7f06027b;
        public static final int dp580 = 0x7f06027c;
        public static final int dp581 = 0x7f06027d;
        public static final int dp582 = 0x7f06027e;
        public static final int dp583 = 0x7f06027f;
        public static final int dp584 = 0x7f060280;
        public static final int dp585 = 0x7f060281;
        public static final int dp586 = 0x7f060282;
        public static final int dp587 = 0x7f060283;
        public static final int dp588 = 0x7f060284;
        public static final int dp589 = 0x7f060285;
        public static final int dp59 = 0x7f060286;
        public static final int dp590 = 0x7f060287;
        public static final int dp591 = 0x7f060288;
        public static final int dp592 = 0x7f060289;
        public static final int dp593 = 0x7f06028a;
        public static final int dp594 = 0x7f06028b;
        public static final int dp595 = 0x7f06028c;
        public static final int dp596 = 0x7f06028d;
        public static final int dp597 = 0x7f06028e;
        public static final int dp598 = 0x7f06028f;
        public static final int dp599 = 0x7f060290;
        public static final int dp6 = 0x7f060291;
        public static final int dp60 = 0x7f060292;
        public static final int dp600 = 0x7f060293;
        public static final int dp601 = 0x7f060294;
        public static final int dp602 = 0x7f060295;
        public static final int dp603 = 0x7f060296;
        public static final int dp604 = 0x7f060297;
        public static final int dp605 = 0x7f060298;
        public static final int dp606 = 0x7f060299;
        public static final int dp607 = 0x7f06029a;
        public static final int dp608 = 0x7f06029b;
        public static final int dp609 = 0x7f06029c;
        public static final int dp61 = 0x7f06029d;
        public static final int dp610 = 0x7f06029e;
        public static final int dp611 = 0x7f06029f;
        public static final int dp612 = 0x7f0602a0;
        public static final int dp613 = 0x7f0602a1;
        public static final int dp614 = 0x7f0602a2;
        public static final int dp615 = 0x7f0602a3;
        public static final int dp616 = 0x7f0602a4;
        public static final int dp617 = 0x7f0602a5;
        public static final int dp618 = 0x7f0602a6;
        public static final int dp619 = 0x7f0602a7;
        public static final int dp62 = 0x7f0602a8;
        public static final int dp620 = 0x7f0602a9;
        public static final int dp621 = 0x7f0602aa;
        public static final int dp622 = 0x7f0602ab;
        public static final int dp623 = 0x7f0602ac;
        public static final int dp624 = 0x7f0602ad;
        public static final int dp625 = 0x7f0602ae;
        public static final int dp626 = 0x7f0602af;
        public static final int dp627 = 0x7f0602b0;
        public static final int dp628 = 0x7f0602b1;
        public static final int dp629 = 0x7f0602b2;
        public static final int dp63 = 0x7f0602b3;
        public static final int dp630 = 0x7f0602b4;
        public static final int dp631 = 0x7f0602b5;
        public static final int dp632 = 0x7f0602b6;
        public static final int dp633 = 0x7f0602b7;
        public static final int dp634 = 0x7f0602b8;
        public static final int dp635 = 0x7f0602b9;
        public static final int dp636 = 0x7f0602ba;
        public static final int dp637 = 0x7f0602bb;
        public static final int dp638 = 0x7f0602bc;
        public static final int dp639 = 0x7f0602bd;
        public static final int dp64 = 0x7f0602be;
        public static final int dp640 = 0x7f0602bf;
        public static final int dp641 = 0x7f0602c0;
        public static final int dp642 = 0x7f0602c1;
        public static final int dp643 = 0x7f0602c2;
        public static final int dp644 = 0x7f0602c3;
        public static final int dp645 = 0x7f0602c4;
        public static final int dp646 = 0x7f0602c5;
        public static final int dp647 = 0x7f0602c6;
        public static final int dp648 = 0x7f0602c7;
        public static final int dp649 = 0x7f0602c8;
        public static final int dp65 = 0x7f0602c9;
        public static final int dp650 = 0x7f0602ca;
        public static final int dp651 = 0x7f0602cb;
        public static final int dp652 = 0x7f0602cc;
        public static final int dp653 = 0x7f0602cd;
        public static final int dp654 = 0x7f0602ce;
        public static final int dp655 = 0x7f0602cf;
        public static final int dp656 = 0x7f0602d0;
        public static final int dp657 = 0x7f0602d1;
        public static final int dp658 = 0x7f0602d2;
        public static final int dp659 = 0x7f0602d3;
        public static final int dp66 = 0x7f0602d4;
        public static final int dp660 = 0x7f0602d5;
        public static final int dp661 = 0x7f0602d6;
        public static final int dp662 = 0x7f0602d7;
        public static final int dp663 = 0x7f0602d8;
        public static final int dp664 = 0x7f0602d9;
        public static final int dp665 = 0x7f0602da;
        public static final int dp666 = 0x7f0602db;
        public static final int dp667 = 0x7f0602dc;
        public static final int dp668 = 0x7f0602dd;
        public static final int dp669 = 0x7f0602de;
        public static final int dp67 = 0x7f0602df;
        public static final int dp670 = 0x7f0602e0;
        public static final int dp671 = 0x7f0602e1;
        public static final int dp672 = 0x7f0602e2;
        public static final int dp673 = 0x7f0602e3;
        public static final int dp674 = 0x7f0602e4;
        public static final int dp675 = 0x7f0602e5;
        public static final int dp676 = 0x7f0602e6;
        public static final int dp677 = 0x7f0602e7;
        public static final int dp678 = 0x7f0602e8;
        public static final int dp679 = 0x7f0602e9;
        public static final int dp68 = 0x7f0602ea;
        public static final int dp680 = 0x7f0602eb;
        public static final int dp681 = 0x7f0602ec;
        public static final int dp682 = 0x7f0602ed;
        public static final int dp683 = 0x7f0602ee;
        public static final int dp684 = 0x7f0602ef;
        public static final int dp685 = 0x7f0602f0;
        public static final int dp686 = 0x7f0602f1;
        public static final int dp687 = 0x7f0602f2;
        public static final int dp688 = 0x7f0602f3;
        public static final int dp689 = 0x7f0602f4;
        public static final int dp69 = 0x7f0602f5;
        public static final int dp690 = 0x7f0602f6;
        public static final int dp691 = 0x7f0602f7;
        public static final int dp692 = 0x7f0602f8;
        public static final int dp693 = 0x7f0602f9;
        public static final int dp694 = 0x7f0602fa;
        public static final int dp695 = 0x7f0602fb;
        public static final int dp696 = 0x7f0602fc;
        public static final int dp697 = 0x7f0602fd;
        public static final int dp698 = 0x7f0602fe;
        public static final int dp699 = 0x7f0602ff;
        public static final int dp7 = 0x7f060300;
        public static final int dp70 = 0x7f060301;
        public static final int dp700 = 0x7f060302;
        public static final int dp701 = 0x7f060303;
        public static final int dp702 = 0x7f060304;
        public static final int dp703 = 0x7f060305;
        public static final int dp704 = 0x7f060306;
        public static final int dp705 = 0x7f060307;
        public static final int dp706 = 0x7f060308;
        public static final int dp707 = 0x7f060309;
        public static final int dp708 = 0x7f06030a;
        public static final int dp709 = 0x7f06030b;
        public static final int dp71 = 0x7f06030c;
        public static final int dp710 = 0x7f06030d;
        public static final int dp711 = 0x7f06030e;
        public static final int dp712 = 0x7f06030f;
        public static final int dp713 = 0x7f060310;
        public static final int dp714 = 0x7f060311;
        public static final int dp715 = 0x7f060312;
        public static final int dp716 = 0x7f060313;
        public static final int dp717 = 0x7f060314;
        public static final int dp718 = 0x7f060315;
        public static final int dp719 = 0x7f060316;
        public static final int dp72 = 0x7f060317;
        public static final int dp720 = 0x7f060318;
        public static final int dp721 = 0x7f060319;
        public static final int dp722 = 0x7f06031a;
        public static final int dp723 = 0x7f06031b;
        public static final int dp724 = 0x7f06031c;
        public static final int dp725 = 0x7f06031d;
        public static final int dp726 = 0x7f06031e;
        public static final int dp727 = 0x7f06031f;
        public static final int dp728 = 0x7f060320;
        public static final int dp729 = 0x7f060321;
        public static final int dp73 = 0x7f060322;
        public static final int dp730 = 0x7f060323;
        public static final int dp731 = 0x7f060324;
        public static final int dp732 = 0x7f060325;
        public static final int dp733 = 0x7f060326;
        public static final int dp734 = 0x7f060327;
        public static final int dp735 = 0x7f060328;
        public static final int dp736 = 0x7f060329;
        public static final int dp737 = 0x7f06032a;
        public static final int dp738 = 0x7f06032b;
        public static final int dp739 = 0x7f06032c;
        public static final int dp74 = 0x7f06032d;
        public static final int dp740 = 0x7f06032e;
        public static final int dp741 = 0x7f06032f;
        public static final int dp742 = 0x7f060330;
        public static final int dp743 = 0x7f060331;
        public static final int dp744 = 0x7f060332;
        public static final int dp745 = 0x7f060333;
        public static final int dp746 = 0x7f060334;
        public static final int dp747 = 0x7f060335;
        public static final int dp748 = 0x7f060336;
        public static final int dp749 = 0x7f060337;
        public static final int dp75 = 0x7f060338;
        public static final int dp750 = 0x7f060339;
        public static final int dp751 = 0x7f06033a;
        public static final int dp752 = 0x7f06033b;
        public static final int dp753 = 0x7f06033c;
        public static final int dp754 = 0x7f06033d;
        public static final int dp755 = 0x7f06033e;
        public static final int dp756 = 0x7f06033f;
        public static final int dp757 = 0x7f060340;
        public static final int dp758 = 0x7f060341;
        public static final int dp759 = 0x7f060342;
        public static final int dp76 = 0x7f060343;
        public static final int dp760 = 0x7f060344;
        public static final int dp761 = 0x7f060345;
        public static final int dp762 = 0x7f060346;
        public static final int dp763 = 0x7f060347;
        public static final int dp764 = 0x7f060348;
        public static final int dp765 = 0x7f060349;
        public static final int dp766 = 0x7f06034a;
        public static final int dp767 = 0x7f06034b;
        public static final int dp768 = 0x7f06034c;
        public static final int dp769 = 0x7f06034d;
        public static final int dp77 = 0x7f06034e;
        public static final int dp770 = 0x7f06034f;
        public static final int dp771 = 0x7f060350;
        public static final int dp772 = 0x7f060351;
        public static final int dp773 = 0x7f060352;
        public static final int dp774 = 0x7f060353;
        public static final int dp775 = 0x7f060354;
        public static final int dp776 = 0x7f060355;
        public static final int dp777 = 0x7f060356;
        public static final int dp778 = 0x7f060357;
        public static final int dp779 = 0x7f060358;
        public static final int dp78 = 0x7f060359;
        public static final int dp780 = 0x7f06035a;
        public static final int dp781 = 0x7f06035b;
        public static final int dp782 = 0x7f06035c;
        public static final int dp783 = 0x7f06035d;
        public static final int dp784 = 0x7f06035e;
        public static final int dp785 = 0x7f06035f;
        public static final int dp786 = 0x7f060360;
        public static final int dp787 = 0x7f060361;
        public static final int dp788 = 0x7f060362;
        public static final int dp789 = 0x7f060363;
        public static final int dp79 = 0x7f060364;
        public static final int dp790 = 0x7f060365;
        public static final int dp791 = 0x7f060366;
        public static final int dp792 = 0x7f060367;
        public static final int dp793 = 0x7f060368;
        public static final int dp794 = 0x7f060369;
        public static final int dp795 = 0x7f06036a;
        public static final int dp796 = 0x7f06036b;
        public static final int dp797 = 0x7f06036c;
        public static final int dp798 = 0x7f06036d;
        public static final int dp799 = 0x7f06036e;
        public static final int dp8 = 0x7f06036f;
        public static final int dp80 = 0x7f060370;
        public static final int dp800 = 0x7f060371;
        public static final int dp801 = 0x7f060372;
        public static final int dp802 = 0x7f060373;
        public static final int dp803 = 0x7f060374;
        public static final int dp804 = 0x7f060375;
        public static final int dp805 = 0x7f060376;
        public static final int dp806 = 0x7f060377;
        public static final int dp807 = 0x7f060378;
        public static final int dp808 = 0x7f060379;
        public static final int dp809 = 0x7f06037a;
        public static final int dp81 = 0x7f06037b;
        public static final int dp810 = 0x7f06037c;
        public static final int dp811 = 0x7f06037d;
        public static final int dp812 = 0x7f06037e;
        public static final int dp813 = 0x7f06037f;
        public static final int dp814 = 0x7f060380;
        public static final int dp815 = 0x7f060381;
        public static final int dp816 = 0x7f060382;
        public static final int dp817 = 0x7f060383;
        public static final int dp818 = 0x7f060384;
        public static final int dp819 = 0x7f060385;
        public static final int dp82 = 0x7f060386;
        public static final int dp820 = 0x7f060387;
        public static final int dp821 = 0x7f060388;
        public static final int dp822 = 0x7f060389;
        public static final int dp823 = 0x7f06038a;
        public static final int dp824 = 0x7f06038b;
        public static final int dp825 = 0x7f06038c;
        public static final int dp826 = 0x7f06038d;
        public static final int dp827 = 0x7f06038e;
        public static final int dp828 = 0x7f06038f;
        public static final int dp829 = 0x7f060390;
        public static final int dp83 = 0x7f060391;
        public static final int dp830 = 0x7f060392;
        public static final int dp831 = 0x7f060393;
        public static final int dp832 = 0x7f060394;
        public static final int dp833 = 0x7f060395;
        public static final int dp834 = 0x7f060396;
        public static final int dp835 = 0x7f060397;
        public static final int dp836 = 0x7f060398;
        public static final int dp837 = 0x7f060399;
        public static final int dp838 = 0x7f06039a;
        public static final int dp839 = 0x7f06039b;
        public static final int dp84 = 0x7f06039c;
        public static final int dp840 = 0x7f06039d;
        public static final int dp841 = 0x7f06039e;
        public static final int dp842 = 0x7f06039f;
        public static final int dp843 = 0x7f0603a0;
        public static final int dp844 = 0x7f0603a1;
        public static final int dp845 = 0x7f0603a2;
        public static final int dp846 = 0x7f0603a3;
        public static final int dp847 = 0x7f0603a4;
        public static final int dp848 = 0x7f0603a5;
        public static final int dp849 = 0x7f0603a6;
        public static final int dp85 = 0x7f0603a7;
        public static final int dp850 = 0x7f0603a8;
        public static final int dp851 = 0x7f0603a9;
        public static final int dp852 = 0x7f0603aa;
        public static final int dp853 = 0x7f0603ab;
        public static final int dp854 = 0x7f0603ac;
        public static final int dp855 = 0x7f0603ad;
        public static final int dp856 = 0x7f0603ae;
        public static final int dp857 = 0x7f0603af;
        public static final int dp858 = 0x7f0603b0;
        public static final int dp859 = 0x7f0603b1;
        public static final int dp86 = 0x7f0603b2;
        public static final int dp860 = 0x7f0603b3;
        public static final int dp861 = 0x7f0603b4;
        public static final int dp862 = 0x7f0603b5;
        public static final int dp863 = 0x7f0603b6;
        public static final int dp864 = 0x7f0603b7;
        public static final int dp865 = 0x7f0603b8;
        public static final int dp866 = 0x7f0603b9;
        public static final int dp867 = 0x7f0603ba;
        public static final int dp868 = 0x7f0603bb;
        public static final int dp869 = 0x7f0603bc;
        public static final int dp87 = 0x7f0603bd;
        public static final int dp870 = 0x7f0603be;
        public static final int dp871 = 0x7f0603bf;
        public static final int dp872 = 0x7f0603c0;
        public static final int dp873 = 0x7f0603c1;
        public static final int dp874 = 0x7f0603c2;
        public static final int dp875 = 0x7f0603c3;
        public static final int dp876 = 0x7f0603c4;
        public static final int dp877 = 0x7f0603c5;
        public static final int dp878 = 0x7f0603c6;
        public static final int dp879 = 0x7f0603c7;
        public static final int dp88 = 0x7f0603c8;
        public static final int dp880 = 0x7f0603c9;
        public static final int dp881 = 0x7f0603ca;
        public static final int dp882 = 0x7f0603cb;
        public static final int dp883 = 0x7f0603cc;
        public static final int dp884 = 0x7f0603cd;
        public static final int dp885 = 0x7f0603ce;
        public static final int dp886 = 0x7f0603cf;
        public static final int dp887 = 0x7f0603d0;
        public static final int dp888 = 0x7f0603d1;
        public static final int dp889 = 0x7f0603d2;
        public static final int dp89 = 0x7f0603d3;
        public static final int dp890 = 0x7f0603d4;
        public static final int dp891 = 0x7f0603d5;
        public static final int dp892 = 0x7f0603d6;
        public static final int dp893 = 0x7f0603d7;
        public static final int dp894 = 0x7f0603d8;
        public static final int dp895 = 0x7f0603d9;
        public static final int dp896 = 0x7f0603da;
        public static final int dp897 = 0x7f0603db;
        public static final int dp898 = 0x7f0603dc;
        public static final int dp899 = 0x7f0603dd;
        public static final int dp9 = 0x7f0603de;
        public static final int dp90 = 0x7f0603df;
        public static final int dp900 = 0x7f0603e0;
        public static final int dp901 = 0x7f0603e1;
        public static final int dp902 = 0x7f0603e2;
        public static final int dp903 = 0x7f0603e3;
        public static final int dp904 = 0x7f0603e4;
        public static final int dp905 = 0x7f0603e5;
        public static final int dp906 = 0x7f0603e6;
        public static final int dp907 = 0x7f0603e7;
        public static final int dp908 = 0x7f0603e8;
        public static final int dp909 = 0x7f0603e9;
        public static final int dp91 = 0x7f0603ea;
        public static final int dp910 = 0x7f0603eb;
        public static final int dp911 = 0x7f0603ec;
        public static final int dp912 = 0x7f0603ed;
        public static final int dp913 = 0x7f0603ee;
        public static final int dp914 = 0x7f0603ef;
        public static final int dp915 = 0x7f0603f0;
        public static final int dp916 = 0x7f0603f1;
        public static final int dp917 = 0x7f0603f2;
        public static final int dp918 = 0x7f0603f3;
        public static final int dp919 = 0x7f0603f4;
        public static final int dp92 = 0x7f0603f5;
        public static final int dp920 = 0x7f0603f6;
        public static final int dp921 = 0x7f0603f7;
        public static final int dp922 = 0x7f0603f8;
        public static final int dp923 = 0x7f0603f9;
        public static final int dp924 = 0x7f0603fa;
        public static final int dp925 = 0x7f0603fb;
        public static final int dp926 = 0x7f0603fc;
        public static final int dp927 = 0x7f0603fd;
        public static final int dp928 = 0x7f0603fe;
        public static final int dp929 = 0x7f0603ff;
        public static final int dp93 = 0x7f060400;
        public static final int dp930 = 0x7f060401;
        public static final int dp931 = 0x7f060402;
        public static final int dp932 = 0x7f060403;
        public static final int dp933 = 0x7f060404;
        public static final int dp934 = 0x7f060405;
        public static final int dp935 = 0x7f060406;
        public static final int dp936 = 0x7f060407;
        public static final int dp937 = 0x7f060408;
        public static final int dp938 = 0x7f060409;
        public static final int dp939 = 0x7f06040a;
        public static final int dp94 = 0x7f06040b;
        public static final int dp940 = 0x7f06040c;
        public static final int dp941 = 0x7f06040d;
        public static final int dp942 = 0x7f06040e;
        public static final int dp943 = 0x7f06040f;
        public static final int dp944 = 0x7f060410;
        public static final int dp945 = 0x7f060411;
        public static final int dp946 = 0x7f060412;
        public static final int dp947 = 0x7f060413;
        public static final int dp948 = 0x7f060414;
        public static final int dp949 = 0x7f060415;
        public static final int dp95 = 0x7f060416;
        public static final int dp950 = 0x7f060417;
        public static final int dp951 = 0x7f060418;
        public static final int dp952 = 0x7f060419;
        public static final int dp953 = 0x7f06041a;
        public static final int dp954 = 0x7f06041b;
        public static final int dp955 = 0x7f06041c;
        public static final int dp956 = 0x7f06041d;
        public static final int dp957 = 0x7f06041e;
        public static final int dp958 = 0x7f06041f;
        public static final int dp959 = 0x7f060420;
        public static final int dp96 = 0x7f060421;
        public static final int dp960 = 0x7f060422;
        public static final int dp961 = 0x7f060423;
        public static final int dp962 = 0x7f060424;
        public static final int dp963 = 0x7f060425;
        public static final int dp964 = 0x7f060426;
        public static final int dp965 = 0x7f060427;
        public static final int dp966 = 0x7f060428;
        public static final int dp967 = 0x7f060429;
        public static final int dp968 = 0x7f06042a;
        public static final int dp969 = 0x7f06042b;
        public static final int dp97 = 0x7f06042c;
        public static final int dp970 = 0x7f06042d;
        public static final int dp971 = 0x7f06042e;
        public static final int dp972 = 0x7f06042f;
        public static final int dp973 = 0x7f060430;
        public static final int dp974 = 0x7f060431;
        public static final int dp975 = 0x7f060432;
        public static final int dp976 = 0x7f060433;
        public static final int dp977 = 0x7f060434;
        public static final int dp978 = 0x7f060435;
        public static final int dp979 = 0x7f060436;
        public static final int dp98 = 0x7f060437;
        public static final int dp980 = 0x7f060438;
        public static final int dp981 = 0x7f060439;
        public static final int dp982 = 0x7f06043a;
        public static final int dp983 = 0x7f06043b;
        public static final int dp984 = 0x7f06043c;
        public static final int dp985 = 0x7f06043d;
        public static final int dp986 = 0x7f06043e;
        public static final int dp987 = 0x7f06043f;
        public static final int dp988 = 0x7f060440;
        public static final int dp989 = 0x7f060441;
        public static final int dp99 = 0x7f060442;
        public static final int dp990 = 0x7f060443;
        public static final int dp991 = 0x7f060444;
        public static final int dp992 = 0x7f060445;
        public static final int dp993 = 0x7f060446;
        public static final int dp994 = 0x7f060447;
        public static final int dp995 = 0x7f060448;
        public static final int dp996 = 0x7f060449;
        public static final int dp997 = 0x7f06044a;
        public static final int dp998 = 0x7f06044b;
        public static final int dp999 = 0x7f06044c;
        public static final int fastscroll_default_thickness = 0x7f06044d;
        public static final int fastscroll_margin = 0x7f06044e;
        public static final int fastscroll_minimum_range = 0x7f06044f;
        public static final int highlight_alpha_material_colored = 0x7f060450;
        public static final int highlight_alpha_material_dark = 0x7f060451;
        public static final int highlight_alpha_material_light = 0x7f060452;
        public static final int hint_alpha_material_dark = 0x7f060453;
        public static final int hint_alpha_material_light = 0x7f060454;
        public static final int hint_pressed_alpha_material_dark = 0x7f060455;
        public static final int hint_pressed_alpha_material_light = 0x7f060456;
        public static final int item_touch_helper_max_drag_scroll_per_frame = 0x7f060457;
        public static final int item_touch_helper_swipe_escape_max_velocity = 0x7f060458;
        public static final int item_touch_helper_swipe_escape_velocity = 0x7f060459;
        public static final int item_translation_z = 0x7f06045a;
        public static final int large_navigator_height = 0x7f06045b;
        public static final int navigator_margin_top = 0x7f06045c;
        public static final int navigator_margin_top_large = 0x7f06045d;
        public static final int navigator_width = 0x7f06045e;
        public static final int notification_action_icon_size = 0x7f06045f;
        public static final int notification_action_text_size = 0x7f060460;
        public static final int notification_big_circle_margin = 0x7f060461;
        public static final int notification_content_margin_start = 0x7f060462;
        public static final int notification_large_icon_height = 0x7f060463;
        public static final int notification_large_icon_width = 0x7f060464;
        public static final int notification_main_column_padding_top = 0x7f060465;
        public static final int notification_media_narrow_margin = 0x7f060466;
        public static final int notification_right_icon_size = 0x7f060467;
        public static final int notification_right_side_padding_top = 0x7f060468;
        public static final int notification_small_icon_background_padding = 0x7f060469;
        public static final int notification_small_icon_size_as_large = 0x7f06046a;
        public static final int notification_subtext_size = 0x7f06046b;
        public static final int notification_top_pad = 0x7f06046c;
        public static final int notification_top_pad_large_text = 0x7f06046d;
        public static final int small_navigator_height = 0x7f06046e;
        public static final int sp10 = 0x7f06046f;
        public static final int sp11 = 0x7f060470;
        public static final int sp12 = 0x7f060471;
        public static final int sp13 = 0x7f060472;
        public static final int sp14 = 0x7f060473;
        public static final int sp15 = 0x7f060474;
        public static final int sp16 = 0x7f060475;
        public static final int sp17 = 0x7f060476;
        public static final int sp18 = 0x7f060477;
        public static final int sp19 = 0x7f060478;
        public static final int sp20 = 0x7f060479;
        public static final int sp21 = 0x7f06047a;
        public static final int sp22 = 0x7f06047b;
        public static final int sp23 = 0x7f06047c;
        public static final int sp24 = 0x7f06047d;
        public static final int sp25 = 0x7f06047e;
        public static final int sp26 = 0x7f06047f;
        public static final int sp27 = 0x7f060480;
        public static final int sp28 = 0x7f060481;
        public static final int sp29 = 0x7f060482;
        public static final int sp30 = 0x7f060483;
        public static final int sp31 = 0x7f060484;
        public static final int sp32 = 0x7f060485;
        public static final int sp33 = 0x7f060486;
        public static final int sp34 = 0x7f060487;
        public static final int sp35 = 0x7f060488;
        public static final int sp36 = 0x7f060489;
        public static final int sp37 = 0x7f06048a;
        public static final int sp38 = 0x7f06048b;
        public static final int sp39 = 0x7f06048c;
        public static final int sp40 = 0x7f06048d;
        public static final int sp41 = 0x7f06048e;
        public static final int sp42 = 0x7f06048f;
        public static final int sp43 = 0x7f060490;
        public static final int sp44 = 0x7f060491;
        public static final int sp45 = 0x7f060492;
        public static final int sp46 = 0x7f060493;
        public static final int sp47 = 0x7f060494;
        public static final int sp48 = 0x7f060495;
        public static final int sp49 = 0x7f060496;
        public static final int sp50 = 0x7f060497;
        public static final int sp51 = 0x7f060498;
        public static final int sp52 = 0x7f060499;
        public static final int sp53 = 0x7f06049a;
        public static final int sp54 = 0x7f06049b;
        public static final int sp55 = 0x7f06049c;
        public static final int sp56 = 0x7f06049d;
        public static final int sp57 = 0x7f06049e;
        public static final int sp58 = 0x7f06049f;
        public static final int sp59 = 0x7f0604a0;
        public static final int sp60 = 0x7f0604a1;
        public static final int sp61 = 0x7f0604a2;
        public static final int sp62 = 0x7f0604a3;
        public static final int sp63 = 0x7f0604a4;
        public static final int sp64 = 0x7f0604a5;
        public static final int sp65 = 0x7f0604a6;
        public static final int sp66 = 0x7f0604a7;
        public static final int sp67 = 0x7f0604a8;
        public static final int sp68 = 0x7f0604a9;
        public static final int sp69 = 0x7f0604aa;
        public static final int sp70 = 0x7f0604ab;
        public static final int sp71 = 0x7f0604ac;
        public static final int sp72 = 0x7f0604ad;
        public static final int sp73 = 0x7f0604ae;
        public static final int sp74 = 0x7f0604af;
        public static final int sp75 = 0x7f0604b0;
        public static final int sp76 = 0x7f0604b1;
        public static final int sp77 = 0x7f0604b2;
        public static final int sp78 = 0x7f0604b3;
        public static final int sp79 = 0x7f0604b4;
        public static final int sp8 = 0x7f0604b5;
        public static final int sp80 = 0x7f0604b6;
        public static final int sp9 = 0x7f0604b7;
        public static final int spb_default_stroke_separator_length = 0x7f0604b8;
        public static final int spb_default_stroke_width = 0x7f0604b9;
        public static final int title_bar_translation_z = 0x7f0604ba;
        public static final int toast_translation_z = 0x7f0604bb;
        public static final int tooltip_corner_radius = 0x7f0604bc;
        public static final int tooltip_horizontal_padding = 0x7f0604bd;
        public static final int tooltip_margin = 0x7f0604be;
        public static final int tooltip_precise_anchor_extra_offset = 0x7f0604bf;
        public static final int tooltip_precise_anchor_threshold = 0x7f0604c0;
        public static final int tooltip_vertical_padding = 0x7f0604c1;
        public static final int tooltip_y_offset_non_touch = 0x7f0604c2;
        public static final int tooltip_y_offset_touch = 0x7f0604c3;
        public static final int window_backdrop_translation_z = 0x7f0604c4;
        public static final int window_content_translation_z = 0x7f0604c5;

        public dimen(
        /*  JADX ERROR: Method generation error
            jadx.core.utils.exceptions.JadxRuntimeException: Method arg registers not loaded: com.ici.connectivity.R.dimen.<init>():void, class status: PROCESS_COMPLETE
            	at jadx.core.dex.nodes.MethodNode.getArgRegs(MethodNode.java:251)
            	at jadx.core.codegen.MethodGen.addDefinition(MethodGen.java:154)
            	at jadx.core.codegen.ClassGen.addMethodCode(ClassGen.java:372)
            	at jadx.core.codegen.ClassGen.addMethod(ClassGen.java:306)
            	at jadx.core.codegen.ClassGen.lambda$addInnerClsAndMethods$2(ClassGen.java:272)
            	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
            	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
            	at java.base/java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:395)
            	at java.base/java.util.stream.Sink$ChainedReference.end(Sink.java:258)
            */
    }

    /* loaded from: classes.dex */
    public final class drawable {
        public static final int abc_ab_share_pack_mtrl_alpha = 0x7f070005;
        public static final int abc_action_bar_item_background_material = 0x7f070006;
        public static final int abc_btn_borderless_material = 0x7f070007;
        public static final int abc_btn_check_material = 0x7f070008;
        public static final int abc_btn_check_material_anim = 0x7f070009;
        public static final int abc_btn_check_to_on_mtrl_000 = 0x7f07000a;
        public static final int abc_btn_check_to_on_mtrl_015 = 0x7f07000b;
        public static final int abc_btn_colored_material = 0x7f07000c;
        public static final int abc_btn_default_mtrl_shape = 0x7f07000d;
        public static final int abc_btn_radio_material = 0x7f07000e;
        public static final int abc_btn_radio_material_anim = 0x7f07000f;
        public static final int abc_btn_radio_to_on_mtrl_000 = 0x7f070010;
        public static final int abc_btn_radio_to_on_mtrl_015 = 0x7f070011;
        public static final int abc_btn_switch_to_on_mtrl_00001 = 0x7f070012;
        public static final int abc_btn_switch_to_on_mtrl_00012 = 0x7f070013;
        public static final int abc_cab_background_internal_bg = 0x7f070014;
        public static final int abc_cab_background_top_material = 0x7f070015;
        public static final int abc_cab_background_top_mtrl_alpha = 0x7f070016;
        public static final int abc_control_background_material = 0x7f070017;
        public static final int abc_dialog_material_background = 0x7f070018;
        public static final int abc_edit_text_material = 0x7f070019;
        public static final int abc_ic_ab_back_material = 0x7f07001a;
        public static final int abc_ic_arrow_drop_right_black_24dp = 0x7f07001b;
        public static final int abc_ic_clear_material = 0x7f07001c;
        public static final int abc_ic_commit_search_api_mtrl_alpha = 0x7f07001d;
        public static final int abc_ic_go_search_api_material = 0x7f07001e;
        public static final int abc_ic_menu_copy_mtrl_am_alpha = 0x7f07001f;
        public static final int abc_ic_menu_cut_mtrl_alpha = 0x7f070020;
        public static final int abc_ic_menu_overflow_material = 0x7f070021;
        public static final int abc_ic_menu_paste_mtrl_am_alpha = 0x7f070022;
        public static final int abc_ic_menu_selectall_mtrl_alpha = 0x7f070023;
        public static final int abc_ic_menu_share_mtrl_alpha = 0x7f070024;
        public static final int abc_ic_search_api_material = 0x7f070025;
        public static final int abc_ic_star_black_16dp = 0x7f070026;
        public static final int abc_ic_star_black_36dp = 0x7f070027;
        public static final int abc_ic_star_black_48dp = 0x7f070028;
        public static final int abc_ic_star_half_black_16dp = 0x7f070029;
        public static final int abc_ic_star_half_black_36dp = 0x7f07002a;
        public static final int abc_ic_star_half_black_48dp = 0x7f07002b;
        public static final int abc_ic_voice_search_api_material = 0x7f07002c;
        public static final int abc_item_background_holo_dark = 0x7f07002d;
        public static final int abc_item_background_holo_light = 0x7f07002e;
        public static final int abc_list_divider_material = 0x7f07002f;
        public static final int abc_list_divider_mtrl_alpha = 0x7f070030;
        public static final int abc_list_focused_holo = 0x7f070031;
        public static final int abc_list_longpressed_holo = 0x7f070032;
        public static final int abc_list_pressed_holo_dark = 0x7f070033;
        public static final int abc_list_pressed_holo_light = 0x7f070034;
        public static final int abc_list_selector_background_transition_holo_dark = 0x7f070035;
        public static final int abc_list_selector_background_transition_holo_light = 0x7f070036;
        public static final int abc_list_selector_disabled_holo_dark = 0x7f070037;
        public static final int abc_list_selector_disabled_holo_light = 0x7f070038;
        public static final int abc_list_selector_holo_dark = 0x7f070039;
        public static final int abc_list_selector_holo_light = 0x7f07003a;
        public static final int abc_menu_hardkey_panel_mtrl_mult = 0x7f07003b;
        public static final int abc_popup_background_mtrl_mult = 0x7f07003c;
        public static final int abc_ratingbar_indicator_material = 0x7f07003d;
        public static final int abc_ratingbar_material = 0x7f07003e;
        public static final int abc_ratingbar_small_material = 0x7f07003f;
        public static final int abc_scrubber_control_off_mtrl_alpha = 0x7f070040;
        public static final int abc_scrubber_control_to_pressed_mtrl_000 = 0x7f070041;
        public static final int abc_scrubber_control_to_pressed_mtrl_005 = 0x7f070042;
        public static final int abc_scrubber_primary_mtrl_alpha = 0x7f070043;
        public static final int abc_scrubber_track_mtrl_alpha = 0x7f070044;
        public static final int abc_seekbar_thumb_material = 0x7f070045;
        public static final int abc_seekbar_tick_mark_material = 0x7f070046;
        public static final int abc_seekbar_track_material = 0x7f070047;
        public static final int abc_spinner_mtrl_am_alpha = 0x7f070048;
        public static final int abc_spinner_textfield_background_material = 0x7f070049;
        public static final int abc_switch_thumb_material = 0x7f07004a;
        public static final int abc_switch_track_mtrl_alpha = 0x7f07004b;
        public static final int abc_tab_indicator_material = 0x7f07004c;
        public static final int abc_tab_indicator_mtrl_alpha = 0x7f07004d;
        public static final int abc_text_cursor_material = 0x7f07004e;
        public static final int abc_text_select_handle_left_mtrl_dark = 0x7f07004f;
        public static final int abc_text_select_handle_left_mtrl_light = 0x7f070050;
        public static final int abc_text_select_handle_middle_mtrl_dark = 0x7f070051;
        public static final int abc_text_select_handle_middle_mtrl_light = 0x7f070052;
        public static final int abc_text_select_handle_right_mtrl_dark = 0x7f070053;
        public static final int abc_text_select_handle_right_mtrl_light = 0x7f070054;
        public static final int abc_textfield_activated_mtrl_alpha = 0x7f070055;
        public static final int abc_textfield_default_mtrl_alpha = 0x7f070056;
        public static final int abc_textfield_search_activated_mtrl_alpha = 0x7f070057;
        public static final int abc_textfield_search_default_mtrl_alpha = 0x7f070058;
        public static final int abc_textfield_search_material = 0x7f070059;
        public static final int abc_vector_test = 0x7f07005a;
        public static final int bg_bluetooth_detail_connect = 0x7f07005b;
        public static final int bg_bluetooth_detail_forget = 0x7f07005c;
        public static final int bg_bluetooth_detail_igro_btn = 0x7f07005d;
        public static final int bg_btsetting_pop = 0x7f07005e;
        public static final int bg_btsetting_toast = 0x7f07005f;
        public static final int bg_cancel_normal = 0x7f070060;
        public static final int bg_cancel_pressed = 0x7f070061;
        public static final int bg_confirm_normal = 0x7f070062;
        public static final int bg_confirm_pressed = 0x7f070063;
        public static final int bg_conflict_pop = 0x7f070064;
        public static final int bg_connect_fail_by_click_my_phone = 0x7f070065;
        public static final int bg_connect_fail_by_click_my_phone_buick = 0x7f070066;
        public static final int bg_cp_indicator_slid = 0x7f070067;
        public static final int bg_cp_list_indicator = 0x7f070068;
        public static final int bg_indicator_background = 0x7f070069;
        public static final int bg_indicator_slid = 0x7f07006a;
        public static final int bg_list_indicator = 0x7f07006b;
        public static final int bg_paring_progress = 0x7f07006c;
        public static final int bg_projection_connect_pop = 0x7f07006d;
        public static final int bg_tab_indicator = 0x7f07006e;
        public static final int bt_add_selector = 0x7f07006f;
        public static final int bt_add_selector_358 = 0x7f070070;
        public static final int bt_add_selector_buick = 0x7f070071;
        public static final int bt_cancel_selector = 0x7f070072;
        public static final int bt_confirm_selector = 0x7f070073;
        public static final int bt_detail_selector = 0x7f070074;
        public static final int bt_flat_button_selector = 0x7f070075;
        public static final int btn_checkbox_checked_mtrl = 0x7f070076;
        public static final int btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f070077;
        public static final int btn_checkbox_unchecked_mtrl = 0x7f070078;
        public static final int btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f070079;
        public static final int btn_radio_off_mtrl = 0x7f07007a;
        public static final int btn_radio_off_to_on_mtrl_animation = 0x7f07007b;
        public static final int btn_radio_on_mtrl = 0x7f07007c;
        public static final int btn_radio_on_to_off_mtrl_animation = 0x7f07007d;
        public static final int buick_cl_title_back_btn_selector = 0x7f07007e;
        public static final int carplay_add_bg = 0x7f07007f;
        public static final int carplay_add_bg_358 = 0x7f070080;
        public static final int carplay_list_progress = 0x7f070081;
        public static final int carplay_list_progress_bg = 0x7f070082;
        public static final int cl_ici28_connecting_bg = 0x7f070083;
        public static final int cl_ici_shape_connected_state_point = 0x7f070084;
        public static final int cl_ici_shape_disconnected_state_point = 0x7f070085;
        public static final int cl_prompt_close_text_btn = 0x7f070086;
        public static final int cl_prompt_primary_btn = 0x7f070087;
        public static final int cl_prompt_primary_btn_buick = 0x7f070088;
        public static final int cl_prompt_second_btn = 0x7f070089;
        public static final int cl_prompt_second_btn_buick = 0x7f07008a;
        public static final int cl_title_back_btn_selector = 0x7f07008b;
        public static final int close_btn_selector = 0x7f07008c;
        public static final int ic_launcher_background = 0x7f07008d;
        public static final int ic_launcher_foreground = 0x7f07008e;
        public static final int ici28_cluster_media_progress_indeterminate_drawable = 0x7f07008f;
        public static final int ici28_cluster_media_progress_indeterminate_drawable_buick = 0x7f070090;
        public static final int ici28_layerlist_carlife_connecting_progress = 0x7f070091;
        public static final int ici28_vector_cluster_media_progress_indeterminate = 0x7f070092;
        public static final int ici28_vector_cluster_media_progress_indeterminate_buick = 0x7f070093;
        public static final int ici_ghost_txt_btn = 0x7f070094;
        public static final int ici_real_blue_btn = 0x7f070095;
        public static final int ici_real_gray_txt_btn = 0x7f070096;
        public static final int ici_real_yellow_btn = 0x7f070097;
        public static final int icon_statusicon_connected = 0x7f070098;
        public static final int loading_progress_bar = 0x7f070099;
        public static final int notification_action_background = 0x7f07009a;
        public static final int notification_bg = 0x7f07009b;
        public static final int notification_bg_low = 0x7f07009c;
        public static final int notification_bg_low_normal = 0x7f07009d;
        public static final int notification_bg_low_pressed = 0x7f07009e;
        public static final int notification_bg_normal = 0x7f07009f;
        public static final int notification_bg_normal_pressed = 0x7f0700a0;
        public static final int notification_icon_background = 0x7f0700a1;
        public static final int notification_template_icon_bg = 0x7f0700a2;
        public static final int notification_template_icon_low_bg = 0x7f0700a3;
        public static final int notification_tile_bg = 0x7f0700a4;
        public static final int notify_panel_notification_icon_bg = 0x7f0700a5;
        public static final int progress_background = 0x7f0700a6;
        public static final int progress_bt_connecting = 0x7f0700a7;
        public static final int progress_bt_connecting_buick = 0x7f0700a8;
        public static final int progress_bt_connecting_dot_buick = 0x7f0700a9;
        public static final int progress_carplay_connecting = 0x7f0700aa;
        public static final int progress_circular_indeterminate = 0x7f0700ab;
        public static final int scroll_thumb = 0x7f0700ac;
        public static final int scrollbar_vertical_thumb = 0x7f0700ad;
        public static final int setting_switch_bg = 0x7f0700ae;
        public static final int switch_check_off_selector = 0x7f0700af;
        public static final int switch_check_on_selector = 0x7f0700b0;
        public static final int switch_check_status = 0x7f0700b1;
        public static final int tooltip_frame_dark = 0x7f0700b2;
        public static final int tooltip_frame_light = 0x7f0700b3;
        public static final int res_0x7f070000_ic_launcher_foreground__0 = 0x7f070000;
        public static final int res_0x7f070001_ici28_vector_cluster_media_progress_indeterminate__0 = 0x7f070001;
        public static final int res_0x7f070002_ici28_vector_cluster_media_progress_indeterminate__1 = 0x7f070002;
        public static final int res_0x7f070003_ici28_vector_cluster_media_progress_indeterminate_buick__0 = 0x7f070003;
        public static final int res_0x7f070004_ici28_vector_cluster_media_progress_indeterminate_buick__1 = 0x7f070004;

        public drawable(
        /*  JADX ERROR: Method generation error
            jadx.core.utils.exceptions.JadxRuntimeException: Method arg registers not loaded: com.ici.connectivity.R.drawable.<init>():void, class status: PROCESS_COMPLETE
            	at jadx.core.dex.nodes.MethodNode.getArgRegs(MethodNode.java:251)
            	at jadx.core.codegen.MethodGen.addDefinition(MethodGen.java:154)
            	at jadx.core.codegen.ClassGen.addMethodCode(ClassGen.java:372)
            	at jadx.core.codegen.ClassGen.addMethod(ClassGen.java:306)
            	at jadx.core.codegen.ClassGen.lambda$addInnerClsAndMethods$2(ClassGen.java:272)
            	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
            	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
            	at java.base/java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:395)
            	at java.base/java.util.stream.Sink$ChainedReference.end(Sink.java:258)
            */
    }

    /* loaded from: classes.dex */
    public final class fraction {
        public static final int letter_to_tile_ratio = 0x7f080000;

        public fraction(
        /*  JADX ERROR: Method generation error
            jadx.core.utils.exceptions.JadxRuntimeException: Method arg registers not loaded: com.ici.connectivity.R.fraction.<init>():void, class status: PROCESS_COMPLETE
            	at jadx.core.dex.nodes.MethodNode.getArgRegs(MethodNode.java:251)
            	at jadx.core.codegen.MethodGen.addDefinition(MethodGen.java:154)
            	at jadx.core.codegen.ClassGen.addMethodCode(ClassGen.java:372)
            	at jadx.core.codegen.ClassGen.addMethod(ClassGen.java:306)
            	at jadx.core.codegen.ClassGen.lambda$addInnerClsAndMethods$2(ClassGen.java:272)
            	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
            	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
            	at java.base/java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:395)
            	at java.base/java.util.stream.Sink$ChainedReference.end(Sink.java:258)
            */
    }

    /* loaded from: classes.dex */
    public final class id {
        public static final int ALT = 0x7f090000;
        public static final int CTRL = 0x7f090001;
        public static final int FUNCTION = 0x7f090002;
        public static final int META = 0x7f090003;
        public static final int SHIFT = 0x7f090004;
        public static final int SYM = 0x7f090005;
        public static final int accessibility_action_clickable_span = 0x7f090006;
        public static final int accessibility_custom_action_0 = 0x7f090007;
        public static final int accessibility_custom_action_1 = 0x7f090008;
        public static final int accessibility_custom_action_10 = 0x7f090009;
        public static final int accessibility_custom_action_11 = 0x7f09000a;
        public static final int accessibility_custom_action_12 = 0x7f09000b;
        public static final int accessibility_custom_action_13 = 0x7f09000c;
        public static final int accessibility_custom_action_14 = 0x7f09000d;
        public static final int accessibility_custom_action_15 = 0x7f09000e;
        public static final int accessibility_custom_action_16 = 0x7f09000f;
        public static final int accessibility_custom_action_17 = 0x7f090010;
        public static final int accessibility_custom_action_18 = 0x7f090011;
        public static final int accessibility_custom_action_19 = 0x7f090012;
        public static final int accessibility_custom_action_2 = 0x7f090013;
        public static final int accessibility_custom_action_20 = 0x7f090014;
        public static final int accessibility_custom_action_21 = 0x7f090015;
        public static final int accessibility_custom_action_22 = 0x7f090016;
        public static final int accessibility_custom_action_23 = 0x7f090017;
        public static final int accessibility_custom_action_24 = 0x7f090018;
        public static final int accessibility_custom_action_25 = 0x7f090019;
        public static final int accessibility_custom_action_26 = 0x7f09001a;
        public static final int accessibility_custom_action_27 = 0x7f09001b;
        public static final int accessibility_custom_action_28 = 0x7f09001c;
        public static final int accessibility_custom_action_29 = 0x7f09001d;
        public static final int accessibility_custom_action_3 = 0x7f09001e;
        public static final int accessibility_custom_action_30 = 0x7f09001f;
        public static final int accessibility_custom_action_31 = 0x7f090020;
        public static final int accessibility_custom_action_4 = 0x7f090021;
        public static final int accessibility_custom_action_5 = 0x7f090022;
        public static final int accessibility_custom_action_6 = 0x7f090023;
        public static final int accessibility_custom_action_7 = 0x7f090024;
        public static final int accessibility_custom_action_8 = 0x7f090025;
        public static final int accessibility_custom_action_9 = 0x7f090026;
        public static final int action_bar = 0x7f090027;
        public static final int action_bar_activity_content = 0x7f090028;
        public static final int action_bar_container = 0x7f090029;
        public static final int action_bar_root = 0x7f09002a;
        public static final int action_bar_spinner = 0x7f09002b;
        public static final int action_bar_subtitle = 0x7f09002c;
        public static final int action_bar_title = 0x7f09002d;
        public static final int action_container = 0x7f09002e;
        public static final int action_context_bar = 0x7f09002f;
        public static final int action_divider = 0x7f090030;
        public static final int action_image = 0x7f090031;
        public static final int action_menu_divider = 0x7f090032;
        public static final int action_menu_presenter = 0x7f090033;
        public static final int action_mode_bar = 0x7f090034;
        public static final int action_mode_bar_stub = 0x7f090035;
        public static final int action_mode_close_button = 0x7f090036;
        public static final int action_text = 0x7f090037;
        public static final int actions = 0x7f090038;
        public static final int activity_chooser_view_content = 0x7f090039;
        public static final int add = 0x7f09003a;
        public static final int alertTitle = 0x7f09003b;
        public static final int always = 0x7f09003c;
        public static final int assets = 0x7f09003d;
        public static final int async = 0x7f09003e;
        public static final int barrier = 0x7f09003f;
        public static final int beginning = 0x7f090040;
        public static final int blocking = 0x7f090041;
        public static final int bottom = 0x7f090042;
        public static final int bottom_container = 0x7f090043;
        public static final int bt_paring_pb = 0x7f090044;
        public static final int buttonPanel = 0x7f090045;
        public static final int cannot_connect_carplay = 0x7f090046;
        public static final int cannot_connect_carplay_tips = 0x7f090047;
        public static final int center_vertical = 0x7f090048;
        public static final int chains = 0x7f090049;
        public static final int charge_only_tv = 0x7f09004a;
        public static final int checkbox = 0x7f09004b;
        public static final int checked = 0x7f09004c;
        public static final int chronometer = 0x7f09004d;
        public static final int cl_id_check_message = 0x7f09004e;
        public static final int cl_id_connect_prompt_detail = 0x7f09004f;
        public static final int cl_id_connecting_close_btn = 0x7f090050;
        public static final int cl_id_connecting_page = 0x7f090051;
        public static final int cl_id_connecting_progress = 0x7f090052;
        public static final int cl_id_connecting_prompt_text = 0x7f090053;
        public static final int cl_id_connecting_prompt_title = 0x7f090054;
        public static final int cl_id_device_list_header = 0x7f090055;
        public static final int cl_id_fragment_container = 0x7f090056;
        public static final int cl_id_progress_carlife_logo = 0x7f090057;
        public static final int cl_id_title_back_btn = 0x7f090058;
        public static final int cl_id_title_text = 0x7f090059;
        public static final int collapseActionView = 0x7f09005a;
        public static final int connect_cancel = 0x7f09005b;
        public static final int connect_carlife_tv = 0x7f09005c;
        public static final int connect_carplay_tv = 0x7f09005d;
        public static final int connect_dev_btn = 0x7f09005e;
        public static final int connect_tips = 0x7f09005f;
        public static final int content = 0x7f090060;
        public static final int contentPanel = 0x7f090061;
        public static final int content_rl = 0x7f090062;
        public static final int cp_connecting_pb = 0x7f090063;
        public static final int cp_list_indicator = 0x7f090064;
        public static final int cp_recycle_view = 0x7f090065;
        public static final int custom = 0x7f090066;
        public static final int customPanel = 0x7f090067;
        public static final int decor_content_parent = 0x7f090068;
        public static final int default_activity_button = 0x7f090069;
        public static final int device_detail_setting_lv = 0x7f09006a;
        public static final int device_name_tv = 0x7f09006b;
        public static final int device_setting_result_tv = 0x7f09006c;
        public static final int device_setting_switch = 0x7f09006d;
        public static final int device_setting_title_tv = 0x7f09006e;
        public static final int dialog_button = 0x7f09006f;
        public static final int dimensions = 0x7f090070;
        public static final int direct = 0x7f090071;
        public static final int disableHome = 0x7f090072;
        public static final int edit_query = 0x7f090073;
        public static final int end = 0x7f090074;
        public static final int expand_activities_button = 0x7f090075;
        public static final int expanded_menu = 0x7f090076;
        public static final int file = 0x7f090077;
        public static final int fitCenter = 0x7f090078;
        public static final int fitCrop = 0x7f090079;
        public static final int fitHeight = 0x7f09007a;
        public static final int fitWidth = 0x7f09007b;
        public static final int fl_countdown = 0x7f09007c;
        public static final int fl_discover = 0x7f09007d;
        public static final int fl_paired = 0x7f09007e;
        public static final int forever = 0x7f09007f;
        public static final int forget_phone_pop_divider = 0x7f090080;
        public static final int global_light = 0x7f090081;
        public static final int global_regular = 0x7f090082;
        public static final int global_thin = 0x7f090083;
        public static final int gone = 0x7f090084;
        public static final int group_divider = 0x7f090085;
        public static final int groups = 0x7f090086;
        public static final int home = 0x7f090087;
        public static final int homeAsUp = 0x7f090088;
        public static final int icon = 0x7f090089;
        public static final int icon_group = 0x7f09008a;
        public static final int id_cancel_btn = 0x7f09008b;
        public static final int id_cannot_connect_image = 0x7f09008c;
        public static final int id_carlife_connect_prompt_view = 0x7f09008d;
        public static final int id_carlife_device_card_view = 0x7f09008e;
        public static final int id_carlife_devices_list = 0x7f09008f;
        public static final int id_carplay_device_card_view = 0x7f090090;
        public static final int id_cl_android_os = 0x7f090091;
        public static final int id_cl_android_os_line = 0x7f090092;
        public static final int id_cl_apple_os = 0x7f090093;
        public static final int id_cl_apple_os_line = 0x7f090094;
        public static final int id_cl_carlife_logo = 0x7f090095;
        public static final int id_cl_check_app_install = 0x7f090096;
        public static final int id_cl_check_app_install_line = 0x7f090097;
        public static final int id_cl_check_usb_cable = 0x7f090098;
        public static final int id_cl_check_usb_cable_detail = 0x7f090099;
        public static final int id_cl_check_usb_cable_line = 0x7f09009a;
        public static final int id_cl_help_detail = 0x7f09009b;
        public static final int id_cl_help_detail_img = 0x7f09009c;
        public static final int id_cl_help_detail_img_2 = 0x7f09009d;
        public static final int id_cl_help_detail_text = 0x7f09009e;
        public static final int id_cl_help_header = 0x7f09009f;
        public static final int id_cl_help_main = 0x7f0900a0;
        public static final int id_cl_help_menu = 0x7f0900a1;
        public static final int id_cl_help_select_os = 0x7f0900a2;
        public static final int id_cl_how_allow_auth = 0x7f0900a3;
        public static final int id_cl_how_allow_auth_line = 0x7f0900a4;
        public static final int id_cl_reconnect_btn = 0x7f0900a5;
        public static final int id_cl_try_restart_phone = 0x7f0900a6;
        public static final int id_close_btn = 0x7f0900a7;
        public static final int id_connect_prompt_detail = 0x7f0900a8;
        public static final int id_connect_prompt_img = 0x7f0900a9;
        public static final int id_connect_prompt_title = 0x7f0900aa;
        public static final int id_connecting_progress = 0x7f0900ab;
        public static final int id_connecting_prompt_img = 0x7f0900ac;
        public static final int id_connecting_prompt_title = 0x7f0900ad;
        public static final int id_cp_connect_state = 0x7f0900ae;
        public static final int id_cp_device_name = 0x7f0900af;
        public static final int id_cp_icon_state = 0x7f0900b0;
        public static final int id_device_connect_state = 0x7f0900b1;
        public static final int id_device_list_connect_state_point = 0x7f0900b2;
        public static final int id_device_name = 0x7f0900b3;
        public static final int id_help_container = 0x7f0900b4;
        public static final int id_help_title_back_btn = 0x7f0900b5;
        public static final int id_help_title_text = 0x7f0900b6;
        public static final int id_left_btn = 0x7f0900b7;
        public static final int id_phone_icon_state = 0x7f0900b8;
        public static final int id_progress_circular = 0x7f0900b9;
        public static final int id_retry_btn = 0x7f0900ba;
        public static final int id_right_btn = 0x7f0900bb;
        public static final int id_window_container = 0x7f0900bc;
        public static final int ifRoom = 0x7f0900bd;
        public static final int image = 0x7f0900be;
        public static final int indicator_container = 0x7f0900bf;
        public static final int info = 0x7f0900c0;
        public static final int invisible = 0x7f0900c1;
        public static final int italic = 0x7f0900c2;
        public static final int item_device_view = 0x7f0900c3;
        public static final int item_touch_helper_previous_elevation = 0x7f0900c4;
        public static final int iv_battery = 0x7f0900c5;
        public static final int iv_bt_add = 0x7f0900c6;
        public static final int iv_bt_back = 0x7f0900c7;
        public static final int iv_bt_icon_anim = 0x7f0900c8;
        public static final int iv_bt_phone = 0x7f0900c9;
        public static final int iv_bt_scan = 0x7f0900ca;
        public static final int iv_card_connect_state = 0x7f0900cb;
        public static final int iv_close = 0x7f0900cc;
        public static final int iv_countdown_close_pop = 0x7f0900cd;
        public static final int iv_device_detail_back = 0x7f0900ce;
        public static final int iv_discover_selected = 0x7f0900cf;
        public static final int iv_introduce_back = 0x7f0900d0;
        public static final int iv_introduce_close = 0x7f0900d1;
        public static final int iv_label_pop = 0x7f0900d2;
        public static final int iv_paired_selected = 0x7f0900d3;
        public static final int iv_pairing_close = 0x7f0900d4;
        public static final int iv_signal = 0x7f0900d5;
        public static final int iv_unpair_bt_phone = 0x7f0900d6;
        public static final int left = 0x7f0900d7;
        public static final int line = 0x7f0900d8;
        public static final int line1 = 0x7f0900d9;
        public static final int line3 = 0x7f0900da;
        public static final int listMode = 0x7f0900db;
        public static final int list_item = 0x7f0900dc;
        public static final int ll_fg_layout = 0x7f0900dd;
        public static final int ll_introduce_text = 0x7f0900de;
        public static final int lly_bt_paring_pb = 0x7f0900df;
        public static final int lly_connect_state = 0x7f0900e0;
        public static final int lly_detail_title = 0x7f0900e1;
        public static final int lly_no_device = 0x7f0900e2;
        public static final int message = 0x7f0900e3;
        public static final int middle = 0x7f0900e4;
        public static final int multiply = 0x7f0900e5;
        public static final int never = 0x7f0900e6;
        public static final int no_available_cp_tv1 = 0x7f0900e7;
        public static final int no_available_cp_tv2 = 0x7f0900e8;
        public static final int none = 0x7f0900e9;
        public static final int normal = 0x7f0900ea;
        public static final int notification_background = 0x7f0900eb;
        public static final int notification_main_column = 0x7f0900ec;
        public static final int notification_main_column_container = 0x7f0900ed;
        public static final int off = 0x7f0900ee;
        public static final int on = 0x7f0900ef;
        public static final int outside_view = 0x7f0900f0;
        public static final int packed = 0x7f0900f1;
        public static final int paired_list_indicator = 0x7f0900f2;
        public static final int paired_recycle_view = 0x7f0900f3;
        public static final int parent = 0x7f0900f4;
        public static final int parentPanel = 0x7f0900f5;
        public static final int pb_connecting = 0x7f0900f6;
        public static final int pb_scan = 0x7f0900f7;
        public static final int pb_sync_contact = 0x7f0900f8;
        public static final int percent = 0x7f0900f9;
        public static final int phone_icon = 0x7f0900fa;
        public static final int progress_circular = 0x7f0900fb;
        public static final int progress_horizontal = 0x7f0900fc;
        public static final int radio = 0x7f0900fd;
        public static final int recycler_view_lv = 0x7f0900fe;
        public static final int right = 0x7f0900ff;
        public static final int right_icon = 0x7f090100;
        public static final int right_side = 0x7f090101;
        public static final int rl_introduce = 0x7f090102;
        public static final int rly_bs_title = 0x7f090103;
        public static final int rly_connect_layout = 0x7f090104;
        public static final int rly_phone_info = 0x7f090105;
        public static final int root = 0x7f090106;
        public static final int root_view = 0x7f090107;
        public static final int screen = 0x7f090108;
        public static final int scrollIndicatorDown = 0x7f090109;
        public static final int scrollIndicatorUp = 0x7f09010a;
        public static final int scrollView = 0x7f09010b;
        public static final int scroll_view = 0x7f09010c;
        public static final int search_badge = 0x7f09010d;
        public static final int search_bar = 0x7f09010e;
        public static final int search_button = 0x7f09010f;
        public static final int search_close_btn = 0x7f090110;
        public static final int search_edit_frame = 0x7f090111;
        public static final int search_go_btn = 0x7f090112;
        public static final int search_mag_icon = 0x7f090113;
        public static final int search_plate = 0x7f090114;
        public static final int search_src_text = 0x7f090115;
        public static final int search_voice_btn = 0x7f090116;
        public static final int select_dialog_listview = 0x7f090117;
        public static final int shortcut = 0x7f090118;
        public static final int showCustom = 0x7f090119;
        public static final int showHome = 0x7f09011a;
        public static final int showTitle = 0x7f09011b;
        public static final int spacer = 0x7f09011c;
        public static final int spb_interpolator_accelerate = 0x7f09011d;
        public static final int spb_interpolator_acceleratedecelerate = 0x7f09011e;
        public static final int spb_interpolator_decelerate = 0x7f09011f;
        public static final int spb_interpolator_linear = 0x7f090120;
        public static final int split_action_bar = 0x7f090121;
        public static final int spread = 0x7f090122;
        public static final int spread_inside = 0x7f090123;
        public static final int src_atop = 0x7f090124;
        public static final int src_in = 0x7f090125;
        public static final int src_over = 0x7f090126;
        public static final int standard = 0x7f090127;
        public static final int start = 0x7f090128;
        public static final int submenuarrow = 0x7f090129;
        public static final int submit_area = 0x7f09012a;
        public static final int switch_phone_hor_divider = 0x7f09012b;
        public static final int switch_phone_pop_divider = 0x7f09012c;
        public static final int tabMode = 0x7f09012d;
        public static final int tab_discover = 0x7f09012e;
        public static final int tab_paired = 0x7f09012f;
        public static final int tag_accessibility_actions = 0x7f090130;
        public static final int tag_accessibility_clickable_spans = 0x7f090131;
        public static final int tag_accessibility_heading = 0x7f090132;
        public static final int tag_accessibility_pane_title = 0x7f090133;
        public static final int tag_screen_reader_focusable = 0x7f090134;
        public static final int tag_transition_group = 0x7f090135;
        public static final int tag_unhandled_key_event_manager = 0x7f090136;
        public static final int tag_unhandled_key_listeners = 0x7f090137;
        public static final int text = 0x7f090138;
        public static final int text2 = 0x7f090139;
        public static final int textSpacerNoButtons = 0x7f09013a;
        public static final int textSpacerNoTitle = 0x7f09013b;
        public static final int time = 0x7f09013c;
        public static final int title = 0x7f09013d;
        public static final int titleDividerNoCustom = 0x7f09013e;
        public static final int title_container = 0x7f09013f;
        public static final int title_template = 0x7f090140;
        public static final int top = 0x7f090141;
        public static final int topPanel = 0x7f090142;
        public static final int tv_cancel = 0x7f090143;
        public static final int tv_cancel_pair = 0x7f090144;
        public static final int tv_card_connect_state = 0x7f090145;
        public static final int tv_card_first_connect = 0x7f090146;
        public static final int tv_check_detail = 0x7f090147;
        public static final int tv_common_cancel = 0x7f090148;
        public static final int tv_common_retry = 0x7f090149;
        public static final int tv_confirm_pair = 0x7f09014a;
        public static final int tv_connecting = 0x7f09014b;
        public static final int tv_countdown_cancel_pair_pop = 0x7f09014c;
        public static final int tv_countdown_notify1_pop = 0x7f09014d;
        public static final int tv_countdown_notify2_pop = 0x7f09014e;
        public static final int tv_countdown_notify3_pop = 0x7f09014f;
        public static final int tv_countdown_notify4_pop = 0x7f090150;
        public static final int tv_countdown_title_pop = 0x7f090151;
        public static final int tv_delete_device_name = 0x7f090152;
        public static final int tv_device_detail_disconnect = 0x7f090153;
        public static final int tv_device_detail_name = 0x7f090154;
        public static final int tv_device_name = 0x7f090155;
        public static final int tv_disconnect_phone_cancel = 0x7f090156;
        public static final int tv_disconnect_phone_continue = 0x7f090157;
        public static final int tv_disconnect_phone_info = 0x7f090158;
        public static final int tv_disconnect_phone_label = 0x7f090159;
        public static final int tv_forget_phone_cancel = 0x7f09015a;
        public static final int tv_forget_phone_confirm = 0x7f09015b;
        public static final int tv_forget_title = 0x7f09015c;
        public static final int tv_ignore_device = 0x7f09015d;
        public static final int tv_introduce = 0x7f09015e;
        public static final int tv_introduce_foot = 0x7f09015f;
        public static final int tv_item_paired_name = 0x7f090160;
        public static final int tv_low_battery = 0x7f090161;
        public static final int tv_open = 0x7f090162;
        public static final int tv_pairing_key = 0x7f090163;
        public static final int tv_pairing_notify = 0x7f090164;
        public static final int tv_pairing_title = 0x7f090165;
        public static final int tv_phone_profile = 0x7f090166;
        public static final int tv_seconds_pop = 0x7f090167;
        public static final int tv_security_cancel = 0x7f090168;
        public static final int tv_security_continue = 0x7f090169;
        public static final int tv_security_label = 0x7f09016a;
        public static final int tv_switch_phone_cancel = 0x7f09016b;
        public static final int tv_switch_phone_continue = 0x7f09016c;
        public static final int tv_switch_phone_info = 0x7f09016d;
        public static final int tv_switch_phone_label = 0x7f09016e;
        public static final int tv_timeout_cancel = 0x7f09016f;
        public static final int tv_timeout_device_name = 0x7f090170;
        public static final int tv_timeout_retry = 0x7f090171;
        public static final int tv_tips = 0x7f090172;
        public static final int tv_toast_info = 0x7f090173;
        public static final int tv_unpair_card_pair = 0x7f090174;
        public static final int tv_unpaired_device_name = 0x7f090175;
        public static final int unchecked = 0x7f090176;
        public static final int uniform = 0x7f090177;
        public static final int unpair_device_card = 0x7f090178;
        public static final int unpair_list_indicator = 0x7f090179;
        public static final int up = 0x7f09017a;
        public static final int useLogo = 0x7f09017b;
        public static final int v_line = 0x7f09017c;
        public static final int view = 0x7f09017d;
        public static final int view_tag_key_window_type = 0x7f09017e;
        public static final int withText = 0x7f09017f;
        public static final int wrap = 0x7f090180;
        public static final int wrap_content = 0x7f090181;

        public id(
        /*  JADX ERROR: Method generation error
            jadx.core.utils.exceptions.JadxRuntimeException: Method arg registers not loaded: com.ici.connectivity.R.id.<init>():void, class status: PROCESS_COMPLETE
            	at jadx.core.dex.nodes.MethodNode.getArgRegs(MethodNode.java:251)
            	at jadx.core.codegen.MethodGen.addDefinition(MethodGen.java:154)
            	at jadx.core.codegen.ClassGen.addMethodCode(ClassGen.java:372)
            	at jadx.core.codegen.ClassGen.addMethod(ClassGen.java:306)
            	at jadx.core.codegen.ClassGen.lambda$addInnerClsAndMethods$2(ClassGen.java:272)
            	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
            	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
            	at java.base/java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:395)
            	at java.base/java.util.stream.Sink$ChainedReference.end(Sink.java:258)
            */
    }

    /* loaded from: classes.dex */
    public final class integer {
        public static final int abc_config_activityDefaultDur = 0x7f0a0000;
        public static final int abc_config_activityShortDur = 0x7f0a0001;
        public static final int cancel_button_image_alpha = 0x7f0a0002;
        public static final int config_tooltipAnimTime = 0x7f0a0003;
        public static final int spb_default_interpolator = 0x7f0a0004;
        public static final int spb_default_sections_count = 0x7f0a0005;
        public static final int status_bar_notification_info_maxnum = 0x7f0a0006;

        public integer(
        /*  JADX ERROR: Method generation error
            jadx.core.utils.exceptions.JadxRuntimeException: Method arg registers not loaded: com.ici.connectivity.R.integer.<init>():void, class status: PROCESS_COMPLETE
            	at jadx.core.dex.nodes.MethodNode.getArgRegs(MethodNode.java:251)
            	at jadx.core.codegen.MethodGen.addDefinition(MethodGen.java:154)
            	at jadx.core.codegen.ClassGen.addMethodCode(ClassGen.java:372)
            	at jadx.core.codegen.ClassGen.addMethod(ClassGen.java:306)
            	at jadx.core.codegen.ClassGen.lambda$addInnerClsAndMethods$2(ClassGen.java:272)
            	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
            	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
            	at java.base/java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:395)
            	at java.base/java.util.stream.Sink$ChainedReference.end(Sink.java:258)
            */
    }

    /* loaded from: classes.dex */
    public final class interpolator {
        public static final int btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0b0000;
        public static final int btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0b0001;
        public static final int btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0b0002;
        public static final int btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0b0003;
        public static final int btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0b0004;
        public static final int btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0b0005;
        public static final int fast_out_slow_in = 0x7f0b0006;

        public interpolator(
        /*  JADX ERROR: Method generation error
            jadx.core.utils.exceptions.JadxRuntimeException: Method arg registers not loaded: com.ici.connectivity.R.interpolator.<init>():void, class status: PROCESS_COMPLETE
            	at jadx.core.dex.nodes.MethodNode.getArgRegs(MethodNode.java:251)
            	at jadx.core.codegen.MethodGen.addDefinition(MethodGen.java:154)
            	at jadx.core.codegen.ClassGen.addMethodCode(ClassGen.java:372)
            	at jadx.core.codegen.ClassGen.addMethod(ClassGen.java:306)
            	at jadx.core.codegen.ClassGen.lambda$addInnerClsAndMethods$2(ClassGen.java:272)
            	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
            	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
            	at java.base/java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:395)
            	at java.base/java.util.stream.Sink$ChainedReference.end(Sink.java:258)
            */
    }

    /* loaded from: classes.dex */
    public final class layout {
        public static final int abc_action_bar_title_item = 0x7f0c0000;
        public static final int abc_action_bar_up_container = 0x7f0c0001;
        public static final int abc_action_menu_item_layout = 0x7f0c0002;
        public static final int abc_action_menu_layout = 0x7f0c0003;
        public static final int abc_action_mode_bar = 0x7f0c0004;
        public static final int abc_action_mode_close_item_material = 0x7f0c0005;
        public static final int abc_activity_chooser_view = 0x7f0c0006;
        public static final int abc_activity_chooser_view_list_item = 0x7f0c0007;
        public static final int abc_alert_dialog_button_bar_material = 0x7f0c0008;
        public static final int abc_alert_dialog_material = 0x7f0c0009;
        public static final int abc_alert_dialog_title_material = 0x7f0c000a;
        public static final int abc_cascading_menu_item_layout = 0x7f0c000b;
        public static final int abc_dialog_title_material = 0x7f0c000c;
        public static final int abc_expanded_menu_layout = 0x7f0c000d;
        public static final int abc_list_menu_item_checkbox = 0x7f0c000e;
        public static final int abc_list_menu_item_icon = 0x7f0c000f;
        public static final int abc_list_menu_item_layout = 0x7f0c0010;
        public static final int abc_list_menu_item_radio = 0x7f0c0011;
        public static final int abc_popup_menu_header_item_layout = 0x7f0c0012;
        public static final int abc_popup_menu_item_layout = 0x7f0c0013;
        public static final int abc_screen_content_include = 0x7f0c0014;
        public static final int abc_screen_simple = 0x7f0c0015;
        public static final int abc_screen_simple_overlay_action_mode = 0x7f0c0016;
        public static final int abc_screen_toolbar = 0x7f0c0017;
        public static final int abc_search_dropdown_item_icons_2line = 0x7f0c0018;
        public static final int abc_search_view = 0x7f0c0019;
        public static final int abc_select_dialog_material = 0x7f0c001a;
        public static final int abc_tooltip = 0x7f0c001b;
        public static final int activity_main = 0x7f0c001c;
        public static final int buick_cl_connecting_page = 0x7f0c001d;
        public static final int buick_cl_device_list_fragment = 0x7f0c001e;
        public static final int buick_cl_ici_pompt_view_not_found_device = 0x7f0c001f;
        public static final int carlife_activity = 0x7f0c0020;
        public static final int cl_check_message = 0x7f0c0021;
        public static final int cl_connecting_page = 0x7f0c0022;
        public static final int cl_device_list_fragment = 0x7f0c0023;
        public static final int cl_device_list_fragment_item = 0x7f0c0024;
        public static final int cl_fragment_header = 0x7f0c0025;
        public static final int cl_help_container = 0x7f0c0026;
        public static final int cl_help_detail_page = 0x7f0c0027;
        public static final int cl_help_header = 0x7f0c0028;
        public static final int cl_help_main_page = 0x7f0c0029;
        public static final int cl_help_menu_page = 0x7f0c002a;
        public static final int cl_ici_pompt_enable_carlife = 0x7f0c002b;
        public static final int cl_ici_pompt_view_below_android_5 = 0x7f0c002c;
        public static final int cl_ici_pompt_view_carlife_unavailable = 0x7f0c002d;
        public static final int cl_ici_pompt_view_not_found_device = 0x7f0c002e;
        public static final int cl_ici_pompt_view_not_install_app = 0x7f0c002f;
        public static final int cl_ici_pompt_view_start_up = 0x7f0c0030;
        public static final int cl_ici_pompt_view_unkown_reason = 0x7f0c0031;
        public static final int cl_ici_pompt_view_user_agreement = 0x7f0c0032;
        public static final int custom_dialog = 0x7f0c0033;
        public static final int device_manager_activity = 0x7f0c0034;
        public static final int device_manager_activity_358 = 0x7f0c0035;
        public static final int device_manager_activity_buick = 0x7f0c0036;
        public static final int fragment_carplay_device_list = 0x7f0c0037;
        public static final int fragment_carplay_device_list_358 = 0x7f0c0038;
        public static final int fragment_carplay_device_list_buick = 0x7f0c0039;
        public static final int fragment_device_detail = 0x7f0c003a;
        public static final int fragment_device_detail_buick = 0x7f0c003b;
        public static final int fragment_discovery_list = 0x7f0c003c;
        public static final int fragment_discovery_list_buick = 0x7f0c003d;
        public static final int fragment_paired_list = 0x7f0c003e;
        public static final int fragment_paired_list_buick = 0x7f0c003f;
        public static final int include_introduce = 0x7f0c0040;
        public static final int item_carplay_device_info = 0x7f0c0041;
        public static final int item_carplay_device_info_358 = 0x7f0c0042;
        public static final int item_carplay_device_info_buick = 0x7f0c0043;
        public static final int item_device_refresh_contact = 0x7f0c0044;
        public static final int item_device_refresh_contact_buick = 0x7f0c0045;
        public static final int item_device_setting = 0x7f0c0046;
        public static final int item_paired_device_card = 0x7f0c0047;
        public static final int item_paired_device_card_358 = 0x7f0c0048;
        public static final int item_paired_device_card_buick = 0x7f0c0049;
        public static final int item_unpair_device_card = 0x7f0c004a;
        public static final int item_unpair_device_card_buick = 0x7f0c004b;
        public static final int layout_bt_pairing = 0x7f0c004c;
        public static final int layout_bt_pairing_buick = 0x7f0c004d;
        public static final int layout_cannot_connected = 0x7f0c004e;
        public static final int layout_cannot_connected_buick = 0x7f0c004f;
        public static final int layout_common_fail = 0x7f0c0050;
        public static final int layout_common_fail_buick = 0x7f0c0051;
        public static final int layout_common_fail_by_click_my_phone = 0x7f0c0052;
        public static final int layout_common_fail_by_click_my_phone_buick = 0x7f0c0053;
        public static final int layout_common_forget_phone_buick = 0x7f0c0054;
        public static final int layout_common_toast = 0x7f0c0055;
        public static final int layout_conflict_carplay = 0x7f0c0056;
        public static final int layout_connecting_carplay = 0x7f0c0057;
        public static final int layout_connecting_carplay_buick = 0x7f0c0058;
        public static final int layout_connecting_state = 0x7f0c0059;
        public static final int layout_countdown = 0x7f0c005a;
        public static final int layout_countdown_buick = 0x7f0c005b;
        public static final int layout_disconnect_phone = 0x7f0c005c;
        public static final int layout_disconnect_phone_buick = 0x7f0c005d;
        public static final int layout_forget_phone = 0x7f0c005e;
        public static final int layout_forget_phone_buick = 0x7f0c005f;
        public static final int layout_low_battery = 0x7f0c0060;
        public static final int layout_no_available_carplay = 0x7f0c0061;
        public static final int layout_no_available_carplay_buick = 0x7f0c0062;
        public static final int layout_pairing_timeout = 0x7f0c0063;
        public static final int layout_pairing_timeout_buick = 0x7f0c0064;
        public static final int layout_projection_connect = 0x7f0c0065;
        public static final int layout_security_notify = 0x7f0c0066;
        public static final int layout_security_notify_buick = 0x7f0c0067;
        public static final int layout_security_restrictions = 0x7f0c0068;
        public static final int layout_security_restrictions_buick = 0x7f0c0069;
        public static final int layout_switch_phone = 0x7f0c006a;
        public static final int layout_switch_phone_buick = 0x7f0c006b;
        public static final int layout_user_terms = 0x7f0c006c;
        public static final int layout_user_terms_buick = 0x7f0c006d;
        public static final int layout_window_container = 0x7f0c006e;
        public static final int notification_action = 0x7f0c006f;
        public static final int notification_action_tombstone = 0x7f0c0070;
        public static final int notification_template_custom_big = 0x7f0c0071;
        public static final int notification_template_icon_group = 0x7f0c0072;
        public static final int notification_template_part_chronometer = 0x7f0c0073;
        public static final int notification_template_part_time = 0x7f0c0074;
        public static final int pager_navigator_layout = 0x7f0c0075;
        public static final int pager_navigator_layout_no_scroll = 0x7f0c0076;
        public static final int select_dialog_item_material = 0x7f0c0077;
        public static final int select_dialog_multichoice_material = 0x7f0c0078;
        public static final int select_dialog_singlechoice_material = 0x7f0c0079;
        public static final int support_simple_spinner_dropdown_item = 0x7f0c007a;

        public layout(
        /*  JADX ERROR: Method generation error
            jadx.core.utils.exceptions.JadxRuntimeException: Method arg registers not loaded: com.ici.connectivity.R.layout.<init>():void, class status: PROCESS_COMPLETE
            	at jadx.core.dex.nodes.MethodNode.getArgRegs(MethodNode.java:251)
            	at jadx.core.codegen.MethodGen.addDefinition(MethodGen.java:154)
            	at jadx.core.codegen.ClassGen.addMethodCode(ClassGen.java:372)
            	at jadx.core.codegen.ClassGen.addMethod(ClassGen.java:306)
            	at jadx.core.codegen.ClassGen.lambda$addInnerClsAndMethods$2(ClassGen.java:272)
            	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
            	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
            	at java.base/java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:395)
            	at java.base/java.util.stream.Sink$ChainedReference.end(Sink.java:258)
            */
    }

    /* loaded from: classes.dex */
    public final class mipmap {
        public static final int battery_1 = 0x7f0d0000;
        public static final int battery_2 = 0x7f0d0001;
        public static final int battery_3 = 0x7f0d0002;
        public static final int bg_app = 0x7f0d0003;
        public static final int bg_app_358 = 0x7f0d0004;
        public static final int bg_app_buick = 0x7f0d0005;
        public static final int bg_app_full_screen_dialog_buick = 0x7f0d0006;
        public static final int bg_card_breakoff = 0x7f0d0007;
        public static final int bg_card_connected = 0x7f0d0008;
        public static final int bg_card_connecting = 0x7f0d0009;
        public static final int bg_card_unconnected = 0x7f0d000a;
        public static final int bg_title = 0x7f0d000b;
        public static final int carlife_connecting_logo = 0x7f0d000c;
        public static final int cl_carlife_logo = 0x7f0d000d;
        public static final int ic_back = 0x7f0d000e;
        public static final int ic_launcher = 0x7f0d000f;
        public static final int ic_launcher_cl = 0x7f0d0010;
        public static final int ic_launcher_cp = 0x7f0d0011;
        public static final int ic_launcher_round = 0x7f0d0012;
        public static final int ici28_actionbar_back = 0x7f0d0013;
        public static final int ici28_basic_module_indicator = 0x7f0d0014;
        public static final int ici28_carlife_card_connected_icon = 0x7f0d0015;
        public static final int ici28_carlife_card_unconnected_icon = 0x7f0d0016;
        public static final int ici28_close_normal = 0x7f0d0017;
        public static final int ici28_close_pressed = 0x7f0d0018;
        public static final int ici28_cluster_media_progress_bg = 0x7f0d0019;
        public static final int ici28_cluster_media_progress_shap = 0x7f0d001a;
        public static final int ici28_list_line = 0x7f0d001b;
        public static final int ici28_list_see_more = 0x7f0d001c;
        public static final int ici28_projection_card_add_icon = 0x7f0d001d;
        public static final int ici28_projection_card_bg_add = 0x7f0d001e;
        public static final int ici28_projection_card_bg_unconnected = 0x7f0d001f;
        public static final int ici28_projection_card_connected = 0x7f0d0020;
        public static final int ici28_projection_carlife_phonetips = 0x7f0d0021;
        public static final int ici28_projection_carlife_placeholder = 0x7f0d0022;
        public static final int ici28_projection_malfunction = 0x7f0d0023;
        public static final int ici28_projection_phone = 0x7f0d0024;
        public static final int ici28_projection_phonetips = 0x7f0d0025;
        public static final int ici28_projection_qr = 0x7f0d0026;
        public static final int ici28_real_txt_btn_disable = 0x7f0d0027;
        public static final int ici28_real_txt_btn_normal = 0x7f0d0028;
        public static final int ici28_real_txt_btn_pressed = 0x7f0d0029;
        public static final int ici28_real_txt_btn_selected = 0x7f0d002a;
        public static final int ici28_wlansetting_img_dot_yellow = 0x7f0d002b;
        public static final int ici28b_button_ghostbutton_disable = 0x7f0d002c;
        public static final int ici28b_button_ghostbutton_normal = 0x7f0d002d;
        public static final int ici28b_button_ghostbutton_pressed = 0x7f0d002e;
        public static final int ici28b_button_realbutton_blue_disable = 0x7f0d002f;
        public static final int ici28b_button_realbutton_blue_normal = 0x7f0d0030;
        public static final int ici28b_button_realbutton_blue_pressed = 0x7f0d0031;
        public static final int ici28b_card_connected_normal_buick = 0x7f0d0032;
        public static final int ici28b_card_unconnected_normal_358 = 0x7f0d0033;
        public static final int ici28b_card_unconnected_normal_buick = 0x7f0d0034;
        public static final int ici28b_connection_ic_btmobile_connected_buick = 0x7f0d0035;
        public static final int ici28b_connection_ic_carplay_connected_buick = 0x7f0d0036;
        public static final int ici28b_connection_ic_carplay_unconnected_buick = 0x7f0d0037;
        public static final int ici28b_connection_ic_mobile_unconnected_buick = 0x7f0d0038;
        public static final int ici28b_connection_img_connectphone = 0x7f0d0039;
        public static final int ici28b_connections_card_phone_selected_buick = 0x7f0d003a;
        public static final int ici28b_connections_img_bluetoothconnectivity_fullscreen = 0x7f0d003b;
        public static final int ici28b_ic_60_refresh_normal = 0x7f0d003c;
        public static final int ici28b_img_warning_fullscreen = 0x7f0d003d;
        public static final int ici28b_progress_circular_indeterminate_medium = 0x7f0d003e;
        public static final int ici28b_progress_circular_indeterminate_small = 0x7f0d003f;
        public static final int ici28b_projection_img_applecarplayconnecting_fullscreen = 0x7f0d0040;
        public static final int ici28b_tabs_light = 0x7f0d0041;
        public static final int ici28c_button_ghostbutton_disable = 0x7f0d0042;
        public static final int ici28c_button_ghostbutton_normal = 0x7f0d0043;
        public static final int ici28c_button_ghostbutton_pressed = 0x7f0d0044;
        public static final int ici28c_button_ghostbutton_selected = 0x7f0d0045;
        public static final int ici28c_button_realbutton_blue_disable = 0x7f0d0046;
        public static final int ici28c_button_realbutton_blue_normal = 0x7f0d0047;
        public static final int ici28c_button_realbutton_blue_pressed = 0x7f0d0048;
        public static final int ici28c_button_realbutton_blue_selected = 0x7f0d0049;
        public static final int ici28c_button_realbutton_yellow_disable = 0x7f0d004a;
        public static final int ici28c_button_realbutton_yellow_normal = 0x7f0d004b;
        public static final int ici28c_button_realbutton_yellow_pressed = 0x7f0d004c;
        public static final int ici28c_button_realbutton_yellow_selected = 0x7f0d004d;
        public static final int ici28c_connection_ic_mobile_unconnected = 0x7f0d004e;
        public static final int ici28c_connections_img_bluetoothconnectivity_fullscreen = 0x7f0d004f;
        public static final int ici28c_navibar_bg = 0x7f0d0050;
        public static final int ici28c_navibar_line = 0x7f0d0051;
        public static final int ici28c_progress_circular_indeterminate_medium = 0x7f0d0052;
        public static final int ici28c_progress_circular_indeterminate_small = 0x7f0d0053;
        public static final int ici28c_projection_card_add_disable = 0x7f0d0054;
        public static final int ici28c_projection_card_add_normal = 0x7f0d0055;
        public static final int ici28c_projection_card_add_pressed = 0x7f0d0056;
        public static final int ici28c_projection_carlife_qr = 0x7f0d0057;
        public static final int ici28c_projection_carlifeqr = 0x7f0d0058;
        public static final int ici28c_projection_ic_carlife_connected = 0x7f0d0059;
        public static final int ici28c_projection_ic_carlife_unconnected = 0x7f0d005a;
        public static final int ici28c_projection_ic_carplay_connected = 0x7f0d005b;
        public static final int ici28c_projection_ic_carplay_unconnected = 0x7f0d005c;
        public static final int ici28c_projection_img_applecarlifeconnecting = 0x7f0d005d;
        public static final int ici28c_projection_img_applecarlifeconnecting_fullscreen = 0x7f0d005e;
        public static final int ici28c_projection_img_applecarplayconnecting = 0x7f0d005f;
        public static final int ici28c_projection_img_applecarplayconnecting_placeholder = 0x7f0d0060;
        public static final int ici28c_projection_img_applecarplaycouldnotconnected_placeholder = 0x7f0d0061;
        public static final int ici28c_projection_img_notice = 0x7f0d0062;
        public static final int ici28c_projection_img_startupcarlifeonphone = 0x7f0d0063;
        public static final int ici_title_back_dark = 0x7f0d0064;
        public static final int ici_title_back_light = 0x7f0d0065;
        public static final int icon_bt_breakoff = 0x7f0d0066;
        public static final int icon_bt_close = 0x7f0d0067;
        public static final int icon_bt_connect = 0x7f0d0068;
        public static final int icon_bt_contacts = 0x7f0d0069;
        public static final int icon_bt_ignorecalls = 0x7f0d006a;
        public static final int icon_btsetting_back = 0x7f0d006b;
        public static final int icon_btsetting_card_add_disable_358 = 0x7f0d006c;
        public static final int icon_btsetting_card_add_disable_buick = 0x7f0d006d;
        public static final int icon_btsetting_card_add_normal = 0x7f0d006e;
        public static final int icon_btsetting_card_add_normal_358 = 0x7f0d006f;
        public static final int icon_btsetting_card_add_normal_buick = 0x7f0d0070;
        public static final int icon_btsetting_card_add_pressed = 0x7f0d0071;
        public static final int icon_btsetting_card_add_pressed_358 = 0x7f0d0072;
        public static final int icon_btsetting_card_add_pressed_buick = 0x7f0d0073;
        public static final int icon_btsetting_card_bg_add = 0x7f0d0074;
        public static final int icon_btsetting_card_connected_icon = 0x7f0d0075;
        public static final int icon_btsetting_card_unconnected_icon = 0x7f0d0076;
        public static final int icon_btsetting_label = 0x7f0d0077;
        public static final int icon_btsetting_scan = 0x7f0d0078;
        public static final int icon_carlife_connected = 0x7f0d0079;
        public static final int icon_carlife_unconnected = 0x7f0d007a;
        public static final int icon_carplay_connected = 0x7f0d007b;
        public static final int icon_carplay_unconnected = 0x7f0d007c;
        public static final int icon_connectivity_top_table_libe = 0x7f0d007d;
        public static final int icon_detail_info_disable = 0x7f0d007e;
        public static final int icon_detail_info_normal = 0x7f0d007f;
        public static final int icon_detail_info_select = 0x7f0d0080;
        public static final int icon_deviceinfo_connected = 0x7f0d0081;
        public static final int icon_electricity_empty = 0x7f0d0082;
        public static final int icon_medium_bg = 0x7f0d0083;
        public static final int icon_small_circular = 0x7f0d0084;
        public static final int icon_small_circular_bg = 0x7f0d0085;
        public static final int icon_statusicon_connected = 0x7f0d0086;
        public static final int icon_statusicon_connecting = 0x7f0d0087;
        public static final int icon_statusicon_unconnected = 0x7f0d0088;
        public static final int img_add_page_bg = 0x7f0d0089;
        public static final int img_selected_tab_bg = 0x7f0d008a;
        public static final int normal_list_line = 0x7f0d008b;
        public static final int signa_0 = 0x7f0d008c;
        public static final int signa_1 = 0x7f0d008d;
        public static final int signa_2 = 0x7f0d008e;
        public static final int signa_3 = 0x7f0d008f;
        public static final int signa_4 = 0x7f0d0090;
        public static final int signa_5 = 0x7f0d0091;
        public static final int switch_off_thumb_normal = 0x7f0d0092;
        public static final int switch_off_thumb_pressed = 0x7f0d0093;
        public static final int switch_on_thumb_normal = 0x7f0d0094;
        public static final int switch_on_thumb_pressed = 0x7f0d0095;
        public static final int tab_navi_bars_bg_short = 0x7f0d0096;

        public mipmap(
        /*  JADX ERROR: Method generation error
            jadx.core.utils.exceptions.JadxRuntimeException: Method arg registers not loaded: com.ici.connectivity.R.mipmap.<init>():void, class status: PROCESS_COMPLETE
            	at jadx.core.dex.nodes.MethodNode.getArgRegs(MethodNode.java:251)
            	at jadx.core.codegen.MethodGen.addDefinition(MethodGen.java:154)
            	at jadx.core.codegen.ClassGen.addMethodCode(ClassGen.java:372)
            	at jadx.core.codegen.ClassGen.addMethod(ClassGen.java:306)
            	at jadx.core.codegen.ClassGen.lambda$addInnerClsAndMethods$2(ClassGen.java:272)
            	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
            	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
            	at java.base/java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:395)
            	at java.base/java.util.stream.Sink$ChainedReference.end(Sink.java:258)
            */
    }

    /* loaded from: classes.dex */
    public final class string {
        public static final int abc_action_bar_home_description = 0x7f0e0000;
        public static final int abc_action_bar_up_description = 0x7f0e0001;
        public static final int abc_action_menu_overflow_description = 0x7f0e0002;
        public static final int abc_action_mode_done = 0x7f0e0003;
        public static final int abc_activity_chooser_view_see_all = 0x7f0e0004;
        public static final int abc_activitychooserview_choose_application = 0x7f0e0005;
        public static final int abc_capital_off = 0x7f0e0006;
        public static final int abc_capital_on = 0x7f0e0007;
        public static final int abc_menu_alt_shortcut_label = 0x7f0e0008;
        public static final int abc_menu_ctrl_shortcut_label = 0x7f0e0009;
        public static final int abc_menu_delete_shortcut_label = 0x7f0e000a;
        public static final int abc_menu_enter_shortcut_label = 0x7f0e000b;
        public static final int abc_menu_function_shortcut_label = 0x7f0e000c;
        public static final int abc_menu_meta_shortcut_label = 0x7f0e000d;
        public static final int abc_menu_shift_shortcut_label = 0x7f0e000e;
        public static final int abc_menu_space_shortcut_label = 0x7f0e000f;
        public static final int abc_menu_sym_shortcut_label = 0x7f0e0010;
        public static final int abc_prepend_shortcut_label = 0x7f0e0011;
        public static final int abc_search_hint = 0x7f0e0012;
        public static final int abc_searchview_description_clear = 0x7f0e0013;
        public static final int abc_searchview_description_query = 0x7f0e0014;
        public static final int abc_searchview_description_search = 0x7f0e0015;
        public static final int abc_searchview_description_submit = 0x7f0e0016;
        public static final int abc_searchview_description_voice = 0x7f0e0017;
        public static final int abc_shareactionprovider_share_with = 0x7f0e0018;
        public static final int abc_shareactionprovider_share_with_application = 0x7f0e0019;
        public static final int abc_toolbar_collapse_description = 0x7f0e001a;
        public static final int app_name = 0x7f0e001b;
        public static final int cannot_connect_carplay = 0x7f0e001c;
        public static final int cannot_connect_carplay_tips = 0x7f0e001d;
        public static final int carlife_name = 0x7f0e001e;
        public static final int carplay_name = 0x7f0e001f;
        public static final int check_device_connect = 0x7f0e0020;
        public static final int cl_btn_text_cancel = 0x7f0e0021;
        public static final int cl_btn_text_continue = 0x7f0e0022;
        public static final int cl_btn_text_disable = 0x7f0e0023;
        public static final int cl_btn_text_enable = 0x7f0e0024;
        public static final int cl_btn_text_help = 0x7f0e0025;
        public static final int cl_btn_text_ok = 0x7f0e0026;
        public static final int cl_btn_text_reconnect = 0x7f0e0027;
        public static final int cl_btn_text_retry = 0x7f0e0028;
        public static final int cl_cant_connect_carlife_service = 0x7f0e0029;
        public static final int cl_carlife_connecting = 0x7f0e002a;
        public static final int cl_carlife_state_available = 0x7f0e002b;
        public static final int cl_carlife_state_connected = 0x7f0e002c;
        public static final int cl_carlife_state_unavailable = 0x7f0e002d;
        public static final int cl_carlife_unavailable = 0x7f0e002e;
        public static final int cl_carlife_unavailable_detail = 0x7f0e002f;
        public static final int cl_check_message_detail = 0x7f0e0030;
        public static final int cl_check_message_title = 0x7f0e0031;
        public static final int cl_connect_device_not_exist = 0x7f0e0032;
        public static final int cl_connect_fail_below_android_5_detail = 0x7f0e0033;
        public static final int cl_connect_fail_below_android_5_title = 0x7f0e0034;
        public static final int cl_connect_fail_not_install_carlife_detail = 0x7f0e0035;
        public static final int cl_connect_fail_not_install_carlife_title = 0x7f0e0036;
        public static final int cl_connect_fail_unknown_reason_detail = 0x7f0e0037;
        public static final int cl_connect_fail_unknown_reason_title = 0x7f0e0038;
        public static final int cl_enable_baidu_carlife_detail = 0x7f0e0039;
        public static final int cl_enable_baidu_carlife_title = 0x7f0e003a;
        public static final int cl_help_allow_auth = 0x7f0e003b;
        public static final int cl_help_allow_auth_detail = 0x7f0e003c;
        public static final int cl_help_android_os = 0x7f0e003d;
        public static final int cl_help_apple_os = 0x7f0e003e;
        public static final int cl_help_check_app_install = 0x7f0e003f;
        public static final int cl_help_check_usb_cable = 0x7f0e0040;
        public static final int cl_help_check_usb_cable_detail = 0x7f0e0041;
        public static final int cl_help_connect_help = 0x7f0e0042;
        public static final int cl_help_how_allow_auth = 0x7f0e0043;
        public static final int cl_help_install_carlife = 0x7f0e0044;
        public static final int cl_help_install_carlife_detail = 0x7f0e0045;
        public static final int cl_help_msg_page1 = 0x7f0e0046;
        public static final int cl_help_msg_page2 = 0x7f0e0047;
        public static final int cl_help_msg_page3 = 0x7f0e0048;
        public static final int cl_help_reconnect = 0x7f0e0049;
        public static final int cl_help_select_os = 0x7f0e004a;
        public static final int cl_help_text_page1 = 0x7f0e004b;
        public static final int cl_help_text_page2 = 0x7f0e004c;
        public static final int cl_help_text_page3 = 0x7f0e004d;
        public static final int cl_help_try_restart_phone = 0x7f0e004e;
        public static final int cl_invalid_carlife_device = 0x7f0e004f;
        public static final int cl_invalid_device_pls_select_again = 0x7f0e0050;
        public static final int cl_may_be_history_chk_usb = 0x7f0e0051;
        public static final int cl_not_connect = 0x7f0e0052;
        public static final int cl_not_found_disconnected_cp_device = 0x7f0e0053;
        public static final int cl_not_fount_available_device_detail = 0x7f0e0054;
        public static final int cl_not_fount_available_device_title = 0x7f0e0055;
        public static final int cl_not_select_available_device = 0x7f0e0056;
        public static final int cl_reconnect_target_device_has_removed = 0x7f0e0057;
        public static final int cl_settings_carlife_enable_text = 0x7f0e0058;
        public static final int cl_settings_connected_phone = 0x7f0e0059;
        public static final int cl_settings_device_manager_title = 0x7f0e005a;
        public static final int cl_settings_select_phone = 0x7f0e005b;
        public static final int cl_settings_switch_phone = 0x7f0e005c;
        public static final int cl_settings_title = 0x7f0e005d;
        public static final int cl_text_baidu_carlife = 0x7f0e005e;
        public static final int cl_user_agreement_detail = 0x7f0e005f;
        public static final int cl_user_agreement_title = 0x7f0e0060;
        public static final int connect_carplay_by_bt_or_usb = 0x7f0e0061;
        public static final int connect_carplay_cancel = 0x7f0e0062;
        public static final int connect_carplay_retry = 0x7f0e0063;
        public static final int connect_device = 0x7f0e0064;
        public static final int connecting_carplay = 0x7f0e0065;
        public static final int connecting_carplay_tips = 0x7f0e0066;
        public static final int cp_device_no_support = 0x7f0e0067;
        public static final int device_setting_name = 0x7f0e0068;
        public static final int no_available_carplay = 0x7f0e0069;
        public static final int search_menu_title = 0x7f0e006a;
        public static final int security_restrictions = 0x7f0e006b;
        public static final int security_restrictions_cancel = 0x7f0e006c;
        public static final int security_restrictions_continue = 0x7f0e006d;
        public static final int spb_default_speed = 0x7f0e006e;
        public static final int status_bar_notification_info_overflow = 0x7f0e006f;
        public static final int switch_phone = 0x7f0e0070;
        public static final int switch_phone_cancel = 0x7f0e0071;
        public static final int switch_phone_continue = 0x7f0e0072;
        public static final int switch_phone_tip = 0x7f0e0073;
        public static final int text_a2dp_state = 0x7f0e0074;
        public static final int text_a2dp_state_unsupport = 0x7f0e0075;
        public static final int text_apple_carplay = 0x7f0e0076;
        public static final int text_baidu_carlife = 0x7f0e0077;
        public static final int text_bt_pairing = 0x7f0e0078;
        public static final int text_bt_pairing_tips = 0x7f0e0079;
        public static final int text_can_not_connet = 0x7f0e007a;
        public static final int text_cancel = 0x7f0e007b;
        public static final int text_cancel_pair = 0x7f0e007c;
        public static final int text_car_play = 0x7f0e007d;
        public static final int text_carplay_connect = 0x7f0e007e;
        public static final int text_check_detail = 0x7f0e007f;
        public static final int text_confirm_devices = 0x7f0e0080;
        public static final int text_confirm_disconnect = 0x7f0e0081;
        public static final int text_confirm_pair = 0x7f0e0082;
        public static final int text_connect = 0x7f0e0083;
        public static final int text_connect_fail = 0x7f0e0084;
        public static final int text_connect_fail_click_by_phone = 0x7f0e0085;
        public static final int text_connect_retry = 0x7f0e0086;
        public static final int text_connect_retry_by_click_phone = 0x7f0e0087;
        public static final int text_connected = 0x7f0e0088;
        public static final int text_connecting = 0x7f0e0089;
        public static final int text_contact_syncing = 0x7f0e008a;
        public static final int text_countdown_info1 = 0x7f0e008b;
        public static final int text_countdown_info2 = 0x7f0e008c;
        public static final int text_countdown_info3 = 0x7f0e008d;
        public static final int text_countdown_info4 = 0x7f0e008e;
        public static final int text_delete_from_list = 0x7f0e008f;
        public static final int text_detial_connecting = 0x7f0e0090;
        public static final int text_device_connected = 0x7f0e0091;
        public static final int text_device_connecting = 0x7f0e0092;
        public static final int text_device_disconnected = 0x7f0e0093;
        public static final int text_device_disconnecting = 0x7f0e0094;
        public static final int text_device_name = 0x7f0e0095;
        public static final int text_disconnect = 0x7f0e0096;
        public static final int text_disconnect_device_info = 0x7f0e0097;
        public static final int text_disconnect_device_label = 0x7f0e0098;
        public static final int text_disconnect_fail = 0x7f0e0099;
        public static final int text_disconnected = 0x7f0e009a;
        public static final int text_first_connect = 0x7f0e009b;
        public static final int text_forget = 0x7f0e009c;
        public static final int text_forget_fail = 0x7f0e009d;
        public static final int text_forget_phone_title = 0x7f0e009e;
        public static final int text_forget_success = 0x7f0e009f;
        public static final int text_hfp_a2dp_state_unsupport = 0x7f0e00a0;
        public static final int text_hfp_state = 0x7f0e00a1;
        public static final int text_hfp_state_unsupport = 0x7f0e00a2;
        public static final int text_low_battery = 0x7f0e00a3;
        public static final int text_no_devices = 0x7f0e00a4;
        public static final int text_no_profile = 0x7f0e00a5;
        public static final int text_open_bt = 0x7f0e00a6;
        public static final int text_pair_fail = 0x7f0e00a7;
        public static final int text_paired_status = 0x7f0e00a8;
        public static final int text_pairing_loading_start = 0x7f0e00a9;
        public static final int text_pairing_notify = 0x7f0e00aa;
        public static final int text_pairing_timeout = 0x7f0e00ab;
        public static final int text_paring_continue = 0x7f0e00ac;
        public static final int text_paring_ignore = 0x7f0e00ad;
        public static final int text_phonebook_fail = 0x7f0e00ae;
        public static final int text_phonebook_success = 0x7f0e00af;
        public static final int text_request_pair = 0x7f0e00b0;
        public static final int text_retry = 0x7f0e00b1;
        public static final int text_security_speed = 0x7f0e00b2;
        public static final int text_swich_off = 0x7f0e00b3;
        public static final int text_swich_on = 0x7f0e00b4;
        public static final int text_switch_device = 0x7f0e00b5;
        public static final int text_switch_device_label = 0x7f0e00b6;
        public static final int text_sync_contact = 0x7f0e00b7;
        public static final int text_tab_discovered = 0x7f0e00b8;
        public static final int text_tab_paired = 0x7f0e00b9;
        public static final int text_unkowndevice = 0x7f0e00ba;
        public static final int text_unsupport_calling = 0x7f0e00bb;
        public static final int text_unsupport_media = 0x7f0e00bc;
        public static final int text_wait_device_connecting = 0x7f0e00bd;
        public static final int user_agreement_detail = 0x7f0e00be;
        public static final int user_terms = 0x7f0e00bf;
        public static final int user_terms_disable = 0x7f0e00c0;
        public static final int user_terms_enable = 0x7f0e00c1;

        public string(
        /*  JADX ERROR: Method generation error
            jadx.core.utils.exceptions.JadxRuntimeException: Method arg registers not loaded: com.ici.connectivity.R.string.<init>():void, class status: PROCESS_COMPLETE
            	at jadx.core.dex.nodes.MethodNode.getArgRegs(MethodNode.java:251)
            	at jadx.core.codegen.MethodGen.addDefinition(MethodGen.java:154)
            	at jadx.core.codegen.ClassGen.addMethodCode(ClassGen.java:372)
            	at jadx.core.codegen.ClassGen.addMethod(ClassGen.java:306)
            	at jadx.core.codegen.ClassGen.lambda$addInnerClsAndMethods$2(ClassGen.java:272)
            	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
            	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
            	at java.base/java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:395)
            	at java.base/java.util.stream.Sink$ChainedReference.end(Sink.java:258)
            */
    }

    /* loaded from: classes.dex */
    public final class style {
        public static final int AlertDialog_AppCompat = 0x7f0f0000;
        public static final int AlertDialog_AppCompat_Light = 0x7f0f0001;
        public static final int Animation = 0x7f0f0002;
        public static final int Animation_AppCompat_Dialog = 0x7f0f0003;
        public static final int Animation_AppCompat_DropDownUp = 0x7f0f0004;
        public static final int Animation_AppCompat_Tooltip = 0x7f0f0005;
        public static final int AppTheme = 0x7f0f0006;
        public static final int Base_AlertDialog_AppCompat = 0x7f0f0007;
        public static final int Base_AlertDialog_AppCompat_Light = 0x7f0f0008;
        public static final int Base_Animation_AppCompat_Dialog = 0x7f0f0009;
        public static final int Base_Animation_AppCompat_DropDownUp = 0x7f0f000a;
        public static final int Base_Animation_AppCompat_Tooltip = 0x7f0f000b;
        public static final int Base_DialogWindowTitleBackground_AppCompat = 0x7f0f000d;
        public static final int Base_DialogWindowTitle_AppCompat = 0x7f0f000c;
        public static final int Base_TextAppearance_AppCompat = 0x7f0f000e;
        public static final int Base_TextAppearance_AppCompat_Body1 = 0x7f0f000f;
        public static final int Base_TextAppearance_AppCompat_Body2 = 0x7f0f0010;
        public static final int Base_TextAppearance_AppCompat_Button = 0x7f0f0011;
        public static final int Base_TextAppearance_AppCompat_Caption = 0x7f0f0012;
        public static final int Base_TextAppearance_AppCompat_Display1 = 0x7f0f0013;
        public static final int Base_TextAppearance_AppCompat_Display2 = 0x7f0f0014;
        public static final int Base_TextAppearance_AppCompat_Display3 = 0x7f0f0015;
        public static final int Base_TextAppearance_AppCompat_Display4 = 0x7f0f0016;
        public static final int Base_TextAppearance_AppCompat_Headline = 0x7f0f0017;
        public static final int Base_TextAppearance_AppCompat_Inverse = 0x7f0f0018;
        public static final int Base_TextAppearance_AppCompat_Large = 0x7f0f0019;
        public static final int Base_TextAppearance_AppCompat_Large_Inverse = 0x7f0f001a;
        public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f0f001b;
        public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f0f001c;
        public static final int Base_TextAppearance_AppCompat_Medium = 0x7f0f001d;
        public static final int Base_TextAppearance_AppCompat_Medium_Inverse = 0x7f0f001e;
        public static final int Base_TextAppearance_AppCompat_Menu = 0x7f0f001f;
        public static final int Base_TextAppearance_AppCompat_SearchResult = 0x7f0f0020;
        public static final int Base_TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f0f0021;
        public static final int Base_TextAppearance_AppCompat_SearchResult_Title = 0x7f0f0022;
        public static final int Base_TextAppearance_AppCompat_Small = 0x7f0f0023;
        public static final int Base_TextAppearance_AppCompat_Small_Inverse = 0x7f0f0024;
        public static final int Base_TextAppearance_AppCompat_Subhead = 0x7f0f0025;
        public static final int Base_TextAppearance_AppCompat_Subhead_Inverse = 0x7f0f0026;
        public static final int Base_TextAppearance_AppCompat_Title = 0x7f0f0027;
        public static final int Base_TextAppearance_AppCompat_Title_Inverse = 0x7f0f0028;
        public static final int Base_TextAppearance_AppCompat_Tooltip = 0x7f0f0029;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f0f002a;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f0f002b;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f0f002c;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f0f002d;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f0f002e;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f0f002f;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f0f0030;
        public static final int Base_TextAppearance_AppCompat_Widget_Button = 0x7f0f0031;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f0f0032;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Colored = 0x7f0f0033;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f0f0034;
        public static final int Base_TextAppearance_AppCompat_Widget_DropDownItem = 0x7f0f0035;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f0f0036;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f0f0037;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f0f0038;
        public static final int Base_TextAppearance_AppCompat_Widget_Switch = 0x7f0f0039;
        public static final int Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f0f003a;
        public static final int Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f0f003b;
        public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f0f003c;
        public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f0f003d;
        public static final int Base_ThemeOverlay_AppCompat = 0x7f0f004c;
        public static final int Base_ThemeOverlay_AppCompat_ActionBar = 0x7f0f004d;
        public static final int Base_ThemeOverlay_AppCompat_Dark = 0x7f0f004e;
        public static final int Base_ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f0f004f;
        public static final int Base_ThemeOverlay_AppCompat_Dialog = 0x7f0f0050;
        public static final int Base_ThemeOverlay_AppCompat_Dialog_Alert = 0x7f0f0051;
        public static final int Base_ThemeOverlay_AppCompat_Light = 0x7f0f0052;
        public static final int Base_Theme_AppCompat = 0x7f0f003e;
        public static final int Base_Theme_AppCompat_CompactMenu = 0x7f0f003f;
        public static final int Base_Theme_AppCompat_Dialog = 0x7f0f0040;
        public static final int Base_Theme_AppCompat_DialogWhenLarge = 0x7f0f0044;
        public static final int Base_Theme_AppCompat_Dialog_Alert = 0x7f0f0041;
        public static final int Base_Theme_AppCompat_Dialog_FixedSize = 0x7f0f0042;
        public static final int Base_Theme_AppCompat_Dialog_MinWidth = 0x7f0f0043;
        public static final int Base_Theme_AppCompat_Light = 0x7f0f0045;
        public static final int Base_Theme_AppCompat_Light_DarkActionBar = 0x7f0f0046;
        public static final int Base_Theme_AppCompat_Light_Dialog = 0x7f0f0047;
        public static final int Base_Theme_AppCompat_Light_DialogWhenLarge = 0x7f0f004b;
        public static final int Base_Theme_AppCompat_Light_Dialog_Alert = 0x7f0f0048;
        public static final int Base_Theme_AppCompat_Light_Dialog_FixedSize = 0x7f0f0049;
        public static final int Base_Theme_AppCompat_Light_Dialog_MinWidth = 0x7f0f004a;
        public static final int Base_V21_ThemeOverlay_AppCompat_Dialog = 0x7f0f0057;
        public static final int Base_V21_Theme_AppCompat = 0x7f0f0053;
        public static final int Base_V21_Theme_AppCompat_Dialog = 0x7f0f0054;
        public static final int Base_V21_Theme_AppCompat_Light = 0x7f0f0055;
        public static final int Base_V21_Theme_AppCompat_Light_Dialog = 0x7f0f0056;
        public static final int Base_V22_Theme_AppCompat = 0x7f0f0058;
        public static final int Base_V22_Theme_AppCompat_Light = 0x7f0f0059;
        public static final int Base_V23_Theme_AppCompat = 0x7f0f005a;
        public static final int Base_V23_Theme_AppCompat_Light = 0x7f0f005b;
        public static final int Base_V26_Theme_AppCompat = 0x7f0f005c;
        public static final int Base_V26_Theme_AppCompat_Light = 0x7f0f005d;
        public static final int Base_V26_Widget_AppCompat_Toolbar = 0x7f0f005e;
        public static final int Base_V28_Theme_AppCompat = 0x7f0f005f;
        public static final int Base_V28_Theme_AppCompat_Light = 0x7f0f0060;
        public static final int Base_V7_ThemeOverlay_AppCompat_Dialog = 0x7f0f0065;
        public static final int Base_V7_Theme_AppCompat = 0x7f0f0061;
        public static final int Base_V7_Theme_AppCompat_Dialog = 0x7f0f0062;
        public static final int Base_V7_Theme_AppCompat_Light = 0x7f0f0063;
        public static final int Base_V7_Theme_AppCompat_Light_Dialog = 0x7f0f0064;
        public static final int Base_V7_Widget_AppCompat_AutoCompleteTextView = 0x7f0f0066;
        public static final int Base_V7_Widget_AppCompat_EditText = 0x7f0f0067;
        public static final int Base_V7_Widget_AppCompat_Toolbar = 0x7f0f0068;
        public static final int Base_Widget_AppCompat_ActionBar = 0x7f0f0069;
        public static final int Base_Widget_AppCompat_ActionBar_Solid = 0x7f0f006a;
        public static final int Base_Widget_AppCompat_ActionBar_TabBar = 0x7f0f006b;
        public static final int Base_Widget_AppCompat_ActionBar_TabText = 0x7f0f006c;
        public static final int Base_Widget_AppCompat_ActionBar_TabView = 0x7f0f006d;
        public static final int Base_Widget_AppCompat_ActionButton = 0x7f0f006e;
        public static final int Base_Widget_AppCompat_ActionButton_CloseMode = 0x7f0f006f;
        public static final int Base_Widget_AppCompat_ActionButton_Overflow = 0x7f0f0070;
        public static final int Base_Widget_AppCompat_ActionMode = 0x7f0f0071;
        public static final int Base_Widget_AppCompat_ActivityChooserView = 0x7f0f0072;
        public static final int Base_Widget_AppCompat_AutoCompleteTextView = 0x7f0f0073;
        public static final int Base_Widget_AppCompat_Button = 0x7f0f0074;
        public static final int Base_Widget_AppCompat_ButtonBar = 0x7f0f007a;
        public static final int Base_Widget_AppCompat_ButtonBar_AlertDialog = 0x7f0f007b;
        public static final int Base_Widget_AppCompat_Button_Borderless = 0x7f0f0075;
        public static final int Base_Widget_AppCompat_Button_Borderless_Colored = 0x7f0f0076;
        public static final int Base_Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f0f0077;
        public static final int Base_Widget_AppCompat_Button_Colored = 0x7f0f0078;
        public static final int Base_Widget_AppCompat_Button_Small = 0x7f0f0079;
        public static final int Base_Widget_AppCompat_CompoundButton_CheckBox = 0x7f0f007c;
        public static final int Base_Widget_AppCompat_CompoundButton_RadioButton = 0x7f0f007d;
        public static final int Base_Widget_AppCompat_CompoundButton_Switch = 0x7f0f007e;
        public static final int Base_Widget_AppCompat_DrawerArrowToggle = 0x7f0f007f;
        public static final int Base_Widget_AppCompat_DrawerArrowToggle_Common = 0x7f0f0080;
        public static final int Base_Widget_AppCompat_DropDownItem_Spinner = 0x7f0f0081;
        public static final int Base_Widget_AppCompat_EditText = 0x7f0f0082;
        public static final int Base_Widget_AppCompat_ImageButton = 0x7f0f0083;
        public static final int Base_Widget_AppCompat_Light_ActionBar = 0x7f0f0084;
        public static final int Base_Widget_AppCompat_Light_ActionBar_Solid = 0x7f0f0085;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabBar = 0x7f0f0086;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabText = 0x7f0f0087;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f0f0088;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabView = 0x7f0f0089;
        public static final int Base_Widget_AppCompat_Light_PopupMenu = 0x7f0f008a;
        public static final int Base_Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f0f008b;
        public static final int Base_Widget_AppCompat_ListMenuView = 0x7f0f008c;
        public static final int Base_Widget_AppCompat_ListPopupWindow = 0x7f0f008d;
        public static final int Base_Widget_AppCompat_ListView = 0x7f0f008e;
        public static final int Base_Widget_AppCompat_ListView_DropDown = 0x7f0f008f;
        public static final int Base_Widget_AppCompat_ListView_Menu = 0x7f0f0090;
        public static final int Base_Widget_AppCompat_PopupMenu = 0x7f0f0091;
        public static final int Base_Widget_AppCompat_PopupMenu_Overflow = 0x7f0f0092;
        public static final int Base_Widget_AppCompat_PopupWindow = 0x7f0f0093;
        public static final int Base_Widget_AppCompat_ProgressBar = 0x7f0f0094;
        public static final int Base_Widget_AppCompat_ProgressBar_Horizontal = 0x7f0f0095;
        public static final int Base_Widget_AppCompat_RatingBar = 0x7f0f0096;
        public static final int Base_Widget_AppCompat_RatingBar_Indicator = 0x7f0f0097;
        public static final int Base_Widget_AppCompat_RatingBar_Small = 0x7f0f0098;
        public static final int Base_Widget_AppCompat_SearchView = 0x7f0f0099;
        public static final int Base_Widget_AppCompat_SearchView_ActionBar = 0x7f0f009a;
        public static final int Base_Widget_AppCompat_SeekBar = 0x7f0f009b;
        public static final int Base_Widget_AppCompat_SeekBar_Discrete = 0x7f0f009c;
        public static final int Base_Widget_AppCompat_Spinner = 0x7f0f009d;
        public static final int Base_Widget_AppCompat_Spinner_Underlined = 0x7f0f009e;
        public static final int Base_Widget_AppCompat_TextView = 0x7f0f009f;
        public static final int Base_Widget_AppCompat_TextView_SpinnerItem = 0x7f0f00a0;
        public static final int Base_Widget_AppCompat_Toolbar = 0x7f0f00a1;
        public static final int Base_Widget_AppCompat_Toolbar_Button_Navigation = 0x7f0f00a2;
        public static final int BtAppTheme = 0x7f0f00a3;
        public static final int BtParingProgressBar = 0x7f0f00a4;
        public static final int CarLifeApp = 0x7f0f00a5;
        public static final int MyAppTheme = 0x7f0f00a6;
        public static final int Platform_AppCompat = 0x7f0f00a7;
        public static final int Platform_AppCompat_Light = 0x7f0f00a8;
        public static final int Platform_ThemeOverlay_AppCompat = 0x7f0f00a9;
        public static final int Platform_ThemeOverlay_AppCompat_Dark = 0x7f0f00aa;
        public static final int Platform_ThemeOverlay_AppCompat_Light = 0x7f0f00ab;
        public static final int Platform_V21_AppCompat = 0x7f0f00ac;
        public static final int Platform_V21_AppCompat_Light = 0x7f0f00ad;
        public static final int Platform_V25_AppCompat = 0x7f0f00ae;
        public static final int Platform_V25_AppCompat_Light = 0x7f0f00af;
        public static final int Platform_Widget_AppCompat_Spinner = 0x7f0f00b0;
        public static final int RtlOverlay_DialogWindowTitle_AppCompat = 0x7f0f00b1;
        public static final int RtlOverlay_Widget_AppCompat_ActionBar_TitleItem = 0x7f0f00b2;
        public static final int RtlOverlay_Widget_AppCompat_DialogTitle_Icon = 0x7f0f00b3;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem = 0x7f0f00b4;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup = 0x7f0f00b5;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut = 0x7f0f00b6;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow = 0x7f0f00b7;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Text = 0x7f0f00b8;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Title = 0x7f0f00b9;
        public static final int RtlOverlay_Widget_AppCompat_SearchView_MagIcon = 0x7f0f00bf;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown = 0x7f0f00ba;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 = 0x7f0f00bb;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 = 0x7f0f00bc;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Query = 0x7f0f00bd;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Text = 0x7f0f00be;
        public static final int RtlUnderlay_Widget_AppCompat_ActionButton = 0x7f0f00c0;
        public static final int RtlUnderlay_Widget_AppCompat_ActionButton_Overflow = 0x7f0f00c1;
        public static final int SPB = 0x7f0f00c2;
        public static final int SmoothProgressBar = 0x7f0f00c3;
        public static final int TextAppearance_AppCompat = 0x7f0f00c4;
        public static final int TextAppearance_AppCompat_Body1 = 0x7f0f00c5;
        public static final int TextAppearance_AppCompat_Body2 = 0x7f0f00c6;
        public static final int TextAppearance_AppCompat_Button = 0x7f0f00c7;
        public static final int TextAppearance_AppCompat_Caption = 0x7f0f00c8;
        public static final int TextAppearance_AppCompat_Display1 = 0x7f0f00c9;
        public static final int TextAppearance_AppCompat_Display2 = 0x7f0f00ca;
        public static final int TextAppearance_AppCompat_Display3 = 0x7f0f00cb;
        public static final int TextAppearance_AppCompat_Display4 = 0x7f0f00cc;
        public static final int TextAppearance_AppCompat_Headline = 0x7f0f00cd;
        public static final int TextAppearance_AppCompat_Inverse = 0x7f0f00ce;
        public static final int TextAppearance_AppCompat_Large = 0x7f0f00cf;
        public static final int TextAppearance_AppCompat_Large_Inverse = 0x7f0f00d0;
        public static final int TextAppearance_AppCompat_Light_SearchResult_Subtitle = 0x7f0f00d1;
        public static final int TextAppearance_AppCompat_Light_SearchResult_Title = 0x7f0f00d2;
        public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f0f00d3;
        public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f0f00d4;
        public static final int TextAppearance_AppCompat_Medium = 0x7f0f00d5;
        public static final int TextAppearance_AppCompat_Medium_Inverse = 0x7f0f00d6;
        public static final int TextAppearance_AppCompat_Menu = 0x7f0f00d7;
        public static final int TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f0f00d8;
        public static final int TextAppearance_AppCompat_SearchResult_Title = 0x7f0f00d9;
        public static final int TextAppearance_AppCompat_Small = 0x7f0f00da;
        public static final int TextAppearance_AppCompat_Small_Inverse = 0x7f0f00db;
        public static final int TextAppearance_AppCompat_Subhead = 0x7f0f00dc;
        public static final int TextAppearance_AppCompat_Subhead_Inverse = 0x7f0f00dd;
        public static final int TextAppearance_AppCompat_Title = 0x7f0f00de;
        public static final int TextAppearance_AppCompat_Title_Inverse = 0x7f0f00df;
        public static final int TextAppearance_AppCompat_Tooltip = 0x7f0f00e0;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f0f00e1;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f0f00e2;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f0f00e3;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f0f00e4;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f0f00e5;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f0f00e6;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse = 0x7f0f00e7;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f0f00e8;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse = 0x7f0f00e9;
        public static final int TextAppearance_AppCompat_Widget_Button = 0x7f0f00ea;
        public static final int TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f0f00eb;
        public static final int TextAppearance_AppCompat_Widget_Button_Colored = 0x7f0f00ec;
        public static final int TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f0f00ed;
        public static final int TextAppearance_AppCompat_Widget_DropDownItem = 0x7f0f00ee;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f0f00ef;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f0f00f0;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f0f00f1;
        public static final int TextAppearance_AppCompat_Widget_Switch = 0x7f0f00f2;
        public static final int TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f0f00f3;
        public static final int TextAppearance_Compat_Notification = 0x7f0f00f4;
        public static final int TextAppearance_Compat_Notification_Info = 0x7f0f00f5;
        public static final int TextAppearance_Compat_Notification_Line2 = 0x7f0f00f6;
        public static final int TextAppearance_Compat_Notification_Time = 0x7f0f00f7;
        public static final int TextAppearance_Compat_Notification_Title = 0x7f0f00f8;
        public static final int TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f0f00f9;
        public static final int TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f0f00fa;
        public static final int TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f0f00fb;
        public static final int ThemeOverlay_AppCompat = 0x7f0f0112;
        public static final int ThemeOverlay_AppCompat_ActionBar = 0x7f0f0113;
        public static final int ThemeOverlay_AppCompat_Dark = 0x7f0f0114;
        public static final int ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f0f0115;
        public static final int ThemeOverlay_AppCompat_DayNight = 0x7f0f0116;
        public static final int ThemeOverlay_AppCompat_DayNight_ActionBar = 0x7f0f0117;
        public static final int ThemeOverlay_AppCompat_Dialog = 0x7f0f0118;
        public static final int ThemeOverlay_AppCompat_Dialog_Alert = 0x7f0f0119;
        public static final int ThemeOverlay_AppCompat_Light = 0x7f0f011a;
        public static final int Theme_AppCompat = 0x7f0f00fc;
        public static final int Theme_AppCompat_CompactMenu = 0x7f0f00fd;
        public static final int Theme_AppCompat_DayNight = 0x7f0f00fe;
        public static final int Theme_AppCompat_DayNight_DarkActionBar = 0x7f0f00ff;
        public static final int Theme_AppCompat_DayNight_Dialog = 0x7f0f0100;
        public static final int Theme_AppCompat_DayNight_DialogWhenLarge = 0x7f0f0103;
        public static final int Theme_AppCompat_DayNight_Dialog_Alert = 0x7f0f0101;
        public static final int Theme_AppCompat_DayNight_Dialog_MinWidth = 0x7f0f0102;
        public static final int Theme_AppCompat_DayNight_NoActionBar = 0x7f0f0104;
        public static final int Theme_AppCompat_Dialog = 0x7f0f0105;
        public static final int Theme_AppCompat_DialogWhenLarge = 0x7f0f0108;
        public static final int Theme_AppCompat_Dialog_Alert = 0x7f0f0106;
        public static final int Theme_AppCompat_Dialog_MinWidth = 0x7f0f0107;
        public static final int Theme_AppCompat_Light = 0x7f0f0109;
        public static final int Theme_AppCompat_Light_DarkActionBar = 0x7f0f010a;
        public static final int Theme_AppCompat_Light_Dialog = 0x7f0f010b;
        public static final int Theme_AppCompat_Light_DialogWhenLarge = 0x7f0f010e;
        public static final int Theme_AppCompat_Light_Dialog_Alert = 0x7f0f010c;
        public static final int Theme_AppCompat_Light_Dialog_MinWidth = 0x7f0f010d;
        public static final int Theme_AppCompat_Light_NoActionBar = 0x7f0f010f;
        public static final int Theme_AppCompat_NoActionBar = 0x7f0f0110;
        public static final int Theme_SmoothProgressBarDefaults = 0x7f0f0111;
        public static final int Widget_AppCompat_ActionBar = 0x7f0f011b;
        public static final int Widget_AppCompat_ActionBar_Solid = 0x7f0f011c;
        public static final int Widget_AppCompat_ActionBar_TabBar = 0x7f0f011d;
        public static final int Widget_AppCompat_ActionBar_TabText = 0x7f0f011e;
        public static final int Widget_AppCompat_ActionBar_TabView = 0x7f0f011f;
        public static final int Widget_AppCompat_ActionButton = 0x7f0f0120;
        public static final int Widget_AppCompat_ActionButton_CloseMode = 0x7f0f0121;
        public static final int Widget_AppCompat_ActionButton_Overflow = 0x7f0f0122;
        public static final int Widget_AppCompat_ActionMode = 0x7f0f0123;
        public static final int Widget_AppCompat_ActivityChooserView = 0x7f0f0124;
        public static final int Widget_AppCompat_AutoCompleteTextView = 0x7f0f0125;
        public static final int Widget_AppCompat_Button = 0x7f0f0126;
        public static final int Widget_AppCompat_ButtonBar = 0x7f0f012c;
        public static final int Widget_AppCompat_ButtonBar_AlertDialog = 0x7f0f012d;
        public static final int Widget_AppCompat_Button_Borderless = 0x7f0f0127;
        public static final int Widget_AppCompat_Button_Borderless_Colored = 0x7f0f0128;
        public static final int Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f0f0129;
        public static final int Widget_AppCompat_Button_Colored = 0x7f0f012a;
        public static final int Widget_AppCompat_Button_Small = 0x7f0f012b;
        public static final int Widget_AppCompat_CompoundButton_CheckBox = 0x7f0f012e;
        public static final int Widget_AppCompat_CompoundButton_RadioButton = 0x7f0f012f;
        public static final int Widget_AppCompat_CompoundButton_Switch = 0x7f0f0130;
        public static final int Widget_AppCompat_DrawerArrowToggle = 0x7f0f0131;
        public static final int Widget_AppCompat_DropDownItem_Spinner = 0x7f0f0132;
        public static final int Widget_AppCompat_EditText = 0x7f0f0133;
        public static final int Widget_AppCompat_ImageButton = 0x7f0f0134;
        public static final int Widget_AppCompat_Light_ActionBar = 0x7f0f0135;
        public static final int Widget_AppCompat_Light_ActionBar_Solid = 0x7f0f0136;
        public static final int Widget_AppCompat_Light_ActionBar_Solid_Inverse = 0x7f0f0137;
        public static final int Widget_AppCompat_Light_ActionBar_TabBar = 0x7f0f0138;
        public static final int Widget_AppCompat_Light_ActionBar_TabBar_Inverse = 0x7f0f0139;
        public static final int Widget_AppCompat_Light_ActionBar_TabText = 0x7f0f013a;
        public static final int Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f0f013b;
        public static final int Widget_AppCompat_Light_ActionBar_TabView = 0x7f0f013c;
        public static final int Widget_AppCompat_Light_ActionBar_TabView_Inverse = 0x7f0f013d;
        public static final int Widget_AppCompat_Light_ActionButton = 0x7f0f013e;
        public static final int Widget_AppCompat_Light_ActionButton_CloseMode = 0x7f0f013f;
        public static final int Widget_AppCompat_Light_ActionButton_Overflow = 0x7f0f0140;
        public static final int Widget_AppCompat_Light_ActionMode_Inverse = 0x7f0f0141;
        public static final int Widget_AppCompat_Light_ActivityChooserView = 0x7f0f0142;
        public static final int Widget_AppCompat_Light_AutoCompleteTextView = 0x7f0f0143;
        public static final int Widget_AppCompat_Light_DropDownItem_Spinner = 0x7f0f0144;
        public static final int Widget_AppCompat_Light_ListPopupWindow = 0x7f0f0145;
        public static final int Widget_AppCompat_Light_ListView_DropDown = 0x7f0f0146;
        public static final int Widget_AppCompat_Light_PopupMenu = 0x7f0f0147;
        public static final int Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f0f0148;
        public static final int Widget_AppCompat_Light_SearchView = 0x7f0f0149;
        public static final int Widget_AppCompat_Light_Spinner_DropDown_ActionBar = 0x7f0f014a;
        public static final int Widget_AppCompat_ListMenuView = 0x7f0f014b;
        public static final int Widget_AppCompat_ListPopupWindow = 0x7f0f014c;
        public static final int Widget_AppCompat_ListView = 0x7f0f014d;
        public static final int Widget_AppCompat_ListView_DropDown = 0x7f0f014e;
        public static final int Widget_AppCompat_ListView_Menu = 0x7f0f014f;
        public static final int Widget_AppCompat_PopupMenu = 0x7f0f0150;
        public static final int Widget_AppCompat_PopupMenu_Overflow = 0x7f0f0151;
        public static final int Widget_AppCompat_PopupWindow = 0x7f0f0152;
        public static final int Widget_AppCompat_ProgressBar = 0x7f0f0153;
        public static final int Widget_AppCompat_ProgressBar_Horizontal = 0x7f0f0154;
        public static final int Widget_AppCompat_RatingBar = 0x7f0f0155;
        public static final int Widget_AppCompat_RatingBar_Indicator = 0x7f0f0156;
        public static final int Widget_AppCompat_RatingBar_Small = 0x7f0f0157;
        public static final int Widget_AppCompat_SearchView = 0x7f0f0158;
        public static final int Widget_AppCompat_SearchView_ActionBar = 0x7f0f0159;
        public static final int Widget_AppCompat_SeekBar = 0x7f0f015a;
        public static final int Widget_AppCompat_SeekBar_Discrete = 0x7f0f015b;
        public static final int Widget_AppCompat_Spinner = 0x7f0f015c;
        public static final int Widget_AppCompat_Spinner_DropDown = 0x7f0f015d;
        public static final int Widget_AppCompat_Spinner_DropDown_ActionBar = 0x7f0f015e;
        public static final int Widget_AppCompat_Spinner_Underlined = 0x7f0f015f;
        public static final int Widget_AppCompat_TextView = 0x7f0f0160;
        public static final int Widget_AppCompat_TextView_SpinnerItem = 0x7f0f0161;
        public static final int Widget_AppCompat_Toolbar = 0x7f0f0162;
        public static final int Widget_AppCompat_Toolbar_Button_Navigation = 0x7f0f0163;
        public static final int Widget_Compat_NotificationActionContainer = 0x7f0f0164;
        public static final int Widget_Compat_NotificationActionText = 0x7f0f0165;
        public static final int switch_tyle = 0x7f0f0166;

        public style(
        /*  JADX ERROR: Method generation error
            jadx.core.utils.exceptions.JadxRuntimeException: Method arg registers not loaded: com.ici.connectivity.R.style.<init>():void, class status: PROCESS_COMPLETE
            	at jadx.core.dex.nodes.MethodNode.getArgRegs(MethodNode.java:251)
            	at jadx.core.codegen.MethodGen.addDefinition(MethodGen.java:154)
            	at jadx.core.codegen.ClassGen.addMethodCode(ClassGen.java:372)
            	at jadx.core.codegen.ClassGen.addMethod(ClassGen.java:306)
            	at jadx.core.codegen.ClassGen.lambda$addInnerClsAndMethods$2(ClassGen.java:272)
            	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
            	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
            	at java.base/java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:395)
            	at java.base/java.util.stream.Sink$ChainedReference.end(Sink.java:258)
            */
    }

    /* loaded from: classes.dex */
    public final class xml {
        public static final int tab_text_color_normal = 0x7f110000;

        public xml(
        /*  JADX ERROR: Method generation error
            jadx.core.utils.exceptions.JadxRuntimeException: Method arg registers not loaded: com.ici.connectivity.R.xml.<init>():void, class status: PROCESS_COMPLETE
            	at jadx.core.dex.nodes.MethodNode.getArgRegs(MethodNode.java:251)
            	at jadx.core.codegen.MethodGen.addDefinition(MethodGen.java:154)
            	at jadx.core.codegen.ClassGen.addMethodCode(ClassGen.java:372)
            	at jadx.core.codegen.ClassGen.addMethod(ClassGen.java:306)
            	at jadx.core.codegen.ClassGen.lambda$addInnerClsAndMethods$2(ClassGen.java:272)
            	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
            	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
            	at java.base/java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:395)
            	at java.base/java.util.stream.Sink$ChainedReference.end(Sink.java:258)
            */
    }
}
