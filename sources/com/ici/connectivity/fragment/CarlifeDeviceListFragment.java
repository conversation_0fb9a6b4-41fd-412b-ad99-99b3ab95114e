package com.ici.connectivity.fragment;

import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import b.a.a.b.y;
import b.a.b.a.a;
import b.d.b.d.e;
import b.d.b.d.f;
import b.d.b.f.l;
import b.d.b.g.v;
import b.d.c.f.d0;
import b.d.c.f.e0;
import b.d.c.h.b;
import b.d.c.h.f.c;
import com.ici.connectivity.R;
import com.ici.connectivity.activity.CarLifeActivity;
import com.ici.connectivity.adapter.CarlifeDeviceListAdapter;
import com.ici.connectivity.application.ConnectivityApplication;
import com.ici.connectivity.view.CarlifeDeviceCardView;
import com.ici.connectivitylib.manager.CarlifeManager;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

/* loaded from: classes.dex */
public class CarlifeDeviceListFragment extends BaseWallPaperFragment<e> implements f, CarlifeDeviceListAdapter.b, View.OnClickListener {
    public static final String TAG = c.e();
    public boolean isDeviceListVisible = false;
    public CarlifeDeviceListAdapter mAdapter;
    public l mPresenter;
    public RecyclerView mRecyclerView;

    public static CarlifeDeviceListFragment getInstance() {
        return new CarlifeDeviceListFragment();
    }

    @Override // b.d.b.d.f
    public void exitActivity() {
        if (getActivity() != null) {
            getActivity().finishAndRemoveTask();
        }
    }

    @Override // com.ici.app.base.BaseFragment
    public int getLayout() {
        return y.k();
    }

    @Override // com.ici.app.base.BaseFragment
    public void initDatas() {
    }

    @Override // com.ici.app.base.BaseFragment
    public void initViews() {
        this.mContentView.findViewById(R.id.cl_id_title_back_btn).setOnClickListener(this);
        this.mRecyclerView = (RecyclerView) this.mContentView.findViewById(R.id.id_carlife_devices_list);
        CarlifeDeviceListAdapter carlifeDeviceListAdapter = new CarlifeDeviceListAdapter();
        this.mAdapter = carlifeDeviceListAdapter;
        carlifeDeviceListAdapter.setItemClickListener(this);
        this.mRecyclerView.setAdapter(this.mAdapter);
        this.mRecyclerView.setLayoutManager(new LinearLayoutManager(getContext(), 0, false));
    }

    @Override // android.view.View.OnClickListener
    public void onClick(View view) {
        if (view.getId() == R.id.cl_id_title_back_btn) {
            exitActivity();
        }
    }

    @Override // com.ici.connectivity.adapter.CarlifeDeviceListAdapter.b
    public void onItemClick(int i, CarlifeDeviceListAdapter.a aVar, CarlifeDeviceCardView carlifeDeviceCardView) {
        int i2 = aVar.c;
        if (i2 == 1) {
            Log.i(TAG, "onItemClick state==STATE_CONNECTED");
            c.h(getContext());
            c.t(CarLifeActivity.class.getName(), "用户点击");
        } else if (i2 != 2) {
            if (i2 != 3) {
                return;
            }
            Log.i(TAG, "onItemClick state==STATE_UNAVAILABLE");
            c.r(getContext(), "历史记录无法连接");
        } else if (TextUtils.isEmpty(aVar.f510b)) {
            String str = TAG;
            StringBuilder e = a.e("onItemClick invalid usb device serial number:");
            e.append(aVar.f510b);
            Log.e(str, e.toString());
        } else {
            l lVar = this.mPresenter;
            if (lVar != null) {
                String str2 = aVar.f510b;
                if (lVar != null) {
                    String str3 = l.i;
                    Log.i(str3, "connect -> index:" + i + ", serial:" + str2);
                    if (TextUtils.isEmpty(str2)) {
                        c.r(lVar.e, "设备序列号无效");
                        return;
                    } else {
                        d0.m().d(2, i);
                        return;
                    }
                }
                throw null;
            }
            Log.e(TAG, "onItemClick. presenter not init");
        }
    }

    @Override // androidx.fragment.app.Fragment
    public void onPause() {
        super.onPause();
        Log.i(TAG, "-- onPause");
        l lVar = this.mPresenter;
        if (lVar != null) {
            Log.i(l.i, "unregisterView");
            lVar.f = false;
            lVar.d.removeMessages(2);
            lVar.d.removeMessages(1);
            lVar.d = null;
            lVar.f238b.showDeviceListView(false);
            v c = v.c(lVar.e);
            c.e = null;
            c.f = null;
            c.g = null;
            lVar.f238b = null;
            d0 d0Var = lVar.c;
            if (d0Var != null) {
                d0Var.x(lVar);
                lVar.c = null;
            }
            e0.d(lVar.e).q(lVar.g);
            y.v("CarLife设备列表", "页面切换", false);
            return;
        }
        throw null;
    }

    @Override // com.ici.connectivity.fragment.BaseWallPaperFragment, androidx.fragment.app.Fragment
    public void onResume() {
        super.onResume();
        StringBuilder e = a.e("[Connectivity][CarlifeDeviceListFragment][onResume][BEFORE][TIME:");
        e.append(System.currentTimeMillis());
        e.append("]");
        Log.i("ICI_TIME_STATISTICS", e.toString());
        Log.i(TAG, "-- onResume");
        b.g(true);
        if (this.mPresenter == null) {
            this.mPresenter = new l();
        }
        final l lVar = this.mPresenter;
        if (lVar != null) {
            Log.i(l.i, "registerView");
            lVar.f = true;
            lVar.e = ConnectivityApplication.c;
            lVar.f238b = this;
            lVar.d = new Handler(lVar.h);
            v c = v.c(lVar.e);
            c.e = new Consumer() { // from class: b.d.b.f.e
                @Override // java.util.function.Consumer
                public final void accept(Object obj) {
                    l.this.j(((Integer) obj).intValue());
                }
            };
            c.f = new Consumer() { // from class: b.d.b.f.g
                @Override // java.util.function.Consumer
                public final void accept(Object obj) {
                    l.this.h(((Integer) obj).intValue());
                }
            };
            c.g = new Consumer() { // from class: b.d.b.f.f
                @Override // java.util.function.Consumer
                public final void accept(Object obj) {
                    l.this.i(((Integer) obj).intValue());
                }
            };
            Log.i(l.i, "init");
            lVar.c = d0.m();
            if (CarlifeManager.getInstance().isCasting()) {
                Log.i(l.i, "current is casting, not show device list");
                lVar.f238b.showDeviceListView(false);
                lVar.f238b.exitActivity();
            } else {
                List<CarlifeDeviceListAdapter.a> g = lVar.g();
                if (((ArrayList) g).isEmpty()) {
                    lVar.o(true);
                } else {
                    lVar.f238b.showDeviceListView(true);
                    lVar.f238b.refreshDeviceList(g);
                    lVar.o(false);
                }
                d0 d0Var = lVar.c;
                if (d0Var != null) {
                    d0Var.v(lVar);
                }
            }
            e0.d(lVar.e).o(lVar.g);
            Log.i("ICI_TIME_STATISTICS", "[Connectivity][CarlifeDeviceListFragment][onResume][AFTER][TIME:" + System.currentTimeMillis() + "]");
            y.u("CarLife设备列表", "用户点击", false);
            return;
        }
        throw null;
    }

    @Override // b.d.b.d.f
    public void refreshDeviceList(List<CarlifeDeviceListAdapter.a> list) {
        CarlifeDeviceListAdapter carlifeDeviceListAdapter;
        if (!this.isDeviceListVisible || list == null || (carlifeDeviceListAdapter = this.mAdapter) == null) {
            return;
        }
        carlifeDeviceListAdapter.updateData(list);
    }

    @Override // b.d.b.d.f
    public void showDeviceListView(boolean z) {
        l lVar;
        if (this.isDeviceListVisible == z) {
            return;
        }
        this.isDeviceListVisible = z;
        a.l("showDeviceListView:", z, TAG);
        this.mContentView.findViewById(R.id.cl_id_device_list_header).setVisibility(z ? 0 : 4);
        this.mContentView.findViewById(R.id.id_carlife_devices_list).setVisibility(z ? 0 : 4);
        if (!z || (lVar = this.mPresenter) == null) {
            return;
        }
        refreshDeviceList(lVar.g());
    }

    @Override // com.ici.app.base.BaseFragment
    public void setPresenter(e eVar) {
        this.mPresenter = (l) eVar;
    }
}
