package com.ici.connectivity.buick_ui.fragment;

import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;
import b.a.b.a.a;
import b.d.b.d.b;
import b.d.b.d.c;
import b.d.b.f.i;
import b.d.c.f.a0;
import b.d.c.f.d0;
import b.d.c.f.e0;
import b.d.c.f.y;
import b.d.c.g.w;
import com.ici.connectivity.R;
import com.ici.connectivity.activity.CarPlayActivity;
import com.ici.connectivity.adapter.HorizontalItemDecoration;
import com.ici.connectivity.adapter.TopLayoutManager;
import com.ici.connectivity.buick_ui.adapter.Buick_CarPlayDeviceListAdapter;
import com.ici.connectivity.buick_ui.fragment.Buick_CarPlayDeviceListFragment;
import com.ici.connectivity.fragment.BaseWallPaperFragment;
import com.ici.connectivity.view.SliderView;
import com.ici.connectivitylib.bean.DeviceInfoBean;
import com.ici.connectivitylib.manager.ConnectionManager;
import java.util.ArrayList;
import java.util.List;
import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

/* loaded from: classes.dex */
public class Buick_CarPlayDeviceListFragment extends BaseWallPaperFragment<b> implements c {
    public static final String TAG = "Connectivity:CarPlayDeviceListFragment";
    public Buick_CarPlayDeviceListAdapter mAdapter;
    public ImageView mAddView;
    public final List<DeviceInfoBean> mDeviceList = new ArrayList();
    public boolean mListState = false;
    public i mPresenter;
    public RecyclerView mRecyclerView;
    public SliderView mSliderView;

    public static Buick_CarPlayDeviceListFragment getInstance() {
        return new Buick_CarPlayDeviceListFragment();
    }

    private int getSelectItemPosition(DeviceInfoBean deviceInfoBean) {
        if (deviceInfoBean != null) {
            for (int i = 0; i < this.mDeviceList.size(); i++) {
                DeviceInfoBean deviceInfoBean2 = this.mDeviceList.get(i);
                StringBuilder e = a.e("getSelectItemPosition serial: ");
                e.append(deviceInfoBean2.getSerialNum());
                e.append(" ,mac: ");
                e.append(deviceInfoBean2.getMacAddress());
                Log.i("Connectivity:CarPlayDeviceListFragment", e.toString());
                if (!TextUtils.isEmpty(deviceInfoBean2.getMacAddress()) && !TextUtils.isEmpty(deviceInfoBean.getMacAddress()) && deviceInfoBean2.getMacAddress().equals(deviceInfoBean.getMacAddress())) {
                    a.i("getSelectItemPosition mac i: ", i, "Connectivity:CarPlayDeviceListFragment");
                    return i;
                } else if (!TextUtils.isEmpty(deviceInfoBean2.getSerialNum()) && !TextUtils.isEmpty(deviceInfoBean.getSerialNum()) && deviceInfoBean2.getSerialNum().equals(b.d.c.h.f.c.q(deviceInfoBean.getSerialNum()))) {
                    a.i("getSelectItemPosition serial i: ", i, "Connectivity:CarPlayDeviceListFragment");
                    return i;
                }
            }
            return -1;
        }
        return -1;
    }

    private void initDataAdapter() {
        if (this.mAdapter == null) {
            Buick_CarPlayDeviceListAdapter buick_CarPlayDeviceListAdapter = new Buick_CarPlayDeviceListAdapter();
            this.mAdapter = buick_CarPlayDeviceListAdapter;
            this.mRecyclerView.setAdapter(buick_CarPlayDeviceListAdapter);
            this.mRecyclerView.setLayoutManager(new TopLayoutManager(getMContext(), 0, false));
            this.mRecyclerView.addItemDecoration(new HorizontalItemDecoration(20, getMContext()));
        }
        this.mAdapter.setItemClickListener(new Buick_CarPlayDeviceListAdapter.a() { // from class: b.d.b.c.b.b
            @Override // com.ici.connectivity.buick_ui.adapter.Buick_CarPlayDeviceListAdapter.a
            public final void a(int i) {
                Buick_CarPlayDeviceListFragment.this.a(i);
            }
        });
        this.mRecyclerView.setOnScrollChangeListener(new View.OnScrollChangeListener() { // from class: b.d.b.c.b.d
            @Override // android.view.View.OnScrollChangeListener
            public final void onScrollChange(View view, int i, int i2, int i3, int i4) {
                Buick_CarPlayDeviceListFragment.this.b(view, i, i2, i3, i4);
            }
        });
        List<DeviceInfoBean> i = this.mPresenter.i();
        if (i != null) {
            this.mDeviceList.clear();
            this.mDeviceList.addAll(i);
        }
        a.r(this.mDeviceList, a.e("mDeviceList != null size = "), "Connectivity:CarPlayDeviceListFragment");
        showDeviceInfoListData();
    }

    private void setViewAndChildrenEnabled(View view, boolean z) {
        view.setEnabled(z);
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                setViewAndChildrenEnabled(viewGroup.getChildAt(i), z);
            }
        }
    }

    private void showDeviceInfoListData() {
        int size = this.mDeviceList.size();
        a.i("count = ", size, "Connectivity:CarPlayDeviceListFragment");
        if (size <= 3) {
            this.mSliderView.setVisibility(4);
        } else {
            this.mSliderView.setVisibility(0);
            int i = size * 470;
            this.mSliderView.a(0, i);
            this.mSliderView.b(1554, i);
        }
        if (this.mPresenter != null) {
            if (ConnectionManager.getInstance().getCarPlayReconnectState()) {
                Log.i("Connectivity:CarPlayDeviceListFragment", "isCarPlayReconnectState true");
                DeviceInfoBean deviceInfoBean = new DeviceInfoBean();
                if (this.mPresenter != null) {
                    deviceInfoBean.setSerialNum(ConnectionManager.getInstance().getCarPlayReconnectSerialNum());
                    deviceInfoBean.setConnectState(1);
                    int selectItemPosition = getSelectItemPosition(deviceInfoBean);
                    a.i("showDeviceInfoListData postion: ", selectItemPosition, "Connectivity:CarPlayDeviceListFragment");
                    if (selectItemPosition != -1) {
                        this.mDeviceList.get(selectItemPosition).setConnectState(deviceInfoBean.getConnectState());
                    }
                    this.mAddView.setEnabled(false);
                    setViewAndChildrenEnabled(this.mRecyclerView, false);
                } else {
                    throw null;
                }
            } else if (this.mPresenter != null) {
                if (ConnectionManager.getInstance().getConnectingCarPlayDevice() != null) {
                    Log.i("Connectivity:CarPlayDeviceListFragment", "getConnectingCarPlayDevice not null");
                    this.mAddView.setEnabled(false);
                    setViewAndChildrenEnabled(this.mRecyclerView, false);
                } else {
                    this.mAddView.setEnabled(true);
                    setViewAndChildrenEnabled(this.mRecyclerView, true);
                }
            } else {
                throw null;
            }
            this.mAdapter.updateData(this.mDeviceList);
            return;
        }
        throw null;
    }

    public void a(int i) {
        a.i("onConnectViewClicked index = ", i, "Connectivity:CarPlayDeviceListFragment");
        if (this.mDeviceList.get(i).getConnectState() == 1) {
            Log.i("Connectivity:CarPlayDeviceListFragment", "onItemClick is connecting ---");
        } else {
            this.mPresenter.h(1, i);
        }
    }

    public void b(View view, int i, int i2, int i3, int i4) {
        int computeHorizontalScrollOffset = this.mRecyclerView.computeHorizontalScrollOffset();
        a.r(this.mDeviceList, a.f("scrollOffset -- ", computeHorizontalScrollOffset, "  size -- "), "Connectivity:CarPlayDeviceListFragment");
        if (computeHorizontalScrollOffset != 0) {
            this.mSliderView.a(computeHorizontalScrollOffset, this.mDeviceList.size() * 470);
        }
    }

    public /* synthetic */ void c(View view) {
        Log.i("Connectivity:CarPlayDeviceListFragment", "点击添加按钮");
        i iVar = this.mPresenter;
        if (iVar != null) {
            iVar.g();
        }
    }

    public /* synthetic */ void d(View view) {
        Log.i("Connectivity:CarPlayDeviceListFragment", "back click");
        ((CarPlayActivity) getMContext()).finish();
    }

    @Override // com.ici.app.base.BaseFragment
    public int getLayout() {
        return b.d.c.a.a.a().b() ? R.layout.fragment_carplay_device_list_358 : R.layout.fragment_carplay_device_list_buick;
    }

    public void hide(boolean z) {
        this.mListState = !z;
        View view = this.mContentView;
        if (view != null) {
            if (z) {
                view.findViewById(R.id.cl_id_device_list_header).setVisibility(4);
                this.mContentView.findViewById(R.id.iv_bt_add).setVisibility(4);
                this.mContentView.findViewById(R.id.cp_recycle_view).setVisibility(4);
                return;
            }
            view.findViewById(R.id.cl_id_device_list_header).setVisibility(0);
            this.mContentView.findViewById(R.id.iv_bt_add).setVisibility(0);
            this.mContentView.findViewById(R.id.cp_recycle_view).setVisibility(0);
        }
    }

    @Override // com.ici.app.base.BaseFragment
    public void initDatas() {
    }

    @Override // com.ici.app.base.BaseFragment
    public void initViews() {
        Log.i("Connectivity:CarPlayDeviceListFragment", "initViews");
        View view = this.mContentView;
        if (view != null) {
            this.mAddView = (ImageView) view.findViewById(R.id.iv_bt_add);
            ((TextView) this.mContentView.findViewById(R.id.cl_id_title_text)).setText(R.string.text_apple_carplay);
            this.mAddView.setOnClickListener(new View.OnClickListener() { // from class: b.d.b.c.b.c
                @Override // android.view.View.OnClickListener
                public final void onClick(View view2) {
                    Buick_CarPlayDeviceListFragment.this.c(view2);
                }
            });
            ((ImageView) this.mContentView.findViewById(R.id.cl_id_title_back_btn)).setOnClickListener(new View.OnClickListener() { // from class: b.d.b.c.b.e
                @Override // android.view.View.OnClickListener
                public final void onClick(View view2) {
                    Buick_CarPlayDeviceListFragment.this.d(view2);
                }
            });
            this.mRecyclerView = (RecyclerView) this.mContentView.findViewById(R.id.cp_recycle_view);
            this.mSliderView = (SliderView) this.mContentView.findViewById(R.id.cp_list_indicator);
        }
    }

    @Subscribe
    public void onBusEventHandle(DeviceInfoBean deviceInfoBean) {
        Log.i("Connectivity:CarPlayDeviceListFragment", "onBusEventHandle:  ---------------> ");
        if (deviceInfoBean == null || this.mAdapter == null) {
            return;
        }
        StringBuilder e = a.e("onBusEventHandle serialNum: ");
        e.append(deviceInfoBean.getSerialNum());
        e.append(" ,mac: ");
        e.append(deviceInfoBean.getMacAddress());
        Log.i("Connectivity:CarPlayDeviceListFragment", e.toString());
        if (deviceInfoBean.getConnectState() == 1) {
            this.mAddView.setEnabled(false);
            setViewAndChildrenEnabled(this.mRecyclerView, false);
        } else {
            this.mAddView.setEnabled(true);
            setViewAndChildrenEnabled(this.mRecyclerView, true);
        }
        int selectItemPosition = getSelectItemPosition(deviceInfoBean);
        a.i("onBusEventHandle postion: ", selectItemPosition, "Connectivity:CarPlayDeviceListFragment");
        if (selectItemPosition != -1) {
            this.mDeviceList.get(selectItemPosition).setConnectState(deviceInfoBean.getConnectState());
            this.mAdapter.updateCarPlayConnectState(selectItemPosition, deviceInfoBean.getConnectState());
        }
    }

    @Override // com.ici.connectivity.fragment.BaseWallPaperFragment, com.ici.app.base.BaseFragment, androidx.fragment.app.Fragment
    public void onHiddenChanged(boolean z) {
        super.onHiddenChanged(z);
        a.l("onHiddenChanged -- ", z, "Connectivity:CarPlayDeviceListFragment");
        if (!z) {
            y.l(getMContext()).z();
        } else {
            y.l(getMContext()).A();
        }
    }

    @Override // androidx.fragment.app.Fragment
    public void onPause() {
        super.onPause();
        Log.i("Connectivity:CarPlayDeviceListFragment", "onPause");
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        if (this.mPresenter != null) {
            y.l(getMContext()).A();
            i iVar = this.mPresenter;
            iVar.f231b = null;
            d0 d0Var = iVar.c;
            if (d0Var != null) {
                d0Var.x(iVar);
            }
            a0.a().e(iVar);
        }
        b.a.a.b.y.v("CarPlay设备列表", "页面切换", true);
    }

    @Override // com.ici.connectivity.fragment.BaseWallPaperFragment, androidx.fragment.app.Fragment
    public void onResume() {
        b.a.a.b.y.q("Connectivity:CarPlayDeviceListFragment", "[super.onResume][BEFOR][TIME:");
        super.onResume();
        Log.i("Connectivity:CarPlayDeviceListFragment", "onResume");
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        if (this.mPresenter == null) {
            this.mPresenter = new i(getMContext());
        }
        this.mPresenter.j(this);
        initDataAdapter();
        y.l(getMContext()).z();
        b.a.a.b.y.q("Connectivity:CarPlayDeviceListFragment", "[super.onResume][AFTER][TIME:");
        b.a.a.b.y.u("CarPlay设备列表", "用户点击", true);
    }

    @Override // b.d.b.d.c
    public void updateDeviceConnectState(DeviceInfoBean deviceInfoBean, int i, int i2) {
    }

    @Override // b.d.b.d.c
    public void updateDeviceList(List<DeviceInfoBean> list) {
        boolean z;
        Log.i("Connectivity:CarPlayDeviceListFragment", "updateDeviceList IN");
        if (list != null) {
            if (this.mListState && list.size() == 0) {
                w wVar = d0.m().f327b;
                if (wVar != null) {
                    z = wVar.v;
                } else {
                    Log.e("Connectivity:DeviceManager", "mDeviceManagerModel == null");
                    z = false;
                }
                if (!z) {
                    e0.d(getMContext()).p(1, e0.c());
                }
            }
            w wVar2 = d0.m().f327b;
            if (wVar2 != null) {
                wVar2.v = false;
            } else {
                Log.e("Connectivity:DeviceManager", "mDeviceManagerModel == null");
            }
            this.mDeviceList.clear();
            this.mDeviceList.addAll(list);
            Log.i("Connectivity:CarPlayDeviceListFragment", "mDeviceList size = " + this.mDeviceList.size());
            showDeviceInfoListData();
        }
    }

    @Override // com.ici.app.base.BaseFragment
    public void setPresenter(b bVar) {
        this.mPresenter = (i) bVar;
    }
}
