package com.ici.connectivity.buick_ui.fragment;

import android.bluetooth.BluetoothDevice;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.FastScroller;
import androidx.recyclerview.widget.RecyclerView;
import b.a.a.b.h;
import b.a.a.b.y;
import b.d.b.d.l;
import b.d.b.d.m;
import b.d.b.f.q;
import b.d.c.f.z;
import com.android.widget_extra.toast.ICIToast;
import com.ici.app.base.BaseFragment;
import com.ici.connectivity.R;
import com.ici.connectivity.adapter.HorizontalItemDecoration;
import com.ici.connectivity.adapter.TopLayoutManager;
import com.ici.connectivity.buick_ui.adapter.Buick_PairDeviceListAdapter;
import com.ici.connectivity.buick_ui.fragment.Buick_PairedDeviceListFragment;
import com.ici.connectivity.view.CardListIndicatorView;
import com.ici.connectivity.view.SlideInRightListAni;
import com.ici.connectivitylib.bean.DeviceInfoBean;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import jp.wasabeef.recyclerview.animators.FadeInAnimator;

/* loaded from: classes.dex */
public class Buick_PairedDeviceListFragment extends BaseFragment<l> implements m {
    public static final String TAG = "PairedDeviceListFragment";
    public Buick_PairDeviceListAdapter mAdapter;
    public b mDeviceDetailListener;
    public HorizontalItemDecoration mHorizontalItemDecoration;
    public CardListIndicatorView mIndicatorView;
    public q mPairedListPresenter;
    public RecyclerView mRecyclerView;
    public TopLayoutManager mTopLayoutManager;
    public AtomicBoolean mAnimationStatus = new AtomicBoolean();
    public List<DeviceInfoBean> mPairedDevices = new ArrayList();
    public int mClickPosition = -1;
    public boolean needRefrshData = true;

    /* loaded from: classes.dex */
    public class a implements Buick_PairDeviceListAdapter.c {
        public a() {
        }
    }

    /* loaded from: classes.dex */
    public interface b {
        void onDetailBtClicked(int i, DeviceInfoBean deviceInfoBean);
    }

    private void doDeviceListItemAnimator(int i) {
        if (this.mAdapter.getData().size() == 1) {
            Log.i("PairedDeviceListFragment", "doDeviceListItemAnimator mAdapter.getData().size() = 1");
            return;
        }
        b.a.b.a.a.i("doDeviceListItemAnimator in state : ", i, "PairedDeviceListFragment");
        this.mAnimationStatus.set(true);
        FadeInAnimator fadeInAnimator = new FadeInAnimator();
        fadeInAnimator.setRemoveDuration(80L);
        this.mRecyclerView.setItemAnimator(fadeInAnimator);
        DeviceInfoBean remove = this.mAdapter.remove(this.mClickPosition);
        if (remove == null) {
            Log.i("PairedDeviceListFragment", "updateDeviceConnectState remove deviceInfoBean is null");
            this.mAnimationStatus.set(false);
            return;
        }
        remove.setConnectState(2);
        SlideInRightListAni slideInRightListAni = new SlideInRightListAni();
        slideInRightListAni.setAddDuration(350L);
        this.mRecyclerView.setItemAnimator(slideInRightListAni);
        remove.setConnectState(i);
        this.mAdapter.add(remove);
        this.mClickPosition = 0;
        this.mRecyclerView.postDelayed(new Runnable() { // from class: b.d.b.c.b.m
            @Override // java.lang.Runnable
            public final void run() {
                Buick_PairedDeviceListFragment.this.a();
            }
        }, 430L);
    }

    private int getSelectItemPosition(BluetoothDevice bluetoothDevice) {
        if (bluetoothDevice == null || this.mAdapter.getData() == null) {
            return -1;
        }
        for (int i = 0; i < this.mAdapter.getData().size(); i++) {
            if (bluetoothDevice.getAddress().equals(this.mAdapter.getData().get(i).getMacAddress())) {
                return i;
            }
        }
        return -1;
    }

    private int getSelectItemPositionBySerNum(String str) {
        if (TextUtils.isEmpty(str) || this.mAdapter.getData() == null) {
            return -1;
        }
        for (int i = 0; i < this.mAdapter.getData().size(); i++) {
            if (str.equals(this.mAdapter.getData().get(i).getSerialNum())) {
                return i;
            }
        }
        return -1;
    }

    private void initDataAdapter() {
        if (this.mAdapter == null) {
            TopLayoutManager topLayoutManager = new TopLayoutManager(getMContext(), 0, false);
            this.mTopLayoutManager = topLayoutManager;
            this.mRecyclerView.setLayoutManager(topLayoutManager);
            HorizontalItemDecoration horizontalItemDecoration = new HorizontalItemDecoration(20, getMContext());
            this.mHorizontalItemDecoration = horizontalItemDecoration;
            this.mRecyclerView.addItemDecoration(horizontalItemDecoration);
            Buick_PairDeviceListAdapter buick_PairDeviceListAdapter = new Buick_PairDeviceListAdapter();
            this.mAdapter = buick_PairDeviceListAdapter;
            this.mRecyclerView.setAdapter(buick_PairDeviceListAdapter);
        }
        this.mAdapter.setItemClickListener(new a());
        this.mRecyclerView.setOnScrollChangeListener(new View.OnScrollChangeListener() { // from class: b.d.b.c.b.l
            @Override // android.view.View.OnScrollChangeListener
            public final void onScrollChange(View view, int i, int i2, int i3, int i4) {
                Buick_PairedDeviceListFragment.this.b(view, i, i2, i3, i4);
            }
        });
        showPairedListData();
    }

    public static Buick_PairedDeviceListFragment newInstance() {
        return new Buick_PairedDeviceListFragment();
    }

    private void showPairedListData() {
        List<DeviceInfoBean> list = this.mPairedDevices;
        if (list != null) {
            int size = list.size();
            b.a.b.a.a.i("count = ", size, "PairedDeviceListFragment");
            if (size <= 4) {
                this.mIndicatorView.setVisibility(4);
            } else {
                this.mIndicatorView.setVisibility(0);
                int computeHorizontalScrollOffset = this.mRecyclerView.computeHorizontalScrollOffset();
                StringBuilder f = b.a.b.a.a.f("scrollOffset -- ", computeHorizontalScrollOffset, "  size -- ");
                f.append(this.mAdapter.getItemCount());
                Log.i("PairedDeviceListFragment", f.toString());
                if (computeHorizontalScrollOffset >= 0) {
                    this.mIndicatorView.a(computeHorizontalScrollOffset, this.mAdapter.getItemCount() * 410);
                }
                this.mIndicatorView.b(FastScroller.HIDE_DELAY_AFTER_DRAGGING_MS, size * 410);
            }
            StringBuilder e = b.a.b.a.a.e("showPairedListData  刷新列表数据 mPairedDevices :\u3000");
            e.append(this.mPairedDevices.toString());
            Log.i("PairedDeviceListFragment", e.toString());
            this.mRecyclerView.setItemAnimator(null);
            this.mAdapter.updateData(this.mPairedDevices);
            return;
        }
        y.f("PairedDeviceListFragment", "mPairedDevices = null ");
    }

    public /* synthetic */ void a() {
        this.mAnimationStatus.set(false);
        this.mRecyclerView.smoothScrollToPosition(0);
        this.mPairedDevices.clear();
        this.mPairedDevices.addAll(this.mAdapter.getData());
        this.mAdapter = null;
        this.mRecyclerView.removeItemDecoration(this.mHorizontalItemDecoration);
        initDataAdapter();
        CardListIndicatorView cardListIndicatorView = this.mIndicatorView;
        if (cardListIndicatorView != null && cardListIndicatorView.getVisibility() == 0) {
            this.mIndicatorView.a(0, this.mAdapter.getItemCount() * 410);
        }
        if (this.mAdapter != null) {
            b.d.c.f.y.l(getContext()).B(this.mAdapter.getData());
        }
    }

    public void b(View view, int i, int i2, int i3, int i4) {
        int computeHorizontalScrollOffset = this.mRecyclerView.computeHorizontalScrollOffset();
        b.a.b.a.a.r(this.mPairedDevices, b.a.b.a.a.f("scrollOffset -- ", computeHorizontalScrollOffset, "  size -- "), "PairedDeviceListFragment");
        Buick_PairDeviceListAdapter buick_PairDeviceListAdapter = this.mAdapter;
        if (buick_PairDeviceListAdapter != null && buick_PairDeviceListAdapter.getData() != null && this.mAdapter.getData().size() <= 4) {
            this.mIndicatorView.setVisibility(4);
        } else if (computeHorizontalScrollOffset >= 0) {
            this.mIndicatorView.setVisibility(0);
            this.mIndicatorView.a(computeHorizontalScrollOffset, this.mPairedDevices.size() * 410);
            StringBuilder sb = new StringBuilder();
            sb.append("mIndicatorView --   size -- ");
            b.a.b.a.a.r(this.mPairedDevices, sb, "PairedDeviceListFragment");
        }
    }

    public void d(String str, int i, int i2) {
        if (this.mPairedDevices == null || this.mAdapter == null) {
            return;
        }
        Log.i("PairedDeviceListFragment", "CarPlay 刷新信号及电池电量状态 macAddress : " + str + "---电量 : " + i + "----信号量 : " + i2);
        int selectItemPosition = getSelectItemPosition(str);
        if (selectItemPosition != -1 && i > 0) {
            this.mRecyclerView.setItemAnimator(null);
            Log.i("PairedDeviceListFragment", "updateDeviceConnectState notifyItemChanged  position3: " + selectItemPosition);
            this.mAdapter.notifyItemChanged(selectItemPosition, 1);
            return;
        }
        Log.i("PairedDeviceListFragment", "position = -1 or battery = 0");
    }

    @Override // com.ici.app.base.BaseFragment
    public int getLayout() {
        return R.layout.fragment_paired_list_buick;
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x002b A[Catch: Exception -> 0x0068, TryCatch #0 {Exception -> 0x0068, blocks: (B:3:0x0003, B:5:0x0007, B:7:0x000f, B:9:0x001b, B:10:0x0025, B:12:0x002b, B:14:0x0038, B:16:0x0042), top: B:22:0x0003 }] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public boolean hasConnettingDevice() {
        /*
            r6 = this;
            java.lang.String r0 = "PairedDeviceListFragment"
            r1 = 0
            com.ici.connectivity.buick_ui.adapter.Buick_PairDeviceListAdapter r2 = r6.mAdapter     // Catch: java.lang.Exception -> L68
            if (r2 == 0) goto L67
            com.ici.connectivity.buick_ui.adapter.Buick_PairDeviceListAdapter r2 = r6.mAdapter     // Catch: java.lang.Exception -> L68
            java.util.List r2 = r2.getData()     // Catch: java.lang.Exception -> L68
            if (r2 == 0) goto L67
            com.ici.connectivity.buick_ui.adapter.Buick_PairDeviceListAdapter r2 = r6.mAdapter     // Catch: java.lang.Exception -> L68
            java.util.List r2 = r2.getData()     // Catch: java.lang.Exception -> L68
            int r2 = r2.size()     // Catch: java.lang.Exception -> L68
            if (r2 <= 0) goto L67
            com.ici.connectivity.buick_ui.adapter.Buick_PairDeviceListAdapter r2 = r6.mAdapter     // Catch: java.lang.Exception -> L68
            java.util.List r2 = r2.getData()     // Catch: java.lang.Exception -> L68
            java.util.Iterator r2 = r2.iterator()     // Catch: java.lang.Exception -> L68
        L25:
            boolean r3 = r2.hasNext()     // Catch: java.lang.Exception -> L68
            if (r3 == 0) goto L67
            java.lang.Object r3 = r2.next()     // Catch: java.lang.Exception -> L68
            com.ici.connectivitylib.bean.DeviceInfoBean r3 = (com.ici.connectivitylib.bean.DeviceInfoBean) r3     // Catch: java.lang.Exception -> L68
            int r4 = r3.getConnectState()     // Catch: java.lang.Exception -> L68
            r5 = 1
            if (r4 == r5) goto L42
            com.yfve.ici.app.carplay.CarPlayProxy r4 = com.yfve.ici.app.carplay.CarPlayProxy.getInstance()     // Catch: java.lang.Exception -> L68
            int r4 = r4.getCPConnectStatus()     // Catch: java.lang.Exception -> L68
            if (r4 != r5) goto L25
        L42:
            java.lang.StringBuilder r2 = new java.lang.StringBuilder     // Catch: java.lang.Exception -> L68
            r2.<init>()     // Catch: java.lang.Exception -> L68
            java.lang.String r4 = "当前已有在连接中状态的设备 deviceName ："
            r2.append(r4)     // Catch: java.lang.Exception -> L68
            java.lang.String r4 = r3.getDeviceName()     // Catch: java.lang.Exception -> L68
            r2.append(r4)     // Catch: java.lang.Exception -> L68
            java.lang.String r4 = " connected : "
            r2.append(r4)     // Catch: java.lang.Exception -> L68
            int r3 = r3.getConnectState()     // Catch: java.lang.Exception -> L68
            r2.append(r3)     // Catch: java.lang.Exception -> L68
            java.lang.String r2 = r2.toString()     // Catch: java.lang.Exception -> L68
            android.util.Log.i(r0, r2)     // Catch: java.lang.Exception -> L68
            return r5
        L67:
            return r1
        L68:
            r2 = move-exception
            java.lang.String r3 = "hasConnettingDevice exception"
            java.lang.StringBuilder r3 = b.a.b.a.a.e(r3)
            java.lang.String r2 = r2.getMessage()
            r3.append(r2)
            java.lang.String r2 = r3.toString()
            android.util.Log.i(r0, r2)
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: com.ici.connectivity.buick_ui.fragment.Buick_PairedDeviceListFragment.hasConnettingDevice():boolean");
    }

    @Override // com.ici.app.base.BaseFragment
    public void initDatas() {
    }

    @Override // com.ici.app.base.BaseFragment
    public void initViews() {
        this.mIndicatorView = (CardListIndicatorView) this.mContentView.findViewById(R.id.paired_list_indicator);
        this.mRecyclerView = (RecyclerView) this.mContentView.findViewById(R.id.paired_recycle_view);
    }

    @Override // b.d.b.d.m
    public void onCarPlayDeviceSupportStatusChanged(DeviceInfoBean deviceInfoBean, boolean z) {
        RecyclerView recyclerView;
        Log.i("PairedDeviceListFragment", "onCarPlayDeviceSupportStatusChanged   bean : " + deviceInfoBean + "------isSupport : " + z);
        final FragmentActivity activity = getActivity();
        if (activity == null || z || deviceInfoBean.getIsSupportCarPlay() != 0 || (recyclerView = this.mRecyclerView) == null) {
            return;
        }
        recyclerView.post(new Runnable() { // from class: b.d.b.c.b.k
            @Override // java.lang.Runnable
            public final void run() {
                ICIToast.makeToastOnlyText(r0, activity.getString(R.string.cp_device_no_support), 1).show();
            }
        });
        List<DeviceInfoBean> list = this.mPairedDevices;
        if (list != null) {
            list.clear();
        }
        this.mPairedDevices = this.mPairedListPresenter.h();
        showPairedListData();
    }

    @Override // com.ici.app.base.BaseFragment, androidx.fragment.app.Fragment
    public void onHiddenChanged(boolean z) {
        super.onHiddenChanged(z);
        this.mAnimationStatus.set(false);
        Log.i("PairedDeviceListFragment", "已配对列表界面已隐藏 -- " + z);
        if (!z) {
            b.d.c.c.a.f309a = 1;
            Log.i("PairedDeviceListFragment", "显示 CURRENT_PAGE -- 已配对列表界面");
            if (this.needRefrshData) {
                Log.i("PairedDeviceListFragment", "onHiddenChanged 主动获取已配对设备");
                if (this.mPairedDevices.size() > 0) {
                    this.mPairedDevices.clear();
                }
                this.mPairedDevices.addAll(this.mPairedListPresenter.h());
                showPairedListData();
                return;
            }
            return;
        }
        Log.i("PairedDeviceListFragment", "onHiddenChanged needRefrshData = true");
        this.needRefrshData = true;
    }

    @Override // androidx.fragment.app.Fragment
    public void onPause() {
        super.onPause();
        Log.i("PairedDeviceListFragment", "onPause");
        if (this.mAdapter != null) {
            b.d.c.f.y.l(getContext()).B(this.mAdapter.getData());
        }
        q qVar = this.mPairedListPresenter;
        if (qVar != null) {
            qVar.j();
        }
        z.b().c("com.ici.connectivity.activity.ConnectivityActivity", "主界面", "我的电话界面", "用户点击");
    }

    @Override // androidx.fragment.app.Fragment
    public void onResume() {
        y.q("PairedDeviceListFragment", "[super.onResume][BEFOR][TIME:");
        super.onResume();
        if (this.mPairedListPresenter == null) {
            this.mPairedListPresenter = new q(getMContext());
        }
        this.mPairedListPresenter.i(this);
        List<DeviceInfoBean> list = this.mPairedDevices;
        if (list != null && list.size() == 0) {
            this.mPairedDevices.addAll(this.mPairedListPresenter.h());
        }
        initDataAdapter();
        z.b().c("com.ici.connectivity.activity.ConnectivityActivity", "主界面", "我的电话界面", "用户点击");
        y.q("PairedDeviceListFragment", "[super.onResume][AFTER][TIME:");
    }

    public void setDeviceDetailListener(b bVar) {
        this.mDeviceDetailListener = bVar;
    }

    public void setPairedDevices(List<DeviceInfoBean> list) {
        if (list == null || list.size() <= 0) {
            return;
        }
        List<DeviceInfoBean> list2 = this.mPairedDevices;
        if (list2 == null) {
            this.mPairedDevices = new ArrayList();
        } else {
            list2.clear();
        }
        StringBuilder e = b.a.b.a.a.e("setPairedDevices = ");
        e.append(list.toString());
        Log.i("PairedDeviceListFragment", e.toString());
        this.needRefrshData = false;
        this.mPairedDevices.addAll(list);
    }

    @Override // b.d.b.d.m
    public void showDeviceDetail(int i, DeviceInfoBean deviceInfoBean) {
        b bVar = this.mDeviceDetailListener;
        if (bVar != null) {
            bVar.onDetailBtClicked(i, deviceInfoBean);
        }
    }

    @Override // b.d.b.d.m
    public void updateA2dpState(BluetoothDevice bluetoothDevice, int i) {
        if (this.mAnimationStatus.get()) {
            Log.i("PairedDeviceListFragment", "updateDeviceConnectState mAnimationStatus  position6 当前在执行动画,不刷新item");
        } else if (this.mPairedDevices == null || this.mAdapter == null) {
        } else {
            StringBuilder e = b.a.b.a.a.e("刷新a2dp协议状态 device :");
            e.append(bluetoothDevice.toString());
            Log.i("PairedDeviceListFragment", e.toString());
            int selectItemPosition = getSelectItemPosition(bluetoothDevice);
            if (selectItemPosition != -1) {
                this.mRecyclerView.setItemAnimator(null);
                Log.i("PairedDeviceListFragment", "updateDeviceConnectState notifyItemChanged  position6: " + selectItemPosition);
                this.mAdapter.notifyItemChanged(selectItemPosition, 1);
                return;
            }
            Log.i("PairedDeviceListFragment", "position = -1");
        }
    }

    @Override // b.d.b.d.m
    public void updateAllPairedBtList(List<DeviceInfoBean> list) {
        if (list != null) {
            b.a.b.a.a.r(list, b.a.b.a.a.e("刷新已配对列表  size = "), "PairedDeviceListFragment");
            List<DeviceInfoBean> list2 = this.mPairedDevices;
            if (list2 != null) {
                list2.clear();
            }
            this.mPairedDevices = list;
        } else {
            List<DeviceInfoBean> list3 = this.mPairedDevices;
            if (list3 != null) {
                list3.clear();
            }
            this.mPairedDevices = this.mPairedListPresenter.h();
        }
        showPairedListData();
    }

    @Override // b.d.b.d.m
    public void updateDeviceConnectState(h hVar, int i) {
        if (hVar == null || this.mPairedDevices == null || this.mAdapter == null) {
            return;
        }
        StringBuilder e = b.a.b.a.a.e("刷新设备连接状态 deviceName :");
        e.append(hVar.f);
        e.append("  state -- ");
        e.append(i);
        e.append("--------mClickPosition :");
        b.a.b.a.a.n(e, this.mClickPosition, "PairedDeviceListFragment");
        if (i == 2) {
            int size = this.mAdapter.getData().size();
            List<DeviceInfoBean> h = this.mPairedListPresenter.h();
            int size2 = h.size();
            if (this.mAdapter.getData().size() != size2) {
                Log.i("PairedDeviceListFragment", "updateDeviceConnectState----->uiSize : " + size + " ------>dbSize : " + size2 + " -----当前显示的设备列表数量与实际本地缓存列表数量不一致.根据本地数据列表数据,重新刷新一次设备列表UI");
                setPairedDevices(h);
                showPairedListData();
                return;
            }
        }
        int selectItemPosition = getSelectItemPosition(hVar.j());
        if (selectItemPosition == -1) {
            selectItemPosition = getSelectItemPositionBySerNum(hVar.j());
        }
        this.mAdapter.updateConnectState(selectItemPosition, i);
        if (i == 2) {
            int i2 = this.mClickPosition;
            if (i2 > 0 && i2 < this.mAdapter.getData().size()) {
                doDeviceListItemAnimator(i);
            } else if (this.mAnimationStatus.get()) {
                Log.i("PairedDeviceListFragment", "updateDeviceConnectState mAnimationStatus  position1 当前在执行动画,不刷新item");
            } else {
                b.a.b.a.a.i("updateDeviceConnectState find device position in deviceList--> position : ", selectItemPosition, "PairedDeviceListFragment");
                if (selectItemPosition != -1 && this.mClickPosition != 0) {
                    this.mClickPosition = selectItemPosition;
                    doDeviceListItemAnimator(i);
                } else if (this.mAdapter.getData().size() == 0) {
                    Log.i("PairedDeviceListFragment", "updateDeviceConnectState mAdapter.getData().size() == 0");
                } else {
                    this.mAdapter.getData().get(0).setConnectState(2);
                    this.mRecyclerView.setItemAnimator(null);
                    Log.i("PairedDeviceListFragment", "updateDeviceConnectState notifyItemChanged  position1 : 0");
                    this.mAdapter.notifyItemChanged(0);
                }
            }
        } else if (this.mAnimationStatus.get()) {
            Log.i("PairedDeviceListFragment", "updateDeviceConnectState mAnimationStatus  position2 当前在执行动画,不刷新item");
        } else {
            this.mRecyclerView.setItemAnimator(null);
            Log.i("PairedDeviceListFragment", "updateDeviceConnectState notifyItemChanged  position2 : " + selectItemPosition);
            this.mAdapter.notifyItemChanged(selectItemPosition, 1);
        }
    }

    @Override // b.d.b.d.m
    public void updateFirstConnectSetting(String str, String str2, boolean z) {
        if (this.mAnimationStatus.get()) {
            Log.i("PairedDeviceListFragment", "updateDeviceConnectState mAnimationStatus  position7 当前在执行动画,不刷新item");
            return;
        }
        Log.i("PairedDeviceListFragment", "刷新首选连接设置");
        int selectItemPosition = getSelectItemPosition(str);
        if (selectItemPosition == -1) {
            selectItemPosition = getSelectItemPositionBySerNum(str2);
        }
        if (selectItemPosition == -1) {
            Log.i("PairedDeviceListFragment", "刷新首选连接设置异常 position 获取失败");
            return;
        }
        Log.i("PairedDeviceListFragment", "position -- " + selectItemPosition + "  isFirstConnect -- " + z);
        this.mAdapter.getData().get(selectItemPosition).setConnectPreferences(z ? 1 : 0);
        this.mRecyclerView.setItemAnimator(null);
        Log.i("PairedDeviceListFragment", "updateDeviceConnectState notifyItemChanged  position7: " + selectItemPosition);
        this.mAdapter.notifyItemChanged(selectItemPosition, 1);
    }

    @Override // b.d.b.d.m
    public void updateHfpState(BluetoothDevice bluetoothDevice, int i) {
        if (this.mAnimationStatus.get()) {
            Log.i("PairedDeviceListFragment", "updateDeviceConnectState mAnimationStatus  position5 当前在执行动画,不刷新item");
        } else if (this.mPairedDevices == null || this.mAdapter == null) {
        } else {
            StringBuilder e = b.a.b.a.a.e("刷新hfp协议状态 device : ");
            e.append(bluetoothDevice.toString());
            Log.i("PairedDeviceListFragment", e.toString());
            int selectItemPosition = getSelectItemPosition(bluetoothDevice);
            if (selectItemPosition != -1) {
                this.mRecyclerView.setItemAnimator(null);
                Log.i("PairedDeviceListFragment", "updateDeviceConnectState notifyItemChanged  position5: " + selectItemPosition);
                this.mAdapter.notifyItemChanged(selectItemPosition, 1);
                return;
            }
            Log.i("PairedDeviceListFragment", "position = -1");
        }
    }

    @Override // b.d.b.d.m
    public void updateSignalAndBattery(BluetoothDevice bluetoothDevice, int i, int i2) {
        if (this.mAnimationStatus.get()) {
            Log.i("PairedDeviceListFragment", "updateDeviceConnectState mAnimationStatus  position4 当前在执行动画,不刷新item");
        } else if (this.mPairedDevices == null || this.mAdapter == null) {
        } else {
            StringBuilder e = b.a.b.a.a.e("刷新信号及电池电量状态 device : ");
            e.append(bluetoothDevice.toString());
            Log.i("PairedDeviceListFragment", e.toString());
            int selectItemPosition = getSelectItemPosition(bluetoothDevice);
            if (selectItemPosition != -1) {
                this.mRecyclerView.setItemAnimator(null);
                Log.i("PairedDeviceListFragment", "updateDeviceConnectState notifyItemChanged  position4: " + selectItemPosition);
                this.mAdapter.notifyItemChanged(selectItemPosition, 1);
                return;
            }
            Log.i("PairedDeviceListFragment", "position = -1");
        }
    }

    @Override // com.ici.app.base.BaseFragment
    public void setPresenter(l lVar) {
        this.mPairedListPresenter = (q) lVar;
    }

    private int getSelectItemPosition(String str) {
        if (TextUtils.isEmpty(str) || this.mAdapter.getData() == null) {
            return -1;
        }
        for (int i = 0; i < this.mAdapter.getData().size(); i++) {
            if (str.equals(this.mAdapter.getData().get(i).getMacAddress())) {
                return i;
            }
        }
        return -1;
    }

    @Override // b.d.b.d.m
    public void updateSignalAndBattery(final String str, String str2, final int i, final int i2) {
        if (this.mRecyclerView != null) {
            if (this.mAnimationStatus.get()) {
                Log.i("PairedDeviceListFragment", "updateDeviceConnectState mAnimationStatus  position3 当前在执行动画,不刷新item");
                return;
            }
            b.d.c.f.y.l(getContext()).u();
            this.mRecyclerView.post(new Runnable() { // from class: b.d.b.c.b.j
                @Override // java.lang.Runnable
                public final void run() {
                    Buick_PairedDeviceListFragment.this.d(str, i2, i);
                }
            });
        }
    }
}
