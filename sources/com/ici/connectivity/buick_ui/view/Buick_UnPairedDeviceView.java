package com.ici.connectivity.buick_ui.view;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import com.ici.connectivity.R;
import com.yfve.ici.appcustomviewlib.textview.CommonTextView;

/* loaded from: classes.dex */
public class Buick_UnPairedDeviceView extends RelativeLayout {

    /* renamed from: b  reason: collision with root package name */
    public CommonTextView f520b;
    public RelativeLayout c;
    public ImageView d;

    public Buick_UnPairedDeviceView(Context context) {
        super(context);
    }

    @Override // android.view.View
    public void onFinishInflate() {
        super.onFinishInflate();
        this.f520b = (CommonTextView) findViewById(R.id.tv_unpaired_device_name);
        this.c = (RelativeLayout) findViewById(R.id.rly_connect_layout);
        this.d = (ImageView) findViewById(R.id.iv_unpair_bt_phone);
    }

    public void setDeviceName(String str) {
        if (TextUtils.isEmpty(str)) {
            return;
        }
        this.f520b.setText(str);
    }

    public Buick_UnPairedDeviceView(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
    }

    public Buick_UnPairedDeviceView(Context context, AttributeSet attributeSet, int i) {
        super(context, attributeSet, i);
    }
}
