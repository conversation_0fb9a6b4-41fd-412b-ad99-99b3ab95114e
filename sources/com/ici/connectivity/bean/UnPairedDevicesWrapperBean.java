package com.ici.connectivity.bean;

import b.a.a.b.h;
import java.io.Serializable;

/* loaded from: classes.dex */
public class UnPairedDevicesWrapperBean implements Serializable {
    public h mCachedBluetoothDevice;
    public int mCurrentStatus;

    public h getCachedBluetoothDevice() {
        return this.mCachedBluetoothDevice;
    }

    public int getCurrentStatus() {
        return this.mCurrentStatus;
    }

    public void setCachedBluetoothDevice(h hVar) {
        this.mCachedBluetoothDevice = hVar;
    }

    public void setCurrentStatus(int i) {
        this.mCurrentStatus = i;
    }
}
