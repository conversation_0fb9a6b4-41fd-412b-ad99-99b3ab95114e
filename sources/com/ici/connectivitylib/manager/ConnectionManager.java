package com.ici.connectivitylib.manager;

import android.annotation.SuppressLint;
import android.content.Context;
import android.net.wifi.WifiManager;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import androidx.appcompat.widget.TooltipCompatHandler;
import androidx.lifecycle.GenericLifecycleObserver;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.recyclerview.widget.ItemTouchHelper;
import b.a.a.b.h;
import b.d.c.f.a0;
import b.d.c.f.b0;
import b.d.c.f.c0;
import b.d.c.f.d0;
import b.d.c.f.e0;
import b.d.c.f.g0;
import b.d.c.f.x;
import b.d.c.f.y;
import b.d.c.f.z;
import b.d.c.g.w;
import b.d.c.h.f.c;
import com.android.widget_extra.toast.ICIToast;
import com.ici.connectivitylib.R$string;
import com.ici.connectivitylib.bean.CarPlayDeviceInfo;
import com.ici.connectivitylib.bean.DeviceInfoBean;
import com.ici.connectivitylib.connection.CarPlayConnection;
import com.ici.connectivitylib.connection.CarlifeConnection;
import com.ici.connectivitylib.constant.DMConstant$BusEvent;
import com.ici.connectivitylib.manager.ConnectionManager;
import com.yfve.ici.app.carlife.CarlifeProxy;
import com.yfve.ici.app.carplay.CPDeviceInfo;
import com.yfve.ici.app.carplay.CarPlayProxy;
import com.yfve.ici.app.carplay.ICPConnectStatusCallback;
import com.yfve.ici.app.carplay.ICPCtlCmdCallback;
import com.yfve.ici.app.carplay.ICPDeviceInfoCallback;
import com.yfve.ici.app.carplay.ICPOOBPairingInfoCallback;
import com.yfve.ici.app.carplay.IWirelessCPBTServerConnectCallback;
import com.yfve.ici.service.contanst.ServiceConstant;
import com.yfve.ici.service.servicemgr.IServiceConnChangeListener;
import com.yfve.overalldatatracking.CommDataTrack;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.function.Consumer;
import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class ConnectionManager implements GenericLifecycleObserver {
    public static final int CONNECT_CARPLAY_DEVICE = 105;
    public static final int CONNECT_SECOND_BT_DEVICE = 104;
    public static final int HIDE_CARPLAY_WINDOW = 101;
    public static final int RECOVERY_CONNECT_FAIL_RECONNECT = 103;
    public static final int RE_CONNECT_SECOND_BT_DEVICE_MAX_COUNT = 5;
    public static final int SHOW_CONNECT_CONFIRM = 107;
    public static final int SHOW_CONNECT_FAIL = 102;
    public static final String TAG = "Connectivity:ConnectionManager";
    public static final int UPDATE_CARPLAY_OOB = 106;
    @SuppressLint({"StaticFieldLeak"})
    public static volatile ConnectionManager mConnectionManager;
    public Context mContext;
    public WifiManager mWifiManager = null;
    public b.d.c.d.c mDbManager = null;
    public DeviceInfoBean mCurConnDevInfo = null;
    public CarPlayConnection mCarPlayConnection = null;
    public CarlifeConnection mCarlifeConnection = null;
    public DeviceInfoBean mCurDeviceInfoBean = null;
    public h mCachedBluetoothDevice = null;
    public int mNeedConnectType = 0;
    public Lifecycle.Event mLifecycleState = Lifecycle.Event.ON_DESTROY;
    public boolean mConnectFailReconnectFlag = false;
    public boolean mCarPlaySwitchFlag = false;
    public boolean mCarLifeDisconnect = false;
    public boolean mCarPlayDisconnect = false;
    public boolean mBtServerConnectCarPlay = false;
    public boolean mCarPlayReconnnectState = false;
    public String mCarPlayReconnectSerialNum = null;
    public final CarPlayDeviceInfo mCarPlayDeviceInfo = new CarPlayDeviceInfo();
    public boolean mAutoConnect = false;
    public boolean mBtServerFirstConnectCarPlay = true;
    public boolean mBtServerRequireConnectComfirm = false;
    public h mCurrentPairedDevice = null;
    public DeviceInfoBean mConnectingCarPlayDevice = null;
    public int mCurrentReConnectSecondBtDeviceCount = 0;
    public h mConnectingbluetoothDevice = null;
    public h mDisConnectBluetoothDevice = null;
    public boolean isCarPlayDisconnectByClickAdd = false;
    @SuppressLint({"HandlerLeak"})
    public final Handler mHandler = new a();
    public final CarlifeProxy.StateChangeListener mIClConnectStatusListener = new b();
    public WifiManager.SoftApCallback mSoftApCallback = new c();
    public final ICPConnectStatusCallback mICPConnectStatusCallback = new d();
    public ICPDeviceInfoCallback mICPDeviceInfoCallback = new ICPDeviceInfoCallback() { // from class: b.d.c.f.n
        @Override // com.yfve.ici.app.carplay.ICPDeviceInfoCallback
        public final void onDeviceInfoChanged(CPDeviceInfo cPDeviceInfo) {
            ConnectionManager.this.c(cPDeviceInfo);
        }
    };
    public ICPCtlCmdCallback mICPCtlCmdCallback = new e();
    public ICPOOBPairingInfoCallback mICPOOBPairingInfoCallback = new ICPOOBPairingInfoCallback() { // from class: b.d.c.f.p
        @Override // com.yfve.ici.app.carplay.ICPOOBPairingInfoCallback
        public final void onCarPlayOOBPairingInfo(String str) {
            ConnectionManager.this.d(str);
        }
    };
    public IWirelessCPBTServerConnectCallback mIWirelessCPBTServerConnectCallback = new IWirelessCPBTServerConnectCallback() { // from class: b.d.c.f.k
        @Override // com.yfve.ici.app.carplay.IWirelessCPBTServerConnectCallback
        public final void onConnectStatusChanged(boolean z, String str) {
            ConnectionManager.this.e(z, str);
        }
    };
    public IServiceConnChangeListener mCarPlayConnListener = new IServiceConnChangeListener() { // from class: b.d.c.f.l
        @Override // com.yfve.ici.service.servicemgr.IServiceConnChangeListener
        public final void onServiceConnChange(boolean z) {
            ConnectionManager.this.f(z);
        }
    };
    public IServiceConnChangeListener mCarLifeConnListener = new IServiceConnChangeListener() { // from class: b.d.c.f.m
        @Override // com.yfve.ici.service.servicemgr.IServiceConnChangeListener
        public final void onServiceConnChange(boolean z) {
            ConnectionManager.this.g(z);
        }
    };

    /* loaded from: classes.dex */
    public class a extends Handler {
        public a() {
        }

        /* JADX WARN: Removed duplicated region for block: B:40:0x0142  */
        /* JADX WARN: Removed duplicated region for block: B:41:0x0152  */
        @Override // android.os.Handler
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct code enable 'Show inconsistent code' option in preferences
        */
        public void handleMessage(@androidx.annotation.NonNull android.os.Message r7) {
            /*
                Method dump skipped, instructions count: 718
                To view this dump change 'Code comments level' option to 'DEBUG'
            */
            throw new UnsupportedOperationException("Method not decompiled: com.ici.connectivitylib.manager.ConnectionManager.a.handleMessage(android.os.Message):void");
        }
    }

    /* loaded from: classes.dex */
    public class b implements CarlifeProxy.StateChangeListener {
        public b() {
        }

        public /* synthetic */ void a() {
            x.a().f(ConnectionManager.this.mCurConnDevInfo, 4);
        }

        @Override // com.yfve.ici.app.carlife.CarlifeProxy.StateChangeListener
        public void onStateChanged(int i) {
            b.a.b.a.a.i("CL onStateChanged state: ", i, ConnectionManager.TAG);
            if (i == 1) {
                ConnectionManager.this.mHandler.sendEmptyMessage(105);
                String string = CarlifeProxy.getInstance().getDeviceInfo().getString(CarlifeProxy.EXTRA_DEV_INFO_SERIAL);
                DeviceInfoBean l = ConnectionManager.this.mDbManager.l(string);
                h j = y.l(ConnectionManager.this.mContext).j();
                if (j != null && l != null && l.getMacAddress() != null && j.j().equals(l.getMacAddress())) {
                    Log.i(ConnectionManager.TAG, "CL disconnect and BT connected is same device");
                    ConnectionManager.this.mDbManager.s(1, null, string);
                }
                a0.a().c(ConnectionManager.this.mCurDeviceInfoBean, 4, 4);
                c0.a().b(3);
                if (ConnectionManager.this.mCurConnDevInfo != null) {
                    b.d.c.h.f.c.n(new Runnable() { // from class: b.d.c.f.d
                        @Override // java.lang.Runnable
                        public final void run() {
                            ConnectionManager.b.this.a();
                        }
                    });
                }
                final List<DeviceInfoBean> l2 = d0.m().l(3);
                b.d.c.h.f.c.n(new Runnable() { // from class: b.d.c.f.e
                    @Override // java.lang.Runnable
                    public final void run() {
                        x.a().d(l2);
                    }
                });
            } else if (i == 2) {
                a0.a().c(ConnectionManager.this.mCurDeviceInfoBean, 4, 1);
            } else if (i == 3) {
                a0.a().c(ConnectionManager.this.mCurDeviceInfoBean, 4, 2);
            } else if (i != 4) {
            } else {
                EventBus.getDefault().post(DMConstant$BusEvent.CARLIFE_CONNECTED);
                String string2 = CarlifeProxy.getInstance().getDeviceInfo().getString(CarlifeProxy.EXTRA_DEV_INFO_SERIAL);
                String string3 = CarlifeProxy.getInstance().getDeviceInfo().getString(CarlifeProxy.EXTRA_DEV_INFO_NAME);
                Log.i(ConnectionManager.TAG, "CL onStateChanged serialNum:" + string2 + " ,name: " + string3);
                ConnectionManager.this.mDbManager.q(string2, string3);
                DeviceInfoBean l3 = ConnectionManager.this.mDbManager.l(CarlifeProxy.getInstance().getDeviceInfo().getString(CarlifeProxy.EXTRA_DEV_INFO_SERIAL));
                if (l3 != null) {
                    ConnectionManager.this.setCurConnDevInfo(l3);
                    h j2 = y.l(ConnectionManager.this.mContext).j();
                    if (j2 == null || l3.getMacAddress() == null || !j2.j().equals(l3.getMacAddress())) {
                        return;
                    }
                    Log.i(ConnectionManager.TAG, "CL and BT connected is same device");
                    ConnectionManager.this.mDbManager.s(5, null, string2);
                }
            }
        }
    }

    /* loaded from: classes.dex */
    public class c implements WifiManager.SoftApCallback {
        public c() {
        }

        public void onNumClientsChanged(int i) {
            b.a.b.a.a.i("onNumClientsChanged: ", i, ConnectionManager.TAG);
            List hotConnectedDevice = ConnectionManager.this.getHotConnectedDevice();
            if (!CarPlayProxy.getInstance().isCarPlaySessionActived()) {
                if (i == 0 || ConnectionManager.this.mCarPlayDeviceInfo == null || !ConnectionManager.this.mCarPlayDeviceInfo.getReconnect() || hotConnectedDevice == null || hotConnectedDevice.size() <= 0 || !hotConnectedDevice.contains(ConnectionManager.this.mCarPlayDeviceInfo.getWlanAddress()) || ConnectionManager.this.mDbManager.k(ConnectionManager.this.mCarPlayDeviceInfo.getMacAddress()) == null) {
                    return;
                }
                if (ConnectionManager.this.getConnectingbluetoothDevice() == null && CarPlayProxy.getInstance().getCPConnectStatus() != 1) {
                    List<h> m = y.l(ConnectionManager.this.mContext).m();
                    if (m.size() > 0) {
                        for (h hVar : m) {
                            if (ConnectionManager.this.mCarPlayDeviceInfo.getMacAddress().equals(hVar.j())) {
                                Log.i(ConnectionManager.TAG, "onNumClientsChanged reconnect wireless carplay");
                                CarPlayProxy.getInstance().connect(hVar.e, false);
                            }
                        }
                        return;
                    }
                    return;
                }
                Log.i(ConnectionManager.TAG, "onNumClientsChanged 有蓝牙或CarPlay正在连接");
                return;
            }
            Log.i(ConnectionManager.TAG, "onNumClientsChanged carplay connected");
            int connectType = CarPlayProxy.getInstance().getDeviceInfo().getConnectType();
            String wlanAddress = CarPlayProxy.getInstance().getDeviceInfo().getWlanAddress();
            Log.i(ConnectionManager.TAG, "onNumClientsChanged connectType:" + connectType + ",wlan:" + wlanAddress);
            if (connectType != 1) {
                if (i == 0) {
                    ConnectionManager.this.mCarPlayDisconnect = true;
                    CarPlayProxy.getInstance().disconnectCP();
                } else if (hotConnectedDevice != null && hotConnectedDevice.size() > 0 && !hotConnectedDevice.contains(wlanAddress.toLowerCase())) {
                    ConnectionManager.this.mCarPlayDisconnect = true;
                    CarPlayProxy.getInstance().disconnectCP();
                }
            }
        }

        public void onStateChanged(int i, int i2) {
        }
    }

    /* loaded from: classes.dex */
    public class d implements ICPConnectStatusCallback {
        public d() {
        }

        public /* synthetic */ void b() {
            x.a().f(ConnectionManager.this.mCurConnDevInfo, 4);
        }

        public /* synthetic */ void d() {
            e0.d(ConnectionManager.this.mContext).h(3);
        }

        @Override // com.yfve.ici.app.carplay.ICPConnectStatusCallback
        public void onConnectStatusChanged(int i) {
            b.a.b.a.a.i("CP onConnectStatusChanged state: ", i, ConnectionManager.TAG);
            if (ConnectionManager.this.mCurDeviceInfoBean != null) {
                StringBuilder e = b.a.b.a.a.e("mCurDeviceInfoBean: ");
                e.append(ConnectionManager.this.mCurDeviceInfoBean.toString());
                Log.i(ConnectionManager.TAG, e.toString());
            } else {
                Log.i(ConnectionManager.TAG, "mCurDeviceInfoBean: null");
            }
            if (1 == i) {
                a0.a().c(ConnectionManager.this.mCurDeviceInfoBean, 3, 1);
            } else if (2 == i) {
                ConnectionManager.this.mCurrentPairedDevice = null;
                ConnectionManager.this.mAutoConnect = false;
                EventBus.getDefault().post(DMConstant$BusEvent.CARPLAY_CONNECTED);
                d0 m = d0.m();
                if (m != null) {
                    Log.i("Connectivity:DeviceManager", "notifyCarPlayConnected called");
                    w wVar = m.f327b;
                    if (wVar != null) {
                        Log.i("Connectivity:DeviceManagerModel", "notifyCarPlayConnected");
                        if (wVar.B.hasMessages(1001)) {
                            wVar.B.removeMessages(1001);
                        }
                        if (wVar.B.hasMessages(1002)) {
                            wVar.B.removeMessages(1002);
                        }
                    } else {
                        Log.e("Connectivity:DeviceManager", "mDeviceManagerModel == null");
                    }
                    ConnectionManager.this.mHandler.sendEmptyMessage(101);
                    String usbSerialNumber = CarPlayProxy.getInstance().getDeviceInfo().getUsbSerialNumber();
                    String btMacAddress = CarPlayProxy.getInstance().getDeviceInfo().getBtMacAddress();
                    Log.i(ConnectionManager.TAG, "CP onConnectStatusChanged serialNum:" + usbSerialNumber + " ,mac: " + btMacAddress);
                    if (!usbSerialNumber.equals("UNKNOWN") && !btMacAddress.equals("UNKNOWN")) {
                        ConnectionManager.this.mDbManager.r(btMacAddress.toUpperCase(), usbSerialNumber);
                    }
                    ConnectionManager.this.mCurDeviceInfoBean.setSerialNum(usbSerialNumber);
                    ConnectionManager.this.mCurDeviceInfoBean.setMacAddress(btMacAddress.toUpperCase());
                    a0.a().c(ConnectionManager.this.mCurDeviceInfoBean, 3, 2);
                    int connectType = CarPlayProxy.getInstance().getDeviceInfo().getConnectType();
                    String wlanAddress = CarPlayProxy.getInstance().getDeviceInfo().getWlanAddress();
                    Log.i(ConnectionManager.TAG, "CP onConnectStatusChanged connectType:" + connectType + " wlan:" + wlanAddress);
                    if (connectType != 1) {
                        ConnectionManager.this.saveCarPlayConnectTypeAndWlanAddress(connectType, wlanAddress.toLowerCase(), btMacAddress.toUpperCase());
                    } else {
                        ConnectionManager.this.clearWirelessCarPlayReconnect();
                        b.d.c.h.f.c.n(new Runnable() { // from class: b.d.c.f.h
                            @Override // java.lang.Runnable
                            public final void run() {
                                x.a().d(null);
                            }
                        });
                    }
                    DeviceInfoBean deviceInfoBean = new DeviceInfoBean();
                    deviceInfoBean.setMacAddress(btMacAddress);
                    deviceInfoBean.setSerialNum(usbSerialNumber);
                    deviceInfoBean.setConnectState(2);
                    EventBus.getDefault().post(deviceInfoBean);
                    ConnectionManager.this.setCarPlayReconnectState(false);
                    ConnectionManager.this.setCarPlayReconnectSerialNum(null);
                } else {
                    throw null;
                }
            } else if (3 == i || 4 == i) {
                ConnectionManager.this.sendCarPlayConnectStatus(3);
                ConnectionManager.this.setConnectingCarPlayDevice(null);
                ConnectionManager.this.mCurrentPairedDevice = null;
                ConnectionManager.this.setConnectFailReconnectFlag(true);
                ConnectionManager.this.mHandler.sendEmptyMessageDelayed(103, ItemTouchHelper.Callback.DRAG_SCROLL_ACCELERATION_LIMIT_TIME_MS);
                ConnectionManager.this.mHandler.sendEmptyMessage(102);
                a0.a().c(ConnectionManager.this.mCurDeviceInfoBean, 3, 3);
                c0.a().b(1);
                ConnectionManager.this.setCarPlayReconnectState(false);
                ConnectionManager.this.setCarPlayReconnectSerialNum(null);
            } else if (5 == i) {
                ConnectionManager.this.sendCarPlayConnectStatus(4);
                ConnectionManager.this.setConnectingCarPlayDevice(null);
                ConnectionManager.this.mCurrentPairedDevice = null;
                ConnectionManager.this.mAutoConnect = false;
                if (ConnectionManager.this.mCurDeviceInfoBean != null) {
                    ConnectionManager.this.mCurDeviceInfoBean.setConnectState(4);
                }
                if (!ConnectionManager.this.mCarPlayDisconnect) {
                    ConnectionManager.this.mHandler.sendEmptyMessage(105);
                }
                c0.a().b(3);
                a0.a().c(ConnectionManager.this.mCurDeviceInfoBean, 3, 4);
                if (ConnectionManager.this.mCurConnDevInfo != null) {
                    b.d.c.h.f.c.n(new Runnable() { // from class: b.d.c.f.g
                        @Override // java.lang.Runnable
                        public final void run() {
                            ConnectionManager.d.this.b();
                        }
                    });
                }
                final List<DeviceInfoBean> l = d0.m().l(3);
                b.d.c.h.f.c.n(new Runnable() { // from class: b.d.c.f.f
                    @Override // java.lang.Runnable
                    public final void run() {
                        x.a().d(l);
                    }
                });
                if (ConnectionManager.this.mCachedBluetoothDevice != null) {
                    StringBuilder e2 = b.a.b.a.a.e("CP_STATUS_DISCONNECTED mCarPlaySwitchFlag: ");
                    e2.append(ConnectionManager.this.mCarPlaySwitchFlag);
                    Log.i(ConnectionManager.TAG, e2.toString());
                    if (!ConnectionManager.this.mCarPlaySwitchFlag) {
                        if (ConnectionManager.this.isCarPlayDisconnectByClickAdd) {
                            ConnectionManager.this.isCarPlayDisconnectByClickAdd = false;
                            Log.i(ConnectionManager.TAG, "CARPLAY DISCONNECT BY CLICK ADD");
                        } else {
                            Log.i(ConnectionManager.TAG, "CARPLAY DISCONNECT, REQUEST CONNECT BT");
                            int k = ConnectionManager.this.mCachedBluetoothDevice.k();
                            b.a.b.a.a.i("PAIR STATE === ", k, ConnectionManager.TAG);
                            if (12 == k) {
                                y.l(ConnectionManager.this.mContext).w(ConnectionManager.this.mCachedBluetoothDevice);
                            }
                        }
                    } else {
                        ConnectionManager.this.mCarPlaySwitchFlag = false;
                    }
                }
                c0.a().b(1);
                ConnectionManager.this.setCarPlayReconnectState(false);
                ConnectionManager.this.setCarPlayReconnectSerialNum(null);
                ConnectionManager.this.saveWirelessCarPlayReconnect();
            } else if (6 == i) {
                ConnectionManager.this.sendCarPlayConnectStatus(3);
                if (ConnectionManager.this.mCurrentPairedDevice != null) {
                    if (e0.d(ConnectionManager.this.mContext).i(3)) {
                        b.d.c.h.f.c.n(new Runnable() { // from class: b.d.c.f.i
                            @Override // java.lang.Runnable
                            public final void run() {
                                ConnectionManager.d.this.d();
                            }
                        });
                    }
                    y.l(ConnectionManager.this.mContext).w(ConnectionManager.this.mCurrentPairedDevice);
                } else {
                    Log.i(ConnectionManager.TAG, "CP onConnectStatusChanged  mCurrentPairedDevice is null");
                    a0.a().c(ConnectionManager.this.mCurDeviceInfoBean, 3, 3);
                }
            }
            z b2 = z.b();
            String deviceName = ConnectionManager.this.mCurDeviceInfoBean.getDeviceName();
            String macAddress = ConnectionManager.this.mCurDeviceInfoBean.getMacAddress();
            long currentTimeMillis = System.currentTimeMillis();
            if (b2 != null) {
                try {
                    CommDataTrack commDataTrack = new CommDataTrack("Connectivity", ServiceConstant.CONNECTIVITY_PACKAGE_NAME, b0.b().d, "com.ici.connectivity/com.ici.connectivity.activity.ConnectivityActivity", "BT连接管理");
                    try {
                        JSONObject jSONObject = new JSONObject();
                        jSONObject.put("G150", b2.d.format(Long.valueOf(currentTimeMillis)));
                        jSONObject.put("G174", b2.f(i));
                        jSONObject.put("G180", macAddress);
                        jSONObject.put("G151", deviceName);
                        b0.b().e("trackCarplayConnectState", commDataTrack, jSONObject.toString());
                        Log.i("ICI_BuryManager", "trackCarplayConnectState deviceName : " + deviceName + "----status : " + b2.f(i) + "----time : " + currentTimeMillis);
                        return;
                    } catch (JSONException e3) {
                        e3.printStackTrace();
                        return;
                    }
                } catch (Exception e4) {
                    b.a.b.a.a.h(e4, b.a.b.a.a.e("trackCarplayConnectState error exception : "), "ICI_BuryManager");
                    return;
                }
            }
            throw null;
        }
    }

    /* loaded from: classes.dex */
    public class e implements ICPCtlCmdCallback {
        public e() {
        }

        @Override // com.yfve.ici.app.carplay.ICPCtlCmdCallback
        public void onControl(int i, byte[] bArr) {
            b.a.b.a.a.i("onControl i ==== ", i, ConnectionManager.TAG);
            if (i == 2) {
                if (ConnectionManager.this.mConnectingbluetoothDevice == null) {
                    if (y.l(ConnectionManager.this.mContext).r()) {
                        Log.i(ConnectionManager.TAG, "onControl isBluetoothConnect");
                        ConnectionManager connectionManager = ConnectionManager.this;
                        connectionManager.mConnectingbluetoothDevice = y.l(connectionManager.mContext).j();
                        y.l(ConnectionManager.this.mContext).x(ConnectionManager.this.mConnectingbluetoothDevice);
                        return;
                    }
                    return;
                }
                Log.i(ConnectionManager.TAG, "onControl mConnectingbluetoothDevice != null");
                y.l(ConnectionManager.this.mContext).x(ConnectionManager.this.mConnectingbluetoothDevice);
            }
        }
    }

    public static /* synthetic */ int access$308(ConnectionManager connectionManager) {
        int i = connectionManager.mCurrentReConnectSecondBtDeviceCount;
        connectionManager.mCurrentReConnectSecondBtDeviceCount = i + 1;
        return i;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void clearWirelessCarPlayReconnect() {
        Log.i(TAG, "clearWirelessCarPlayReconnect In");
        CarPlayDeviceInfo carPlayDeviceInfo = this.mCarPlayDeviceInfo;
        if (carPlayDeviceInfo != null) {
            carPlayDeviceInfo.setReconnect(false);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public List<String> getHotConnectedDevice() {
        Log.i(TAG, "getHotConnectedDevice is called");
        String hostapdCmdReply = this.mWifiManager.getHostapdCmdReply("sta_mac_show");
        Log.i(TAG, "getHotConnectedDevice: " + hostapdCmdReply);
        ArrayList arrayList = new ArrayList();
        if (hostapdCmdReply != null && !hostapdCmdReply.contains("ERROR")) {
            String[] split = hostapdCmdReply.split(" ");
            for (int i = 0; i < split.length; i++) {
                if (!TextUtils.isEmpty(split[i])) {
                    b.a.b.a.a.p(b.a.b.a.a.e("getHotConnectedDevice address: "), split[i], TAG);
                    arrayList.add(0, split[i]);
                }
            }
        }
        StringBuilder e2 = b.a.b.a.a.e("getHotConnectedDevice size: ");
        e2.append(arrayList.size());
        Log.i(TAG, e2.toString());
        return arrayList;
    }

    public static ConnectionManager getInstance() {
        ConnectionManager connectionManager;
        if (mConnectionManager == null) {
            synchronized (ConnectionManager.class) {
                if (mConnectionManager == null) {
                    mConnectionManager = new ConnectionManager();
                }
                connectionManager = mConnectionManager;
            }
            return connectionManager;
        }
        return mConnectionManager;
    }

    private boolean isHotDisconnectOfCarPlay(String str) {
        Log.i(TAG, "isHotDisconnectOfCarPlay is called");
        List<String> hotConnectedDevice = getHotConnectedDevice();
        boolean z = true;
        if (hotConnectedDevice != null && hotConnectedDevice.size() > 0) {
            Iterator<String> it = hotConnectedDevice.iterator();
            while (true) {
                if (!it.hasNext()) {
                    break;
                } else if (it.next().equals(str)) {
                    z = false;
                    break;
                }
            }
        }
        b.a.b.a.a.l("isHotDisconnectOfCarPlay isHotDisconnect: ", z, TAG);
        return z;
    }

    private boolean isShowConnectConfirmByBtServer(String str) {
        if (y.l(this.mContext).r()) {
            Log.i(TAG, "isShowConnectConfirmByBtServer bt connected");
            h j = y.l(this.mContext).j();
            if (!TextUtils.isEmpty(j.j())) {
                if (!j.j().equalsIgnoreCase(str)) {
                    Log.i(TAG, "连接的蓝牙和需要连接的CarPlay不是同一个设备");
                } else {
                    Log.i(TAG, "连接的蓝牙和需要连接的CarPlay是同一个设备");
                    return false;
                }
            } else {
                Log.e(TAG, "cachedBluetoothDevice.getAddress() is null");
            }
        } else if (CarPlayProxy.getInstance().isCarPlaySessionActived()) {
            Log.i(TAG, "isShowConnectConfirmByBtServer carplay connected");
            String btMacAddress = CarPlayProxy.getInstance().getDeviceInfo().getBtMacAddress();
            if (!TextUtils.isEmpty(btMacAddress)) {
                if (!btMacAddress.equalsIgnoreCase(str)) {
                    Log.i(TAG, "已连接CarPlay和需要连接的CarPlay不是同一个设备");
                } else {
                    Log.i(TAG, "已连接CarPlay和需要连接的CarPlay是同一个设备");
                    return false;
                }
            } else {
                Log.e(TAG, "获取已连接CarPlay设备的Mac为null");
            }
        } else if (!CarlifeProxy.getInstance().isCarLifeSessionActived()) {
            return false;
        } else {
            Log.i(TAG, "isShowConnectConfirmByBtServer carlife connected");
        }
        return true;
    }

    private void registerSoftApCallback() {
        Context context = this.mContext;
        if (context == null || context.getSystemService("wifi") == null) {
            return;
        }
        WifiManager wifiManager = (WifiManager) this.mContext.getSystemService("wifi");
        this.mWifiManager = wifiManager;
        if (wifiManager != null) {
            wifiManager.registerSoftApCallback(this.mSoftApCallback, this.mHandler);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void saveCarPlayConnectTypeAndWlanAddress(int i, String str, String str2) {
        Log.i(TAG, "saveCarPlayDeviceInfo type: " + i + " ,wlan: " + str);
        CarPlayDeviceInfo carPlayDeviceInfo = this.mCarPlayDeviceInfo;
        if (carPlayDeviceInfo != null) {
            carPlayDeviceInfo.setConnectType(i);
            this.mCarPlayDeviceInfo.setWlanAddress(str);
            this.mCarPlayDeviceInfo.setMacAddress(str2);
            DeviceInfoBean k = this.mDbManager.k(str2);
            if (k != null) {
                this.mCarPlayDeviceInfo.setConnectTime(k.getConnectTime());
                List<h> n = y.l(this.mContext).n();
                if (n != null) {
                    for (h hVar : n) {
                        if (str2.equals(hVar.j())) {
                            Log.i(TAG, "cp connected success saveCarPlayConnectTypeAndWlanAddress set CachedBluetoothDevice to mCurDeviceInfoBean");
                            this.mCachedBluetoothDevice = hVar;
                            this.mCurDeviceInfoBean.setCachedBluetoothDevice(hVar);
                            this.mCarPlayDeviceInfo.setBluetoothDevice(hVar.e);
                            if (hVar.m()) {
                                Log.i(TAG, "saveCarPlayConnectTypeAndWlanAddress carPlay connect by wireless and current device bt is connect, disconnet bt");
                                hVar.e();
                                return;
                            }
                            return;
                        }
                    }
                    return;
                }
                return;
            }
            return;
        }
        Log.i(TAG, "mCarPlayDeviceInfo == null");
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void saveWirelessCarPlayReconnect() {
        Log.i(TAG, "saveWirelessCarPlayReconnect is called");
        CarPlayDeviceInfo carPlayDeviceInfo = this.mCarPlayDeviceInfo;
        if (carPlayDeviceInfo == null || carPlayDeviceInfo.getConnectType() == 1) {
            return;
        }
        Log.i(TAG, "saveWirelessCarPlayReconnect is wireless");
        if (this.mCarPlayDeviceInfo.getWlanAddress() == null || !isHotDisconnectOfCarPlay(this.mCarPlayDeviceInfo.getWlanAddress())) {
            return;
        }
        Log.i(TAG, "saveWirelessCarPlayReconnect need reconnect after disconnect");
        this.mCarPlayDeviceInfo.setReconnect(true);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void sendCarPlayConnectStatus(int i) {
        String str;
        CPDeviceInfo deviceInfo = CarPlayProxy.getInstance().getDeviceInfo();
        String str2 = null;
        if (deviceInfo != null) {
            str2 = deviceInfo.getBtMacAddress();
            str = deviceInfo.getUsbSerialNumber();
        } else {
            str = null;
        }
        DeviceInfoBean deviceInfoBean = new DeviceInfoBean();
        deviceInfoBean.setMacAddress(str2);
        deviceInfoBean.setSerialNum(str);
        Log.i(TAG, "sendCarPlayConnectStatus connectStatus : " + i + " --- mac : " + str2 + " ------ serial : " + str);
        deviceInfoBean.setConnectState(i);
        EventBus.getDefault().post(deviceInfoBean);
    }

    private void setConnectingBluetoothDevice(h hVar) {
        if (this.mConnectingbluetoothDevice != hVar) {
            this.mConnectingbluetoothDevice = hVar;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void setCurConnDevInfo(DeviceInfoBean deviceInfoBean) {
        this.mCurConnDevInfo = deviceInfoBean;
    }

    private void unregisterSoftApCallback() {
        WifiManager wifiManager = this.mWifiManager;
        if (wifiManager != null) {
            wifiManager.unregisterSoftApCallback(this.mSoftApCallback);
            this.mWifiManager = null;
        }
    }

    private void updateDeviceInfo() {
        if (CarPlayProxy.getInstance().getCPConnectStatus() == 2) {
            Log.i(TAG, "updateDeviceInfo getCPConnectStatus CONNECTED");
            y l = y.l(this.mContext);
            if (l.r()) {
                Log.i(TAG, "updateDeviceInfo isBluetoothConnect true");
                h j = l.j();
                if (j != null) {
                    l.x(j);
                }
            } else {
                h hVar = this.mConnectingbluetoothDevice;
                if (hVar != null) {
                    l.x(hVar);
                }
            }
            CPDeviceInfo deviceInfo = CarPlayProxy.getInstance().getDeviceInfo();
            if (deviceInfo != null) {
                Log.i(TAG, "updateDeviceInfo cpDeviceInfo != null");
                this.mDbManager.t(deviceInfo.getDeviceName(), deviceInfo.getBtMacAddress().toUpperCase(), deviceInfo.getUsbSerialNumber(), deviceInfo.getConnectType());
                this.mDbManager.r(deviceInfo.getBtMacAddress().toUpperCase(), deviceInfo.getUsbSerialNumber());
            }
        } else if (CarlifeProxy.getInstance().isCarLifeSessionActived()) {
            Log.i(TAG, "updateDeviceInfo isCarLifeSessionActived");
            String string = CarlifeProxy.getInstance().getDeviceInfo().getString(CarlifeProxy.EXTRA_DEV_INFO_SERIAL);
            String string2 = CarlifeProxy.getInstance().getDeviceInfo().getString(CarlifeProxy.EXTRA_DEV_INFO_NAME);
            Log.i(TAG, "updateDeviceInfo serialNum:" + string + " ,name: " + string2);
            this.mDbManager.q(string, string2);
        }
    }

    public void a(String str, DeviceInfoBean deviceInfoBean, String str2) {
        if (b.d.c.h.f.c.j(str)) {
            String f = b.d.c.h.f.c.f("ConnectionManager");
            Log.i(f, "[CL->CL]handleCarlifeConnectConflict connect -> " + str);
            CarlifeManager.getInstance().updateSwitching(false);
            this.mCarlifeConnection.handleCarlifeConnection(deviceInfoBean);
            return;
        }
        String f2 = b.d.c.h.f.c.f("ConnectionManager");
        Log.i(f2, "carlife conflict fail, target device not exit. , [OLD]" + str2 + " -> [NEW]" + deviceInfoBean.getSerialNum());
        CarlifeManager.getInstance().hideWindow(2008);
    }

    public void b(String str, DeviceInfoBean deviceInfoBean) {
        if (deviceInfoBean == null) {
            String f = b.d.c.h.f.c.f("ConnectionManager");
            Log.e(f, "wait device:" + str + " timeout");
            CarlifeManager.getInstance().setConnectingDeviceSerial(null);
            this.mCarlifeConnection.hideWindow(2008);
            Context context = this.mContext;
            ICIToast.makeToastOnlyText(context, context.getString(R$string.cl_not_found_disconnected_cp_device), 1).show();
            return;
        }
        b.a.b.a.a.k("[CP->CL]handleCarlifeConnectConflict connect -> ", str, b.d.c.h.f.c.f("ConnectionManager"));
        this.mCarlifeConnection.handleCarlifeConnection(deviceInfoBean);
    }

    public /* synthetic */ void c(CPDeviceInfo cPDeviceInfo) {
        DeviceInfoBean k;
        StringBuilder e2 = b.a.b.a.a.e("onDeviceInfoChanged MAC:");
        e2.append(cPDeviceInfo.getBtMacAddress());
        Log.i(TAG, e2.toString());
        Log.i(TAG, "onDeviceInfoChanged SERIALNUM:" + cPDeviceInfo.getUsbSerialNumber());
        Log.i(TAG, "onDeviceInfoChanged connectType:" + cPDeviceInfo.getConnectType());
        Log.i(TAG, "onDeviceInfoChanged deviceName:" + cPDeviceInfo.getDeviceName());
        Log.i(TAG, "onDeviceInfoChanged signalStrength:" + cPDeviceInfo.getSignalStrength());
        Log.i(TAG, "onDeviceInfoChanged batteryLevel:" + cPDeviceInfo.getBatteryLevel());
        if (cPDeviceInfo.getDeviceName().equals("UNKNOWN") || cPDeviceInfo.getBtMacAddress().equals("UNKNOWN") || cPDeviceInfo.getUsbSerialNumber().equals("UNKNOWN")) {
            return;
        }
        this.mDbManager.t(cPDeviceInfo.getDeviceName(), cPDeviceInfo.getBtMacAddress().toUpperCase(), cPDeviceInfo.getUsbSerialNumber(), cPDeviceInfo.getConnectType());
        if (cPDeviceInfo.getConnectType() == 1) {
            k = this.mDbManager.l(cPDeviceInfo.getUsbSerialNumber());
        } else {
            k = this.mDbManager.k(cPDeviceInfo.getBtMacAddress().toUpperCase());
        }
        setCurConnDevInfo(k);
        c0.a().c(cPDeviceInfo.getBtMacAddress().toUpperCase(), cPDeviceInfo.getUsbSerialNumber(), cPDeviceInfo.getSignalStrength(), cPDeviceInfo.getBatteryLevel());
        x.a().e(cPDeviceInfo.getBatteryLevel());
    }

    public void cancelReConnected() {
        Log.i(TAG, "cancelReConnected in");
        if (this.mDisConnectBluetoothDevice != null) {
            Log.i(TAG, "cancelReConnected mDisConnectBluetoothDevice set null");
            this.mDisConnectBluetoothDevice = null;
        }
        if (this.mHandler.hasMessages(104)) {
            Log.i(TAG, "cancelReConnected remove handler msg CONNECT_SECOND_BT_DEVICE");
            this.mHandler.removeMessages(104);
        }
    }

    public void d(String str) {
        b.a.b.a.a.k("onCarPlayOOBPairingInfo mac: ", str, TAG);
        this.mDbManager.v(str, true);
    }

    public void e(boolean z, String str) {
        DeviceInfoBean k;
        Log.i(TAG, "BT server onConnectStatusChanged state:" + z + ",mac:" + str);
        int i = -1;
        if (str != null && (k = this.mDbManager.k(str)) != null) {
            this.mBtServerFirstConnectCarPlay = k.getCarPlayFirstConnect() == 1;
            i = k.getCarPlaySwitch();
        }
        if (z) {
            Message message = new Message();
            message.what = 107;
            message.arg1 = isShowConnectConfirmByBtServer(str) ? 1 : 0;
            message.arg2 = i;
            this.mHandler.sendMessage(message);
        }
    }

    public /* synthetic */ void f(boolean z) {
        if (z) {
            Log.i(TAG, "CP onServiceConnChange is true");
            CarPlayProxy.getInstance().registerCPConnectStatusCallback(this.mICPConnectStatusCallback);
            CarPlayProxy.getInstance().registerDeviceInfoCallback(this.mICPDeviceInfoCallback);
            CarPlayProxy.getInstance().registerCPCtlCmdCallback(this.mICPCtlCmdCallback);
            CarPlayProxy.getInstance().registerCPOOBPairingInfoCallback(this.mICPOOBPairingInfoCallback);
            CarPlayProxy.getInstance().registerWirelessCPBTServerConnectCallback(this.mIWirelessCPBTServerConnectCallback);
        }
    }

    public /* synthetic */ void g(boolean z) {
        if (z) {
            Log.i(TAG, "CL onServiceConnChange is true");
            CarlifeProxy.getInstance().registerConnectStateChangeListener(this.mIClConnectStatusListener);
        }
    }

    public boolean getBtServerRequireConnectComfirm() {
        return this.mBtServerRequireConnectComfirm;
    }

    public String getCarPlayReconnectSerialNum() {
        return this.mCarPlayReconnectSerialNum;
    }

    public boolean getCarPlayReconnectState() {
        return this.mCarPlayReconnnectState;
    }

    public boolean getConnectFailReconnectFlag() {
        return this.mConnectFailReconnectFlag;
    }

    public DeviceInfoBean getConnectingCarPlayDevice() {
        int cPConnectStatus = CarPlayProxy.getInstance().getCPConnectStatus();
        StringBuilder f = b.a.b.a.a.f("getConnectingCarPlayDevice currentCarPlayConnectState : ", cPConnectStatus, " ----- mConnectingCarPlayDevice : ");
        f.append(this.mConnectingCarPlayDevice);
        Log.i(TAG, f.toString());
        if (cPConnectStatus == 1) {
            return this.mConnectingCarPlayDevice;
        }
        this.mConnectingCarPlayDevice = null;
        return null;
    }

    public h getConnectingbluetoothDevice() {
        return this.mConnectingbluetoothDevice;
    }

    public int getCurCarPlayConnectType() {
        return this.mCarPlayConnection.getCurCarPlayConnectType();
    }

    public DeviceInfoBean getCurConnDevInfo() {
        Log.i(TAG, "getCurConnDevInfo in");
        if (isHaveDevConnection()) {
            int curConnectType = getCurConnectType();
            b.a.b.a.a.i("getCurConnDevInfo type : ", curConnectType, TAG);
            if (curConnectType != 1 && curConnectType != 5) {
                return this.mCurConnDevInfo;
            }
            h j = y.l(this.mContext).j();
            if (j != null) {
                DeviceInfoBean deviceInfoBean = new DeviceInfoBean();
                deviceInfoBean.setDeviceName(j.f);
                deviceInfoBean.setMacAddress(j.j());
                deviceInfoBean.setCachedBluetoothDevice(j);
                return deviceInfoBean;
            }
            Log.i(TAG, "getCurConnDevInfo dev = null");
        }
        return null;
    }

    public int getCurConnectType() {
        Log.i(TAG, "getCurConnectType in");
        if (CarPlayProxy.getInstance().isCarPlaySessionActived()) {
            return 3;
        }
        return y.l(this.mContext).r() ? CarlifeProxy.getInstance().isCarLifeSessionActived() ? 5 : 1 : CarlifeProxy.getInstance().isCarLifeSessionActived() ? 4 : 0;
    }

    public String getCurrentConnectdBluetoothDeviceMac() {
        if (y.l(this.mContext).j() != null) {
            return y.l(this.mContext).j().j();
        }
        return null;
    }

    public LifecycleObserver getLifecycleObserver() {
        return this;
    }

    public int getNeedConnectType() {
        return this.mNeedConnectType;
    }

    public void handleBtConnectConflict() {
        h j;
        int curConnectType = getCurConnectType();
        b.a.b.a.a.i("handleBtConnectConflict connectType: ", curConnectType, TAG);
        String str = null;
        if (curConnectType == 1) {
            if (y.l(this.mContext) != null && y.l(this.mContext).j() != null && (j = y.l(this.mContext).j()) != null) {
                str = j.j();
                this.mDisConnectBluetoothDevice = j;
                y.l(this.mContext).x(j);
            }
        } else if (curConnectType == 5) {
            h j2 = y.l(this.mContext).j();
            this.mDisConnectBluetoothDevice = j2;
            y.l(this.mContext).x(j2);
            d0.m().f();
        } else if (curConnectType == 3) {
            d0.m().g();
        }
        DeviceInfoBean k = d0.m().k();
        if (str != null && str.equals(k.getMacAddress())) {
            Log.i(TAG, "handleBtConnectConflict  macAddress equals deviceInfoBean mac  return");
        } else if (k != null && k.getCachedBluetoothDevice() != null) {
            if (this.mCachedBluetoothDevice != null) {
                this.mCarPlaySwitchFlag = true;
            }
            Log.i(TAG, "handleBtConnectConflict CONNECT_SECOND_BT_DEVICE delayed 1000 ---> ");
            Message message = new Message();
            message.obj = k;
            message.what = 104;
            this.mHandler.sendMessageDelayed(message, 1000L);
        } else {
            b.a.a.b.y.f(TAG, "deviceInfoBean = null");
        }
    }

    public void handleBtServerConnectCarPlay() {
        if (y.l(this.mContext).r()) {
            Log.i(TAG, "handleBtServerConnectCarPlay bt connected");
            y.l(this.mContext).x(y.l(this.mContext).j());
            CarPlayProxy.getInstance().confirmConnectWirelessCPServer(this.mBtServerFirstConnectCarPlay);
        } else if (CarPlayProxy.getInstance().isCarPlaySessionActived()) {
            Log.i(TAG, "handleBtServerConnectCarPlay carplay connected");
            d0.m().g();
            this.mBtServerConnectCarPlay = true;
        } else if (CarlifeProxy.getInstance().isCarLifeSessionActived()) {
            Log.i(TAG, "handleBtServerConnectCarPlay carlife connected");
            d0.m().f();
            CarPlayProxy.getInstance().confirmConnectWirelessCPServer(this.mBtServerFirstConnectCarPlay);
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0061  */
    /* JADX WARN: Removed duplicated region for block: B:13:0x007f  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void handleCarPlayConnectConflict(com.ici.connectivitylib.bean.DeviceInfoBean r7) {
        /*
            r6 = this;
            int r0 = r6.getCurConnectType()
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            java.lang.String r2 = "handleCarPlayConnectConflict connectType: "
            r1.append(r2)
            r1.append(r0)
            java.lang.String r1 = r1.toString()
            java.lang.String r2 = "Connectivity:ConnectionManager"
            android.util.Log.i(r2, r1)
            android.hardware.usb.UsbDevice r1 = r7.getUsbDevice()
            r3 = 0
            if (r1 == 0) goto L5d
            android.hardware.usb.UsbDevice r1 = r7.getUsbDevice()
            boolean r1 = b.d.c.h.b.e(r1)
            if (r1 == 0) goto L5d
            java.lang.String r1 = "handleCarPlayConnectConflict isUDCDevice"
            android.util.Log.i(r2, r1)
            com.yfve.ici.app.carlife.CarlifeProxy r1 = com.yfve.ici.app.carlife.CarlifeProxy.getInstance()
            boolean r1 = r1.isCarLifeSessionActived()
            if (r1 == 0) goto L5d
            com.yfve.ici.app.carlife.CarlifeProxy r1 = com.yfve.ici.app.carlife.CarlifeProxy.getInstance()
            android.os.Bundle r1 = r1.getDeviceInfo()
            java.lang.String r4 = "EXTRA_DEV_INFO_SERIAL"
            java.lang.String r1 = r1.getString(r4)
            java.lang.StringBuilder r4 = new java.lang.StringBuilder
            r4.<init>()
            java.lang.String r5 = "handleCarPlayConnectConflict serial: "
            r4.append(r5)
            r4.append(r1)
            java.lang.String r4 = r4.toString()
            android.util.Log.i(r2, r4)
            goto L5e
        L5d:
            r1 = r3
        L5e:
            r2 = 1
            if (r0 != r2) goto L7f
            android.content.Context r0 = r6.mContext
            b.d.c.f.y r0 = b.d.c.f.y.l(r0)
            b.a.a.b.h r0 = r0.j()
            android.content.Context r1 = r6.mContext
            b.d.c.f.y r1 = b.d.c.f.y.l(r1)
            r1.x(r0)
            r6.mCachedBluetoothDevice = r0
            r0 = 0
            r6.mCarPlaySwitchFlag = r0
            com.ici.connectivitylib.connection.CarPlayConnection r0 = r6.mCarPlayConnection
            r0.handleCarPlayConnection(r7, r3)
            goto Lc6
        L7f:
            r4 = 4
            if (r0 != r4) goto L8c
            b.d.c.f.d0 r7 = b.d.c.f.d0.m()
            r7.f()
            r6.mCarLifeDisconnect = r2
            goto Lc6
        L8c:
            r4 = 5
            if (r0 != r4) goto Laf
            android.content.Context r0 = r6.mContext
            b.d.c.f.y r0 = b.d.c.f.y.l(r0)
            b.a.a.b.h r0 = r0.j()
            android.content.Context r2 = r6.mContext
            b.d.c.f.y r2 = b.d.c.f.y.l(r2)
            r2.x(r0)
            b.d.c.f.d0 r0 = b.d.c.f.d0.m()
            r0.f()
            com.ici.connectivitylib.connection.CarPlayConnection r0 = r6.mCarPlayConnection
            r0.handleCarPlayConnection(r7, r1)
            goto Lc6
        Laf:
            r1 = 3
            if (r0 != r1) goto Lc6
            b.a.a.b.h r0 = r6.mCachedBluetoothDevice
            if (r0 == 0) goto Lb8
            r6.mCarPlaySwitchFlag = r2
        Lb8:
            b.d.c.f.d0 r0 = b.d.c.f.d0.m()
            r0.g()
            r6.mCarPlayDisconnect = r2
            com.ici.connectivitylib.connection.CarPlayConnection r0 = r6.mCarPlayConnection
            r0.handleCarPlayConnection(r7, r3)
        Lc6:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: com.ici.connectivitylib.manager.ConnectionManager.handleCarPlayConnectConflict(com.ici.connectivitylib.bean.DeviceInfoBean):void");
    }

    public void handleCarlifeConnectConflict(final DeviceInfoBean deviceInfoBean) {
        if (!b.d.c.a.a.a().d()) {
            Log.i(TAG, "handleCarlifeConnection is not support carlife");
        } else if (deviceInfoBean != null && deviceInfoBean.getUsbDevice() != null && !TextUtils.isEmpty(deviceInfoBean.getSerialNum())) {
            boolean z = false;
            if (!b.d.c.h.f.c.j(deviceInfoBean.getSerialNum())) {
                String string = this.mContext.getString(R$string.cl_may_be_history_chk_usb, deviceInfoBean.getDeviceName());
                b.a.b.a.a.k("handleCarlifeConnectConflict:", string, b.d.c.h.f.c.f("ConnectionManager"));
                b.d.c.h.f.c.r(this.mContext, string);
                return;
            }
            boolean m = b.d.c.h.f.c.m(deviceInfoBean.getUsbDevice());
            int curConnectType = getCurConnectType();
            if (curConnectType != 5 && curConnectType != 4) {
                if (curConnectType != 3) {
                    if (curConnectType == 1) {
                        Log.i(b.d.c.h.f.c.f("ConnectionManager"), "handleCarlifeConnectConflict: TRADITIONAL_BT");
                        return;
                    }
                    return;
                }
                Log.i(b.d.c.h.f.c.f("ConnectionManager"), "handleCarlifeConnectConflict: CARPLAY -> CARLIFE");
                if (m) {
                    CPDeviceInfo deviceInfo = CarPlayProxy.getInstance().getDeviceInfo();
                    if (deviceInfo != null) {
                        final String usbSerialNumber = deviceInfo.getUsbSerialNumber();
                        if (!TextUtils.isEmpty(usbSerialNumber)) {
                            Log.i(b.d.c.h.f.c.f("ConnectionManager"), "cpSerial:" + usbSerialNumber);
                            CarlifeManager.getInstance().setConnectingDeviceSerial(usbSerialNumber);
                            this.mCarlifeConnection.showWindow(2008);
                            new c.a(usbSerialNumber, TooltipCompatHandler.HOVER_HIDE_TIMEOUT_SHORT_MS, new Consumer() { // from class: b.d.c.f.o
                                @Override // java.util.function.Consumer
                                public final void accept(Object obj) {
                                    ConnectionManager.this.b(usbSerialNumber, (DeviceInfoBean) obj);
                                }
                            });
                            d0.m().g();
                            return;
                        }
                    }
                    Log.e(b.d.c.h.f.c.f("ConnectionManager"), "carlife conflict CP switch to CL fail. not get cp device serial");
                    return;
                }
                d0.m().g();
                this.mCarlifeConnection.handleCarlifeConnection(deviceInfoBean);
                return;
            }
            if (curConnectType == 5) {
                Log.i(b.d.c.h.f.c.f("ConnectionManager"), "handleCarlifeConnectConflict: BT_CARLIFE -> CARLIFE");
            } else {
                Log.i(b.d.c.h.f.c.f("ConnectionManager"), "handleCarlifeConnectConflict: CARLIFE -> CARLIFE");
            }
            final String string2 = CarlifeProxy.getInstance().getDeviceInfo().getString(CarlifeProxy.EXTRA_DEV_INFO_SERIAL);
            Log.i(b.d.c.h.f.c.f("ConnectionManager"), "carlife conflict: [OLD]" + string2 + " -> [NEW]" + deviceInfoBean.getSerialNum());
            if (m || TextUtils.equals(deviceInfoBean.getSerialNum(), string2)) {
                z = true;
            }
            int carlifeConnectState = CarlifeProxy.getInstance().getCarlifeConnectState();
            if (!z) {
                CarlifeManager.getInstance().updateSwitching(true);
                d0.m().f();
                CarlifeManager.getInstance().showWindow(2008);
                final String serialNum = deviceInfoBean.getSerialNum();
                CarlifeManager.getInstance().setConnectingDeviceSerial(serialNum);
                b.d.c.h.f.c.o(new Runnable() { // from class: b.d.c.f.j
                    @Override // java.lang.Runnable
                    public final void run() {
                        ConnectionManager.this.a(serialNum, deviceInfoBean, string2);
                    }
                }, TooltipCompatHandler.LONG_CLICK_HIDE_TIMEOUT_MS);
            } else if (carlifeConnectState == 2 || carlifeConnectState == 3) {
                b.d.c.h.f.c.i(this.mContext);
            } else if (carlifeConnectState == 4) {
                b.d.c.h.f.c.h(this.mContext);
            }
        } else {
            Log.e(b.d.c.h.f.c.f("ConnectionManager"), "handleCarlifeConnectConflict fail, mCurDeviceInfoBean invalid");
            String string3 = this.mContext.getString(R$string.cl_invalid_carlife_device);
            if (deviceInfoBean != null && !TextUtils.isEmpty(deviceInfoBean.getDeviceName())) {
                string3 = string3 + ":" + deviceInfoBean.getDeviceName();
            }
            b.d.c.h.f.c.r(this.mContext, string3);
        }
    }

    public void handleDeviceConnection(DeviceInfoBean deviceInfoBean, int i) {
        b.a.b.a.a.i("handleDeviceConnection IN connectType : ", i, TAG);
        if (deviceInfoBean != null && (deviceInfoBean.getUsbDevice() != null || deviceInfoBean.getCachedBluetoothDevice() != null)) {
            this.mCurDeviceInfoBean = deviceInfoBean;
            if (deviceInfoBean.getCachedBluetoothDevice() != null) {
                this.mCachedBluetoothDevice = deviceInfoBean.getCachedBluetoothDevice();
            } else {
                Log.i(TAG, "handleDeviceConnection bean.getCachedBluetoothDevice() is null");
            }
            this.mNeedConnectType = i;
            if (deviceInfoBean.getCachedBluetoothDevice() != null) {
                this.mCachedBluetoothDevice = deviceInfoBean.getCachedBluetoothDevice();
            } else {
                Log.i(TAG, "handleDeviceConnection bean.getCachedBluetoothDevice() is null");
            }
            if (isNeedShowConflictWindow(this.mNeedConnectType)) {
                Log.i(TAG, "Need Show Connect Conflict Window");
                e0.d(this.mContext).p(6, e0.c());
                return;
            } else if (i != 3) {
                if (i == 4) {
                    this.mCarlifeConnection.handleCarlifeConnection(deviceInfoBean);
                    return;
                } else if (i == 1) {
                    if (deviceInfoBean.getCachedBluetoothDevice() != null) {
                        if (deviceInfoBean.getBtPairedState() == 0) {
                            y.l(this.mContext).y(deviceInfoBean.getCachedBluetoothDevice());
                            return;
                        } else {
                            y.l(this.mContext).w(deviceInfoBean.getCachedBluetoothDevice());
                            return;
                        }
                    }
                    b.a.a.b.y.f(TAG, "getCachedBluetoothDevice = null");
                    return;
                } else {
                    return;
                }
            } else {
                if (getCurConnectType() == 1 && y.l(this.mContext) != null && y.l(this.mContext).j() != null) {
                    h j = y.l(this.mContext).j();
                    this.mCachedBluetoothDevice = j;
                    this.mCarPlaySwitchFlag = false;
                    if (j != null) {
                        if (!TextUtils.isEmpty(deviceInfoBean.getMacAddress()) && j.j().equals(deviceInfoBean.getMacAddress())) {
                            this.mCarPlayConnection.handleCarPlayConnection(deviceInfoBean, null);
                            return;
                        }
                        y.l(this.mContext).x(j);
                    }
                }
                d0.m().s();
                this.mCarPlayConnection.handleCarPlayConnection(deviceInfoBean, null);
                return;
            }
        }
        Log.i(TAG, "Device is not invalid");
        Context context = this.mContext;
        ICIToast.makeToastOnlyText(context, context.getString(R$string.check_device_connect), 1).show();
    }

    public void initialize(Context context) {
        b.a.a.b.y.q(TAG, "[ConnectionManager initialize][IN][TIME:");
        this.mContext = context;
        this.mDbManager = b.d.c.d.c.b(context);
        this.mCarPlayConnection = new CarPlayConnection(context);
        CarlifeConnection carlifeConnection = new CarlifeConnection(context);
        this.mCarlifeConnection = carlifeConnection;
        carlifeConnection.initialize();
        updateDeviceInfo();
        if (CarPlayProxy.getInstance().isAvailable()) {
            Log.i(TAG, "CarPlayProxy.getInstance().isAvailable()");
            CarPlayProxy.getInstance().registerCPConnectStatusCallback(this.mICPConnectStatusCallback);
            CarPlayProxy.getInstance().registerDeviceInfoCallback(this.mICPDeviceInfoCallback);
            CarPlayProxy.getInstance().registerCPCtlCmdCallback(this.mICPCtlCmdCallback);
            CarPlayProxy.getInstance().registerCPOOBPairingInfoCallback(this.mICPOOBPairingInfoCallback);
            CarPlayProxy.getInstance().registerWirelessCPBTServerConnectCallback(this.mIWirelessCPBTServerConnectCallback);
        }
        CarPlayProxy.getInstance().registerConnChangeListener(this.mCarPlayConnListener);
        if (CarlifeProxy.getInstance().isAvailable()) {
            Log.i(TAG, "CarlifeProxy.getInstance().isAvailable()");
            CarlifeProxy.getInstance().registerConnectStateChangeListener(this.mIClConnectStatusListener);
        }
        CarlifeProxy.getInstance().registerConnChangeListener(this.mCarLifeConnListener);
        registerSoftApCallback();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        b.a.a.b.y.q(TAG, "[ConnectionManager initialize][OUT][TIME:");
    }

    public boolean isBluetoothConnected() {
        return y.l(this.mContext).r();
    }

    public boolean isCarPlayDisconnectByClickAdd() {
        return this.isCarPlayDisconnectByClickAdd;
    }

    public boolean isCarPlayUiFront() {
        Lifecycle.Event event = this.mLifecycleState;
        return event == Lifecycle.Event.ON_CREATE || event == Lifecycle.Event.ON_RESUME || event == Lifecycle.Event.ON_START;
    }

    public boolean isHaveDevConnection() {
        Log.i(TAG, "getCurConnDevInfo in");
        if (y.l(this.mContext).r() || CarPlayProxy.getInstance().isCarPlaySessionActived() || CarlifeProxy.getInstance().isCarLifeSessionActived()) {
            Log.i(TAG, "getCurConnDevInfo return true");
            return true;
        }
        return false;
    }

    /* JADX WARN: Code restructure failed: missing block: B:11:0x005b, code lost:
        if (r0.j().equals(r6.getMacAddress()) != false) goto L18;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x005e, code lost:
        if (r8 == 4) goto L18;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public boolean isNeedShowConflictWindow(int r8) {
        /*
            r7 = this;
            boolean r0 = r7.isHaveDevConnection()
            java.lang.String r1 = "Connectivity:ConnectionManager"
            r2 = 0
            r3 = 1
            if (r0 == 0) goto L6c
            int r0 = r7.getCurConnectType()
            java.lang.String r4 = "isNeedShowConflictWindow curType : "
            b.a.b.a.a.i(r4, r0, r1)
            r4 = 3
            r5 = 4
            if (r0 != r3) goto L61
            android.content.Context r0 = r7.mContext
            b.d.c.f.y r0 = b.d.c.f.y.l(r0)
            b.a.a.b.h r0 = r0.j()
            b.d.c.f.d0 r6 = b.d.c.f.d0.m()
            com.ici.connectivitylib.bean.DeviceInfoBean r6 = r6.k()
            if (r8 != r4) goto L5e
            if (r0 == 0) goto L6c
            if (r6 == 0) goto L6c
            java.lang.String r8 = "cache mac: "
            java.lang.StringBuilder r8 = b.a.b.a.a.e(r8)
            java.lang.String r4 = r0.j()
            r8.append(r4)
            java.lang.String r4 = " ,bean mac: "
            r8.append(r4)
            java.lang.String r4 = r6.getMacAddress()
            r8.append(r4)
            java.lang.String r8 = r8.toString()
            android.util.Log.i(r1, r8)
            java.lang.String r8 = r0.j()
            java.lang.String r0 = r6.getMacAddress()
            boolean r8 = r8.equals(r0)
            if (r8 == 0) goto L6b
            goto L6c
        L5e:
            if (r8 != r5) goto L6b
            goto L6c
        L61:
            if (r0 != r5) goto L66
            if (r8 == r3) goto L6c
            goto L6b
        L66:
            r8 = 5
            if (r0 == r8) goto L6b
            if (r0 != r4) goto L6c
        L6b:
            r2 = r3
        L6c:
            java.lang.String r8 = "isNeedShowConflictWindow isNeedShow : "
            b.a.b.a.a.l(r8, r2, r1)
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: com.ici.connectivitylib.manager.ConnectionManager.isNeedShowConflictWindow(int):boolean");
    }

    public void notifyDeviceConnectState(h hVar, int i) {
        b.a.b.a.a.i("BT updateDeviceConnectState state: ", i, TAG);
        if (hVar != null) {
            Log.i(TAG, "BT updateDeviceConnectState device != null");
            if (i == 2) {
                setConnectingBluetoothDevice(null);
                this.mDbManager.o(hVar, true, hVar.e.getSupportCarPlay() == 1);
                String j = hVar.j();
                DeviceInfoBean k = this.mDbManager.k(j);
                if (k != null) {
                    k.setCachedBluetoothDevice(hVar);
                    setCurConnDevInfo(k);
                    if (CarlifeProxy.getInstance().isCarLifeSessionActived()) {
                        Log.i(TAG, "BT STATE_CONNECTED --isCarLifeSessionActived");
                        String string = CarlifeProxy.getInstance().getDeviceInfo().getString(CarlifeProxy.EXTRA_DEV_INFO_SERIAL);
                        if (k.getSerialNum() != null && k.getSerialNum().equals(string)) {
                            Log.i(TAG, "Bt and CarLife connected is same device");
                            this.mDbManager.s(5, j, null);
                        }
                    }
                    a0.a().c(k, 1, 2);
                }
            } else if (i == 1) {
                setConnectingBluetoothDevice(hVar);
                DeviceInfoBean k2 = this.mDbManager.k(hVar.j());
                if (k2 != null) {
                    k2.setCachedBluetoothDevice(hVar);
                }
                setCurConnDevInfo(k2);
                a0.a().c(k2, 1, 1);
            } else if (i != 0) {
                if (i == 3) {
                    setConnectingBluetoothDevice(null);
                }
            } else {
                CarPlayProxy.getInstance().getCPConnectStatus();
                setConnectingBluetoothDevice(null);
                String j2 = hVar.j();
                DeviceInfoBean k3 = this.mDbManager.k(j2);
                if (k3 != null) {
                    k3.setCachedBluetoothDevice(hVar);
                }
                setCurConnDevInfo(k3);
                if (CarlifeProxy.getInstance().isCarLifeSessionActived()) {
                    Log.i(TAG, "BT STATE_DISCONNECTED isCarLifeSessionActived");
                    String string2 = CarlifeProxy.getInstance().getDeviceInfo().getString(CarlifeProxy.EXTRA_DEV_INFO_SERIAL);
                    if (k3 != null && k3.getSerialNum() != null && k3.getSerialNum().equals(string2)) {
                        Log.i(TAG, "BT disconnect and CL connected is same device");
                        this.mDbManager.s(4, j2, null);
                    }
                }
                a0.a().c(k3, 1, 4);
            }
        }
    }

    @Subscribe
    public void onCurrentPairedDeviceEvent(h hVar) {
        Log.i(TAG, "onCurrentPairedDeviceEvent -----");
        this.mCurrentPairedDevice = hVar;
    }

    @Override // androidx.lifecycle.LifecycleEventObserver
    public void onStateChanged(LifecycleOwner lifecycleOwner, Lifecycle.Event event) {
        Log.i(TAG, "lifecycle changed -> event:" + event + ", owner:" + lifecycleOwner);
        this.mLifecycleState = event;
    }

    public void registerConnectionManagerCallBack(g0 g0Var) {
        a0.a().d(g0Var);
    }

    public void setBootCarPlayReconnect(boolean z) {
        this.mAutoConnect = z;
    }

    public void setBtServerRequireConnectComfirm(boolean z) {
        this.mBtServerRequireConnectComfirm = z;
    }

    public void setCarPlayDisconnectByClickAdd(boolean z) {
        this.isCarPlayDisconnectByClickAdd = z;
    }

    public void setCarPlayReconnectSerialNum(String str) {
        this.mCarPlayReconnectSerialNum = str;
    }

    public void setCarPlayReconnectState(boolean z) {
        this.mCarPlayReconnnectState = z;
    }

    public void setConnectFailReconnectFlag(boolean z) {
        this.mConnectFailReconnectFlag = z;
    }

    public void setConnectingCarPlayDevice(DeviceInfoBean deviceInfoBean) {
        this.mConnectingCarPlayDevice = deviceInfoBean;
    }

    public void setCurDeviceInfoBean(DeviceInfoBean deviceInfoBean) {
        this.mCurDeviceInfoBean = deviceInfoBean;
    }

    public void setNeedConnectType(int i) {
        this.mNeedConnectType = i;
    }

    public void unInitialize() {
        this.mCarPlayConnection = null;
        this.mCarlifeConnection.unInitialize();
        this.mCarlifeConnection = null;
        CarPlayProxy.getInstance().unregisterCPConnectStatusCallback(this.mICPConnectStatusCallback);
        CarPlayProxy.getInstance().unregisterDeviceInfoCallback(this.mICPDeviceInfoCallback);
        CarPlayProxy.getInstance().unregisterCPCtlCmdCallback(this.mICPCtlCmdCallback);
        CarlifeProxy.getInstance().unregisterConnectStateChangeListener(this.mIClConnectStatusListener);
        CarPlayProxy.getInstance().unregisterCPOOBPairingInfoCallback(this.mICPOOBPairingInfoCallback);
        CarPlayProxy.getInstance().unregisterWirelessCPBTServerConnectCallback(this.mIWirelessCPBTServerConnectCallback);
        unregisterSoftApCallback();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    public void unregisterConnectionManagerCallBack(g0 g0Var) {
        a0.a().e(g0Var);
    }
}
