package com.ici.connectivitylib.connection;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.IntentFilter;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;
import androidx.recyclerview.widget.ItemTouchHelper;
import b.a.a.b.y;
import b.a.b.a.a;
import b.d.c.b.u;
import b.d.c.b.v;
import b.d.c.b.w;
import b.d.c.f.d0;
import b.d.c.f.e0;
import b.d.c.f.h0;
import b.d.c.f.i0;
import b.d.c.h.b;
import b.d.c.h.f.c;
import com.ici.connectivitylib.R$string;
import com.ici.connectivitylib.bean.DeviceInfoBean;
import com.ici.connectivitylib.connection.CarlifeConnection;
import com.ici.connectivitylib.manager.CarlifeManager;
import com.ici.connectivitylib.manager.ConnectionManager;
import com.ici.connectivitylib.manager.ICarlifeWindowContract;
import com.yfve.ici.app.carlife.CarlifeProxy;
import java.util.function.Consumer;
import java.util.function.IntConsumer;
import org.greenrobot.eventbus.EventBus;

@SuppressLint({"RestrictedApi"})
/* loaded from: classes.dex */
public class CarlifeConnection implements w, h0 {
    public static final int EVT_CONNECT_FAIL_AOA_NOT_SUPPORT = -16383997;
    public static final int EVT_CONNECT_FAIL_APP_NOT_INSTALL = -16383998;
    public static final int EVT_CONNECT_FAIL_ENCODER_ERROR = -16383994;
    public static final int EVT_CONNECT_FAIL_IAP2_AUTH_FAIL = -16383989;
    public static final int EVT_CONNECT_FAIL_INVALID_DEVICE_INFO = -16383992;
    public static final int EVT_CONNECT_FAIL_NOT_FOUND_DEVICE = -16383995;
    public static final int EVT_CONNECT_FAIL_PERMISSION_DENIED = -16383993;
    public static final int EVT_CONNECT_FAIL_PROTO_NOT_MATCH = -16383990;
    public static final int EVT_CONNECT_FAIL_TIMEOUT = -16383999;
    public static final int EVT_CONNECT_FAIL_UNKNOWN = -16383996;
    public static final int EVT_CONNECT_FAIL_VIDEO_NOT_READY = -16383991;
    public static final int EVT_NOTIFY_HU_CASTING_BG = -16318461;
    public static final int EVT_NOTIFY_HU_CASTING_FG = -16318462;
    public static final int EVT_NOTIFY_MD_APP_EXIT = -16318459;
    public static final int EVT_NOTIFY_MD_APP_START_REQUEST = -16318460;
    public static final int EVT_NOTIFY_MD_SCREEN_SHARE_REQUEST = -16318463;
    public static final int EVT_TYPE_CONNECT_FAIL = -16384000;
    public static final int EVT_TYPE_CONNECT_PROGRESS = -16449536;
    public static final int EVT_TYPE_CONNECT_STATE_CHANGE = 1;
    public static final int EVT_TYPE_NOTIFICATION = -16318464;
    public static final int EVT_TYPE_WINDOW_STATE_CHANGE = 2;
    public static final int RECOVERY_CONNECT_FAIL_RECONNECT = 101;
    public static final String TAG = c.e();
    public long mConnectedTime;
    public Context mContext;
    public e0 mDWM;
    public final v mServiceHolder;
    public boolean mCarlifeUiFront = false;
    public boolean mHasConnectException = false;
    public int mConnectProgress = -1;
    public u mCarlifeContext = new u();
    public final Runnable mCarlifeDeviceCheckRunnable = new Runnable() { // from class: b.d.c.b.l
        @Override // java.lang.Runnable
        public final void run() {
            CarlifeConnection.this.g();
        }
    };
    public Handler mProcessMockHandler = null;
    public final CarlifeProxy.StateChangeListener mCarlifeSateListener = new CarlifeProxy.StateChangeListener() { // from class: b.d.c.b.c
        @Override // com.yfve.ici.app.carlife.CarlifeProxy.StateChangeListener
        public final void onStateChanged(int i) {
            CarlifeConnection.this.k(i);
        }
    };
    public final CarlifeProxy.ConnectionEventObserver mConnectionEventObserver = new CarlifeProxy.ConnectionEventObserver() { // from class: b.d.c.b.o
        @Override // com.yfve.ici.app.carlife.CarlifeProxy.ConnectionEventObserver
        public final void onEvent(int i, int i2, int i3, Object obj) {
            CarlifeConnection.this.l(i, i2, i3, obj);
        }
    };
    public final Handler.Callback mHandlerCB = new Handler.Callback() { // from class: b.d.c.b.b
        @Override // android.os.Handler.Callback
        public final boolean handleMessage(Message message) {
            return CarlifeConnection.this.h(message);
        }
    };
    public final e0.c mWindowStateCB = new e0.c() { // from class: b.d.c.b.e
        @Override // b.d.c.f.e0.c
        public final void a(int i, boolean z) {
            CarlifeConnection.this.i(i, z);
        }
    };
    public final IntConsumer mSystemKeyObs = new IntConsumer() { // from class: b.d.c.b.k
        @Override // java.util.function.IntConsumer
        public final void accept(int i) {
            CarlifeConnection.this.j(i);
        }
    };
    public Handler mHandler = new Handler(Looper.getMainLooper(), this.mHandlerCB);

    public CarlifeConnection(Context context) {
        this.mContext = context;
        this.mDWM = e0.d(context);
        this.mServiceHolder = new v(context);
    }

    private void handleConnectFailEvent(int i, Object obj) {
        Handler handler;
        this.mHasConnectException = true;
        c.o(new Runnable() { // from class: b.d.c.b.j
            @Override // java.lang.Runnable
            public final void run() {
                CarlifeConnection.this.c();
            }
        }, ItemTouchHelper.Callback.DRAG_SCROLL_ACCELERATION_LIMIT_TIME_MS);
        if (getConnectionStatus() == 4) {
            String str = TAG;
            StringBuilder e = a.e("handleConnectFailEvent CARLIFE_STATE_CASTING. skip event:");
            e.append(c.d(CarlifeConnection.class, i));
            Log.i(str, e.toString());
        } else if (!CarlifeManager.getInstance().isCarlifeUiFront() && !c.k(this.mContext)) {
            String str2 = TAG;
            StringBuilder e2 = a.e("handleConnectFailEvent carlife ui not front, skip event:");
            e2.append(c.d(CarlifeConnection.class, i));
            Log.i(str2, e2.toString());
        } else {
            switch (i) {
                case EVT_CONNECT_FAIL_TIMEOUT /* -16383999 */:
                case EVT_CONNECT_FAIL_UNKNOWN /* -16383996 */:
                case EVT_CONNECT_FAIL_NOT_FOUND_DEVICE /* -16383995 */:
                case EVT_CONNECT_FAIL_INVALID_DEVICE_INFO /* -16383992 */:
                case EVT_CONNECT_FAIL_PROTO_NOT_MATCH /* -16383990 */:
                case EVT_CONNECT_FAIL_IAP2_AUTH_FAIL /* -16383989 */:
                    String str3 = TAG;
                    StringBuilder e3 = a.e("-- ");
                    e3.append(c.d(CarlifeConnection.class, i));
                    Log.i(str3, e3.toString());
                    showWindowInternal(2005);
                    d0.m().f();
                    return;
                case EVT_CONNECT_FAIL_APP_NOT_INSTALL /* -16383998 */:
                    Log.i(TAG, "EVT_CONNECT_FAIL_APP_NOT_INSTALL");
                    showWindowInternal(2007);
                    d0.m().f();
                    return;
                case EVT_CONNECT_FAIL_AOA_NOT_SUPPORT /* -16383997 */:
                    Log.i(TAG, "EVT_CONNECT_FAIL_AOA_NOT_SUPPORT");
                    showWindowInternal(2006);
                    return;
                case EVT_CONNECT_FAIL_ENCODER_ERROR /* -16383994 */:
                    Log.i(TAG, "EVT_CONNECT_FAIL_ENCODER_ERROR");
                    return;
                case EVT_CONNECT_FAIL_PERMISSION_DENIED /* -16383993 */:
                    if (i == -16383993) {
                        Log.i(TAG, "EVT_CONNECT_FAIL_PERMISSION_DENIED");
                    }
                    showWindowInternal(2010);
                    hideWindowInternal(2008);
                    return;
                case EVT_CONNECT_FAIL_VIDEO_NOT_READY /* -16383991 */:
                    int connectionStatus = getConnectionStatus();
                    a.i("EVT_CONNECT_FAIL_VIDEO_NOT_READY current state:", connectionStatus, TAG);
                    if (connectionStatus != 1 || (handler = this.mHandler) == null) {
                        return;
                    }
                    handler.sendMessage(Message.obtain(null, EVT_TYPE_CONNECT_FAIL, EVT_CONNECT_FAIL_UNKNOWN, 0, null));
                    return;
                default:
                    return;
            }
        }
    }

    private void handleConnectProgress(int i) {
        if (i < 0) {
            a.i("handleConnectProgress fail, invalid progress value:", i, TAG);
            return;
        }
        this.mConnectProgress = i;
        EventBus.getDefault().post(new CarlifeManager.a(i));
    }

    private void handleConnectStateChanged(int i) {
        a.i("handleConnectStateChanged:", i, TAG);
        if (i == 1) {
            this.mHandler.removeCallbacks(this.mCarlifeDeviceCheckRunnable);
            if (!CarlifeManager.getInstance().isSwitching()) {
                stopConnectProgressMock();
            }
            this.mCarlifeContext.f305a = null;
            if (!CarlifeManager.getInstance().isSwitching()) {
                hideWindowInternal(2008);
                hideWindowInternal(2009);
            }
            Context context = this.mContext;
            System.currentTimeMillis();
            c.s(context, false);
        } else if (i == 2) {
            if (CarlifeManager.getInstance().isCarlifeUiFront() || c.k(this.mContext)) {
                startConnectProgressMock();
                showWindowInternal(2008);
            }
        } else if (i == 3) {
            this.mConnectedTime = System.currentTimeMillis();
        } else if (i != 4) {
        } else {
            stopConnectProgressMock();
            Context context2 = this.mContext;
            if (context2 == null) {
                Log.e(TAG, "CARLIFE_STATE_CASTING context is null");
                return;
            }
            c.s(context2, true);
            c.h(this.mContext);
            if (isCarlifeUiFront()) {
                c.t("com.ici.connectivity.activity.CarLifeActivity", "连接成功跳转");
            } else {
                c.t("", "连接成功跳转");
            }
            this.mHandler.postDelayed(new Runnable() { // from class: b.d.c.b.d
                @Override // java.lang.Runnable
                public final void run() {
                    CarlifeConnection.this.d();
                }
            }, 500L);
        }
    }

    private void handleNotification(int i, Object obj) {
        if (i == -16318459) {
            Log.i(TAG, "-- EVT_NOTIFY_MD_APP_EXIT");
            this.mHasConnectException = true;
            c.o(new Runnable() { // from class: b.d.c.b.g
                @Override // java.lang.Runnable
                public final void run() {
                    CarlifeConnection.this.e();
                }
            }, ItemTouchHelper.Callback.DRAG_SCROLL_ACCELERATION_LIMIT_TIME_MS);
        }
        if (!CarlifeManager.getInstance().isCarlifeUiFront() && !c.k(this.mContext)) {
            String str = TAG;
            StringBuilder e = a.e("handleNotification carlife ui not front, skip event:");
            e.append(c.d(CarlifeConnection.class, i));
            Log.i(str, e.toString());
            return;
        }
        switch (i) {
            case EVT_NOTIFY_MD_SCREEN_SHARE_REQUEST /* -16318463 */:
                if (getConnectionStatus() == 4) {
                    Log.i(TAG, "current is casting, skip event");
                    return;
                } else {
                    showWindowInternal(2009);
                    return;
                }
            case EVT_NOTIFY_HU_CASTING_FG /* -16318462 */:
                Log.i(TAG, "EVT_NOTIFY_HU_CASTING_FG");
                if (e0.d(this.mContext).b() > 0) {
                    c.n(new Runnable() { // from class: b.d.c.b.p
                        @Override // java.lang.Runnable
                        public final void run() {
                            CarlifeConnection.this.f();
                        }
                    });
                    return;
                }
                return;
            case EVT_NOTIFY_HU_CASTING_BG /* -16318461 */:
                Log.i(TAG, "EVT_NOTIFY_HU_CASTING_BG");
                return;
            case EVT_NOTIFY_MD_APP_START_REQUEST /* -16318460 */:
                if (getConnectionStatus() == 4) {
                    Log.i(TAG, "current is casting, skip event");
                    return;
                }
                try {
                    if (((Boolean) obj).booleanValue()) {
                        showWindowInternal(2009);
                        return;
                    } else {
                        showWindowInternal(2008);
                        return;
                    }
                } catch (Exception e2) {
                    a.h(e2, a.e("EVT_NOTIFY_MD_APP_START_REQUEST fail. "), TAG);
                    return;
                }
            default:
                return;
        }
    }

    private ICarlifeWindowContract helpWindowContract() {
        View e = this.mDWM.e(2011);
        if (e == null) {
            return ICarlifeWindowContract.f545a;
        }
        Object tag = e.getTag();
        if (tag instanceof ICarlifeWindowContract) {
            return (ICarlifeWindowContract) tag;
        }
        return ICarlifeWindowContract.f545a;
    }

    private void hideWindowInternal(int i) {
        this.mDWM.h(i);
    }

    public static /* synthetic */ void n(ICarlifeWindowContract.ButtonID buttonID) {
        if (buttonID == ICarlifeWindowContract.ButtonID.PrimaryButton) {
            d0.m().f();
        }
    }

    private void setWindowButtonHook(int i) {
        View view = this.mDWM.f334b.get(Integer.valueOf(i));
        if (!(view instanceof ICarlifeWindowContract)) {
            String str = TAG;
            StringBuilder e = a.e("setWindowButtonHook fail, ");
            e.append(y.w(i));
            e.append(" view invalid");
            Log.e(str, e.toString());
            return;
        }
        ICarlifeWindowContract iCarlifeWindowContract = (ICarlifeWindowContract) view;
        if (i == 2005) {
            Log.i(TAG, "setWindowButtonHook TYPE_FAIL_UNKNOWN_REASON");
            iCarlifeWindowContract.setButtonEventHook(new Consumer() { // from class: b.d.c.b.m
                @Override // java.util.function.Consumer
                public final void accept(Object obj) {
                    CarlifeConnection.this.m((ICarlifeWindowContract.ButtonID) obj);
                }
            });
        } else if (i == 2010) {
            Log.i(TAG, "setWindowButtonHook TYPE_CARLIFE_UNAVAILABLE");
            iCarlifeWindowContract.setButtonEventHook(new Consumer() { // from class: b.d.c.b.i
                @Override // java.util.function.Consumer
                public final void accept(Object obj) {
                    CarlifeConnection.n((ICarlifeWindowContract.ButtonID) obj);
                }
            });
        } else if (i == 2011) {
            Log.i(TAG, "setWindowButtonHook TYPE_CARLIFE_HELP");
            iCarlifeWindowContract.setButtonEventHook(new Consumer() { // from class: b.d.c.b.a
                @Override // java.util.function.Consumer
                public final void accept(Object obj) {
                    CarlifeConnection.this.o((ICarlifeWindowContract.ButtonID) obj);
                }
            });
        }
    }

    private void showUserAgreementDialog(final DeviceInfoBean deviceInfoBean) {
        showWindowInternal(2004).setButtonEventHook(new Consumer() { // from class: b.d.c.b.q
            @Override // java.util.function.Consumer
            public final void accept(Object obj) {
                CarlifeConnection.this.p(deviceInfoBean, (ICarlifeWindowContract.ButtonID) obj);
            }
        });
    }

    private ICarlifeWindowContract showWindowInternal(int i) {
        if (CarlifeProxy.getInstance().getCarlifeConnectState() == 4) {
            String str = TAG;
            Log.e(str, "showWindowInternal current is casting, skip show window:" + i);
            return ICarlifeWindowContract.f545a;
        }
        this.mDWM.p(i, e0.c());
        if (i == 2011) {
            return helpWindowContract();
        }
        try {
            return (ICarlifeWindowContract) this.mDWM.f334b.get(Integer.valueOf(i));
        } catch (Exception unused) {
            return ICarlifeWindowContract.f545a;
        }
    }

    private void startConnectProgressMock() {
        int connectionStatus = getConnectionStatus();
        String str = TAG;
        StringBuilder e = a.e("startConnectProgressMock, mProcessMockHandler == null:");
        e.append(this.mProcessMockHandler == null);
        e.append(", state:");
        e.append(connectionStatus);
        Log.i(str, e.toString());
        if (connectionStatus == 4) {
            stopConnectProgressMock();
        } else if (this.mProcessMockHandler == null) {
            this.mConnectProgress = 0;
            Handler handler = new Handler(new Handler.Callback() { // from class: b.d.c.b.h
                @Override // android.os.Handler.Callback
                public final boolean handleMessage(Message message) {
                    return CarlifeConnection.this.q(message);
                }
            });
            this.mProcessMockHandler = handler;
            handler.sendEmptyMessageDelayed(1, 500L);
        }
    }

    private void stopConnectProgressMock() {
        Log.i(TAG, "stopConnectProgressMock");
        Handler handler = this.mProcessMockHandler;
        if (handler != null) {
            handler.removeMessages(1);
            this.mProcessMockHandler = null;
        }
        this.mConnectProgress = -1;
    }

    public void a(String str, int i) {
        if (this.mServiceHolder.b()) {
            Log.i(TAG, "connect waitServiceAlive OK, start connect...");
            CarlifeProxy.getInstance().connect(str, i);
            return;
        }
        Log.e(TAG, "connect waitServiceAlive timeout");
        this.mCarlifeContext.f305a = null;
        if (c.k(this.mContext)) {
            Log.e(TAG, "wait service alive fail, show unknown reason prompt");
            showWindow(2005);
        }
    }

    public void b() {
        String str = TAG;
        StringBuilder e = a.e("disconnect waitServiceAlive:");
        e.append(this.mServiceHolder.b());
        Log.i(str, e.toString());
    }

    public /* synthetic */ void c() {
        this.mHasConnectException = false;
    }

    @Override // b.d.c.b.w
    public void connect(final String str, final int i) {
        if (TextUtils.isEmpty(str)) {
            Log.i(TAG, "connect fail, serial invalid");
            hideWindow(2008);
            hideWindow(2009);
            return;
        }
        if (TextUtils.isEmpty(this.mCarlifeContext.f305a) || !this.mCarlifeContext.f305a.equals(str)) {
            this.mCarlifeContext.f305a = str;
        }
        if (!this.mServiceHolder.b()) {
            Log.i(TAG, "connect waitServiceAlive");
            this.mServiceHolder.f(new Runnable() { // from class: b.d.c.b.n
                @Override // java.lang.Runnable
                public final void run() {
                    CarlifeConnection.this.a(str, i);
                }
            });
            return;
        }
        CarlifeProxy.getInstance().connect(str, i);
    }

    public /* synthetic */ void d() {
        e0.d(this.mContext).f();
    }

    @Override // b.d.c.b.w
    public void disconnect() {
        this.mCarlifeContext.f305a = null;
        if (!this.mServiceHolder.b()) {
            Log.i(TAG, "disconnect waitServiceAlive");
            this.mServiceHolder.f(new Runnable() { // from class: b.d.c.b.f
                @Override // java.lang.Runnable
                public final void run() {
                    CarlifeConnection.this.b();
                }
            });
            return;
        }
        CarlifeProxy.getInstance().exitCarlife();
    }

    public /* synthetic */ void e() {
        this.mHasConnectException = false;
    }

    public /* synthetic */ void f() {
        e0.d(this.mContext).f();
    }

    public void g() {
        if (TextUtils.isEmpty(this.mCarlifeContext.f305a)) {
            Log.i(TAG, "mCarlifeDeviceCheckRunnable: carlife context serial is empty");
            return;
        }
        boolean z = false;
        DeviceInfoBean b2 = c.b(this.mCarlifeContext.f305a);
        if ((b2 == null || b2.getUsbDevice() == null) && c.g() == null) {
            z = true;
        }
        if (z) {
            Log.i(TAG, "mCarlifeDeviceCheckRunnable: device not found, disconnect carlife");
            hideWindow(2008);
            hideWindow(2009);
            disconnect();
        }
    }

    @Override // b.d.c.b.w
    public u getCarlifeContext() {
        return this.mCarlifeContext;
    }

    @Override // b.d.c.b.w
    public int getConnectProgress() {
        a.n(a.e("CarlifeConnection getConnectProgress p:"), this.mConnectProgress, TAG);
        int i = this.mConnectProgress;
        if (i == -1) {
            this.mConnectProgress = 0;
            startConnectProgressMock();
            return 0;
        }
        return i;
    }

    @Override // b.d.c.b.w
    public int getConnectionStatus() {
        return CarlifeProxy.getInstance().getCarlifeConnectState();
    }

    public boolean h(Message message) {
        int i = message.what;
        if (i == -16449536) {
            Object obj = message.obj;
            if (obj == null) {
                return true;
            }
            try {
                handleConnectProgress(((Integer) obj).intValue());
            } catch (Exception unused) {
            }
        } else if (i == -16384000) {
            ConnectionManager.getInstance().setConnectFailReconnectFlag(true);
            this.mHandler.sendEmptyMessageDelayed(101, ItemTouchHelper.Callback.DRAG_SCROLL_ACCELERATION_LIMIT_TIME_MS);
            handleConnectFailEvent(message.arg1, message.obj);
        } else if (i == -16318464) {
            handleNotification(message.arg1, message.obj);
        } else if (i == 101) {
            ConnectionManager.getInstance().setConnectFailReconnectFlag(false);
        } else if (i != 1) {
            if (i == 2 && message.obj != null) {
                int b2 = this.mDWM.b();
                if (b2 < 0) {
                    String str = TAG;
                    StringBuilder e = a.e("EVT_TYPE_WINDOW_STATE_CHANGE type < 0, CL state:");
                    e.append(getConnectionStatus());
                    Log.i(str, e.toString());
                    if (getConnectionStatus() != 4) {
                        b.g(true);
                    }
                } else if (b2 > 2000 && b2 == 2011) {
                    b.g(true);
                }
            }
        } else {
            handleConnectStateChanged(message.arg1);
        }
        return true;
    }

    public void handleCarlifeConnection(DeviceInfoBean deviceInfoBean) {
        if (!b.d.c.a.a.a().d()) {
            Log.i(TAG, "handleCarlifeConnection is not support carlife");
        } else if (deviceInfoBean != null && !TextUtils.isEmpty(deviceInfoBean.getSerialNum())) {
            if (!c.j(deviceInfoBean.getSerialNum())) {
                String string = this.mContext.getString(R$string.cl_may_be_history_chk_usb, deviceInfoBean.getDeviceName());
                a.k("handleCarlifeConnection:", string, TAG);
                c.r(this.mContext, string);
            } else if (d0.m().p(4, null, deviceInfoBean.getSerialNum())) {
                showWindowInternal(2008);
                d0.m().c();
            } else {
                showUserAgreementDialog(deviceInfoBean);
            }
        } else {
            String string2 = this.mContext.getString(R$string.cl_invalid_carlife_device);
            if (deviceInfoBean != null && !TextUtils.isEmpty(deviceInfoBean.getDeviceName())) {
                string2 = string2 + ":" + deviceInfoBean.getDeviceName();
            }
            a.k("handleCarlifeConnection:", string2, TAG);
            c.r(this.mContext, string2);
        }
    }

    @Override // b.d.c.b.w
    public boolean hasConnectException() {
        return this.mHasConnectException;
    }

    @Override // b.d.c.b.w
    public void hideWindow(int i) {
        hideWindowInternal(i);
    }

    public void i(int i, boolean z) {
        if (i <= 2000) {
            return;
        }
        String str = TAG;
        StringBuilder e = a.e("carlife window state changed -> ");
        e.append(y.w(i));
        a.p(e, z ? ", SHOW" : ", HIDE", str);
        Handler handler = this.mHandler;
        if (handler != null) {
            handler.removeMessages(2);
            this.mHandler.sendMessageDelayed(Message.obtain(null, 2, i, 0, Boolean.valueOf(z)), 200L);
        }
        if (i == 2008 && !z && getConnectionStatus() == 1) {
            stopConnectProgressMock();
        }
        if (z) {
            setWindowButtonHook(i);
        }
    }

    public void initialize() {
        CarlifeManager.getInstance().initialize(this, this.mContext);
        v vVar = this.mServiceHolder;
        if (vVar != null) {
            Log.i(v.j, "-- registerHolder");
            vVar.a(false);
            CarlifeProxy.getInstance().registerConnectionEventObserver(this.mConnectionEventObserver);
            CarlifeProxy.getInstance().registerConnectStateChangeListener(this.mCarlifeSateListener);
            e0.d(this.mContext).o(this.mWindowStateCB);
            i0 a2 = i0.a(this.mContext);
            IntConsumer intConsumer = this.mSystemKeyObs;
            if (a2 != null) {
                if (intConsumer != null && !a2.f342b.contains(intConsumer)) {
                    if (a2.f342b.isEmpty()) {
                        IntentFilter intentFilter = new IntentFilter();
                        intentFilter.addAction("android.intent.action.CLOSE_SYSTEM_DIALOGS");
                        a2.f341a.registerReceiver(a2, intentFilter);
                    }
                    a2.f342b.add(intConsumer);
                }
                d0.m().v(this);
                return;
            }
            throw null;
        }
        throw null;
    }

    public boolean isCarlifeUiFront() {
        return this.mCarlifeUiFront;
    }

    public void j(int i) {
        int b2 = this.mDWM.b();
        if (b2 > 2000) {
            String str = TAG;
            StringBuilder e = a.e("HOME KEY hide window -> ");
            e.append(y.w(b2));
            Log.i(str, e.toString());
            hideWindowInternal(b2);
        }
    }

    public /* synthetic */ void k(int i) {
        this.mHandler.sendMessage(Message.obtain(null, 1, i, 0));
    }

    public /* synthetic */ void l(int i, int i2, int i3, Object obj) {
        Handler handler = this.mHandler;
        if (handler != null) {
            handler.sendMessage(Message.obtain(null, i2, i3, 0, obj));
        }
    }

    public /* synthetic */ void m(ICarlifeWindowContract.ButtonID buttonID) {
        if (buttonID == ICarlifeWindowContract.ButtonID.PrimaryButton && reconnect()) {
            showWindowInternal(2008);
            hideWindowInternal(2005);
        }
    }

    public /* synthetic */ void o(ICarlifeWindowContract.ButtonID buttonID) {
        if (buttonID == ICarlifeWindowContract.ButtonID.PrimaryButton && reconnect()) {
            showWindowInternal(2008);
            hideWindowInternal(2011);
            hideWindowInternal(2005);
        }
    }

    @Override // b.d.c.f.h0
    public void onBtPairedStateChanged(int i) {
    }

    /* JADX WARN: Removed duplicated region for block: B:25:0x0061  */
    /* JADX WARN: Removed duplicated region for block: B:26:0x0070  */
    @Override // b.d.c.f.h0
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void onDeviceListChanged(int r6) {
        /*
            r5 = this;
            r0 = 2
            if (r6 == r0) goto L4
            return
        L4:
            b.d.c.b.u r6 = r5.mCarlifeContext
            java.lang.String r6 = r6.f305a
            boolean r6 = android.text.TextUtils.isEmpty(r6)
            if (r6 == 0) goto L16
            java.lang.String r6 = com.ici.connectivitylib.connection.CarlifeConnection.TAG
            java.lang.String r0 = "onDeviceListChanged: carlife context serial is empty"
            android.util.Log.i(r6, r0)
            return
        L16:
            b.d.c.f.d0 r6 = b.d.c.f.d0.m()
            java.util.List r6 = r6.l(r0)
            r0 = 1
            r1 = 0
            if (r6 == 0) goto L5e
            boolean r2 = r6.isEmpty()
            if (r2 != 0) goto L5e
            java.util.Iterator r6 = r6.iterator()
            r2 = r1
        L2d:
            boolean r3 = r6.hasNext()
            if (r3 == 0) goto L52
            java.lang.Object r2 = r6.next()
            com.ici.connectivitylib.bean.DeviceInfoBean r2 = (com.ici.connectivitylib.bean.DeviceInfoBean) r2
            android.hardware.usb.UsbDevice r3 = r2.getUsbDevice()
            boolean r3 = b.d.c.h.f.c.m(r3)
            b.d.c.b.u r4 = r5.mCarlifeContext
            java.lang.String r4 = r4.f305a
            java.lang.String r2 = r2.getSerialNum()
            boolean r2 = android.text.TextUtils.equals(r4, r2)
            if (r2 == 0) goto L50
            r1 = r0
        L50:
            r2 = r3
            goto L2d
        L52:
            if (r1 != 0) goto L5e
            if (r2 == 0) goto L5e
            java.lang.String r6 = com.ici.connectivitylib.connection.CarlifeConnection.TAG
            java.lang.String r1 = "onDeviceListChanged:not found target serial device, but has udc, set found is true"
            android.util.Log.i(r6, r1)
            goto L5f
        L5e:
            r0 = r1
        L5f:
            if (r0 == 0) goto L70
            java.lang.String r6 = com.ici.connectivitylib.connection.CarlifeConnection.TAG
            java.lang.String r0 = "onDeviceListChanged: found target device, clear mCarlifeDeviceCheckRunnable"
            android.util.Log.i(r6, r0)
            android.os.Handler r6 = r5.mHandler
            java.lang.Runnable r0 = r5.mCarlifeDeviceCheckRunnable
            r6.removeCallbacks(r0)
            goto L80
        L70:
            java.lang.String r6 = com.ici.connectivitylib.connection.CarlifeConnection.TAG
            java.lang.String r0 = "onDeviceListChanged: not found target device, set mCarlifeDeviceCheckRunnable"
            android.util.Log.i(r6, r0)
            android.os.Handler r6 = r5.mHandler
            java.lang.Runnable r0 = r5.mCarlifeDeviceCheckRunnable
            r1 = 2000(0x7d0, double:9.88E-321)
            r6.postDelayed(r0, r1)
        L80:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: com.ici.connectivitylib.connection.CarlifeConnection.onDeviceListChanged(int):void");
    }

    public void onForgetDeviceSuccess(boolean z) {
    }

    @Override // b.d.c.b.w
    public void onLifecycleChanged(LifecycleOwner lifecycleOwner, Lifecycle.Event event) {
        String str = TAG;
        Log.i(str, "lifecycle changed -> event:" + event + ", owner:" + lifecycleOwner);
        if (event != Lifecycle.Event.ON_CREATE && event != Lifecycle.Event.ON_RESUME) {
            if (event == Lifecycle.Event.ON_PAUSE || event == Lifecycle.Event.ON_DESTROY) {
                this.mCarlifeUiFront = false;
                int b2 = this.mDWM.b();
                if (b2 == 2011) {
                    String str2 = TAG;
                    StringBuilder e = a.e("lifecycle hide window -> ");
                    e.append(y.w(b2));
                    Log.i(str2, e.toString());
                    hideWindowInternal(b2);
                    return;
                }
                return;
            }
            return;
        }
        this.mCarlifeUiFront = true;
    }

    @Override // b.d.c.f.h0
    public void onWiredCarPlayDetect(boolean z) {
    }

    public void p(DeviceInfoBean deviceInfoBean, ICarlifeWindowContract.ButtonID buttonID) {
        if (buttonID == ICarlifeWindowContract.ButtonID.CloseButton) {
            return;
        }
        if (deviceInfoBean != null && !TextUtils.isEmpty(deviceInfoBean.getSerialNum())) {
            if (buttonID == ICarlifeWindowContract.ButtonID.PrimaryButton) {
                showWindowInternal(2008);
                d0.m().c();
                d0.m().y(4, true);
                return;
            } else if (buttonID == ICarlifeWindowContract.ButtonID.SecondButton) {
                d0.m().y(4, false);
                return;
            } else {
                return;
            }
        }
        String str = TAG;
        Log.e(str, "showUserAgreementDialog. invalid device. " + deviceInfoBean);
    }

    public boolean q(Message message) {
        int i = this.mConnectProgress;
        if (i == -1) {
            Log.e(TAG, "mProcessMockHandler mConnectProgress == -1, stop mock");
            stopConnectProgressMock();
            return true;
        }
        this.mConnectProgress = i + 10;
        String str = TAG;
        StringBuilder e = a.e("mProcessMockHandler mConnectProgress=");
        e.append(this.mConnectProgress);
        Log.i(str, e.toString());
        EventBus.getDefault().post(new CarlifeManager.a(this.mConnectProgress));
        if (this.mConnectProgress == 100) {
            this.mConnectProgress = 0;
        }
        int connectionStatus = getConnectionStatus();
        if (this.mProcessMockHandler != null && connectionStatus != 4 && (connectionStatus != 1 || CarlifeManager.getInstance().isSwitching())) {
            this.mProcessMockHandler.sendEmptyMessageDelayed(1, 500L);
        }
        return true;
    }

    public boolean reconnect() {
        c.i(this.mContext);
        hideWindowInternal(2011);
        hideWindowInternal(2005);
        return false;
    }

    @Override // b.d.c.b.w
    public void setConnectingDeviceSerial(String str) {
        this.mCarlifeContext.f305a = str;
    }

    @Override // b.d.c.b.w
    public void showWindow(int i) {
        showWindowInternal(i);
    }

    public void unInitialize() {
        CarlifeManager.getInstance().unInitialize();
        c c = c.c();
        if (c != null) {
            c.c = null;
            HandlerThread handlerThread = c.f474a;
            if (handlerThread != null) {
                try {
                    handlerThread.quitSafely();
                } catch (Exception unused) {
                }
            }
            v vVar = this.mServiceHolder;
            if (vVar != null) {
                Log.i(v.j, "-- unregisterHolder");
                Log.i(v.j, "unbindCarlifeService");
                try {
                    vVar.c.removeMessages(0);
                    vVar.f306a.unbindService(vVar.g);
                    vVar.c.sendEmptyMessageDelayed(0, 1500L);
                } catch (Exception e) {
                    a.h(e, a.e("unbindCarlifeService error: "), v.j);
                    vVar.c.removeMessages(0);
                    vVar.c.sendEmptyMessage(0);
                }
                CarlifeProxy.getInstance().unregisterConnectionEventObserver(this.mConnectionEventObserver);
                CarlifeProxy.getInstance().unregisterConnectStateChangeListener(this.mCarlifeSateListener);
                e0.d(this.mContext).q(this.mWindowStateCB);
                i0 a2 = i0.a(this.mContext);
                IntConsumer intConsumer = this.mSystemKeyObs;
                if (intConsumer != null) {
                    a2.f342b.remove(intConsumer);
                }
                if (a2.f342b.isEmpty()) {
                    a2.f341a.unregisterReceiver(a2);
                }
                d0.m().x(this);
                return;
            }
            throw null;
        }
        throw null;
    }

    @Override // b.d.c.f.h0
    public void updateSignalAndBattery(String str, String str2, int i, int i2) {
    }
}
