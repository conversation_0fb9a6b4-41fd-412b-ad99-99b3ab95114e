package com.ici.connectivitylib.bean;

import android.text.TextUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;

/* loaded from: classes.dex */
public class ConnectionRecord {
    @JSONField(name = "serial")
    public String serial = "";
    @JSONField(name = "port")
    public int port = -1;
    @JSONField(name = "connectTimestamp")
    public long connectTimestamp = 0;
    @JSONField(name = "disconnectTimestamp")
    public long disconnectTimestamp = 0;

    public static ConnectionRecord parser(String str) {
        if (TextUtils.isEmpty(str)) {
            return null;
        }
        try {
            return (ConnectionRecord) JSON.toJavaObject(JSON.parseObject(str), ConnectionRecord.class);
        } catch (Exception unused) {
            return null;
        }
    }

    public String buildJsonString() {
        return JSON.toJSONString(this);
    }

    public long getConnectTimestamp() {
        return this.connectTimestamp;
    }

    public long getDisconnectTimestamp() {
        return this.disconnectTimestamp;
    }

    public int getPort() {
        return this.port;
    }

    public String getSerial() {
        return this.serial;
    }

    public void setConnectTimestamp(long j) {
        this.connectTimestamp = j;
    }

    public void setDisconnectTimestamp(long j) {
        this.disconnectTimestamp = j;
    }

    public void setPort(int i) {
        this.port = i;
    }

    public void setSerial(String str) {
        this.serial = str;
    }
}
