package com.alibaba.fastjson.parser.deserializer;

import b.a.b.a.a;
import com.alibaba.fastjson.asm.ClassWriter;
import com.alibaba.fastjson.asm.FieldWriter;
import com.alibaba.fastjson.asm.Label;
import com.alibaba.fastjson.asm.MethodVisitor;
import com.alibaba.fastjson.asm.MethodWriter;
import com.alibaba.fastjson.asm.Opcodes;
import com.alibaba.fastjson.asm.Type;
import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.parser.JSONLexer;
import com.alibaba.fastjson.parser.JSONLexerBase;
import com.alibaba.fastjson.parser.ParseContext;
import com.alibaba.fastjson.parser.ParserConfig;
import com.alibaba.fastjson.parser.SymbolTable;
import com.alibaba.fastjson.util.ASMClassLoader;
import com.alibaba.fastjson.util.ASMUtils;
import com.alibaba.fastjson.util.FieldInfo;
import com.alibaba.fastjson.util.JavaBeanInfo;
import com.alibaba.fastjson.util.TypeUtils;
import com.baidu.che.voice.control.vts.param.VtsConstParam;
import com.yfve.ici.service.contanst.ServiceConstant;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

/* loaded from: classes.dex */
public class ASMDeserializerFactory implements Opcodes {
    public static final String DefaultJSONParser = ASMUtils.type(DefaultJSONParser.class);
    public static final String JSONLexerBase = ASMUtils.type(JSONLexerBase.class);
    public final ASMClassLoader classLoader;
    public final AtomicLong seed = new AtomicLong();

    public ASMDeserializerFactory(ClassLoader classLoader) {
        this.classLoader = classLoader instanceof ASMClassLoader ? (ASMClassLoader) classLoader : new ASMClassLoader(classLoader);
    }

    private void _batchSet(Context context, MethodVisitor methodVisitor) {
        _batchSet(context, methodVisitor, true);
    }

    private void _createInstance(Context context, MethodVisitor methodVisitor) {
        Constructor<?> constructor = context.beanInfo.defaultConstructor;
        if (Modifier.isPublic(constructor.getModifiers())) {
            methodVisitor.visitTypeInsn(Opcodes.NEW, ASMUtils.type(context.getInstClass()));
            methodVisitor.visitInsn(89);
            methodVisitor.visitMethodInsn(Opcodes.INVOKESPECIAL, ASMUtils.type(constructor.getDeclaringClass()), "<init>", "()V");
            methodVisitor.visitVarInsn(58, context.var("instance"));
            return;
        }
        methodVisitor.visitVarInsn(25, 0);
        methodVisitor.visitVarInsn(25, 1);
        methodVisitor.visitVarInsn(25, 0);
        methodVisitor.visitFieldInsn(Opcodes.GETFIELD, ASMUtils.type(JavaBeanDeserializer.class), "clazz", "Ljava/lang/Class;");
        methodVisitor.visitMethodInsn(Opcodes.INVOKESPECIAL, ASMUtils.type(JavaBeanDeserializer.class), "createInstance", a.c(a.e("(L"), DefaultJSONParser, ";Ljava/lang/reflect/Type;)Ljava/lang/Object;"));
        methodVisitor.visitTypeInsn(Opcodes.CHECKCAST, ASMUtils.type(context.getInstClass()));
        methodVisitor.visitVarInsn(58, context.var("instance"));
    }

    private void _deserObject(Context context, MethodVisitor methodVisitor, FieldInfo fieldInfo, Class<?> cls, int i) {
        int i2;
        _getFieldDeser(context, methodVisitor, fieldInfo);
        Label label = new Label();
        Label label2 = new Label();
        int i3 = 1;
        if ((fieldInfo.parserFeatures & Feature.SupportArrayToBean.mask) != 0) {
            methodVisitor.visitInsn(89);
            methodVisitor.visitTypeInsn(Opcodes.INSTANCEOF, ASMUtils.type(JavaBeanDeserializer.class));
            methodVisitor.visitJumpInsn(Opcodes.IFEQ, label);
            methodVisitor.visitTypeInsn(Opcodes.CHECKCAST, ASMUtils.type(JavaBeanDeserializer.class));
            methodVisitor.visitVarInsn(25, 1);
            if (fieldInfo.fieldType instanceof Class) {
                methodVisitor.visitLdcInsn(Type.getType(ASMUtils.desc(fieldInfo.fieldClass)));
            } else {
                methodVisitor.visitVarInsn(25, 0);
                methodVisitor.visitLdcInsn(Integer.valueOf(i));
                methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, ASMUtils.type(JavaBeanDeserializer.class), "getFieldType", "(I)Ljava/lang/reflect/Type;");
            }
            methodVisitor.visitLdcInsn(fieldInfo.name);
            methodVisitor.visitLdcInsn(Integer.valueOf(fieldInfo.parserFeatures));
            methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, ASMUtils.type(JavaBeanDeserializer.class), "deserialze", a.c(a.e("(L"), DefaultJSONParser, ";Ljava/lang/reflect/Type;Ljava/lang/Object;I)Ljava/lang/Object;"));
            methodVisitor.visitTypeInsn(Opcodes.CHECKCAST, ASMUtils.type(cls));
            methodVisitor.visitVarInsn(58, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
            methodVisitor.visitJumpInsn(Opcodes.GOTO, label2);
            methodVisitor.visitLabel(label);
            i3 = 1;
            i2 = 25;
        } else {
            i2 = 25;
        }
        methodVisitor.visitVarInsn(i2, i3);
        if (fieldInfo.fieldType instanceof Class) {
            methodVisitor.visitLdcInsn(Type.getType(ASMUtils.desc(fieldInfo.fieldClass)));
        } else {
            methodVisitor.visitVarInsn(i2, 0);
            methodVisitor.visitLdcInsn(Integer.valueOf(i));
            methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, ASMUtils.type(JavaBeanDeserializer.class), "getFieldType", "(I)Ljava/lang/reflect/Type;");
        }
        methodVisitor.visitLdcInsn(fieldInfo.name);
        methodVisitor.visitMethodInsn(Opcodes.INVOKEINTERFACE, ASMUtils.type(ObjectDeserializer.class), "deserialze", a.c(a.e("(L"), DefaultJSONParser, ";Ljava/lang/reflect/Type;Ljava/lang/Object;)Ljava/lang/Object;"));
        methodVisitor.visitTypeInsn(Opcodes.CHECKCAST, ASMUtils.type(cls));
        methodVisitor.visitVarInsn(58, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
        methodVisitor.visitLabel(label2);
    }

    private void _deserialize_endCheck(Context context, MethodVisitor methodVisitor, Label label) {
        methodVisitor.visitIntInsn(21, context.var("matchedCount"));
        methodVisitor.visitJumpInsn(Opcodes.IFLE, label);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "token", "()I");
        methodVisitor.visitLdcInsn(13);
        methodVisitor.visitJumpInsn(160, label);
        _quickNextTokenComma(context, methodVisitor);
    }

    /* JADX WARN: Removed duplicated region for block: B:131:0x0c05  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private void _deserialze(com.alibaba.fastjson.asm.ClassWriter r24, com.alibaba.fastjson.parser.deserializer.ASMDeserializerFactory.Context r25) {
        /*
            Method dump skipped, instructions count: 3555
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.alibaba.fastjson.parser.deserializer.ASMDeserializerFactory._deserialze(com.alibaba.fastjson.asm.ClassWriter, com.alibaba.fastjson.parser.deserializer.ASMDeserializerFactory$Context):void");
    }

    private void _deserialzeArrayMapping(ClassWriter classWriter, Context context) {
        MethodWriter methodWriter;
        Class<JavaBeanDeserializer> cls;
        Class<JavaBeanDeserializer> cls2 = JavaBeanDeserializer.class;
        MethodWriter methodWriter2 = new MethodWriter(classWriter, 1, "deserialzeArrayMapping", a.c(a.e("(L"), DefaultJSONParser, ";Ljava/lang/reflect/Type;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;"), null, null);
        defineVarLexer(context, methodWriter2);
        methodWriter2.visitVarInsn(25, context.var("lexer"));
        methodWriter2.visitVarInsn(25, 1);
        String str = DefaultJSONParser;
        StringBuilder e = a.e("()");
        e.append(ASMUtils.desc(SymbolTable.class));
        methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, str, "getSymbolTable", e.toString());
        String str2 = JSONLexerBase;
        StringBuilder e2 = a.e("(");
        e2.append(ASMUtils.desc(SymbolTable.class));
        e2.append(")Ljava/lang/String;");
        methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, str2, "scanTypeName", e2.toString());
        methodWriter2.visitVarInsn(58, context.var("typeName"));
        Label label = new Label();
        methodWriter2.visitVarInsn(25, context.var("typeName"));
        methodWriter2.visitJumpInsn(Opcodes.IFNULL, label);
        methodWriter2.visitVarInsn(25, 1);
        String str3 = DefaultJSONParser;
        StringBuilder e3 = a.e("()");
        e3.append(ASMUtils.desc(ParserConfig.class));
        methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, str3, "getConfig", e3.toString());
        int i = 0;
        methodWriter2.visitVarInsn(25, 0);
        methodWriter2.visitFieldInsn(Opcodes.GETFIELD, ASMUtils.type(cls2), "beanInfo", ASMUtils.desc(JavaBeanInfo.class));
        methodWriter2.visitVarInsn(25, context.var("typeName"));
        String type = ASMUtils.type(cls2);
        StringBuilder e4 = a.e("(");
        e4.append(ASMUtils.desc(ParserConfig.class));
        e4.append(ASMUtils.desc(JavaBeanInfo.class));
        e4.append("Ljava/lang/String;)");
        e4.append(ASMUtils.desc(cls2));
        methodWriter2.visitMethodInsn(Opcodes.INVOKESTATIC, type, "getSeeAlso", e4.toString());
        methodWriter2.visitVarInsn(58, context.var("userTypeDeser"));
        methodWriter2.visitVarInsn(25, context.var("userTypeDeser"));
        methodWriter2.visitTypeInsn(Opcodes.INSTANCEOF, ASMUtils.type(cls2));
        methodWriter2.visitJumpInsn(Opcodes.IFEQ, label);
        methodWriter2.visitVarInsn(25, context.var("userTypeDeser"));
        methodWriter2.visitVarInsn(25, 1);
        methodWriter2.visitVarInsn(25, 2);
        methodWriter2.visitVarInsn(25, 3);
        methodWriter2.visitVarInsn(25, 4);
        methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, ASMUtils.type(cls2), "deserialzeArrayMapping", a.c(a.e("(L"), DefaultJSONParser, ";Ljava/lang/reflect/Type;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;"));
        methodWriter2.visitInsn(Opcodes.ARETURN);
        methodWriter2.visitLabel(label);
        _createInstance(context, methodWriter2);
        FieldInfo[] fieldInfoArr = context.beanInfo.sortedFields;
        int length = fieldInfoArr.length;
        while (i < length) {
            boolean z = i == length + (-1);
            int i2 = z ? 93 : 44;
            FieldInfo fieldInfo = fieldInfoArr[i];
            Class<?> cls3 = fieldInfo.fieldClass;
            java.lang.reflect.Type type2 = fieldInfo.fieldType;
            FieldInfo[] fieldInfoArr2 = fieldInfoArr;
            int i3 = length;
            if (cls3 != Byte.TYPE && cls3 != Short.TYPE && cls3 != Integer.TYPE) {
                int i4 = i;
                if (cls3 == Byte.class) {
                    methodWriter2.visitVarInsn(25, context.var("lexer"));
                    methodWriter2.visitVarInsn(16, i2);
                    methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "scanInt", "(C)I");
                    methodWriter2.visitVarInsn(58, a.m(a.d(methodWriter2, Opcodes.INVOKESTATIC, "java/lang/Byte", "valueOf", "(B)Ljava/lang/Byte;"), fieldInfo.name, "_asm", context));
                    Label label2 = new Label();
                    methodWriter2.visitVarInsn(25, context.var("lexer"));
                    methodWriter2.visitFieldInsn(Opcodes.GETFIELD, JSONLexerBase, "matchStat", "I");
                    methodWriter2.visitLdcInsn(5);
                    methodWriter2.visitJumpInsn(160, label2);
                    methodWriter2.visitInsn(1);
                    methodWriter2.visitVarInsn(58, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
                    methodWriter2.visitLabel(label2);
                } else if (cls3 == Short.class) {
                    methodWriter2.visitVarInsn(25, context.var("lexer"));
                    methodWriter2.visitVarInsn(16, i2);
                    methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "scanInt", "(C)I");
                    methodWriter2.visitVarInsn(58, a.m(a.d(methodWriter2, Opcodes.INVOKESTATIC, "java/lang/Short", "valueOf", "(S)Ljava/lang/Short;"), fieldInfo.name, "_asm", context));
                    Label label3 = new Label();
                    methodWriter2.visitVarInsn(25, context.var("lexer"));
                    methodWriter2.visitFieldInsn(Opcodes.GETFIELD, JSONLexerBase, "matchStat", "I");
                    methodWriter2.visitLdcInsn(5);
                    methodWriter2.visitJumpInsn(160, label3);
                    methodWriter2.visitInsn(1);
                    methodWriter2.visitVarInsn(58, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
                    methodWriter2.visitLabel(label3);
                } else if (cls3 == Integer.class) {
                    methodWriter2.visitVarInsn(25, context.var("lexer"));
                    methodWriter2.visitVarInsn(16, i2);
                    methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "scanInt", "(C)I");
                    methodWriter2.visitVarInsn(58, a.m(a.d(methodWriter2, Opcodes.INVOKESTATIC, "java/lang/Integer", "valueOf", "(I)Ljava/lang/Integer;"), fieldInfo.name, "_asm", context));
                    Label label4 = new Label();
                    methodWriter2.visitVarInsn(25, context.var("lexer"));
                    methodWriter2.visitFieldInsn(Opcodes.GETFIELD, JSONLexerBase, "matchStat", "I");
                    methodWriter2.visitLdcInsn(5);
                    methodWriter2.visitJumpInsn(160, label4);
                    methodWriter2.visitInsn(1);
                    methodWriter2.visitVarInsn(58, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
                    methodWriter2.visitLabel(label4);
                } else if (cls3 == Long.TYPE) {
                    methodWriter2.visitVarInsn(25, context.var("lexer"));
                    methodWriter2.visitVarInsn(16, i2);
                    StringBuilder d = a.d(methodWriter2, Opcodes.INVOKEVIRTUAL, JSONLexerBase, "scanLong", "(C)J");
                    d.append(fieldInfo.name);
                    d.append("_asm");
                    methodWriter2.visitVarInsn(55, context.var(d.toString(), 2));
                } else if (cls3 == Long.class) {
                    methodWriter2.visitVarInsn(25, context.var("lexer"));
                    methodWriter2.visitVarInsn(16, i2);
                    methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "scanLong", "(C)J");
                    methodWriter2.visitVarInsn(58, a.m(a.d(methodWriter2, Opcodes.INVOKESTATIC, "java/lang/Long", "valueOf", "(J)Ljava/lang/Long;"), fieldInfo.name, "_asm", context));
                    Label label5 = new Label();
                    methodWriter2.visitVarInsn(25, context.var("lexer"));
                    methodWriter2.visitFieldInsn(Opcodes.GETFIELD, JSONLexerBase, "matchStat", "I");
                    methodWriter2.visitLdcInsn(5);
                    methodWriter2.visitJumpInsn(160, label5);
                    methodWriter2.visitInsn(1);
                    methodWriter2.visitVarInsn(58, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
                    methodWriter2.visitLabel(label5);
                } else if (cls3 == Boolean.TYPE) {
                    methodWriter2.visitVarInsn(25, context.var("lexer"));
                    methodWriter2.visitVarInsn(16, i2);
                    methodWriter2.visitVarInsn(54, a.m(a.d(methodWriter2, Opcodes.INVOKEVIRTUAL, JSONLexerBase, "scanBoolean", "(C)Z"), fieldInfo.name, "_asm", context));
                } else if (cls3 == Float.TYPE) {
                    methodWriter2.visitVarInsn(25, context.var("lexer"));
                    methodWriter2.visitVarInsn(16, i2);
                    methodWriter2.visitVarInsn(56, a.m(a.d(methodWriter2, Opcodes.INVOKEVIRTUAL, JSONLexerBase, "scanFloat", "(C)F"), fieldInfo.name, "_asm", context));
                } else if (cls3 == Float.class) {
                    methodWriter2.visitVarInsn(25, context.var("lexer"));
                    methodWriter2.visitVarInsn(16, i2);
                    methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "scanFloat", "(C)F");
                    methodWriter2.visitVarInsn(58, a.m(a.d(methodWriter2, Opcodes.INVOKESTATIC, "java/lang/Float", "valueOf", "(F)Ljava/lang/Float;"), fieldInfo.name, "_asm", context));
                    Label label6 = new Label();
                    methodWriter2.visitVarInsn(25, context.var("lexer"));
                    methodWriter2.visitFieldInsn(Opcodes.GETFIELD, JSONLexerBase, "matchStat", "I");
                    methodWriter2.visitLdcInsn(5);
                    methodWriter2.visitJumpInsn(160, label6);
                    methodWriter2.visitInsn(1);
                    methodWriter2.visitVarInsn(58, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
                    methodWriter2.visitLabel(label6);
                } else if (cls3 == Double.TYPE) {
                    methodWriter2.visitVarInsn(25, context.var("lexer"));
                    methodWriter2.visitVarInsn(16, i2);
                    StringBuilder d2 = a.d(methodWriter2, Opcodes.INVOKEVIRTUAL, JSONLexerBase, "scanDouble", "(C)D");
                    d2.append(fieldInfo.name);
                    d2.append("_asm");
                    methodWriter2.visitVarInsn(57, context.var(d2.toString(), 2));
                } else if (cls3 == Double.class) {
                    methodWriter2.visitVarInsn(25, context.var("lexer"));
                    methodWriter2.visitVarInsn(16, i2);
                    methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "scanDouble", "(C)D");
                    methodWriter2.visitVarInsn(58, a.m(a.d(methodWriter2, Opcodes.INVOKESTATIC, "java/lang/Double", "valueOf", "(D)Ljava/lang/Double;"), fieldInfo.name, "_asm", context));
                    Label label7 = new Label();
                    methodWriter2.visitVarInsn(25, context.var("lexer"));
                    methodWriter2.visitFieldInsn(Opcodes.GETFIELD, JSONLexerBase, "matchStat", "I");
                    methodWriter2.visitLdcInsn(5);
                    methodWriter2.visitJumpInsn(160, label7);
                    methodWriter2.visitInsn(1);
                    methodWriter2.visitVarInsn(58, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
                    methodWriter2.visitLabel(label7);
                } else if (cls3 == Character.TYPE) {
                    methodWriter2.visitVarInsn(25, context.var("lexer"));
                    methodWriter2.visitVarInsn(16, i2);
                    methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "scanString", "(C)Ljava/lang/String;");
                    methodWriter2.visitInsn(3);
                    methodWriter2.visitVarInsn(54, a.m(a.d(methodWriter2, Opcodes.INVOKEVIRTUAL, "java/lang/String", "charAt", "(I)C"), fieldInfo.name, "_asm", context));
                } else {
                    if (cls3 == String.class) {
                        methodWriter2.visitVarInsn(25, context.var("lexer"));
                        methodWriter2.visitVarInsn(16, i2);
                        methodWriter2.visitVarInsn(58, a.m(a.d(methodWriter2, Opcodes.INVOKEVIRTUAL, JSONLexerBase, "scanString", "(C)Ljava/lang/String;"), fieldInfo.name, "_asm", context));
                    } else if (cls3 == BigDecimal.class) {
                        methodWriter2.visitVarInsn(25, context.var("lexer"));
                        methodWriter2.visitVarInsn(16, i2);
                        methodWriter2.visitVarInsn(58, a.m(a.d(methodWriter2, Opcodes.INVOKEVIRTUAL, JSONLexerBase, "scanDecimal", "(C)Ljava/math/BigDecimal;"), fieldInfo.name, "_asm", context));
                    } else if (cls3 == Date.class) {
                        methodWriter2.visitVarInsn(25, context.var("lexer"));
                        methodWriter2.visitVarInsn(16, i2);
                        methodWriter2.visitVarInsn(58, a.m(a.d(methodWriter2, Opcodes.INVOKEVIRTUAL, JSONLexerBase, "scanDate", "(C)Ljava/util/Date;"), fieldInfo.name, "_asm", context));
                    } else if (cls3 == UUID.class) {
                        methodWriter2.visitVarInsn(25, context.var("lexer"));
                        methodWriter2.visitVarInsn(16, i2);
                        methodWriter2.visitVarInsn(58, a.m(a.d(methodWriter2, Opcodes.INVOKEVIRTUAL, JSONLexerBase, "scanUUID", "(C)Ljava/util/UUID;"), fieldInfo.name, "_asm", context));
                    } else if (cls3.isEnum()) {
                        Label label8 = new Label();
                        Label label9 = new Label();
                        Label label10 = new Label();
                        Label label11 = new Label();
                        cls = cls2;
                        methodWriter2.visitVarInsn(25, context.var("lexer"));
                        methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "getCurrent", "()C");
                        methodWriter2.visitInsn(89);
                        methodWriter2.visitVarInsn(54, context.var("ch"));
                        methodWriter2.visitLdcInsn(110);
                        methodWriter2.visitJumpInsn(Opcodes.IF_ICMPEQ, label11);
                        methodWriter2.visitVarInsn(21, context.var("ch"));
                        methodWriter2.visitLdcInsn(34);
                        methodWriter2.visitJumpInsn(160, label8);
                        methodWriter2.visitLabel(label11);
                        methodWriter2.visitVarInsn(25, context.var("lexer"));
                        methodWriter2.visitLdcInsn(Type.getType(ASMUtils.desc(cls3)));
                        methodWriter2.visitVarInsn(25, 1);
                        String str4 = DefaultJSONParser;
                        StringBuilder e5 = a.e("()");
                        e5.append(ASMUtils.desc(SymbolTable.class));
                        methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, str4, "getSymbolTable", e5.toString());
                        methodWriter2.visitVarInsn(16, i2);
                        String str5 = JSONLexerBase;
                        StringBuilder e6 = a.e("(Ljava/lang/Class;");
                        e6.append(ASMUtils.desc(SymbolTable.class));
                        e6.append("C)Ljava/lang/Enum;");
                        methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, str5, "scanEnum", e6.toString());
                        methodWriter2.visitJumpInsn(Opcodes.GOTO, label10);
                        methodWriter2.visitLabel(label8);
                        methodWriter2.visitVarInsn(21, context.var("ch"));
                        methodWriter2.visitLdcInsn(48);
                        methodWriter2.visitJumpInsn(Opcodes.IF_ICMPLT, label9);
                        methodWriter2.visitVarInsn(21, context.var("ch"));
                        methodWriter2.visitLdcInsn(57);
                        methodWriter2.visitJumpInsn(Opcodes.IF_ICMPGT, label9);
                        _getFieldDeser(context, methodWriter2, fieldInfo);
                        methodWriter2.visitTypeInsn(Opcodes.CHECKCAST, ASMUtils.type(EnumDeserializer.class));
                        methodWriter2.visitVarInsn(25, context.var("lexer"));
                        methodWriter2.visitVarInsn(16, i2);
                        methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "scanInt", "(C)I");
                        methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, ASMUtils.type(EnumDeserializer.class), "valueOf", "(I)Ljava/lang/Enum;");
                        methodWriter2.visitJumpInsn(Opcodes.GOTO, label10);
                        methodWriter2.visitLabel(label9);
                        methodWriter2.visitVarInsn(25, 0);
                        methodWriter2.visitVarInsn(25, context.var("lexer"));
                        methodWriter2.visitVarInsn(16, i2);
                        methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, ASMUtils.type(cls), "scanEnum", a.c(a.e("(L"), JSONLexerBase, ";C)Ljava/lang/Enum;"));
                        methodWriter2.visitLabel(label10);
                        methodWriter2.visitTypeInsn(Opcodes.CHECKCAST, ASMUtils.type(cls3));
                        methodWriter2.visitVarInsn(58, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
                        methodWriter = methodWriter2;
                        i = i4;
                    } else {
                        cls = cls2;
                        if (Collection.class.isAssignableFrom(cls3)) {
                            Class<?> collectionItemClass = TypeUtils.getCollectionItemClass(type2);
                            if (collectionItemClass == String.class) {
                                if (cls3 != List.class && cls3 != Collections.class && cls3 != ArrayList.class) {
                                    methodWriter2.visitLdcInsn(Type.getType(ASMUtils.desc(cls3)));
                                    methodWriter2.visitMethodInsn(Opcodes.INVOKESTATIC, ASMUtils.type(TypeUtils.class), "createCollection", "(Ljava/lang/Class;)Ljava/util/Collection;");
                                } else {
                                    methodWriter2.visitTypeInsn(Opcodes.NEW, ASMUtils.type(ArrayList.class));
                                    methodWriter2.visitInsn(89);
                                    methodWriter2.visitMethodInsn(Opcodes.INVOKESPECIAL, ASMUtils.type(ArrayList.class), "<init>", "()V");
                                }
                                methodWriter2.visitVarInsn(58, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
                                methodWriter2.visitVarInsn(25, context.var("lexer"));
                                methodWriter2.visitVarInsn(25, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
                                methodWriter2.visitVarInsn(16, i2);
                                methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "scanStringArray", "(Ljava/util/Collection;C)V");
                                Label label12 = new Label();
                                methodWriter2.visitVarInsn(25, context.var("lexer"));
                                methodWriter2.visitFieldInsn(Opcodes.GETFIELD, JSONLexerBase, "matchStat", "I");
                                methodWriter2.visitLdcInsn(5);
                                methodWriter2.visitJumpInsn(160, label12);
                                methodWriter2.visitInsn(1);
                                methodWriter2.visitVarInsn(58, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
                                methodWriter2.visitLabel(label12);
                                i = i4;
                            } else {
                                Label label13 = new Label();
                                methodWriter2.visitVarInsn(25, context.var("lexer"));
                                methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "token", "()I");
                                methodWriter2.visitVarInsn(54, context.var("token"));
                                methodWriter2.visitVarInsn(21, context.var("token"));
                                int i5 = i4 == 0 ? 14 : 16;
                                methodWriter2.visitLdcInsn(Integer.valueOf(i5));
                                methodWriter2.visitJumpInsn(Opcodes.IF_ICMPEQ, label13);
                                methodWriter2.visitVarInsn(25, 1);
                                methodWriter2.visitLdcInsn(Integer.valueOf(i5));
                                methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, DefaultJSONParser, "throwException", "(I)V");
                                methodWriter2.visitLabel(label13);
                                Label label14 = new Label();
                                Label label15 = new Label();
                                methodWriter2.visitVarInsn(25, context.var("lexer"));
                                methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "getCurrent", "()C");
                                methodWriter2.visitVarInsn(16, 91);
                                methodWriter2.visitJumpInsn(160, label14);
                                methodWriter2.visitVarInsn(25, context.var("lexer"));
                                methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, VtsConstParam.CHOOSE_NEXT, "()C");
                                methodWriter2.visitInsn(87);
                                methodWriter2.visitVarInsn(25, context.var("lexer"));
                                methodWriter2.visitLdcInsn(14);
                                methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "setToken", "(I)V");
                                methodWriter2.visitJumpInsn(Opcodes.GOTO, label15);
                                methodWriter2.visitLabel(label14);
                                methodWriter2.visitVarInsn(25, context.var("lexer"));
                                methodWriter2.visitLdcInsn(14);
                                methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "nextToken", "(I)V");
                                methodWriter2.visitLabel(label15);
                                i = i4;
                                _newCollection(methodWriter2, cls3, i, false);
                                methodWriter2.visitInsn(89);
                                methodWriter2.visitVarInsn(58, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
                                _getCollectionFieldItemDeser(context, methodWriter2, fieldInfo, collectionItemClass);
                                methodWriter2.visitVarInsn(25, 1);
                                methodWriter2.visitLdcInsn(Type.getType(ASMUtils.desc(collectionItemClass)));
                                methodWriter2.visitVarInsn(25, 3);
                                String type3 = ASMUtils.type(cls);
                                StringBuilder e7 = a.e("(Ljava/util/Collection;");
                                e7.append(ASMUtils.desc(ObjectDeserializer.class));
                                e7.append("L");
                                methodWriter2.visitMethodInsn(Opcodes.INVOKESTATIC, type3, "parseArray", a.c(e7, DefaultJSONParser, ";Ljava/lang/reflect/Type;Ljava/lang/Object;)V"));
                            }
                        } else {
                            i = i4;
                            if (cls3.isArray()) {
                                methodWriter2.visitVarInsn(25, context.var("lexer"));
                                methodWriter2.visitLdcInsn(14);
                                methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "nextToken", "(I)V");
                                methodWriter2.visitVarInsn(25, 1);
                                methodWriter2.visitVarInsn(25, 0);
                                methodWriter2.visitLdcInsn(Integer.valueOf(i));
                                methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, ASMUtils.type(cls), "getFieldType", "(I)Ljava/lang/reflect/Type;");
                                methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, DefaultJSONParser, "parseObject", "(Ljava/lang/reflect/Type;)Ljava/lang/Object;");
                                methodWriter2.visitTypeInsn(Opcodes.CHECKCAST, ASMUtils.type(cls3));
                                methodWriter2.visitVarInsn(58, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
                            } else {
                                Label label16 = new Label();
                                Label label17 = new Label();
                                if (cls3 == Date.class) {
                                    methodWriter2.visitVarInsn(25, context.var("lexer"));
                                    methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "getCurrent", "()C");
                                    methodWriter2.visitLdcInsn(49);
                                    methodWriter2.visitJumpInsn(160, label16);
                                    methodWriter2.visitTypeInsn(Opcodes.NEW, ASMUtils.type(Date.class));
                                    methodWriter2.visitInsn(89);
                                    methodWriter2.visitVarInsn(25, context.var("lexer"));
                                    methodWriter2.visitVarInsn(16, i2);
                                    methodWriter2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "scanLong", "(C)J");
                                    methodWriter2.visitMethodInsn(Opcodes.INVOKESPECIAL, ASMUtils.type(Date.class), "<init>", "(J)V");
                                    methodWriter2.visitVarInsn(58, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
                                    methodWriter2.visitJumpInsn(Opcodes.GOTO, label17);
                                }
                                methodWriter2.visitLabel(label16);
                                _quickNextToken(context, methodWriter2, 14);
                                methodWriter = methodWriter2;
                                _deserObject(context, methodWriter2, fieldInfo, cls3, i);
                                methodWriter.visitVarInsn(25, context.var("lexer"));
                                methodWriter.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "token", "()I");
                                methodWriter.visitLdcInsn(15);
                                methodWriter.visitJumpInsn(Opcodes.IF_ICMPEQ, label17);
                                methodWriter.visitVarInsn(25, 0);
                                methodWriter.visitVarInsn(25, context.var("lexer"));
                                if (!z) {
                                    methodWriter.visitLdcInsn(16);
                                } else {
                                    methodWriter.visitLdcInsn(15);
                                }
                                String type4 = ASMUtils.type(cls);
                                StringBuilder e8 = a.e("(");
                                e8.append(ASMUtils.desc(JSONLexer.class));
                                e8.append("I)V");
                                methodWriter.visitMethodInsn(Opcodes.INVOKESPECIAL, type4, "check", e8.toString());
                                methodWriter.visitLabel(label17);
                            }
                        }
                        methodWriter = methodWriter2;
                    }
                    cls = cls2;
                    i = i4;
                    methodWriter = methodWriter2;
                }
                cls = cls2;
                methodWriter = methodWriter2;
                i = i4;
            } else {
                methodWriter = methodWriter2;
                cls = cls2;
                methodWriter.visitVarInsn(25, context.var("lexer"));
                methodWriter.visitVarInsn(16, i2);
                methodWriter.visitVarInsn(54, a.m(a.d(methodWriter, Opcodes.INVOKEVIRTUAL, JSONLexerBase, "scanInt", "(C)I"), fieldInfo.name, "_asm", context));
            }
            i++;
            fieldInfoArr = fieldInfoArr2;
            methodWriter2 = methodWriter;
            length = i3;
            cls2 = cls;
        }
        MethodVisitor methodVisitor = methodWriter2;
        _batchSet(context, methodVisitor, false);
        Label label18 = new Label();
        Label label19 = new Label();
        Label label20 = new Label();
        Label label21 = new Label();
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "getCurrent", "()C");
        methodVisitor.visitInsn(89);
        methodVisitor.visitVarInsn(54, context.var("ch"));
        methodVisitor.visitVarInsn(16, 44);
        methodVisitor.visitJumpInsn(160, label19);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, VtsConstParam.CHOOSE_NEXT, "()C");
        methodVisitor.visitInsn(87);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitLdcInsn(16);
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "setToken", "(I)V");
        methodVisitor.visitJumpInsn(Opcodes.GOTO, label21);
        methodVisitor.visitLabel(label19);
        methodVisitor.visitVarInsn(21, context.var("ch"));
        methodVisitor.visitVarInsn(16, 93);
        methodVisitor.visitJumpInsn(160, label20);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, VtsConstParam.CHOOSE_NEXT, "()C");
        methodVisitor.visitInsn(87);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitLdcInsn(15);
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "setToken", "(I)V");
        methodVisitor.visitJumpInsn(Opcodes.GOTO, label21);
        methodVisitor.visitLabel(label20);
        methodVisitor.visitVarInsn(21, context.var("ch"));
        methodVisitor.visitVarInsn(16, 26);
        methodVisitor.visitJumpInsn(160, label18);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, VtsConstParam.CHOOSE_NEXT, "()C");
        methodVisitor.visitInsn(87);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitLdcInsn(20);
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "setToken", "(I)V");
        methodVisitor.visitJumpInsn(Opcodes.GOTO, label21);
        methodVisitor.visitLabel(label18);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitLdcInsn(16);
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "nextToken", "(I)V");
        methodVisitor.visitLabel(label21);
        methodVisitor.visitVarInsn(25, context.var("instance"));
        methodVisitor.visitInsn(Opcodes.ARETURN);
        methodVisitor.visitMaxs(5, context.variantIndex);
        methodVisitor.visitEnd();
    }

    private void _deserialze_list_obj(Context context, MethodVisitor methodVisitor, Label label, FieldInfo fieldInfo, Class<?> cls, Class<?> cls2, int i) {
        String str;
        String str2;
        String str3;
        int i2;
        Label label2 = new Label();
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "matchField", "([C)Z");
        methodVisitor.visitJumpInsn(Opcodes.IFEQ, label2);
        _setFlag(methodVisitor, context, i);
        Label label3 = new Label();
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "token", "()I");
        methodVisitor.visitLdcInsn(8);
        methodVisitor.visitJumpInsn(160, label3);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitLdcInsn(16);
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "nextToken", "(I)V");
        methodVisitor.visitJumpInsn(Opcodes.GOTO, label2);
        methodVisitor.visitLabel(label3);
        Label label4 = new Label();
        Label label5 = new Label();
        Label label6 = new Label();
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "token", "()I");
        methodVisitor.visitLdcInsn(21);
        methodVisitor.visitJumpInsn(160, label5);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitLdcInsn(14);
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "nextToken", "(I)V");
        _newCollection(methodVisitor, cls, i, true);
        methodVisitor.visitJumpInsn(Opcodes.GOTO, label4);
        methodVisitor.visitLabel(label5);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "token", "()I");
        methodVisitor.visitLdcInsn(14);
        methodVisitor.visitJumpInsn(Opcodes.IF_ICMPEQ, label6);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "token", "()I");
        methodVisitor.visitLdcInsn(12);
        methodVisitor.visitJumpInsn(160, label);
        _newCollection(methodVisitor, cls, i, false);
        methodVisitor.visitVarInsn(58, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
        _getCollectionFieldItemDeser(context, methodVisitor, fieldInfo, cls2);
        methodVisitor.visitVarInsn(25, 1);
        methodVisitor.visitLdcInsn(Type.getType(ASMUtils.desc(cls2)));
        methodVisitor.visitInsn(3);
        methodVisitor.visitMethodInsn(Opcodes.INVOKESTATIC, "java/lang/Integer", "valueOf", "(I)Ljava/lang/Integer;");
        methodVisitor.visitMethodInsn(Opcodes.INVOKEINTERFACE, ASMUtils.type(ObjectDeserializer.class), "deserialze", a.c(a.e("(L"), DefaultJSONParser, ";Ljava/lang/reflect/Type;Ljava/lang/Object;)Ljava/lang/Object;"));
        methodVisitor.visitVarInsn(58, context.var("list_item_value"));
        methodVisitor.visitVarInsn(25, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
        methodVisitor.visitVarInsn(25, context.var("list_item_value"));
        if (cls.isInterface()) {
            str = "list_item_value";
            methodVisitor.visitMethodInsn(Opcodes.INVOKEINTERFACE, ASMUtils.type(cls), VtsConstParam.FAVORITE_TYPE_ADD, "(Ljava/lang/Object;)Z");
        } else {
            str = "list_item_value";
            methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, ASMUtils.type(cls), VtsConstParam.FAVORITE_TYPE_ADD, "(Ljava/lang/Object;)Z");
        }
        methodVisitor.visitInsn(87);
        methodVisitor.visitJumpInsn(Opcodes.GOTO, label2);
        methodVisitor.visitLabel(label6);
        _newCollection(methodVisitor, cls, i, false);
        methodVisitor.visitLabel(label4);
        methodVisitor.visitVarInsn(58, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
        boolean isPrimitive2 = ParserConfig.isPrimitive2(fieldInfo.fieldClass);
        _getCollectionFieldItemDeser(context, methodVisitor, fieldInfo, cls2);
        if (isPrimitive2) {
            methodVisitor.visitMethodInsn(Opcodes.INVOKEINTERFACE, ASMUtils.type(ObjectDeserializer.class), "getFastMatchToken", "()I");
            methodVisitor.visitVarInsn(54, context.var("fastMatchToken"));
            methodVisitor.visitVarInsn(25, context.var("lexer"));
            methodVisitor.visitVarInsn(21, context.var("fastMatchToken"));
            str2 = "(I)V";
            str3 = "nextToken";
            methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, str3, str2);
        } else {
            str2 = "(I)V";
            str3 = "nextToken";
            methodVisitor.visitInsn(87);
            methodVisitor.visitLdcInsn(12);
            methodVisitor.visitVarInsn(54, context.var("fastMatchToken"));
            _quickNextToken(context, methodVisitor, 12);
        }
        methodVisitor.visitVarInsn(25, 1);
        String str4 = DefaultJSONParser;
        StringBuilder e = a.e("()");
        e.append(ASMUtils.desc(ParseContext.class));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, str4, "getContext", e.toString());
        methodVisitor.visitVarInsn(58, context.var("listContext"));
        methodVisitor.visitVarInsn(25, 1);
        methodVisitor.visitVarInsn(25, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
        methodVisitor.visitLdcInsn(fieldInfo.name);
        String str5 = DefaultJSONParser;
        StringBuilder e2 = a.e("(Ljava/lang/Object;Ljava/lang/Object;)");
        e2.append(ASMUtils.desc(ParseContext.class));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, str5, "setContext", e2.toString());
        methodVisitor.visitInsn(87);
        Label label7 = new Label();
        Label label8 = new Label();
        methodVisitor.visitInsn(3);
        methodVisitor.visitVarInsn(54, context.var("i"));
        methodVisitor.visitLabel(label7);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        String str6 = str2;
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "token", "()I");
        methodVisitor.visitLdcInsn(15);
        methodVisitor.visitJumpInsn(Opcodes.IF_ICMPEQ, label8);
        methodVisitor.visitVarInsn(25, 0);
        String str7 = str3;
        methodVisitor.visitFieldInsn(Opcodes.GETFIELD, context.className, a.c(new StringBuilder(), fieldInfo.name, "_asm_list_item_deser__"), ASMUtils.desc(ObjectDeserializer.class));
        methodVisitor.visitVarInsn(25, 1);
        methodVisitor.visitLdcInsn(Type.getType(ASMUtils.desc(cls2)));
        methodVisitor.visitVarInsn(21, context.var("i"));
        methodVisitor.visitMethodInsn(Opcodes.INVOKESTATIC, "java/lang/Integer", "valueOf", "(I)Ljava/lang/Integer;");
        methodVisitor.visitMethodInsn(Opcodes.INVOKEINTERFACE, ASMUtils.type(ObjectDeserializer.class), "deserialze", a.c(a.e("(L"), DefaultJSONParser, ";Ljava/lang/reflect/Type;Ljava/lang/Object;)Ljava/lang/Object;"));
        String str8 = str;
        methodVisitor.visitVarInsn(58, context.var(str8));
        methodVisitor.visitIincInsn(context.var("i"), 1);
        methodVisitor.visitVarInsn(25, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
        methodVisitor.visitVarInsn(25, context.var(str8));
        if (cls.isInterface()) {
            methodVisitor.visitMethodInsn(Opcodes.INVOKEINTERFACE, ASMUtils.type(cls), VtsConstParam.FAVORITE_TYPE_ADD, "(Ljava/lang/Object;)Z");
        } else {
            methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, ASMUtils.type(cls), VtsConstParam.FAVORITE_TYPE_ADD, "(Ljava/lang/Object;)Z");
        }
        methodVisitor.visitInsn(87);
        methodVisitor.visitVarInsn(25, 1);
        methodVisitor.visitVarInsn(25, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, DefaultJSONParser, "checkListResolve", "(Ljava/util/Collection;)V");
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "token", "()I");
        methodVisitor.visitLdcInsn(16);
        methodVisitor.visitJumpInsn(160, label7);
        if (isPrimitive2) {
            methodVisitor.visitVarInsn(25, context.var("lexer"));
            methodVisitor.visitVarInsn(21, context.var("fastMatchToken"));
            methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, str7, str6);
            i2 = Opcodes.GOTO;
        } else {
            _quickNextToken(context, methodVisitor, 12);
            i2 = Opcodes.GOTO;
        }
        methodVisitor.visitJumpInsn(i2, label7);
        methodVisitor.visitLabel(label8);
        methodVisitor.visitVarInsn(25, 1);
        methodVisitor.visitVarInsn(25, context.var("listContext"));
        String str9 = DefaultJSONParser;
        StringBuilder e3 = a.e("(");
        e3.append(ASMUtils.desc(ParseContext.class));
        e3.append(")V");
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, str9, "setContext", e3.toString());
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "token", "()I");
        methodVisitor.visitLdcInsn(15);
        methodVisitor.visitJumpInsn(160, label);
        _quickNextTokenComma(context, methodVisitor);
        methodVisitor.visitLabel(label2);
    }

    private void _deserialze_obj(Context context, MethodVisitor methodVisitor, Label label, FieldInfo fieldInfo, Class<?> cls, int i) {
        Label label2 = new Label();
        Label label3 = new Label();
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitVarInsn(25, 0);
        methodVisitor.visitFieldInsn(Opcodes.GETFIELD, context.className, a.c(new StringBuilder(), fieldInfo.name, "_asm_prefix__"), "[C");
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "matchField", "([C)Z");
        methodVisitor.visitJumpInsn(Opcodes.IFNE, label2);
        methodVisitor.visitInsn(1);
        methodVisitor.visitVarInsn(58, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
        methodVisitor.visitJumpInsn(Opcodes.GOTO, label3);
        methodVisitor.visitLabel(label2);
        _setFlag(methodVisitor, context, i);
        methodVisitor.visitVarInsn(21, context.var("matchedCount"));
        methodVisitor.visitInsn(4);
        methodVisitor.visitInsn(96);
        methodVisitor.visitVarInsn(54, context.var("matchedCount"));
        _deserObject(context, methodVisitor, fieldInfo, cls, i);
        methodVisitor.visitVarInsn(25, 1);
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, DefaultJSONParser, "getResolveStatus", "()I");
        methodVisitor.visitLdcInsn(1);
        methodVisitor.visitJumpInsn(160, label3);
        methodVisitor.visitVarInsn(25, 1);
        String str = DefaultJSONParser;
        StringBuilder e = a.e("()");
        e.append(ASMUtils.desc(DefaultJSONParser.ResolveTask.class));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, str, "getLastResolveTask", e.toString());
        methodVisitor.visitVarInsn(58, context.var("resolveTask"));
        methodVisitor.visitVarInsn(25, context.var("resolveTask"));
        methodVisitor.visitVarInsn(25, 1);
        String str2 = DefaultJSONParser;
        StringBuilder e2 = a.e("()");
        e2.append(ASMUtils.desc(ParseContext.class));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, str2, "getContext", e2.toString());
        methodVisitor.visitFieldInsn(Opcodes.PUTFIELD, ASMUtils.type(DefaultJSONParser.ResolveTask.class), "ownerContext", ASMUtils.desc(ParseContext.class));
        methodVisitor.visitVarInsn(25, context.var("resolveTask"));
        methodVisitor.visitVarInsn(25, 0);
        methodVisitor.visitLdcInsn(fieldInfo.name);
        String type = ASMUtils.type(JavaBeanDeserializer.class);
        StringBuilder e3 = a.e("(Ljava/lang/String;)");
        e3.append(ASMUtils.desc(FieldDeserializer.class));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, type, "getFieldDeserializer", e3.toString());
        methodVisitor.visitFieldInsn(Opcodes.PUTFIELD, ASMUtils.type(DefaultJSONParser.ResolveTask.class), "fieldDeserializer", ASMUtils.desc(FieldDeserializer.class));
        methodVisitor.visitVarInsn(25, 1);
        methodVisitor.visitLdcInsn(0);
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, DefaultJSONParser, "setResolveStatus", "(I)V");
        methodVisitor.visitLabel(label3);
    }

    private void _getCollectionFieldItemDeser(Context context, MethodVisitor methodVisitor, FieldInfo fieldInfo, Class<?> cls) {
        Label label = new Label();
        methodVisitor.visitVarInsn(25, 0);
        methodVisitor.visitFieldInsn(Opcodes.GETFIELD, context.className, a.c(new StringBuilder(), fieldInfo.name, "_asm_list_item_deser__"), ASMUtils.desc(ObjectDeserializer.class));
        methodVisitor.visitJumpInsn(Opcodes.IFNONNULL, label);
        methodVisitor.visitVarInsn(25, 0);
        methodVisitor.visitVarInsn(25, 1);
        String str = DefaultJSONParser;
        StringBuilder e = a.e("()");
        e.append(ASMUtils.desc(ParserConfig.class));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, str, "getConfig", e.toString());
        methodVisitor.visitLdcInsn(Type.getType(ASMUtils.desc(cls)));
        String type = ASMUtils.type(ParserConfig.class);
        StringBuilder e2 = a.e("(Ljava/lang/reflect/Type;)");
        e2.append(ASMUtils.desc(ObjectDeserializer.class));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, type, "getDeserializer", e2.toString());
        methodVisitor.visitFieldInsn(Opcodes.PUTFIELD, context.className, a.c(new StringBuilder(), fieldInfo.name, "_asm_list_item_deser__"), ASMUtils.desc(ObjectDeserializer.class));
        methodVisitor.visitLabel(label);
        methodVisitor.visitVarInsn(25, 0);
        methodVisitor.visitFieldInsn(Opcodes.GETFIELD, context.className, a.c(new StringBuilder(), fieldInfo.name, "_asm_list_item_deser__"), ASMUtils.desc(ObjectDeserializer.class));
    }

    private void _getFieldDeser(Context context, MethodVisitor methodVisitor, FieldInfo fieldInfo) {
        Label label = new Label();
        methodVisitor.visitVarInsn(25, 0);
        methodVisitor.visitFieldInsn(Opcodes.GETFIELD, context.className, a.c(new StringBuilder(), fieldInfo.name, "_asm_deser__"), ASMUtils.desc(ObjectDeserializer.class));
        methodVisitor.visitJumpInsn(Opcodes.IFNONNULL, label);
        methodVisitor.visitVarInsn(25, 0);
        methodVisitor.visitVarInsn(25, 1);
        String str = DefaultJSONParser;
        StringBuilder e = a.e("()");
        e.append(ASMUtils.desc(ParserConfig.class));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, str, "getConfig", e.toString());
        methodVisitor.visitLdcInsn(Type.getType(ASMUtils.desc(fieldInfo.fieldClass)));
        String type = ASMUtils.type(ParserConfig.class);
        StringBuilder e2 = a.e("(Ljava/lang/reflect/Type;)");
        e2.append(ASMUtils.desc(ObjectDeserializer.class));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, type, "getDeserializer", e2.toString());
        methodVisitor.visitFieldInsn(Opcodes.PUTFIELD, context.className, a.c(new StringBuilder(), fieldInfo.name, "_asm_deser__"), ASMUtils.desc(ObjectDeserializer.class));
        methodVisitor.visitLabel(label);
        methodVisitor.visitVarInsn(25, 0);
        methodVisitor.visitFieldInsn(Opcodes.GETFIELD, context.className, a.c(new StringBuilder(), fieldInfo.name, "_asm_deser__"), ASMUtils.desc(ObjectDeserializer.class));
    }

    private void _init(ClassWriter classWriter, Context context) {
        int length = context.fieldInfoList.length;
        for (int i = 0; i < length; i++) {
            new FieldWriter(classWriter, 1, a.c(new StringBuilder(), context.fieldInfoList[i].name, "_asm_prefix__"), "[C").visitEnd();
        }
        int length2 = context.fieldInfoList.length;
        for (int i2 = 0; i2 < length2; i2++) {
            FieldInfo fieldInfo = context.fieldInfoList[i2];
            Class<?> cls = fieldInfo.fieldClass;
            if (!cls.isPrimitive()) {
                if (Collection.class.isAssignableFrom(cls)) {
                    new FieldWriter(classWriter, 1, a.c(new StringBuilder(), fieldInfo.name, "_asm_list_item_deser__"), ASMUtils.desc(ObjectDeserializer.class)).visitEnd();
                } else {
                    new FieldWriter(classWriter, 1, a.c(new StringBuilder(), fieldInfo.name, "_asm_deser__"), ASMUtils.desc(ObjectDeserializer.class)).visitEnd();
                }
            }
        }
        StringBuilder e = a.e("(");
        e.append(ASMUtils.desc(ParserConfig.class));
        e.append(ASMUtils.desc(JavaBeanInfo.class));
        e.append(")V");
        MethodWriter methodWriter = new MethodWriter(classWriter, 1, "<init>", e.toString(), null, null);
        methodWriter.visitVarInsn(25, 0);
        methodWriter.visitVarInsn(25, 1);
        methodWriter.visitVarInsn(25, 2);
        String type = ASMUtils.type(JavaBeanDeserializer.class);
        StringBuilder e2 = a.e("(");
        e2.append(ASMUtils.desc(ParserConfig.class));
        e2.append(ASMUtils.desc(JavaBeanInfo.class));
        e2.append(")V");
        methodWriter.visitMethodInsn(Opcodes.INVOKESPECIAL, type, "<init>", e2.toString());
        int length3 = context.fieldInfoList.length;
        for (int i3 = 0; i3 < length3; i3++) {
            FieldInfo fieldInfo2 = context.fieldInfoList[i3];
            methodWriter.visitVarInsn(25, 0);
            methodWriter.visitLdcInsn("\"" + fieldInfo2.name + "\":");
            methodWriter.visitMethodInsn(Opcodes.INVOKEVIRTUAL, "java/lang/String", "toCharArray", "()[C");
            methodWriter.visitFieldInsn(Opcodes.PUTFIELD, context.className, a.c(new StringBuilder(), fieldInfo2.name, "_asm_prefix__"), "[C");
        }
        methodWriter.visitInsn(Opcodes.RETURN);
        methodWriter.visitMaxs(4, 4);
        methodWriter.visitEnd();
    }

    private void _isFlag(MethodVisitor methodVisitor, Context context, int i, Label label) {
        StringBuilder e = a.e("_asm_flag_");
        e.append(i / 32);
        methodVisitor.visitVarInsn(21, context.var(e.toString()));
        methodVisitor.visitLdcInsn(Integer.valueOf(1 << i));
        methodVisitor.visitInsn(Opcodes.IAND);
        methodVisitor.visitJumpInsn(Opcodes.IFEQ, label);
    }

    private void _loadAndSet(Context context, MethodVisitor methodVisitor, FieldInfo fieldInfo) {
        Class<?> cls = fieldInfo.fieldClass;
        java.lang.reflect.Type type = fieldInfo.fieldType;
        if (cls == Boolean.TYPE) {
            methodVisitor.visitVarInsn(25, context.var("instance"));
            methodVisitor.visitVarInsn(21, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
            _set(context, methodVisitor, fieldInfo);
        } else if (cls != Byte.TYPE && cls != Short.TYPE && cls != Integer.TYPE && cls != Character.TYPE) {
            if (cls == Long.TYPE) {
                methodVisitor.visitVarInsn(25, context.var("instance"));
                methodVisitor.visitVarInsn(22, context.var(fieldInfo.name + "_asm", 2));
                if (fieldInfo.method != null) {
                    methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, ASMUtils.type(context.getInstClass()), fieldInfo.method.getName(), ASMUtils.desc(fieldInfo.method));
                    if (fieldInfo.method.getReturnType().equals(Void.TYPE)) {
                        return;
                    }
                    methodVisitor.visitInsn(87);
                    return;
                }
                methodVisitor.visitFieldInsn(Opcodes.PUTFIELD, ASMUtils.type(fieldInfo.declaringClass), fieldInfo.field.getName(), ASMUtils.desc(fieldInfo.fieldClass));
            } else if (cls == Float.TYPE) {
                methodVisitor.visitVarInsn(25, context.var("instance"));
                methodVisitor.visitVarInsn(23, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
                _set(context, methodVisitor, fieldInfo);
            } else if (cls != Double.TYPE) {
                if (cls == String.class) {
                    methodVisitor.visitVarInsn(25, context.var("instance"));
                    methodVisitor.visitVarInsn(25, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
                    _set(context, methodVisitor, fieldInfo);
                } else if (cls.isEnum()) {
                    methodVisitor.visitVarInsn(25, context.var("instance"));
                    methodVisitor.visitVarInsn(25, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
                    _set(context, methodVisitor, fieldInfo);
                } else if (Collection.class.isAssignableFrom(cls)) {
                    methodVisitor.visitVarInsn(25, context.var("instance"));
                    if (TypeUtils.getCollectionItemClass(type) == String.class) {
                        methodVisitor.visitVarInsn(25, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
                        methodVisitor.visitTypeInsn(Opcodes.CHECKCAST, ASMUtils.type(cls));
                    } else {
                        methodVisitor.visitVarInsn(25, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
                    }
                    _set(context, methodVisitor, fieldInfo);
                } else {
                    methodVisitor.visitVarInsn(25, context.var("instance"));
                    methodVisitor.visitVarInsn(25, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
                    _set(context, methodVisitor, fieldInfo);
                }
            } else {
                methodVisitor.visitVarInsn(25, context.var("instance"));
                methodVisitor.visitVarInsn(24, context.var(fieldInfo.name + "_asm", 2));
                _set(context, methodVisitor, fieldInfo);
            }
        } else {
            methodVisitor.visitVarInsn(25, context.var("instance"));
            methodVisitor.visitVarInsn(21, a.m(new StringBuilder(), fieldInfo.name, "_asm", context));
            _set(context, methodVisitor, fieldInfo);
        }
    }

    private void _newCollection(MethodVisitor methodVisitor, Class<?> cls, int i, boolean z) {
        if (cls.isAssignableFrom(ArrayList.class) && !z) {
            methodVisitor.visitTypeInsn(Opcodes.NEW, "java/util/ArrayList");
            methodVisitor.visitInsn(89);
            methodVisitor.visitMethodInsn(Opcodes.INVOKESPECIAL, "java/util/ArrayList", "<init>", "()V");
        } else if (cls.isAssignableFrom(LinkedList.class) && !z) {
            methodVisitor.visitTypeInsn(Opcodes.NEW, ASMUtils.type(LinkedList.class));
            methodVisitor.visitInsn(89);
            methodVisitor.visitMethodInsn(Opcodes.INVOKESPECIAL, ASMUtils.type(LinkedList.class), "<init>", "()V");
        } else if (cls.isAssignableFrom(HashSet.class)) {
            methodVisitor.visitTypeInsn(Opcodes.NEW, ASMUtils.type(HashSet.class));
            methodVisitor.visitInsn(89);
            methodVisitor.visitMethodInsn(Opcodes.INVOKESPECIAL, ASMUtils.type(HashSet.class), "<init>", "()V");
        } else if (cls.isAssignableFrom(TreeSet.class)) {
            methodVisitor.visitTypeInsn(Opcodes.NEW, ASMUtils.type(TreeSet.class));
            methodVisitor.visitInsn(89);
            methodVisitor.visitMethodInsn(Opcodes.INVOKESPECIAL, ASMUtils.type(TreeSet.class), "<init>", "()V");
        } else if (cls.isAssignableFrom(LinkedHashSet.class)) {
            methodVisitor.visitTypeInsn(Opcodes.NEW, ASMUtils.type(LinkedHashSet.class));
            methodVisitor.visitInsn(89);
            methodVisitor.visitMethodInsn(Opcodes.INVOKESPECIAL, ASMUtils.type(LinkedHashSet.class), "<init>", "()V");
        } else if (z) {
            methodVisitor.visitTypeInsn(Opcodes.NEW, ASMUtils.type(HashSet.class));
            methodVisitor.visitInsn(89);
            methodVisitor.visitMethodInsn(Opcodes.INVOKESPECIAL, ASMUtils.type(HashSet.class), "<init>", "()V");
        } else {
            methodVisitor.visitVarInsn(25, 0);
            methodVisitor.visitLdcInsn(Integer.valueOf(i));
            methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, ASMUtils.type(JavaBeanDeserializer.class), "getFieldType", "(I)Ljava/lang/reflect/Type;");
            methodVisitor.visitMethodInsn(Opcodes.INVOKESTATIC, ASMUtils.type(TypeUtils.class), "createCollection", "(Ljava/lang/reflect/Type;)Ljava/util/Collection;");
        }
        methodVisitor.visitTypeInsn(Opcodes.CHECKCAST, ASMUtils.type(cls));
    }

    private void _quickNextToken(Context context, MethodVisitor methodVisitor, int i) {
        Label label = new Label();
        Label label2 = new Label();
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "getCurrent", "()C");
        if (i == 12) {
            methodVisitor.visitVarInsn(16, 123);
        } else if (i == 14) {
            methodVisitor.visitVarInsn(16, 91);
        } else {
            throw new IllegalStateException();
        }
        methodVisitor.visitJumpInsn(160, label);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, VtsConstParam.CHOOSE_NEXT, "()C");
        methodVisitor.visitInsn(87);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitLdcInsn(Integer.valueOf(i));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "setToken", "(I)V");
        methodVisitor.visitJumpInsn(Opcodes.GOTO, label2);
        methodVisitor.visitLabel(label);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitLdcInsn(Integer.valueOf(i));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "nextToken", "(I)V");
        methodVisitor.visitLabel(label2);
    }

    private void _quickNextTokenComma(Context context, MethodVisitor methodVisitor) {
        Label label = new Label();
        Label label2 = new Label();
        Label label3 = new Label();
        Label label4 = new Label();
        Label label5 = new Label();
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "getCurrent", "()C");
        methodVisitor.visitInsn(89);
        methodVisitor.visitVarInsn(54, context.var("ch"));
        methodVisitor.visitVarInsn(16, 44);
        methodVisitor.visitJumpInsn(160, label2);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, VtsConstParam.CHOOSE_NEXT, "()C");
        methodVisitor.visitInsn(87);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitLdcInsn(16);
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "setToken", "(I)V");
        methodVisitor.visitJumpInsn(Opcodes.GOTO, label5);
        methodVisitor.visitLabel(label2);
        methodVisitor.visitVarInsn(21, context.var("ch"));
        methodVisitor.visitVarInsn(16, 125);
        methodVisitor.visitJumpInsn(160, label3);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, VtsConstParam.CHOOSE_NEXT, "()C");
        methodVisitor.visitInsn(87);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitLdcInsn(13);
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "setToken", "(I)V");
        methodVisitor.visitJumpInsn(Opcodes.GOTO, label5);
        methodVisitor.visitLabel(label3);
        methodVisitor.visitVarInsn(21, context.var("ch"));
        methodVisitor.visitVarInsn(16, 93);
        methodVisitor.visitJumpInsn(160, label4);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, VtsConstParam.CHOOSE_NEXT, "()C");
        methodVisitor.visitInsn(87);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitLdcInsn(15);
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "setToken", "(I)V");
        methodVisitor.visitJumpInsn(Opcodes.GOTO, label5);
        methodVisitor.visitLabel(label4);
        methodVisitor.visitVarInsn(21, context.var("ch"));
        methodVisitor.visitVarInsn(16, 26);
        methodVisitor.visitJumpInsn(160, label);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitLdcInsn(20);
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "setToken", "(I)V");
        methodVisitor.visitJumpInsn(Opcodes.GOTO, label5);
        methodVisitor.visitLabel(label);
        methodVisitor.visitVarInsn(25, context.var("lexer"));
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, JSONLexerBase, "nextToken", "()V");
        methodVisitor.visitLabel(label5);
    }

    private void _set(Context context, MethodVisitor methodVisitor, FieldInfo fieldInfo) {
        Method method = fieldInfo.method;
        if (method != null) {
            methodVisitor.visitMethodInsn(method.getDeclaringClass().isInterface() ? Opcodes.INVOKEINTERFACE : Opcodes.INVOKEVIRTUAL, ASMUtils.type(fieldInfo.declaringClass), method.getName(), ASMUtils.desc(method));
            if (fieldInfo.method.getReturnType().equals(Void.TYPE)) {
                return;
            }
            methodVisitor.visitInsn(87);
            return;
        }
        methodVisitor.visitFieldInsn(Opcodes.PUTFIELD, ASMUtils.type(fieldInfo.declaringClass), fieldInfo.field.getName(), ASMUtils.desc(fieldInfo.fieldClass));
    }

    private void _setContext(Context context, MethodVisitor methodVisitor) {
        methodVisitor.visitVarInsn(25, 1);
        methodVisitor.visitVarInsn(25, context.var("context"));
        String str = DefaultJSONParser;
        StringBuilder e = a.e("(");
        e.append(ASMUtils.desc(ParseContext.class));
        e.append(")V");
        methodVisitor.visitMethodInsn(Opcodes.INVOKEVIRTUAL, str, "setContext", e.toString());
        Label label = new Label();
        methodVisitor.visitVarInsn(25, context.var("childContext"));
        methodVisitor.visitJumpInsn(Opcodes.IFNULL, label);
        methodVisitor.visitVarInsn(25, context.var("childContext"));
        methodVisitor.visitVarInsn(25, context.var("instance"));
        methodVisitor.visitFieldInsn(Opcodes.PUTFIELD, ASMUtils.type(ParseContext.class), "object", "Ljava/lang/Object;");
        methodVisitor.visitLabel(label);
    }

    private void _setFlag(MethodVisitor methodVisitor, Context context, int i) {
        StringBuilder e = a.e("_asm_flag_");
        e.append(i / 32);
        String sb = e.toString();
        methodVisitor.visitVarInsn(21, context.var(sb));
        methodVisitor.visitLdcInsn(Integer.valueOf(1 << i));
        methodVisitor.visitInsn(128);
        methodVisitor.visitVarInsn(54, context.var(sb));
    }

    private void defineVarLexer(Context context, MethodVisitor methodVisitor) {
        methodVisitor.visitVarInsn(25, 1);
        methodVisitor.visitFieldInsn(Opcodes.GETFIELD, DefaultJSONParser, "lexer", ASMUtils.desc(JSONLexer.class));
        methodVisitor.visitTypeInsn(Opcodes.CHECKCAST, JSONLexerBase);
        methodVisitor.visitVarInsn(58, context.var("lexer"));
    }

    public ObjectDeserializer createJavaBeanDeserializer(ParserConfig parserConfig, JavaBeanInfo javaBeanInfo) throws Exception {
        String str;
        String name;
        Class<?> cls = javaBeanInfo.clazz;
        if (!cls.isPrimitive()) {
            StringBuilder e = a.e("FastjsonASMDeserializer_");
            e.append(this.seed.incrementAndGet());
            e.append("_");
            e.append(cls.getSimpleName());
            String sb = e.toString();
            Package r1 = ASMDeserializerFactory.class.getPackage();
            if (r1 != null) {
                String str2 = name.replace('.', '/') + ServiceConstant.SEPARATOR + sb;
                str = a.y(r1.getName(), ".", sb);
                sb = str2;
            } else {
                str = sb;
            }
            ClassWriter classWriter = new ClassWriter();
            classWriter.visit(49, 33, sb, ASMUtils.type(JavaBeanDeserializer.class), null);
            _init(classWriter, new Context(sb, parserConfig, javaBeanInfo, 3));
            _createInstance(classWriter, new Context(sb, parserConfig, javaBeanInfo, 3));
            _deserialze(classWriter, new Context(sb, parserConfig, javaBeanInfo, 5));
            _deserialzeArrayMapping(classWriter, new Context(sb, parserConfig, javaBeanInfo, 4));
            byte[] byteArray = classWriter.toByteArray();
            return (ObjectDeserializer) this.classLoader.defineClassPublic(str, byteArray, 0, byteArray.length).getConstructor(ParserConfig.class, JavaBeanInfo.class).newInstance(parserConfig, javaBeanInfo);
        }
        StringBuilder e2 = a.e("not support type :");
        e2.append(cls.getName());
        throw new IllegalArgumentException(e2.toString());
    }

    private void _batchSet(Context context, MethodVisitor methodVisitor, boolean z) {
        int length = context.fieldInfoList.length;
        for (int i = 0; i < length; i++) {
            Label label = new Label();
            if (z) {
                _isFlag(methodVisitor, context, i, label);
            }
            _loadAndSet(context, methodVisitor, context.fieldInfoList[i]);
            if (z) {
                methodVisitor.visitLabel(label);
            }
        }
    }

    /* loaded from: classes.dex */
    public static class Context {
        public static final int fieldName = 3;
        public static final int parser = 1;
        public static final int type = 2;
        public final JavaBeanInfo beanInfo;
        public final String className;
        public final Class<?> clazz;
        public FieldInfo[] fieldInfoList;
        public int variantIndex;
        public final Map<String, Integer> variants = new HashMap();

        public Context(String str, ParserConfig parserConfig, JavaBeanInfo javaBeanInfo, int i) {
            this.variantIndex = -1;
            this.className = str;
            this.clazz = javaBeanInfo.clazz;
            this.variantIndex = i;
            this.beanInfo = javaBeanInfo;
            this.fieldInfoList = javaBeanInfo.fields;
        }

        public Class<?> getInstClass() {
            Class<?> cls = this.beanInfo.builderClass;
            return cls == null ? this.clazz : cls;
        }

        public int var(String str, int i) {
            if (this.variants.get(str) == null) {
                this.variants.put(str, Integer.valueOf(this.variantIndex));
                this.variantIndex += i;
            }
            return this.variants.get(str).intValue();
        }

        public int var(String str) {
            if (this.variants.get(str) == null) {
                Map<String, Integer> map = this.variants;
                int i = this.variantIndex;
                this.variantIndex = i + 1;
                map.put(str, Integer.valueOf(i));
            }
            return this.variants.get(str).intValue();
        }
    }

    private void _createInstance(ClassWriter classWriter, Context context) {
        if (Modifier.isPublic(context.beanInfo.defaultConstructor.getModifiers())) {
            MethodWriter methodWriter = new MethodWriter(classWriter, 1, "createInstance", a.c(a.e("(L"), DefaultJSONParser, ";Ljava/lang/reflect/Type;)Ljava/lang/Object;"), null, null);
            methodWriter.visitTypeInsn(Opcodes.NEW, ASMUtils.type(context.getInstClass()));
            methodWriter.visitInsn(89);
            methodWriter.visitMethodInsn(Opcodes.INVOKESPECIAL, ASMUtils.type(context.getInstClass()), "<init>", "()V");
            methodWriter.visitInsn(Opcodes.ARETURN);
            methodWriter.visitMaxs(3, 3);
            methodWriter.visitEnd();
        }
    }
}
