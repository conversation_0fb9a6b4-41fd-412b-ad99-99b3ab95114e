package com.alibaba.fastjson.parser;

import b.a.b.a.a;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.JSONPathException;
import com.alibaba.fastjson.parser.deserializer.ExtraProcessable;
import com.alibaba.fastjson.parser.deserializer.ExtraProcessor;
import com.alibaba.fastjson.parser.deserializer.ExtraTypeProvider;
import com.alibaba.fastjson.parser.deserializer.FieldDeserializer;
import com.alibaba.fastjson.parser.deserializer.FieldTypeResolver;
import com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.alibaba.fastjson.parser.deserializer.ResolveFieldDeserializer;
import com.alibaba.fastjson.serializer.BeanContext;
import com.alibaba.fastjson.serializer.IntegerCodec;
import com.alibaba.fastjson.serializer.LongCodec;
import com.alibaba.fastjson.serializer.StringCodec;
import com.alibaba.fastjson.util.FieldInfo;
import com.alibaba.fastjson.util.TypeUtils;
import java.io.Closeable;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.lang.reflect.TypeVariable;
import java.lang.reflect.WildcardType;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

/* loaded from: classes.dex */
public class DefaultJSONParser implements Closeable {
    public static final int NONE = 0;
    public static final int NeedToResolve = 1;
    public static final int TypeNameRedirect = 2;
    public static final Set<Class<?>> primitiveClasses = new HashSet();
    public String[] autoTypeAccept;
    public boolean autoTypeEnable;
    public ParserConfig config;
    public ParseContext context;
    public ParseContext[] contextArray;
    public int contextArrayIndex;
    public DateFormat dateFormat;
    public String dateFormatPattern;
    public List<ExtraProcessor> extraProcessors;
    public List<ExtraTypeProvider> extraTypeProviders;
    public FieldTypeResolver fieldTypeResolver;
    public final Object input;
    public transient BeanContext lastBeanContext;
    public final JSONLexer lexer;
    public int objectKeyLevel;
    public int resolveStatus;
    public List<ResolveTask> resolveTaskList;
    public final SymbolTable symbolTable;

    /* loaded from: classes.dex */
    public static class ResolveTask {
        public final ParseContext context;
        public FieldDeserializer fieldDeserializer;
        public ParseContext ownerContext;
        public final String referenceValue;

        public ResolveTask(ParseContext parseContext, String str) {
            this.context = parseContext;
            this.referenceValue = str;
        }
    }

    static {
        primitiveClasses.addAll(Arrays.asList(Boolean.TYPE, Byte.TYPE, Short.TYPE, Integer.TYPE, Long.TYPE, Float.TYPE, Double.TYPE, Boolean.class, Byte.class, Short.class, Integer.class, Long.class, Float.class, Double.class, BigInteger.class, BigDecimal.class, String.class));
    }

    public DefaultJSONParser(String str) {
        this(str, ParserConfig.getGlobalInstance(), JSON.DEFAULT_PARSER_FEATURE);
    }

    private void addContext(ParseContext parseContext) {
        int i = this.contextArrayIndex;
        this.contextArrayIndex = i + 1;
        ParseContext[] parseContextArr = this.contextArray;
        if (parseContextArr == null) {
            this.contextArray = new ParseContext[8];
        } else if (i >= parseContextArr.length) {
            ParseContext[] parseContextArr2 = new ParseContext[(parseContextArr.length * 3) / 2];
            System.arraycopy(parseContextArr, 0, parseContextArr2, 0, parseContextArr.length);
            this.contextArray = parseContextArr2;
        }
        this.contextArray[i] = parseContext;
    }

    public final void accept(int i) {
        JSONLexer jSONLexer = this.lexer;
        if (jSONLexer.token() == i) {
            jSONLexer.nextToken();
            return;
        }
        StringBuilder e = a.e("syntax error, expect ");
        e.append(JSONToken.name(i));
        e.append(", actual ");
        e.append(JSONToken.name(jSONLexer.token()));
        throw new JSONException(e.toString());
    }

    public void acceptType(String str) {
        JSONLexer jSONLexer = this.lexer;
        jSONLexer.nextTokenWithColon();
        if (jSONLexer.token() == 4) {
            if (str.equals(jSONLexer.stringVal())) {
                jSONLexer.nextToken();
                if (jSONLexer.token() == 16) {
                    jSONLexer.nextToken();
                    return;
                }
                return;
            }
            throw new JSONException("type not match error");
        }
        throw new JSONException("type not match error");
    }

    public void addResolveTask(ResolveTask resolveTask) {
        if (this.resolveTaskList == null) {
            this.resolveTaskList = new ArrayList(2);
        }
        this.resolveTaskList.add(resolveTask);
    }

    public void checkListResolve(Collection collection) {
        if (this.resolveStatus == 1) {
            if (collection instanceof List) {
                ResolveTask lastResolveTask = getLastResolveTask();
                lastResolveTask.fieldDeserializer = new ResolveFieldDeserializer(this, (List) collection, collection.size() - 1);
                lastResolveTask.ownerContext = this.context;
                setResolveStatus(0);
                return;
            }
            ResolveTask lastResolveTask2 = getLastResolveTask();
            lastResolveTask2.fieldDeserializer = new ResolveFieldDeserializer(collection);
            lastResolveTask2.ownerContext = this.context;
            setResolveStatus(0);
        }
    }

    public void checkMapResolve(Map map, Object obj) {
        if (this.resolveStatus == 1) {
            ResolveFieldDeserializer resolveFieldDeserializer = new ResolveFieldDeserializer(map, obj);
            ResolveTask lastResolveTask = getLastResolveTask();
            lastResolveTask.fieldDeserializer = resolveFieldDeserializer;
            lastResolveTask.ownerContext = this.context;
            setResolveStatus(0);
        }
    }

    @Override // java.io.Closeable, java.lang.AutoCloseable
    public void close() {
        JSONLexer jSONLexer = this.lexer;
        try {
            if (jSONLexer.isEnabled(Feature.AutoCloseSource) && jSONLexer.token() != 20) {
                throw new JSONException("not close json text, token : " + JSONToken.name(jSONLexer.token()));
            }
        } finally {
            jSONLexer.close();
        }
    }

    public void config(Feature feature, boolean z) {
        this.lexer.config(feature, z);
    }

    public ParserConfig getConfig() {
        return this.config;
    }

    public ParseContext getContext() {
        return this.context;
    }

    public String getDateFomartPattern() {
        return this.dateFormatPattern;
    }

    public DateFormat getDateFormat() {
        if (this.dateFormat == null) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(this.dateFormatPattern, this.lexer.getLocale());
            this.dateFormat = simpleDateFormat;
            simpleDateFormat.setTimeZone(this.lexer.getTimeZone());
        }
        return this.dateFormat;
    }

    public List<ExtraProcessor> getExtraProcessors() {
        if (this.extraProcessors == null) {
            this.extraProcessors = new ArrayList(2);
        }
        return this.extraProcessors;
    }

    public List<ExtraTypeProvider> getExtraTypeProviders() {
        if (this.extraTypeProviders == null) {
            this.extraTypeProviders = new ArrayList(2);
        }
        return this.extraTypeProviders;
    }

    public FieldTypeResolver getFieldTypeResolver() {
        return this.fieldTypeResolver;
    }

    public String getInput() {
        Object obj = this.input;
        if (obj instanceof char[]) {
            return new String((char[]) this.input);
        }
        return obj.toString();
    }

    public ResolveTask getLastResolveTask() {
        List<ResolveTask> list = this.resolveTaskList;
        return list.get(list.size() - 1);
    }

    public JSONLexer getLexer() {
        return this.lexer;
    }

    public Object getObject(String str) {
        for (int i = 0; i < this.contextArrayIndex; i++) {
            if (str.equals(this.contextArray[i].toString())) {
                return this.contextArray[i].object;
            }
        }
        return null;
    }

    public int getResolveStatus() {
        return this.resolveStatus;
    }

    public List<ResolveTask> getResolveTaskList() {
        if (this.resolveTaskList == null) {
            this.resolveTaskList = new ArrayList(2);
        }
        return this.resolveTaskList;
    }

    public SymbolTable getSymbolTable() {
        return this.symbolTable;
    }

    public void handleResovleTask(Object obj) {
        Object obj2;
        FieldInfo fieldInfo;
        List<ResolveTask> list = this.resolveTaskList;
        if (list == null) {
            return;
        }
        int size = list.size();
        for (int i = 0; i < size; i++) {
            ResolveTask resolveTask = this.resolveTaskList.get(i);
            String str = resolveTask.referenceValue;
            ParseContext parseContext = resolveTask.ownerContext;
            Object obj3 = parseContext != null ? parseContext.object : null;
            if (str.startsWith("$")) {
                obj2 = getObject(str);
                if (obj2 == null) {
                    try {
                        JSONPath compile = JSONPath.compile(str);
                        if (compile.isRef()) {
                            obj2 = compile.eval(obj);
                        }
                    } catch (JSONPathException unused) {
                    }
                }
            } else {
                obj2 = resolveTask.context.object;
            }
            FieldDeserializer fieldDeserializer = resolveTask.fieldDeserializer;
            if (fieldDeserializer != null) {
                if (obj2 != null && obj2.getClass() == JSONObject.class && (fieldInfo = fieldDeserializer.fieldInfo) != null && !Map.class.isAssignableFrom(fieldInfo.fieldClass)) {
                    Object obj4 = this.contextArray[0].object;
                    JSONPath compile2 = JSONPath.compile(str);
                    if (compile2.isRef()) {
                        obj2 = compile2.eval(obj4);
                    }
                }
                fieldDeserializer.setValue(obj3, obj2);
            }
        }
    }

    public boolean isEnabled(Feature feature) {
        return this.lexer.isEnabled(feature);
    }

    public Object parse() {
        return parse(null);
    }

    public <T> List<T> parseArray(Class<T> cls) {
        ArrayList arrayList = new ArrayList();
        parseArray((Class<?>) cls, (Collection) arrayList);
        return arrayList;
    }

    public Object parseArrayWithType(Type type) {
        if (this.lexer.token() == 8) {
            this.lexer.nextToken();
            return null;
        }
        Type[] actualTypeArguments = ((ParameterizedType) type).getActualTypeArguments();
        if (actualTypeArguments.length == 1) {
            Type type2 = actualTypeArguments[0];
            if (type2 instanceof Class) {
                ArrayList arrayList = new ArrayList();
                parseArray((Class) type2, (Collection) arrayList);
                return arrayList;
            } else if (type2 instanceof WildcardType) {
                WildcardType wildcardType = (WildcardType) type2;
                Type type3 = wildcardType.getUpperBounds()[0];
                if (Object.class.equals(type3)) {
                    if (wildcardType.getLowerBounds().length == 0) {
                        return parse();
                    }
                    throw new JSONException("not support type : " + type);
                }
                ArrayList arrayList2 = new ArrayList();
                parseArray((Class) type3, (Collection) arrayList2);
                return arrayList2;
            } else {
                if (type2 instanceof TypeVariable) {
                    TypeVariable typeVariable = (TypeVariable) type2;
                    Type[] bounds = typeVariable.getBounds();
                    if (bounds.length == 1) {
                        Type type4 = bounds[0];
                        if (type4 instanceof Class) {
                            ArrayList arrayList3 = new ArrayList();
                            parseArray((Class) type4, (Collection) arrayList3);
                            return arrayList3;
                        }
                    } else {
                        throw new JSONException("not support : " + typeVariable);
                    }
                }
                if (type2 instanceof ParameterizedType) {
                    ArrayList arrayList4 = new ArrayList();
                    parseArray((ParameterizedType) type2, arrayList4);
                    return arrayList4;
                }
                throw new JSONException("TODO : " + type);
            }
        }
        throw new JSONException("not support type " + type);
    }

    public void parseExtra(Object obj, String str) {
        Object parseObject;
        this.lexer.nextTokenWithColon();
        List<ExtraTypeProvider> list = this.extraTypeProviders;
        Type type = null;
        if (list != null) {
            for (ExtraTypeProvider extraTypeProvider : list) {
                type = extraTypeProvider.getExtraType(obj, str);
            }
        }
        if (type == null) {
            parseObject = parse();
        } else {
            parseObject = parseObject(type);
        }
        if (obj instanceof ExtraProcessable) {
            ((ExtraProcessable) obj).processExtra(str, parseObject);
            return;
        }
        List<ExtraProcessor> list2 = this.extraProcessors;
        if (list2 != null) {
            for (ExtraProcessor extraProcessor : list2) {
                extraProcessor.processExtra(obj, str, parseObject);
            }
        }
        if (this.resolveStatus == 1) {
            this.resolveStatus = 0;
        }
    }

    public Object parseKey() {
        if (this.lexer.token() == 18) {
            String stringVal = this.lexer.stringVal();
            this.lexer.nextToken(16);
            return stringVal;
        }
        return parse(null);
    }

    /* JADX WARN: Code restructure failed: missing block: B:139:0x027b, code lost:
        r1.nextToken(16);
     */
    /* JADX WARN: Code restructure failed: missing block: B:140:0x0286, code lost:
        if (r1.token() != 13) goto L293;
     */
    /* JADX WARN: Code restructure failed: missing block: B:141:0x0288, code lost:
        r1.nextToken(16);
     */
    /* JADX WARN: Code restructure failed: missing block: B:143:0x0293, code lost:
        if ((r13.config.getDeserializer(r7) instanceof com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer) == false) goto L277;
     */
    /* JADX WARN: Code restructure failed: missing block: B:144:0x0295, code lost:
        r8 = com.alibaba.fastjson.util.TypeUtils.cast((java.lang.Object) r14, (java.lang.Class<java.lang.Object>) r7, r13.config);
     */
    /* JADX WARN: Code restructure failed: missing block: B:145:0x029b, code lost:
        if (r8 != null) goto L288;
     */
    /* JADX WARN: Code restructure failed: missing block: B:147:0x029f, code lost:
        if (r7 != java.lang.Cloneable.class) goto L281;
     */
    /* JADX WARN: Code restructure failed: missing block: B:148:0x02a1, code lost:
        r8 = new java.util.HashMap();
     */
    /* JADX WARN: Code restructure failed: missing block: B:150:0x02ad, code lost:
        if ("java.util.Collections$EmptyMap".equals(r6) == false) goto L284;
     */
    /* JADX WARN: Code restructure failed: missing block: B:151:0x02af, code lost:
        r8 = java.util.Collections.emptyMap();
     */
    /* JADX WARN: Code restructure failed: missing block: B:153:0x02ba, code lost:
        if ("java.util.Collections$UnmodifiableMap".equals(r6) == false) goto L287;
     */
    /* JADX WARN: Code restructure failed: missing block: B:154:0x02bc, code lost:
        r8 = java.util.Collections.unmodifiableMap(new java.util.HashMap());
     */
    /* JADX WARN: Code restructure failed: missing block: B:155:0x02c6, code lost:
        r8 = r7.newInstance();
     */
    /* JADX WARN: Code restructure failed: missing block: B:157:0x02cd, code lost:
        return r8;
     */
    /* JADX WARN: Code restructure failed: missing block: B:158:0x02ce, code lost:
        r14 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:160:0x02d6, code lost:
        throw new com.alibaba.fastjson.JSONException("create instance error", r14);
     */
    /* JADX WARN: Code restructure failed: missing block: B:161:0x02d7, code lost:
        setResolveStatus(2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:162:0x02dd, code lost:
        if (r13.context == null) goto L301;
     */
    /* JADX WARN: Code restructure failed: missing block: B:163:0x02df, code lost:
        if (r15 == null) goto L301;
     */
    /* JADX WARN: Code restructure failed: missing block: B:165:0x02e3, code lost:
        if ((r15 instanceof java.lang.Integer) != false) goto L301;
     */
    /* JADX WARN: Code restructure failed: missing block: B:167:0x02eb, code lost:
        if ((r13.context.fieldName instanceof java.lang.Integer) != false) goto L301;
     */
    /* JADX WARN: Code restructure failed: missing block: B:168:0x02ed, code lost:
        popContext();
     */
    /* JADX WARN: Code restructure failed: missing block: B:170:0x02f4, code lost:
        if (r14.size() <= 0) goto L306;
     */
    /* JADX WARN: Code restructure failed: missing block: B:171:0x02f6, code lost:
        r14 = com.alibaba.fastjson.util.TypeUtils.cast((java.lang.Object) r14, (java.lang.Class<java.lang.Object>) r7, r13.config);
        setResolveStatus(0);
        parseObject(r14);
     */
    /* JADX WARN: Code restructure failed: missing block: B:173:0x0306, code lost:
        return r14;
     */
    /* JADX WARN: Code restructure failed: missing block: B:174:0x0307, code lost:
        r14 = r13.config.getDeserializer(r7);
        r0 = r14.getClass();
     */
    /* JADX WARN: Code restructure failed: missing block: B:175:0x0317, code lost:
        if (com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.class.isAssignableFrom(r0) == false) goto L316;
     */
    /* JADX WARN: Code restructure failed: missing block: B:177:0x031b, code lost:
        if (r0 == com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.class) goto L316;
     */
    /* JADX WARN: Code restructure failed: missing block: B:179:0x031f, code lost:
        if (r0 == com.alibaba.fastjson.parser.deserializer.ThrowableDeserializer.class) goto L316;
     */
    /* JADX WARN: Code restructure failed: missing block: B:180:0x0321, code lost:
        setResolveStatus(0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:182:0x0328, code lost:
        if ((r14 instanceof com.alibaba.fastjson.parser.deserializer.MapDeserializer) == false) goto L313;
     */
    /* JADX WARN: Code restructure failed: missing block: B:183:0x032a, code lost:
        setResolveStatus(0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:186:0x0335, code lost:
        return r14.deserialze(r13, r7, r15);
     */
    /* JADX WARN: Removed duplicated region for block: B:112:0x0208 A[Catch: all -> 0x0670, TryCatch #0 {all -> 0x0670, blocks: (B:24:0x0068, B:26:0x006c, B:29:0x0076, B:32:0x0089, B:36:0x00a1, B:112:0x0208, B:113:0x020e, B:115:0x0219, B:117:0x0221, B:121:0x0235, B:123:0x0243, B:138:0x0274, B:139:0x027b, B:141:0x0288, B:142:0x028b, B:144:0x0295, B:148:0x02a1, B:149:0x02a7, B:151:0x02af, B:152:0x02b4, B:154:0x02bc, B:155:0x02c6, B:159:0x02cf, B:160:0x02d6, B:161:0x02d7, B:164:0x02e1, B:166:0x02e5, B:168:0x02ed, B:169:0x02f0, B:171:0x02f6, B:174:0x0307, B:180:0x0321, B:184:0x032e, B:181:0x0326, B:183:0x032a, B:125:0x024a, B:127:0x0250, B:132:0x025d, B:135:0x0263, B:191:0x033e, B:193:0x0344, B:195:0x034c, B:197:0x0356, B:199:0x0367, B:200:0x036c, B:202:0x0374, B:204:0x0378, B:206:0x0380, B:209:0x0386, B:211:0x038a, B:233:0x03ed, B:235:0x03f5, B:238:0x03fe, B:239:0x0418, B:212:0x038f, B:214:0x0397, B:216:0x039b, B:217:0x039e, B:218:0x03aa, B:221:0x03b3, B:223:0x03b7, B:224:0x03ba, B:226:0x03be, B:227:0x03c1, B:228:0x03cd, B:230:0x03d7, B:232:0x03e4, B:240:0x0419, B:241:0x0437, B:243:0x043a, B:245:0x043e, B:247:0x0444, B:249:0x044a, B:250:0x044d, B:254:0x0455, B:260:0x0465, B:262:0x0474, B:264:0x047f, B:265:0x0487, B:266:0x048a, B:278:0x04b6, B:280:0x04c1, B:283:0x04ca, B:286:0x04da, B:287:0x04fa, B:273:0x049a, B:275:0x04a4, B:277:0x04b3, B:276:0x04a9, B:290:0x04ff, B:292:0x0509, B:294:0x050e, B:295:0x0511, B:297:0x051c, B:298:0x0520, B:300:0x052b, B:303:0x0532, B:306:0x053c, B:307:0x0541, B:310:0x0546, B:312:0x054b, B:316:0x0556, B:318:0x055e, B:320:0x0573, B:324:0x0592, B:326:0x0598, B:329:0x059e, B:331:0x05a4, B:333:0x05ac, B:336:0x05bc, B:339:0x05c4, B:341:0x05c8, B:342:0x05cf, B:344:0x05d4, B:345:0x05d7, B:347:0x05df, B:350:0x05e9, B:353:0x05f3, B:354:0x05f8, B:355:0x05fd, B:356:0x0617, B:321:0x057e, B:322:0x0585, B:357:0x0618, B:359:0x062a, B:362:0x0631, B:365:0x063b, B:366:0x065b, B:39:0x00b2, B:40:0x00d0, B:43:0x00d5, B:45:0x00e0, B:47:0x00e4, B:49:0x00ea, B:51:0x00f0, B:52:0x00f3, B:59:0x0102, B:61:0x010a, B:64:0x011b, B:65:0x0133, B:66:0x0134, B:67:0x0139, B:78:0x014e, B:79:0x0154, B:81:0x015b, B:83:0x0164, B:87:0x0172, B:90:0x0179, B:91:0x0191, B:86:0x016e, B:82:0x0160, B:92:0x0192, B:93:0x01aa, B:99:0x01b4, B:101:0x01bc, B:104:0x01cd, B:105:0x01ed, B:106:0x01ee, B:107:0x01f3, B:108:0x01f4, B:110:0x01fe, B:367:0x065c, B:368:0x0663, B:369:0x0664, B:370:0x0669, B:371:0x066a, B:372:0x066f), top: B:376:0x0068, inners: #1, #2 }] */
    /* JADX WARN: Removed duplicated region for block: B:243:0x043a A[Catch: all -> 0x0670, TryCatch #0 {all -> 0x0670, blocks: (B:24:0x0068, B:26:0x006c, B:29:0x0076, B:32:0x0089, B:36:0x00a1, B:112:0x0208, B:113:0x020e, B:115:0x0219, B:117:0x0221, B:121:0x0235, B:123:0x0243, B:138:0x0274, B:139:0x027b, B:141:0x0288, B:142:0x028b, B:144:0x0295, B:148:0x02a1, B:149:0x02a7, B:151:0x02af, B:152:0x02b4, B:154:0x02bc, B:155:0x02c6, B:159:0x02cf, B:160:0x02d6, B:161:0x02d7, B:164:0x02e1, B:166:0x02e5, B:168:0x02ed, B:169:0x02f0, B:171:0x02f6, B:174:0x0307, B:180:0x0321, B:184:0x032e, B:181:0x0326, B:183:0x032a, B:125:0x024a, B:127:0x0250, B:132:0x025d, B:135:0x0263, B:191:0x033e, B:193:0x0344, B:195:0x034c, B:197:0x0356, B:199:0x0367, B:200:0x036c, B:202:0x0374, B:204:0x0378, B:206:0x0380, B:209:0x0386, B:211:0x038a, B:233:0x03ed, B:235:0x03f5, B:238:0x03fe, B:239:0x0418, B:212:0x038f, B:214:0x0397, B:216:0x039b, B:217:0x039e, B:218:0x03aa, B:221:0x03b3, B:223:0x03b7, B:224:0x03ba, B:226:0x03be, B:227:0x03c1, B:228:0x03cd, B:230:0x03d7, B:232:0x03e4, B:240:0x0419, B:241:0x0437, B:243:0x043a, B:245:0x043e, B:247:0x0444, B:249:0x044a, B:250:0x044d, B:254:0x0455, B:260:0x0465, B:262:0x0474, B:264:0x047f, B:265:0x0487, B:266:0x048a, B:278:0x04b6, B:280:0x04c1, B:283:0x04ca, B:286:0x04da, B:287:0x04fa, B:273:0x049a, B:275:0x04a4, B:277:0x04b3, B:276:0x04a9, B:290:0x04ff, B:292:0x0509, B:294:0x050e, B:295:0x0511, B:297:0x051c, B:298:0x0520, B:300:0x052b, B:303:0x0532, B:306:0x053c, B:307:0x0541, B:310:0x0546, B:312:0x054b, B:316:0x0556, B:318:0x055e, B:320:0x0573, B:324:0x0592, B:326:0x0598, B:329:0x059e, B:331:0x05a4, B:333:0x05ac, B:336:0x05bc, B:339:0x05c4, B:341:0x05c8, B:342:0x05cf, B:344:0x05d4, B:345:0x05d7, B:347:0x05df, B:350:0x05e9, B:353:0x05f3, B:354:0x05f8, B:355:0x05fd, B:356:0x0617, B:321:0x057e, B:322:0x0585, B:357:0x0618, B:359:0x062a, B:362:0x0631, B:365:0x063b, B:366:0x065b, B:39:0x00b2, B:40:0x00d0, B:43:0x00d5, B:45:0x00e0, B:47:0x00e4, B:49:0x00ea, B:51:0x00f0, B:52:0x00f3, B:59:0x0102, B:61:0x010a, B:64:0x011b, B:65:0x0133, B:66:0x0134, B:67:0x0139, B:78:0x014e, B:79:0x0154, B:81:0x015b, B:83:0x0164, B:87:0x0172, B:90:0x0179, B:91:0x0191, B:86:0x016e, B:82:0x0160, B:92:0x0192, B:93:0x01aa, B:99:0x01b4, B:101:0x01bc, B:104:0x01cd, B:105:0x01ed, B:106:0x01ee, B:107:0x01f3, B:108:0x01f4, B:110:0x01fe, B:367:0x065c, B:368:0x0663, B:369:0x0664, B:370:0x0669, B:371:0x066a, B:372:0x066f), top: B:376:0x0068, inners: #1, #2 }] */
    /* JADX WARN: Removed duplicated region for block: B:267:0x048e  */
    /* JADX WARN: Removed duplicated region for block: B:280:0x04c1 A[Catch: all -> 0x0670, TryCatch #0 {all -> 0x0670, blocks: (B:24:0x0068, B:26:0x006c, B:29:0x0076, B:32:0x0089, B:36:0x00a1, B:112:0x0208, B:113:0x020e, B:115:0x0219, B:117:0x0221, B:121:0x0235, B:123:0x0243, B:138:0x0274, B:139:0x027b, B:141:0x0288, B:142:0x028b, B:144:0x0295, B:148:0x02a1, B:149:0x02a7, B:151:0x02af, B:152:0x02b4, B:154:0x02bc, B:155:0x02c6, B:159:0x02cf, B:160:0x02d6, B:161:0x02d7, B:164:0x02e1, B:166:0x02e5, B:168:0x02ed, B:169:0x02f0, B:171:0x02f6, B:174:0x0307, B:180:0x0321, B:184:0x032e, B:181:0x0326, B:183:0x032a, B:125:0x024a, B:127:0x0250, B:132:0x025d, B:135:0x0263, B:191:0x033e, B:193:0x0344, B:195:0x034c, B:197:0x0356, B:199:0x0367, B:200:0x036c, B:202:0x0374, B:204:0x0378, B:206:0x0380, B:209:0x0386, B:211:0x038a, B:233:0x03ed, B:235:0x03f5, B:238:0x03fe, B:239:0x0418, B:212:0x038f, B:214:0x0397, B:216:0x039b, B:217:0x039e, B:218:0x03aa, B:221:0x03b3, B:223:0x03b7, B:224:0x03ba, B:226:0x03be, B:227:0x03c1, B:228:0x03cd, B:230:0x03d7, B:232:0x03e4, B:240:0x0419, B:241:0x0437, B:243:0x043a, B:245:0x043e, B:247:0x0444, B:249:0x044a, B:250:0x044d, B:254:0x0455, B:260:0x0465, B:262:0x0474, B:264:0x047f, B:265:0x0487, B:266:0x048a, B:278:0x04b6, B:280:0x04c1, B:283:0x04ca, B:286:0x04da, B:287:0x04fa, B:273:0x049a, B:275:0x04a4, B:277:0x04b3, B:276:0x04a9, B:290:0x04ff, B:292:0x0509, B:294:0x050e, B:295:0x0511, B:297:0x051c, B:298:0x0520, B:300:0x052b, B:303:0x0532, B:306:0x053c, B:307:0x0541, B:310:0x0546, B:312:0x054b, B:316:0x0556, B:318:0x055e, B:320:0x0573, B:324:0x0592, B:326:0x0598, B:329:0x059e, B:331:0x05a4, B:333:0x05ac, B:336:0x05bc, B:339:0x05c4, B:341:0x05c8, B:342:0x05cf, B:344:0x05d4, B:345:0x05d7, B:347:0x05df, B:350:0x05e9, B:353:0x05f3, B:354:0x05f8, B:355:0x05fd, B:356:0x0617, B:321:0x057e, B:322:0x0585, B:357:0x0618, B:359:0x062a, B:362:0x0631, B:365:0x063b, B:366:0x065b, B:39:0x00b2, B:40:0x00d0, B:43:0x00d5, B:45:0x00e0, B:47:0x00e4, B:49:0x00ea, B:51:0x00f0, B:52:0x00f3, B:59:0x0102, B:61:0x010a, B:64:0x011b, B:65:0x0133, B:66:0x0134, B:67:0x0139, B:78:0x014e, B:79:0x0154, B:81:0x015b, B:83:0x0164, B:87:0x0172, B:90:0x0179, B:91:0x0191, B:86:0x016e, B:82:0x0160, B:92:0x0192, B:93:0x01aa, B:99:0x01b4, B:101:0x01bc, B:104:0x01cd, B:105:0x01ed, B:106:0x01ee, B:107:0x01f3, B:108:0x01f4, B:110:0x01fe, B:367:0x065c, B:368:0x0663, B:369:0x0664, B:370:0x0669, B:371:0x066a, B:372:0x066f), top: B:376:0x0068, inners: #1, #2 }] */
    /* JADX WARN: Removed duplicated region for block: B:336:0x05bc A[Catch: all -> 0x0670, TryCatch #0 {all -> 0x0670, blocks: (B:24:0x0068, B:26:0x006c, B:29:0x0076, B:32:0x0089, B:36:0x00a1, B:112:0x0208, B:113:0x020e, B:115:0x0219, B:117:0x0221, B:121:0x0235, B:123:0x0243, B:138:0x0274, B:139:0x027b, B:141:0x0288, B:142:0x028b, B:144:0x0295, B:148:0x02a1, B:149:0x02a7, B:151:0x02af, B:152:0x02b4, B:154:0x02bc, B:155:0x02c6, B:159:0x02cf, B:160:0x02d6, B:161:0x02d7, B:164:0x02e1, B:166:0x02e5, B:168:0x02ed, B:169:0x02f0, B:171:0x02f6, B:174:0x0307, B:180:0x0321, B:184:0x032e, B:181:0x0326, B:183:0x032a, B:125:0x024a, B:127:0x0250, B:132:0x025d, B:135:0x0263, B:191:0x033e, B:193:0x0344, B:195:0x034c, B:197:0x0356, B:199:0x0367, B:200:0x036c, B:202:0x0374, B:204:0x0378, B:206:0x0380, B:209:0x0386, B:211:0x038a, B:233:0x03ed, B:235:0x03f5, B:238:0x03fe, B:239:0x0418, B:212:0x038f, B:214:0x0397, B:216:0x039b, B:217:0x039e, B:218:0x03aa, B:221:0x03b3, B:223:0x03b7, B:224:0x03ba, B:226:0x03be, B:227:0x03c1, B:228:0x03cd, B:230:0x03d7, B:232:0x03e4, B:240:0x0419, B:241:0x0437, B:243:0x043a, B:245:0x043e, B:247:0x0444, B:249:0x044a, B:250:0x044d, B:254:0x0455, B:260:0x0465, B:262:0x0474, B:264:0x047f, B:265:0x0487, B:266:0x048a, B:278:0x04b6, B:280:0x04c1, B:283:0x04ca, B:286:0x04da, B:287:0x04fa, B:273:0x049a, B:275:0x04a4, B:277:0x04b3, B:276:0x04a9, B:290:0x04ff, B:292:0x0509, B:294:0x050e, B:295:0x0511, B:297:0x051c, B:298:0x0520, B:300:0x052b, B:303:0x0532, B:306:0x053c, B:307:0x0541, B:310:0x0546, B:312:0x054b, B:316:0x0556, B:318:0x055e, B:320:0x0573, B:324:0x0592, B:326:0x0598, B:329:0x059e, B:331:0x05a4, B:333:0x05ac, B:336:0x05bc, B:339:0x05c4, B:341:0x05c8, B:342:0x05cf, B:344:0x05d4, B:345:0x05d7, B:347:0x05df, B:350:0x05e9, B:353:0x05f3, B:354:0x05f8, B:355:0x05fd, B:356:0x0617, B:321:0x057e, B:322:0x0585, B:357:0x0618, B:359:0x062a, B:362:0x0631, B:365:0x063b, B:366:0x065b, B:39:0x00b2, B:40:0x00d0, B:43:0x00d5, B:45:0x00e0, B:47:0x00e4, B:49:0x00ea, B:51:0x00f0, B:52:0x00f3, B:59:0x0102, B:61:0x010a, B:64:0x011b, B:65:0x0133, B:66:0x0134, B:67:0x0139, B:78:0x014e, B:79:0x0154, B:81:0x015b, B:83:0x0164, B:87:0x0172, B:90:0x0179, B:91:0x0191, B:86:0x016e, B:82:0x0160, B:92:0x0192, B:93:0x01aa, B:99:0x01b4, B:101:0x01bc, B:104:0x01cd, B:105:0x01ed, B:106:0x01ee, B:107:0x01f3, B:108:0x01f4, B:110:0x01fe, B:367:0x065c, B:368:0x0663, B:369:0x0664, B:370:0x0669, B:371:0x066a, B:372:0x066f), top: B:376:0x0068, inners: #1, #2 }] */
    /* JADX WARN: Removed duplicated region for block: B:341:0x05c8 A[Catch: all -> 0x0670, TryCatch #0 {all -> 0x0670, blocks: (B:24:0x0068, B:26:0x006c, B:29:0x0076, B:32:0x0089, B:36:0x00a1, B:112:0x0208, B:113:0x020e, B:115:0x0219, B:117:0x0221, B:121:0x0235, B:123:0x0243, B:138:0x0274, B:139:0x027b, B:141:0x0288, B:142:0x028b, B:144:0x0295, B:148:0x02a1, B:149:0x02a7, B:151:0x02af, B:152:0x02b4, B:154:0x02bc, B:155:0x02c6, B:159:0x02cf, B:160:0x02d6, B:161:0x02d7, B:164:0x02e1, B:166:0x02e5, B:168:0x02ed, B:169:0x02f0, B:171:0x02f6, B:174:0x0307, B:180:0x0321, B:184:0x032e, B:181:0x0326, B:183:0x032a, B:125:0x024a, B:127:0x0250, B:132:0x025d, B:135:0x0263, B:191:0x033e, B:193:0x0344, B:195:0x034c, B:197:0x0356, B:199:0x0367, B:200:0x036c, B:202:0x0374, B:204:0x0378, B:206:0x0380, B:209:0x0386, B:211:0x038a, B:233:0x03ed, B:235:0x03f5, B:238:0x03fe, B:239:0x0418, B:212:0x038f, B:214:0x0397, B:216:0x039b, B:217:0x039e, B:218:0x03aa, B:221:0x03b3, B:223:0x03b7, B:224:0x03ba, B:226:0x03be, B:227:0x03c1, B:228:0x03cd, B:230:0x03d7, B:232:0x03e4, B:240:0x0419, B:241:0x0437, B:243:0x043a, B:245:0x043e, B:247:0x0444, B:249:0x044a, B:250:0x044d, B:254:0x0455, B:260:0x0465, B:262:0x0474, B:264:0x047f, B:265:0x0487, B:266:0x048a, B:278:0x04b6, B:280:0x04c1, B:283:0x04ca, B:286:0x04da, B:287:0x04fa, B:273:0x049a, B:275:0x04a4, B:277:0x04b3, B:276:0x04a9, B:290:0x04ff, B:292:0x0509, B:294:0x050e, B:295:0x0511, B:297:0x051c, B:298:0x0520, B:300:0x052b, B:303:0x0532, B:306:0x053c, B:307:0x0541, B:310:0x0546, B:312:0x054b, B:316:0x0556, B:318:0x055e, B:320:0x0573, B:324:0x0592, B:326:0x0598, B:329:0x059e, B:331:0x05a4, B:333:0x05ac, B:336:0x05bc, B:339:0x05c4, B:341:0x05c8, B:342:0x05cf, B:344:0x05d4, B:345:0x05d7, B:347:0x05df, B:350:0x05e9, B:353:0x05f3, B:354:0x05f8, B:355:0x05fd, B:356:0x0617, B:321:0x057e, B:322:0x0585, B:357:0x0618, B:359:0x062a, B:362:0x0631, B:365:0x063b, B:366:0x065b, B:39:0x00b2, B:40:0x00d0, B:43:0x00d5, B:45:0x00e0, B:47:0x00e4, B:49:0x00ea, B:51:0x00f0, B:52:0x00f3, B:59:0x0102, B:61:0x010a, B:64:0x011b, B:65:0x0133, B:66:0x0134, B:67:0x0139, B:78:0x014e, B:79:0x0154, B:81:0x015b, B:83:0x0164, B:87:0x0172, B:90:0x0179, B:91:0x0191, B:86:0x016e, B:82:0x0160, B:92:0x0192, B:93:0x01aa, B:99:0x01b4, B:101:0x01bc, B:104:0x01cd, B:105:0x01ed, B:106:0x01ee, B:107:0x01f3, B:108:0x01f4, B:110:0x01fe, B:367:0x065c, B:368:0x0663, B:369:0x0664, B:370:0x0669, B:371:0x066a, B:372:0x066f), top: B:376:0x0068, inners: #1, #2 }] */
    /* JADX WARN: Removed duplicated region for block: B:344:0x05d4 A[Catch: all -> 0x0670, TryCatch #0 {all -> 0x0670, blocks: (B:24:0x0068, B:26:0x006c, B:29:0x0076, B:32:0x0089, B:36:0x00a1, B:112:0x0208, B:113:0x020e, B:115:0x0219, B:117:0x0221, B:121:0x0235, B:123:0x0243, B:138:0x0274, B:139:0x027b, B:141:0x0288, B:142:0x028b, B:144:0x0295, B:148:0x02a1, B:149:0x02a7, B:151:0x02af, B:152:0x02b4, B:154:0x02bc, B:155:0x02c6, B:159:0x02cf, B:160:0x02d6, B:161:0x02d7, B:164:0x02e1, B:166:0x02e5, B:168:0x02ed, B:169:0x02f0, B:171:0x02f6, B:174:0x0307, B:180:0x0321, B:184:0x032e, B:181:0x0326, B:183:0x032a, B:125:0x024a, B:127:0x0250, B:132:0x025d, B:135:0x0263, B:191:0x033e, B:193:0x0344, B:195:0x034c, B:197:0x0356, B:199:0x0367, B:200:0x036c, B:202:0x0374, B:204:0x0378, B:206:0x0380, B:209:0x0386, B:211:0x038a, B:233:0x03ed, B:235:0x03f5, B:238:0x03fe, B:239:0x0418, B:212:0x038f, B:214:0x0397, B:216:0x039b, B:217:0x039e, B:218:0x03aa, B:221:0x03b3, B:223:0x03b7, B:224:0x03ba, B:226:0x03be, B:227:0x03c1, B:228:0x03cd, B:230:0x03d7, B:232:0x03e4, B:240:0x0419, B:241:0x0437, B:243:0x043a, B:245:0x043e, B:247:0x0444, B:249:0x044a, B:250:0x044d, B:254:0x0455, B:260:0x0465, B:262:0x0474, B:264:0x047f, B:265:0x0487, B:266:0x048a, B:278:0x04b6, B:280:0x04c1, B:283:0x04ca, B:286:0x04da, B:287:0x04fa, B:273:0x049a, B:275:0x04a4, B:277:0x04b3, B:276:0x04a9, B:290:0x04ff, B:292:0x0509, B:294:0x050e, B:295:0x0511, B:297:0x051c, B:298:0x0520, B:300:0x052b, B:303:0x0532, B:306:0x053c, B:307:0x0541, B:310:0x0546, B:312:0x054b, B:316:0x0556, B:318:0x055e, B:320:0x0573, B:324:0x0592, B:326:0x0598, B:329:0x059e, B:331:0x05a4, B:333:0x05ac, B:336:0x05bc, B:339:0x05c4, B:341:0x05c8, B:342:0x05cf, B:344:0x05d4, B:345:0x05d7, B:347:0x05df, B:350:0x05e9, B:353:0x05f3, B:354:0x05f8, B:355:0x05fd, B:356:0x0617, B:321:0x057e, B:322:0x0585, B:357:0x0618, B:359:0x062a, B:362:0x0631, B:365:0x063b, B:366:0x065b, B:39:0x00b2, B:40:0x00d0, B:43:0x00d5, B:45:0x00e0, B:47:0x00e4, B:49:0x00ea, B:51:0x00f0, B:52:0x00f3, B:59:0x0102, B:61:0x010a, B:64:0x011b, B:65:0x0133, B:66:0x0134, B:67:0x0139, B:78:0x014e, B:79:0x0154, B:81:0x015b, B:83:0x0164, B:87:0x0172, B:90:0x0179, B:91:0x0191, B:86:0x016e, B:82:0x0160, B:92:0x0192, B:93:0x01aa, B:99:0x01b4, B:101:0x01bc, B:104:0x01cd, B:105:0x01ed, B:106:0x01ee, B:107:0x01f3, B:108:0x01f4, B:110:0x01fe, B:367:0x065c, B:368:0x0663, B:369:0x0664, B:370:0x0669, B:371:0x066a, B:372:0x066f), top: B:376:0x0068, inners: #1, #2 }] */
    /* JADX WARN: Removed duplicated region for block: B:350:0x05e9 A[Catch: all -> 0x0670, TRY_ENTER, TryCatch #0 {all -> 0x0670, blocks: (B:24:0x0068, B:26:0x006c, B:29:0x0076, B:32:0x0089, B:36:0x00a1, B:112:0x0208, B:113:0x020e, B:115:0x0219, B:117:0x0221, B:121:0x0235, B:123:0x0243, B:138:0x0274, B:139:0x027b, B:141:0x0288, B:142:0x028b, B:144:0x0295, B:148:0x02a1, B:149:0x02a7, B:151:0x02af, B:152:0x02b4, B:154:0x02bc, B:155:0x02c6, B:159:0x02cf, B:160:0x02d6, B:161:0x02d7, B:164:0x02e1, B:166:0x02e5, B:168:0x02ed, B:169:0x02f0, B:171:0x02f6, B:174:0x0307, B:180:0x0321, B:184:0x032e, B:181:0x0326, B:183:0x032a, B:125:0x024a, B:127:0x0250, B:132:0x025d, B:135:0x0263, B:191:0x033e, B:193:0x0344, B:195:0x034c, B:197:0x0356, B:199:0x0367, B:200:0x036c, B:202:0x0374, B:204:0x0378, B:206:0x0380, B:209:0x0386, B:211:0x038a, B:233:0x03ed, B:235:0x03f5, B:238:0x03fe, B:239:0x0418, B:212:0x038f, B:214:0x0397, B:216:0x039b, B:217:0x039e, B:218:0x03aa, B:221:0x03b3, B:223:0x03b7, B:224:0x03ba, B:226:0x03be, B:227:0x03c1, B:228:0x03cd, B:230:0x03d7, B:232:0x03e4, B:240:0x0419, B:241:0x0437, B:243:0x043a, B:245:0x043e, B:247:0x0444, B:249:0x044a, B:250:0x044d, B:254:0x0455, B:260:0x0465, B:262:0x0474, B:264:0x047f, B:265:0x0487, B:266:0x048a, B:278:0x04b6, B:280:0x04c1, B:283:0x04ca, B:286:0x04da, B:287:0x04fa, B:273:0x049a, B:275:0x04a4, B:277:0x04b3, B:276:0x04a9, B:290:0x04ff, B:292:0x0509, B:294:0x050e, B:295:0x0511, B:297:0x051c, B:298:0x0520, B:300:0x052b, B:303:0x0532, B:306:0x053c, B:307:0x0541, B:310:0x0546, B:312:0x054b, B:316:0x0556, B:318:0x055e, B:320:0x0573, B:324:0x0592, B:326:0x0598, B:329:0x059e, B:331:0x05a4, B:333:0x05ac, B:336:0x05bc, B:339:0x05c4, B:341:0x05c8, B:342:0x05cf, B:344:0x05d4, B:345:0x05d7, B:347:0x05df, B:350:0x05e9, B:353:0x05f3, B:354:0x05f8, B:355:0x05fd, B:356:0x0617, B:321:0x057e, B:322:0x0585, B:357:0x0618, B:359:0x062a, B:362:0x0631, B:365:0x063b, B:366:0x065b, B:39:0x00b2, B:40:0x00d0, B:43:0x00d5, B:45:0x00e0, B:47:0x00e4, B:49:0x00ea, B:51:0x00f0, B:52:0x00f3, B:59:0x0102, B:61:0x010a, B:64:0x011b, B:65:0x0133, B:66:0x0134, B:67:0x0139, B:78:0x014e, B:79:0x0154, B:81:0x015b, B:83:0x0164, B:87:0x0172, B:90:0x0179, B:91:0x0191, B:86:0x016e, B:82:0x0160, B:92:0x0192, B:93:0x01aa, B:99:0x01b4, B:101:0x01bc, B:104:0x01cd, B:105:0x01ed, B:106:0x01ee, B:107:0x01f3, B:108:0x01f4, B:110:0x01fe, B:367:0x065c, B:368:0x0663, B:369:0x0664, B:370:0x0669, B:371:0x066a, B:372:0x066f), top: B:376:0x0068, inners: #1, #2 }] */
    /* JADX WARN: Removed duplicated region for block: B:404:0x04c6 A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:407:0x0465 A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:412:0x05df A[SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final java.lang.Object parseObject(java.util.Map r14, java.lang.Object r15) {
        /*
            Method dump skipped, instructions count: 1653
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.alibaba.fastjson.parser.DefaultJSONParser.parseObject(java.util.Map, java.lang.Object):java.lang.Object");
    }

    public void popContext() {
        if (this.lexer.isEnabled(Feature.DisableCircularReferenceDetect)) {
            return;
        }
        this.context = this.context.parent;
        int i = this.contextArrayIndex;
        if (i <= 0) {
            return;
        }
        int i2 = i - 1;
        this.contextArrayIndex = i2;
        this.contextArray[i2] = null;
    }

    public Object resolveReference(String str) {
        if (this.contextArray == null) {
            return null;
        }
        int i = 0;
        while (true) {
            ParseContext[] parseContextArr = this.contextArray;
            if (i >= parseContextArr.length || i >= this.contextArrayIndex) {
                break;
            }
            ParseContext parseContext = parseContextArr[i];
            if (parseContext.toString().equals(str)) {
                return parseContext.object;
            }
            i++;
        }
        return null;
    }

    public void setConfig(ParserConfig parserConfig) {
        this.config = parserConfig;
    }

    public void setContext(ParseContext parseContext) {
        if (this.lexer.isEnabled(Feature.DisableCircularReferenceDetect)) {
            return;
        }
        this.context = parseContext;
    }

    public void setDateFomrat(DateFormat dateFormat) {
        setDateFormat(dateFormat);
    }

    public void setDateFormat(String str) {
        this.dateFormatPattern = str;
        this.dateFormat = null;
    }

    public void setFieldTypeResolver(FieldTypeResolver fieldTypeResolver) {
        this.fieldTypeResolver = fieldTypeResolver;
    }

    public void setResolveStatus(int i) {
        this.resolveStatus = i;
    }

    public void throwException(int i) {
        StringBuilder e = a.e("syntax error, expect ");
        e.append(JSONToken.name(i));
        e.append(", actual ");
        e.append(JSONToken.name(this.lexer.token()));
        throw new JSONException(e.toString());
    }

    public DefaultJSONParser(String str, ParserConfig parserConfig) {
        this(str, new JSONScanner(str, JSON.DEFAULT_PARSER_FEATURE), parserConfig);
    }

    public Object parse(Object obj) {
        JSONLexer jSONLexer = this.lexer;
        int i = jSONLexer.token();
        if (i == 2) {
            Number integerValue = jSONLexer.integerValue();
            jSONLexer.nextToken();
            return integerValue;
        } else if (i == 3) {
            Number decimalValue = jSONLexer.decimalValue(jSONLexer.isEnabled(Feature.UseBigDecimal));
            jSONLexer.nextToken();
            return decimalValue;
        } else if (i == 4) {
            String stringVal = jSONLexer.stringVal();
            jSONLexer.nextToken(16);
            if (jSONLexer.isEnabled(Feature.AllowISO8601DateFormat)) {
                JSONScanner jSONScanner = new JSONScanner(stringVal);
                try {
                    if (jSONScanner.scanISO8601DateIfMatch()) {
                        return jSONScanner.getCalendar().getTime();
                    }
                } finally {
                    jSONScanner.close();
                }
            }
            return stringVal;
        } else if (i != 12) {
            if (i == 14) {
                JSONArray jSONArray = new JSONArray();
                parseArray(jSONArray, obj);
                return jSONLexer.isEnabled(Feature.UseObjectArray) ? jSONArray.toArray() : jSONArray;
            } else if (i == 18) {
                if ("NaN".equals(jSONLexer.stringVal())) {
                    jSONLexer.nextToken();
                    return null;
                }
                StringBuilder e = a.e("syntax error, ");
                e.append(jSONLexer.info());
                throw new JSONException(e.toString());
            } else if (i != 26) {
                switch (i) {
                    case 6:
                        jSONLexer.nextToken();
                        return Boolean.TRUE;
                    case 7:
                        jSONLexer.nextToken();
                        return Boolean.FALSE;
                    case 8:
                        jSONLexer.nextToken();
                        return null;
                    case 9:
                        jSONLexer.nextToken(18);
                        if (jSONLexer.token() == 18) {
                            jSONLexer.nextToken(10);
                            accept(10);
                            long longValue = jSONLexer.integerValue().longValue();
                            accept(2);
                            accept(11);
                            return new Date(longValue);
                        }
                        throw new JSONException("syntax error");
                    default:
                        switch (i) {
                            case 20:
                                if (jSONLexer.isBlankInput()) {
                                    return null;
                                }
                                StringBuilder e2 = a.e("unterminated json string, ");
                                e2.append(jSONLexer.info());
                                throw new JSONException(e2.toString());
                            case 21:
                                jSONLexer.nextToken();
                                HashSet hashSet = new HashSet();
                                parseArray(hashSet, obj);
                                return hashSet;
                            case 22:
                                jSONLexer.nextToken();
                                TreeSet treeSet = new TreeSet();
                                parseArray(treeSet, obj);
                                return treeSet;
                            case 23:
                                jSONLexer.nextToken();
                                return null;
                            default:
                                StringBuilder e3 = a.e("syntax error, ");
                                e3.append(jSONLexer.info());
                                throw new JSONException(e3.toString());
                        }
                }
            } else {
                byte[] bytesValue = jSONLexer.bytesValue();
                jSONLexer.nextToken();
                return bytesValue;
            }
        } else {
            return parseObject(new JSONObject(jSONLexer.isEnabled(Feature.OrderedField)), obj);
        }
    }

    public DefaultJSONParser(String str, ParserConfig parserConfig, int i) {
        this(str, new JSONScanner(str, i), parserConfig);
    }

    public void parseArray(Class<?> cls, Collection collection) {
        parseArray((Type) cls, collection);
    }

    public ParseContext setContext(Object obj, Object obj2) {
        if (this.lexer.isEnabled(Feature.DisableCircularReferenceDetect)) {
            return null;
        }
        return setContext(this.context, obj, obj2);
    }

    public void setDateFormat(DateFormat dateFormat) {
        this.dateFormat = dateFormat;
    }

    public DefaultJSONParser(char[] cArr, int i, ParserConfig parserConfig, int i2) {
        this(cArr, new JSONScanner(cArr, i, i2), parserConfig);
    }

    public void parseArray(Type type, Collection collection) {
        parseArray(type, collection, null);
    }

    public DefaultJSONParser(JSONLexer jSONLexer) {
        this(jSONLexer, ParserConfig.getGlobalInstance());
    }

    public void parseArray(Type type, Collection collection, Object obj) {
        ObjectDeserializer deserializer;
        int i = this.lexer.token();
        if (i == 21 || i == 22) {
            this.lexer.nextToken();
            i = this.lexer.token();
        }
        if (i == 14) {
            if (Integer.TYPE != type) {
                if (String.class == type) {
                    deserializer = StringCodec.instance;
                    this.lexer.nextToken(4);
                } else {
                    deserializer = this.config.getDeserializer(type);
                    this.lexer.nextToken(deserializer.getFastMatchToken());
                }
            } else {
                deserializer = IntegerCodec.instance;
                this.lexer.nextToken(2);
            }
            ParseContext parseContext = this.context;
            setContext(collection, obj);
            int i2 = 0;
            while (true) {
                try {
                    if (this.lexer.isEnabled(Feature.AllowArbitraryCommas)) {
                        while (this.lexer.token() == 16) {
                            this.lexer.nextToken();
                        }
                    }
                    if (this.lexer.token() == 15) {
                        setContext(parseContext);
                        this.lexer.nextToken(16);
                        return;
                    }
                    Object obj2 = null;
                    if (Integer.TYPE != type) {
                        if (String.class == type) {
                            if (this.lexer.token() == 4) {
                                obj2 = this.lexer.stringVal();
                                this.lexer.nextToken(16);
                            } else {
                                Object parse = parse();
                                if (parse != null) {
                                    obj2 = parse.toString();
                                }
                            }
                            collection.add(obj2);
                        } else {
                            if (this.lexer.token() == 8) {
                                this.lexer.nextToken();
                            } else {
                                obj2 = deserializer.deserialze(this, type, Integer.valueOf(i2));
                            }
                            collection.add(obj2);
                            checkListResolve(collection);
                        }
                    } else {
                        collection.add(IntegerCodec.instance.deserialze(this, null, null));
                    }
                    if (this.lexer.token() == 16) {
                        this.lexer.nextToken(deserializer.getFastMatchToken());
                    }
                    i2++;
                } catch (Throwable th) {
                    setContext(parseContext);
                    throw th;
                }
            }
        } else {
            StringBuilder e = a.e("expect '[', but ");
            e.append(JSONToken.name(i));
            e.append(", ");
            e.append(this.lexer.info());
            throw new JSONException(e.toString());
        }
    }

    public ParseContext setContext(ParseContext parseContext, Object obj, Object obj2) {
        if (this.lexer.isEnabled(Feature.DisableCircularReferenceDetect)) {
            return null;
        }
        ParseContext parseContext2 = new ParseContext(parseContext, obj, obj2);
        this.context = parseContext2;
        addContext(parseContext2);
        return this.context;
    }

    public DefaultJSONParser(JSONLexer jSONLexer, ParserConfig parserConfig) {
        this((Object) null, jSONLexer, parserConfig);
    }

    public final void accept(int i, int i2) {
        JSONLexer jSONLexer = this.lexer;
        if (jSONLexer.token() == i) {
            jSONLexer.nextToken(i2);
        } else {
            throwException(i);
        }
    }

    public DefaultJSONParser(Object obj, JSONLexer jSONLexer, ParserConfig parserConfig) {
        this.dateFormatPattern = JSON.DEFFAULT_DATE_FORMAT;
        this.contextArrayIndex = 0;
        this.resolveStatus = 0;
        this.extraTypeProviders = null;
        this.extraProcessors = null;
        this.fieldTypeResolver = null;
        this.objectKeyLevel = 0;
        this.autoTypeAccept = null;
        this.lexer = jSONLexer;
        this.input = obj;
        this.config = parserConfig;
        this.symbolTable = parserConfig.symbolTable;
        char current = jSONLexer.getCurrent();
        if (current == '{') {
            jSONLexer.next();
            ((JSONLexerBase) jSONLexer).token = 12;
        } else if (current == '[') {
            jSONLexer.next();
            ((JSONLexerBase) jSONLexer).token = 14;
        } else {
            jSONLexer.nextToken();
        }
    }

    public Object[] parseArray(Type[] typeArr) {
        Object cast;
        Class<?> cls;
        boolean z;
        Class cls2;
        int i = 8;
        if (this.lexer.token() == 8) {
            this.lexer.nextToken(16);
            return null;
        }
        int i2 = 14;
        if (this.lexer.token() == 14) {
            Object[] objArr = new Object[typeArr.length];
            if (typeArr.length == 0) {
                this.lexer.nextToken(15);
                if (this.lexer.token() == 15) {
                    this.lexer.nextToken(16);
                    return new Object[0];
                }
                throw new JSONException("syntax error");
            }
            this.lexer.nextToken(2);
            int i3 = 0;
            while (i3 < typeArr.length) {
                if (this.lexer.token() == i) {
                    this.lexer.nextToken(16);
                    cast = null;
                } else {
                    Type type = typeArr[i3];
                    if (type != Integer.TYPE && type != Integer.class) {
                        if (type == String.class) {
                            if (this.lexer.token() == 4) {
                                cast = this.lexer.stringVal();
                                this.lexer.nextToken(16);
                            } else {
                                cast = TypeUtils.cast(parse(), type, this.config);
                            }
                        } else {
                            if (i3 == typeArr.length - 1 && (type instanceof Class) && (((cls2 = (Class) type) != byte[].class && cls2 != char[].class) || this.lexer.token() != 4)) {
                                z = cls2.isArray();
                                cls = cls2.getComponentType();
                            } else {
                                cls = null;
                                z = false;
                            }
                            if (z && this.lexer.token() != i2) {
                                ArrayList arrayList = new ArrayList();
                                ObjectDeserializer deserializer = this.config.getDeserializer(cls);
                                int fastMatchToken = deserializer.getFastMatchToken();
                                if (this.lexer.token() != 15) {
                                    while (true) {
                                        arrayList.add(deserializer.deserialze(this, type, null));
                                        if (this.lexer.token() != 16) {
                                            break;
                                        }
                                        this.lexer.nextToken(fastMatchToken);
                                    }
                                    if (this.lexer.token() != 15) {
                                        StringBuilder e = a.e("syntax error :");
                                        e.append(JSONToken.name(this.lexer.token()));
                                        throw new JSONException(e.toString());
                                    }
                                }
                                cast = TypeUtils.cast(arrayList, type, this.config);
                            } else {
                                cast = this.config.getDeserializer(type).deserialze(this, type, Integer.valueOf(i3));
                            }
                        }
                    } else if (this.lexer.token() == 2) {
                        cast = Integer.valueOf(this.lexer.intValue());
                        this.lexer.nextToken(16);
                    } else {
                        cast = TypeUtils.cast(parse(), type, this.config);
                    }
                }
                objArr[i3] = cast;
                if (this.lexer.token() == 15) {
                    break;
                } else if (this.lexer.token() == 16) {
                    if (i3 == typeArr.length - 1) {
                        this.lexer.nextToken(15);
                    } else {
                        this.lexer.nextToken(2);
                    }
                    i3++;
                    i = 8;
                    i2 = 14;
                } else {
                    StringBuilder e2 = a.e("syntax error :");
                    e2.append(JSONToken.name(this.lexer.token()));
                    throw new JSONException(e2.toString());
                }
            }
            if (this.lexer.token() == 15) {
                this.lexer.nextToken(16);
                return objArr;
            }
            throw new JSONException("syntax error");
        }
        StringBuilder e3 = a.e("syntax error : ");
        e3.append(this.lexer.tokenName());
        throw new JSONException(e3.toString());
    }

    /* JADX WARN: Code restructure failed: missing block: B:86:0x020d, code lost:
        return r11;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public java.lang.Object parse(com.alibaba.fastjson.parser.deserializer.PropertyProcessable r11, java.lang.Object r12) {
        /*
            Method dump skipped, instructions count: 572
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.alibaba.fastjson.parser.DefaultJSONParser.parse(com.alibaba.fastjson.parser.deserializer.PropertyProcessable, java.lang.Object):java.lang.Object");
    }

    public final void parseArray(Collection collection) {
        parseArray(collection, (Object) null);
    }

    public final void parseArray(Collection collection, Object obj) {
        Number decimalValue;
        JSONLexer jSONLexer = this.lexer;
        if (jSONLexer.token() == 21 || jSONLexer.token() == 22) {
            jSONLexer.nextToken();
        }
        if (jSONLexer.token() == 14) {
            jSONLexer.nextToken(4);
            ParseContext parseContext = this.context;
            if (parseContext != null && parseContext.level > 512) {
                throw new JSONException("array level > 512");
            }
            ParseContext parseContext2 = this.context;
            setContext(collection, obj);
            int i = 0;
            while (true) {
                try {
                    if (jSONLexer.isEnabled(Feature.AllowArbitraryCommas)) {
                        while (jSONLexer.token() == 16) {
                            jSONLexer.nextToken();
                        }
                    }
                    int i2 = jSONLexer.token();
                    Number number = null;
                    number = null;
                    if (i2 == 2) {
                        Number integerValue = jSONLexer.integerValue();
                        jSONLexer.nextToken(16);
                        number = integerValue;
                    } else if (i2 == 3) {
                        if (jSONLexer.isEnabled(Feature.UseBigDecimal)) {
                            decimalValue = jSONLexer.decimalValue(true);
                        } else {
                            decimalValue = jSONLexer.decimalValue(false);
                        }
                        number = decimalValue;
                        jSONLexer.nextToken(16);
                    } else if (i2 == 4) {
                        String stringVal = jSONLexer.stringVal();
                        jSONLexer.nextToken(16);
                        number = stringVal;
                        if (jSONLexer.isEnabled(Feature.AllowISO8601DateFormat)) {
                            JSONScanner jSONScanner = new JSONScanner(stringVal);
                            Date date = stringVal;
                            if (jSONScanner.scanISO8601DateIfMatch()) {
                                date = jSONScanner.getCalendar().getTime();
                            }
                            jSONScanner.close();
                            number = date;
                        }
                    } else if (i2 == 6) {
                        Boolean bool = Boolean.TRUE;
                        jSONLexer.nextToken(16);
                        number = bool;
                    } else if (i2 == 7) {
                        Boolean bool2 = Boolean.FALSE;
                        jSONLexer.nextToken(16);
                        number = bool2;
                    } else if (i2 == 8) {
                        jSONLexer.nextToken(4);
                    } else if (i2 == 12) {
                        number = parseObject(new JSONObject(jSONLexer.isEnabled(Feature.OrderedField)), Integer.valueOf(i));
                    } else if (i2 == 20) {
                        throw new JSONException("unclosed jsonArray");
                    } else {
                        if (i2 == 23) {
                            jSONLexer.nextToken(4);
                        } else if (i2 == 14) {
                            JSONArray jSONArray = new JSONArray();
                            parseArray(jSONArray, Integer.valueOf(i));
                            number = jSONArray;
                            if (jSONLexer.isEnabled(Feature.UseObjectArray)) {
                                number = jSONArray.toArray();
                            }
                        } else if (i2 != 15) {
                            number = parse();
                        } else {
                            jSONLexer.nextToken(16);
                            return;
                        }
                    }
                    collection.add(number);
                    checkListResolve(collection);
                    if (jSONLexer.token() == 16) {
                        jSONLexer.nextToken(4);
                    }
                    i++;
                } finally {
                    setContext(parseContext2);
                }
            }
        } else {
            StringBuilder e = a.e("syntax error, expect [, actual ");
            e.append(JSONToken.name(jSONLexer.token()));
            e.append(", pos ");
            e.append(jSONLexer.pos());
            e.append(", fieldName ");
            e.append(obj);
            throw new JSONException(e.toString());
        }
    }

    public <T> T parseObject(Class<T> cls) {
        return (T) parseObject(cls, (Object) null);
    }

    public <T> T parseObject(Type type) {
        return (T) parseObject(type, (Object) null);
    }

    public <T> T parseObject(Type type, Object obj) {
        int i = this.lexer.token();
        if (i == 8) {
            this.lexer.nextToken();
            return null;
        }
        if (i == 4) {
            if (type == byte[].class) {
                T t = (T) this.lexer.bytesValue();
                this.lexer.nextToken();
                return t;
            } else if (type == char[].class) {
                String stringVal = this.lexer.stringVal();
                this.lexer.nextToken();
                return (T) stringVal.toCharArray();
            }
        }
        ObjectDeserializer deserializer = this.config.getDeserializer(type);
        try {
            if (deserializer.getClass() == JavaBeanDeserializer.class) {
                if (this.lexer.token() != 12 && this.lexer.token() != 14) {
                    throw new JSONException("syntax error,except start with { or [,but actually start with " + this.lexer.tokenName());
                }
                return (T) ((JavaBeanDeserializer) deserializer).deserialze(this, type, obj, 0);
            }
            return (T) deserializer.deserialze(this, type, obj);
        } catch (JSONException e) {
            throw e;
        } catch (Throwable th) {
            throw new JSONException(th.getMessage(), th);
        }
    }

    public void parseObject(Object obj) {
        Object deserialze;
        Class<?> cls = obj.getClass();
        ObjectDeserializer deserializer = this.config.getDeserializer(cls);
        JavaBeanDeserializer javaBeanDeserializer = deserializer instanceof JavaBeanDeserializer ? (JavaBeanDeserializer) deserializer : null;
        if (this.lexer.token() != 12 && this.lexer.token() != 16) {
            StringBuilder e = a.e("syntax error, expect {, actual ");
            e.append(this.lexer.tokenName());
            throw new JSONException(e.toString());
        }
        while (true) {
            String scanSymbol = this.lexer.scanSymbol(this.symbolTable);
            if (scanSymbol == null) {
                if (this.lexer.token() == 13) {
                    this.lexer.nextToken(16);
                    return;
                } else if (this.lexer.token() == 16 && this.lexer.isEnabled(Feature.AllowArbitraryCommas)) {
                }
            }
            FieldDeserializer fieldDeserializer = javaBeanDeserializer != null ? javaBeanDeserializer.getFieldDeserializer(scanSymbol) : null;
            if (fieldDeserializer == null) {
                if (this.lexer.isEnabled(Feature.IgnoreNotMatch)) {
                    this.lexer.nextTokenWithColon();
                    parse();
                    if (this.lexer.token() == 13) {
                        this.lexer.nextToken();
                        return;
                    }
                } else {
                    StringBuilder e2 = a.e("setter not found, class ");
                    e2.append(cls.getName());
                    e2.append(", property ");
                    e2.append(scanSymbol);
                    throw new JSONException(e2.toString());
                }
            } else {
                FieldInfo fieldInfo = fieldDeserializer.fieldInfo;
                Class<?> cls2 = fieldInfo.fieldClass;
                Type type = fieldInfo.fieldType;
                if (cls2 == Integer.TYPE) {
                    this.lexer.nextTokenWithColon(2);
                    deserialze = IntegerCodec.instance.deserialze(this, type, null);
                } else if (cls2 == String.class) {
                    this.lexer.nextTokenWithColon(4);
                    deserialze = StringCodec.deserialze(this);
                } else if (cls2 == Long.TYPE) {
                    this.lexer.nextTokenWithColon(2);
                    deserialze = LongCodec.instance.deserialze(this, type, null);
                } else {
                    ObjectDeserializer deserializer2 = this.config.getDeserializer(cls2, type);
                    this.lexer.nextTokenWithColon(deserializer2.getFastMatchToken());
                    deserialze = deserializer2.deserialze(this, type, null);
                }
                fieldDeserializer.setValue(obj, deserialze);
                if (this.lexer.token() != 16 && this.lexer.token() == 13) {
                    this.lexer.nextToken(16);
                    return;
                }
            }
        }
    }

    public Object parseObject(Map map) {
        return parseObject(map, (Object) null);
    }

    public JSONObject parseObject() {
        Object parseObject = parseObject((Map) new JSONObject(this.lexer.isEnabled(Feature.OrderedField)));
        if (parseObject instanceof JSONObject) {
            return (JSONObject) parseObject;
        }
        if (parseObject == null) {
            return null;
        }
        return new JSONObject((Map) parseObject);
    }
}
