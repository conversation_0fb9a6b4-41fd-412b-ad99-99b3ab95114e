package com.yfve.overalldatatracking;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface IDataTracking extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IDataTracking {
        @Override // com.yfve.overalldatatracking.IDataTracking
        public void accountLogOutTime(String str, String str2, String str3, String str4, String str5, String str6) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void accountLoginTime(String str, String str2, String str3, String str4, String str5, String str6) throws RemoteException {
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void carlifeConnect(String str, String str2, String str3, String str4, String str5, String str6) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void changeMediaSource(String str, String str2, String str3, String str4, String str5, String str6) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void enterFragment(String str, String str2, String str3) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void enterPage(String str, String str2) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void exitFragment(String str, String str2, String str3) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void exitPage(String str, String str2) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void getSWInfo() throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void globalSearchBack(String str, String str2, String str3, String str4, boolean z) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void globalSearchInfo(String str, String str2, String str3, String str4, String str5, String str6, int i) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void globalSearchResultInfo(String str, String str2, String str3, String str4, String str5) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void hmiEvent(String str, String str2, String str3, String str4, String str5, String str6, String str7, String str8, String str9, String str10) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void jumpApp(String str, String str2, String str3, String str4, String str5) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void jumpFragment(String str, String str2, String str3, String str4) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void jumpPage(String str, String str2, String str3, String str4) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void musicInfo(String str, String str2, String str3, String str4, String str5, String str6, String str7) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void nextSong(String str, String str2, String str3, String str4, String str5, String str6, String str7, String str8, String str9, String str10, String str11, String str12) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void nextVideo(String str, String str2, String str3, String str4, String str5, String str6, String str7, String str8, String str9, String str10, String str11, String str12) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void onceWifiTime(String str, String str2, String str3, String str4, String str5) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void openWifiAppCount(String str, String str2, String str3, String str4, String str5) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void radioInfo(String str, String str2, String str3, String str4, String str5, String str6, String str7, String str8, String str9, String str10, String str11) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void sendAndroidVersion(String str) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void sendHardkeyEvent(String str) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void sendIdpUserId(String str) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void sendVinCode(String str) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void setClimeTemp(String str, String str2, String str3, String str4, String str5) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void shutdownReason(String str, String str2) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void systemBootTime(String str) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void systemError(String str, String str2, String str3) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void systemShutdownTime(String str) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void trackAccount(CommDataTrack commDataTrack, String str, String str2, String str3, String str4) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void trackCarlife(CommDataTrack commDataTrack, String str, String str2, String str3) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void trackCommonEvent(String str, CommDataTrack commDataTrack, String str2) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void trackHmiEvent(HmiDataTrack hmiDataTrack) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void trackHvacTemperature(CommDataTrack commDataTrack, String str, String str2) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void trackPlayStatus(CommDataTrack commDataTrack, String str, String str2) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void trackWifiHotSpot(CommDataTrack commDataTrack, String str, String str2) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void videoInfo(String str, String str2, String str3, String str4, String str5, String str6, String str7) throws RemoteException {
        }

        @Override // com.yfve.overalldatatracking.IDataTracking
        public void volumeChange(String str, int i, int i2) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IDataTracking {
        public static final String DESCRIPTOR = "com.yfve.overalldatatracking.IDataTracking";
        public static final int TRANSACTION_accountLogOutTime = 30;
        public static final int TRANSACTION_accountLoginTime = 29;
        public static final int TRANSACTION_carlifeConnect = 28;
        public static final int TRANSACTION_changeMediaSource = 23;
        public static final int TRANSACTION_enterFragment = 16;
        public static final int TRANSACTION_enterPage = 13;
        public static final int TRANSACTION_exitFragment = 17;
        public static final int TRANSACTION_exitPage = 14;
        public static final int TRANSACTION_getSWInfo = 8;
        public static final int TRANSACTION_globalSearchBack = 11;
        public static final int TRANSACTION_globalSearchInfo = 9;
        public static final int TRANSACTION_globalSearchResultInfo = 10;
        public static final int TRANSACTION_hmiEvent = 26;
        public static final int TRANSACTION_jumpApp = 19;
        public static final int TRANSACTION_jumpFragment = 18;
        public static final int TRANSACTION_jumpPage = 15;
        public static final int TRANSACTION_musicInfo = 21;
        public static final int TRANSACTION_nextSong = 24;
        public static final int TRANSACTION_nextVideo = 25;
        public static final int TRANSACTION_onceWifiTime = 33;
        public static final int TRANSACTION_openWifiAppCount = 32;
        public static final int TRANSACTION_radioInfo = 20;
        public static final int TRANSACTION_sendAndroidVersion = 7;
        public static final int TRANSACTION_sendHardkeyEvent = 12;
        public static final int TRANSACTION_sendIdpUserId = 31;
        public static final int TRANSACTION_sendVinCode = 6;
        public static final int TRANSACTION_setClimeTemp = 27;
        public static final int TRANSACTION_shutdownReason = 3;
        public static final int TRANSACTION_systemBootTime = 5;
        public static final int TRANSACTION_systemError = 2;
        public static final int TRANSACTION_systemShutdownTime = 4;
        public static final int TRANSACTION_trackAccount = 39;
        public static final int TRANSACTION_trackCarlife = 37;
        public static final int TRANSACTION_trackCommonEvent = 35;
        public static final int TRANSACTION_trackHmiEvent = 34;
        public static final int TRANSACTION_trackHvacTemperature = 36;
        public static final int TRANSACTION_trackPlayStatus = 40;
        public static final int TRANSACTION_trackWifiHotSpot = 38;
        public static final int TRANSACTION_videoInfo = 22;
        public static final int TRANSACTION_volumeChange = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements IDataTracking {
            public static IDataTracking sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void accountLogOutTime(String str, String str2, String str3, String str4, String str5, String str6) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    obtain.writeString(str4);
                    obtain.writeString(str5);
                    obtain.writeString(str6);
                } catch (Throwable th) {
                    th = th;
                }
                try {
                    if (!this.mRemote.transact(30, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().accountLogOutTime(str, str2, str3, str4, str5, str6);
                        obtain2.recycle();
                        obtain.recycle();
                        return;
                    }
                    obtain2.readException();
                    obtain2.recycle();
                    obtain.recycle();
                } catch (Throwable th2) {
                    th = th2;
                    obtain2.recycle();
                    obtain.recycle();
                    throw th;
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void accountLoginTime(String str, String str2, String str3, String str4, String str5, String str6) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    obtain.writeString(str4);
                    obtain.writeString(str5);
                    obtain.writeString(str6);
                } catch (Throwable th) {
                    th = th;
                }
                try {
                    if (!this.mRemote.transact(29, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().accountLoginTime(str, str2, str3, str4, str5, str6);
                        obtain2.recycle();
                        obtain.recycle();
                        return;
                    }
                    obtain2.readException();
                    obtain2.recycle();
                    obtain.recycle();
                } catch (Throwable th2) {
                    th = th2;
                    obtain2.recycle();
                    obtain.recycle();
                    throw th;
                }
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void carlifeConnect(String str, String str2, String str3, String str4, String str5, String str6) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    obtain.writeString(str4);
                    obtain.writeString(str5);
                    obtain.writeString(str6);
                } catch (Throwable th) {
                    th = th;
                }
                try {
                    if (!this.mRemote.transact(28, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().carlifeConnect(str, str2, str3, str4, str5, str6);
                        obtain2.recycle();
                        obtain.recycle();
                        return;
                    }
                    obtain2.readException();
                    obtain2.recycle();
                    obtain.recycle();
                } catch (Throwable th2) {
                    th = th2;
                    obtain2.recycle();
                    obtain.recycle();
                    throw th;
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void changeMediaSource(String str, String str2, String str3, String str4, String str5, String str6) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    obtain.writeString(str4);
                    obtain.writeString(str5);
                    obtain.writeString(str6);
                    if (this.mRemote.transact(23, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        return;
                    }
                    Stub.getDefaultImpl().changeMediaSource(str, str2, str3, str4, str5, str6);
                } finally {
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void enterFragment(String str, String str2, String str3) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    if (!this.mRemote.transact(16, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().enterFragment(str, str2, str3);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void enterPage(String str, String str2) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    if (!this.mRemote.transact(13, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().enterPage(str, str2);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void exitFragment(String str, String str2, String str3) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    if (!this.mRemote.transact(17, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().exitFragment(str, str2, str3);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void exitPage(String str, String str2) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    if (!this.mRemote.transact(14, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().exitPage(str, str2);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void getSWInfo() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(8, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().getSWInfo();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void globalSearchBack(String str, String str2, String str3, String str4, boolean z) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    obtain.writeString(str4);
                    obtain.writeInt(z ? 1 : 0);
                    if (this.mRemote.transact(11, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        return;
                    }
                    Stub.getDefaultImpl().globalSearchBack(str, str2, str3, str4, z);
                } finally {
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void globalSearchInfo(String str, String str2, String str3, String str4, String str5, String str6, int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    obtain.writeString(str4);
                    obtain.writeString(str5);
                    obtain.writeString(str6);
                    obtain.writeInt(i);
                } catch (Throwable th) {
                    th = th;
                }
                try {
                    if (this.mRemote.transact(9, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        obtain.recycle();
                        return;
                    }
                    Stub.getDefaultImpl().globalSearchInfo(str, str2, str3, str4, str5, str6, i);
                    obtain.recycle();
                } catch (Throwable th2) {
                    th = th2;
                    obtain.recycle();
                    throw th;
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void globalSearchResultInfo(String str, String str2, String str3, String str4, String str5) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    obtain.writeString(str4);
                    obtain.writeString(str5);
                    if (this.mRemote.transact(10, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        return;
                    }
                    Stub.getDefaultImpl().globalSearchResultInfo(str, str2, str3, str4, str5);
                } finally {
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void hmiEvent(String str, String str2, String str3, String str4, String str5, String str6, String str7, String str8, String str9, String str10) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    obtain.writeString(str4);
                    obtain.writeString(str5);
                    obtain.writeString(str6);
                    obtain.writeString(str7);
                    obtain.writeString(str8);
                    obtain.writeString(str9);
                    obtain.writeString(str10);
                } catch (Throwable th) {
                    th = th;
                }
                try {
                    if (this.mRemote.transact(26, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        obtain.recycle();
                        return;
                    }
                    Stub.getDefaultImpl().hmiEvent(str, str2, str3, str4, str5, str6, str7, str8, str9, str10);
                    obtain.recycle();
                } catch (Throwable th2) {
                    th = th2;
                    obtain.recycle();
                    throw th;
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void jumpApp(String str, String str2, String str3, String str4, String str5) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    obtain.writeString(str4);
                    obtain.writeString(str5);
                    if (!this.mRemote.transact(19, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().jumpApp(str, str2, str3, str4, str5);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void jumpFragment(String str, String str2, String str3, String str4) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    obtain.writeString(str4);
                    if (!this.mRemote.transact(18, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().jumpFragment(str, str2, str3, str4);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void jumpPage(String str, String str2, String str3, String str4) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    obtain.writeString(str4);
                    if (!this.mRemote.transact(15, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().jumpPage(str, str2, str3, str4);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void musicInfo(String str, String str2, String str3, String str4, String str5, String str6, String str7) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    obtain.writeString(str4);
                    obtain.writeString(str5);
                    obtain.writeString(str6);
                    obtain.writeString(str7);
                } catch (Throwable th) {
                    th = th;
                }
                try {
                    if (this.mRemote.transact(21, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        obtain.recycle();
                        return;
                    }
                    Stub.getDefaultImpl().musicInfo(str, str2, str3, str4, str5, str6, str7);
                    obtain.recycle();
                } catch (Throwable th2) {
                    th = th2;
                    obtain.recycle();
                    throw th;
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void nextSong(String str, String str2, String str3, String str4, String str5, String str6, String str7, String str8, String str9, String str10, String str11, String str12) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    obtain.writeString(str4);
                    obtain.writeString(str5);
                    obtain.writeString(str6);
                    obtain.writeString(str7);
                    obtain.writeString(str8);
                    obtain.writeString(str9);
                    obtain.writeString(str10);
                    obtain.writeString(str11);
                    obtain.writeString(str12);
                } catch (Throwable th) {
                    th = th;
                }
                try {
                    if (this.mRemote.transact(24, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        obtain.recycle();
                        return;
                    }
                    Stub.getDefaultImpl().nextSong(str, str2, str3, str4, str5, str6, str7, str8, str9, str10, str11, str12);
                    obtain.recycle();
                } catch (Throwable th2) {
                    th = th2;
                    obtain.recycle();
                    throw th;
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void nextVideo(String str, String str2, String str3, String str4, String str5, String str6, String str7, String str8, String str9, String str10, String str11, String str12) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    obtain.writeString(str4);
                    obtain.writeString(str5);
                    obtain.writeString(str6);
                    obtain.writeString(str7);
                    obtain.writeString(str8);
                    obtain.writeString(str9);
                    obtain.writeString(str10);
                    obtain.writeString(str11);
                    obtain.writeString(str12);
                } catch (Throwable th) {
                    th = th;
                }
                try {
                    if (this.mRemote.transact(25, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        obtain.recycle();
                        return;
                    }
                    Stub.getDefaultImpl().nextVideo(str, str2, str3, str4, str5, str6, str7, str8, str9, str10, str11, str12);
                    obtain.recycle();
                } catch (Throwable th2) {
                    th = th2;
                    obtain.recycle();
                    throw th;
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void onceWifiTime(String str, String str2, String str3, String str4, String str5) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    obtain.writeString(str4);
                    obtain.writeString(str5);
                    if (!this.mRemote.transact(33, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onceWifiTime(str, str2, str3, str4, str5);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void openWifiAppCount(String str, String str2, String str3, String str4, String str5) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    obtain.writeString(str4);
                    obtain.writeString(str5);
                    if (!this.mRemote.transact(32, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().openWifiAppCount(str, str2, str3, str4, str5);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void radioInfo(String str, String str2, String str3, String str4, String str5, String str6, String str7, String str8, String str9, String str10, String str11) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    obtain.writeString(str4);
                    obtain.writeString(str5);
                    obtain.writeString(str6);
                    obtain.writeString(str7);
                    obtain.writeString(str8);
                    obtain.writeString(str9);
                    obtain.writeString(str10);
                    obtain.writeString(str11);
                } catch (Throwable th) {
                    th = th;
                }
                try {
                    if (this.mRemote.transact(20, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        obtain.recycle();
                        return;
                    }
                    Stub.getDefaultImpl().radioInfo(str, str2, str3, str4, str5, str6, str7, str8, str9, str10, str11);
                    obtain.recycle();
                } catch (Throwable th2) {
                    th = th2;
                    obtain.recycle();
                    throw th;
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void sendAndroidVersion(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    if (!this.mRemote.transact(7, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().sendAndroidVersion(str);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void sendHardkeyEvent(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    if (!this.mRemote.transact(12, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().sendHardkeyEvent(str);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void sendIdpUserId(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    if (!this.mRemote.transact(31, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().sendIdpUserId(str);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void sendVinCode(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    if (!this.mRemote.transact(6, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().sendVinCode(str);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void setClimeTemp(String str, String str2, String str3, String str4, String str5) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    obtain.writeString(str4);
                    obtain.writeString(str5);
                    if (!this.mRemote.transact(27, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().setClimeTemp(str, str2, str3, str4, str5);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void shutdownReason(String str, String str2) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    if (!this.mRemote.transact(3, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().shutdownReason(str, str2);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void systemBootTime(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    if (!this.mRemote.transact(5, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().systemBootTime(str);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void systemError(String str, String str2, String str3) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().systemError(str, str2, str3);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void systemShutdownTime(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    if (!this.mRemote.transact(4, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().systemShutdownTime(str);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void trackAccount(CommDataTrack commDataTrack, String str, String str2, String str3, String str4) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (commDataTrack != null) {
                        obtain.writeInt(1);
                        commDataTrack.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    obtain.writeString(str4);
                    if (this.mRemote.transact(39, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        return;
                    }
                    Stub.getDefaultImpl().trackAccount(commDataTrack, str, str2, str3, str4);
                } finally {
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void trackCarlife(CommDataTrack commDataTrack, String str, String str2, String str3) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (commDataTrack != null) {
                        obtain.writeInt(1);
                        commDataTrack.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    if (this.mRemote.transact(37, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        return;
                    }
                    Stub.getDefaultImpl().trackCarlife(commDataTrack, str, str2, str3);
                } finally {
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void trackCommonEvent(String str, CommDataTrack commDataTrack, String str2) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    if (commDataTrack != null) {
                        obtain.writeInt(1);
                        commDataTrack.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    obtain.writeString(str2);
                    if (this.mRemote.transact(35, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        return;
                    }
                    Stub.getDefaultImpl().trackCommonEvent(str, commDataTrack, str2);
                } finally {
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void trackHmiEvent(HmiDataTrack hmiDataTrack) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (hmiDataTrack != null) {
                        obtain.writeInt(1);
                        hmiDataTrack.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    if (this.mRemote.transact(34, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        return;
                    }
                    Stub.getDefaultImpl().trackHmiEvent(hmiDataTrack);
                } finally {
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void trackHvacTemperature(CommDataTrack commDataTrack, String str, String str2) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (commDataTrack != null) {
                        obtain.writeInt(1);
                        commDataTrack.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    if (this.mRemote.transact(36, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        return;
                    }
                    Stub.getDefaultImpl().trackHvacTemperature(commDataTrack, str, str2);
                } finally {
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void trackPlayStatus(CommDataTrack commDataTrack, String str, String str2) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (commDataTrack != null) {
                        obtain.writeInt(1);
                        commDataTrack.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    if (this.mRemote.transact(40, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        return;
                    }
                    Stub.getDefaultImpl().trackPlayStatus(commDataTrack, str, str2);
                } finally {
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void trackWifiHotSpot(CommDataTrack commDataTrack, String str, String str2) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (commDataTrack != null) {
                        obtain.writeInt(1);
                        commDataTrack.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    if (this.mRemote.transact(38, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        return;
                    }
                    Stub.getDefaultImpl().trackWifiHotSpot(commDataTrack, str, str2);
                } finally {
                    obtain.recycle();
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void videoInfo(String str, String str2, String str3, String str4, String str5, String str6, String str7) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    obtain.writeString(str4);
                    obtain.writeString(str5);
                    obtain.writeString(str6);
                    obtain.writeString(str7);
                } catch (Throwable th) {
                    th = th;
                }
                try {
                    if (this.mRemote.transact(22, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        obtain.recycle();
                        return;
                    }
                    Stub.getDefaultImpl().videoInfo(str, str2, str3, str4, str5, str6, str7);
                    obtain.recycle();
                } catch (Throwable th2) {
                    th = th2;
                    obtain.recycle();
                    throw th;
                }
            }

            @Override // com.yfve.overalldatatracking.IDataTracking
            public void volumeChange(String str, int i, int i2) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeInt(i);
                    obtain.writeInt(i2);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().volumeChange(str, i, i2);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static IDataTracking asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof IDataTracking)) {
                return (IDataTracking) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IDataTracking getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IDataTracking iDataTracking) {
            if (Proxy.sDefaultImpl != null || iDataTracking == null) {
                return false;
            }
            Proxy.sDefaultImpl = iDataTracking;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1598968902) {
                switch (i) {
                    case 1:
                        parcel.enforceInterface(DESCRIPTOR);
                        volumeChange(parcel.readString(), parcel.readInt(), parcel.readInt());
                        parcel2.writeNoException();
                        return true;
                    case 2:
                        parcel.enforceInterface(DESCRIPTOR);
                        systemError(parcel.readString(), parcel.readString(), parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 3:
                        parcel.enforceInterface(DESCRIPTOR);
                        shutdownReason(parcel.readString(), parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 4:
                        parcel.enforceInterface(DESCRIPTOR);
                        systemShutdownTime(parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 5:
                        parcel.enforceInterface(DESCRIPTOR);
                        systemBootTime(parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 6:
                        parcel.enforceInterface(DESCRIPTOR);
                        sendVinCode(parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 7:
                        parcel.enforceInterface(DESCRIPTOR);
                        sendAndroidVersion(parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 8:
                        parcel.enforceInterface(DESCRIPTOR);
                        getSWInfo();
                        parcel2.writeNoException();
                        return true;
                    case 9:
                        parcel.enforceInterface(DESCRIPTOR);
                        globalSearchInfo(parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readInt());
                        return true;
                    case 10:
                        parcel.enforceInterface(DESCRIPTOR);
                        globalSearchResultInfo(parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString());
                        return true;
                    case 11:
                        parcel.enforceInterface(DESCRIPTOR);
                        globalSearchBack(parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readInt() != 0);
                        return true;
                    case 12:
                        parcel.enforceInterface(DESCRIPTOR);
                        sendHardkeyEvent(parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 13:
                        parcel.enforceInterface(DESCRIPTOR);
                        enterPage(parcel.readString(), parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 14:
                        parcel.enforceInterface(DESCRIPTOR);
                        exitPage(parcel.readString(), parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 15:
                        parcel.enforceInterface(DESCRIPTOR);
                        jumpPage(parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 16:
                        parcel.enforceInterface(DESCRIPTOR);
                        enterFragment(parcel.readString(), parcel.readString(), parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 17:
                        parcel.enforceInterface(DESCRIPTOR);
                        exitFragment(parcel.readString(), parcel.readString(), parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 18:
                        parcel.enforceInterface(DESCRIPTOR);
                        jumpFragment(parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 19:
                        parcel.enforceInterface(DESCRIPTOR);
                        jumpApp(parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 20:
                        parcel.enforceInterface(DESCRIPTOR);
                        radioInfo(parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString());
                        return true;
                    case 21:
                        parcel.enforceInterface(DESCRIPTOR);
                        musicInfo(parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString());
                        return true;
                    case 22:
                        parcel.enforceInterface(DESCRIPTOR);
                        videoInfo(parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString());
                        return true;
                    case 23:
                        parcel.enforceInterface(DESCRIPTOR);
                        changeMediaSource(parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString());
                        return true;
                    case 24:
                        parcel.enforceInterface(DESCRIPTOR);
                        nextSong(parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString());
                        return true;
                    case 25:
                        parcel.enforceInterface(DESCRIPTOR);
                        nextVideo(parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString());
                        return true;
                    case 26:
                        parcel.enforceInterface(DESCRIPTOR);
                        hmiEvent(parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString());
                        return true;
                    case 27:
                        parcel.enforceInterface(DESCRIPTOR);
                        setClimeTemp(parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 28:
                        parcel.enforceInterface(DESCRIPTOR);
                        carlifeConnect(parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 29:
                        parcel.enforceInterface(DESCRIPTOR);
                        accountLoginTime(parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 30:
                        parcel.enforceInterface(DESCRIPTOR);
                        accountLogOutTime(parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 31:
                        parcel.enforceInterface(DESCRIPTOR);
                        sendIdpUserId(parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 32:
                        parcel.enforceInterface(DESCRIPTOR);
                        openWifiAppCount(parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 33:
                        parcel.enforceInterface(DESCRIPTOR);
                        onceWifiTime(parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 34:
                        parcel.enforceInterface(DESCRIPTOR);
                        trackHmiEvent(parcel.readInt() != 0 ? HmiDataTrack.CREATOR.createFromParcel(parcel) : null);
                        return true;
                    case 35:
                        parcel.enforceInterface(DESCRIPTOR);
                        trackCommonEvent(parcel.readString(), parcel.readInt() != 0 ? CommDataTrack.CREATOR.createFromParcel(parcel) : null, parcel.readString());
                        return true;
                    case 36:
                        parcel.enforceInterface(DESCRIPTOR);
                        trackHvacTemperature(parcel.readInt() != 0 ? CommDataTrack.CREATOR.createFromParcel(parcel) : null, parcel.readString(), parcel.readString());
                        return true;
                    case 37:
                        parcel.enforceInterface(DESCRIPTOR);
                        trackCarlife(parcel.readInt() != 0 ? CommDataTrack.CREATOR.createFromParcel(parcel) : null, parcel.readString(), parcel.readString(), parcel.readString());
                        return true;
                    case 38:
                        parcel.enforceInterface(DESCRIPTOR);
                        trackWifiHotSpot(parcel.readInt() != 0 ? CommDataTrack.CREATOR.createFromParcel(parcel) : null, parcel.readString(), parcel.readString());
                        return true;
                    case 39:
                        parcel.enforceInterface(DESCRIPTOR);
                        trackAccount(parcel.readInt() != 0 ? CommDataTrack.CREATOR.createFromParcel(parcel) : null, parcel.readString(), parcel.readString(), parcel.readString(), parcel.readString());
                        return true;
                    case 40:
                        parcel.enforceInterface(DESCRIPTOR);
                        trackPlayStatus(parcel.readInt() != 0 ? CommDataTrack.CREATOR.createFromParcel(parcel) : null, parcel.readString(), parcel.readString());
                        return true;
                    default:
                        return super.onTransact(i, parcel, parcel2, i2);
                }
            }
            parcel2.writeString(DESCRIPTOR);
            return true;
        }
    }

    void accountLogOutTime(String str, String str2, String str3, String str4, String str5, String str6) throws RemoteException;

    void accountLoginTime(String str, String str2, String str3, String str4, String str5, String str6) throws RemoteException;

    void carlifeConnect(String str, String str2, String str3, String str4, String str5, String str6) throws RemoteException;

    void changeMediaSource(String str, String str2, String str3, String str4, String str5, String str6) throws RemoteException;

    void enterFragment(String str, String str2, String str3) throws RemoteException;

    void enterPage(String str, String str2) throws RemoteException;

    void exitFragment(String str, String str2, String str3) throws RemoteException;

    void exitPage(String str, String str2) throws RemoteException;

    void getSWInfo() throws RemoteException;

    void globalSearchBack(String str, String str2, String str3, String str4, boolean z) throws RemoteException;

    void globalSearchInfo(String str, String str2, String str3, String str4, String str5, String str6, int i) throws RemoteException;

    void globalSearchResultInfo(String str, String str2, String str3, String str4, String str5) throws RemoteException;

    void hmiEvent(String str, String str2, String str3, String str4, String str5, String str6, String str7, String str8, String str9, String str10) throws RemoteException;

    void jumpApp(String str, String str2, String str3, String str4, String str5) throws RemoteException;

    void jumpFragment(String str, String str2, String str3, String str4) throws RemoteException;

    void jumpPage(String str, String str2, String str3, String str4) throws RemoteException;

    void musicInfo(String str, String str2, String str3, String str4, String str5, String str6, String str7) throws RemoteException;

    void nextSong(String str, String str2, String str3, String str4, String str5, String str6, String str7, String str8, String str9, String str10, String str11, String str12) throws RemoteException;

    void nextVideo(String str, String str2, String str3, String str4, String str5, String str6, String str7, String str8, String str9, String str10, String str11, String str12) throws RemoteException;

    void onceWifiTime(String str, String str2, String str3, String str4, String str5) throws RemoteException;

    void openWifiAppCount(String str, String str2, String str3, String str4, String str5) throws RemoteException;

    void radioInfo(String str, String str2, String str3, String str4, String str5, String str6, String str7, String str8, String str9, String str10, String str11) throws RemoteException;

    void sendAndroidVersion(String str) throws RemoteException;

    void sendHardkeyEvent(String str) throws RemoteException;

    void sendIdpUserId(String str) throws RemoteException;

    void sendVinCode(String str) throws RemoteException;

    void setClimeTemp(String str, String str2, String str3, String str4, String str5) throws RemoteException;

    void shutdownReason(String str, String str2) throws RemoteException;

    void systemBootTime(String str) throws RemoteException;

    void systemError(String str, String str2, String str3) throws RemoteException;

    void systemShutdownTime(String str) throws RemoteException;

    void trackAccount(CommDataTrack commDataTrack, String str, String str2, String str3, String str4) throws RemoteException;

    void trackCarlife(CommDataTrack commDataTrack, String str, String str2, String str3) throws RemoteException;

    void trackCommonEvent(String str, CommDataTrack commDataTrack, String str2) throws RemoteException;

    void trackHmiEvent(HmiDataTrack hmiDataTrack) throws RemoteException;

    void trackHvacTemperature(CommDataTrack commDataTrack, String str, String str2) throws RemoteException;

    void trackPlayStatus(CommDataTrack commDataTrack, String str, String str2) throws RemoteException;

    void trackWifiHotSpot(CommDataTrack commDataTrack, String str, String str2) throws RemoteException;

    void videoInfo(String str, String str2, String str3, String str4, String str5, String str6, String str7) throws RemoteException;

    void volumeChange(String str, int i, int i2) throws RemoteException;
}
