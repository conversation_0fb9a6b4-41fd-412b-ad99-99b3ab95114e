package com.yfve.ici.appcustomviewlib.blur;

import android.graphics.Bitmap;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import androidx.fragment.app.FragmentActivity;
import com.yfve.ici.appcustomviewlib.R;
import com.yfve.ici.appcustomviewlib.blur.util.UtilBitmap;
import com.yfve.ici.appcustomviewlib.blur.util.UtilBox;

/* loaded from: classes.dex */
public class BlurUtil {
    public FragmentActivity mActivity;
    public UtilBox utils = UtilBox.getBox();

    public BlurUtil(FragmentActivity fragmentActivity) {
        this.mActivity = fragmentActivity;
    }

    public void clickBlurImg(ImageView imageView, Bitmap bitmap) {
        this.utils.bitmap.blurImageView(this.mActivity, imageView, bitmap, 20.0f);
    }

    public void clickClosePopupWindow(RelativeLayout relativeLayout, ImageView imageView) {
        this.utils.anim.hidePopupWindow(relativeLayout, imageView);
    }

    public void clickPopupWindow(RelativeLayout relativeLayout, ImageView imageView) {
        Bitmap drawing = this.utils.ui.getDrawing(this.mActivity);
        if (this.utils.info.getPhoneSDK() >= 19 && drawing != null) {
            imageView.setImageBitmap(drawing);
            UtilBitmap utilBitmap = this.utils.bitmap;
            FragmentActivity fragmentActivity = this.mActivity;
            utilBitmap.blurImageView(fragmentActivity, imageView, 25.0f, fragmentActivity.getResources().getColor(R.color.colorWhite_t8));
        } else {
            imageView.setBackgroundColor(this.mActivity.getResources().getColor(R.color.colorWhite_tD));
        }
        this.utils.anim.showPopupWindow(relativeLayout, imageView);
    }
}
