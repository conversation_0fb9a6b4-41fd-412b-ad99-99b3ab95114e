package com.yfve.ici.appcustomviewlib.blur.util;

import android.annotation.TargetApi;
import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import androidx.core.text.BidiFormatter;

/* loaded from: classes.dex */
public class UtilUI {
    public int barHeight = -1;

    public static void setRootViewFitsSystemWindows(Activity activity, boolean z) {
        ViewGroup viewGroup;
        ViewGroup viewGroup2 = (ViewGroup) activity.findViewById(16908290);
        if (viewGroup2.getChildCount() <= 0 || (viewGroup = (ViewGroup) viewGroup2.getChildAt(0)) == null) {
            return;
        }
        viewGroup.setFitsSystemWindows(z);
    }

    @TargetApi(19)
    public static void setTranslucentStatus(Activity activity, boolean z) {
        Window window = activity.getWindow();
        window.getDecorView().setSystemUiVisibility(z ? BidiFormatter.DirectionalityEstimator.DIR_TYPE_CACHE_SIZE : 1280);
        window.addFlags(Integer.MIN_VALUE);
        window.setStatusBarColor(0);
        if (z) {
            window.setNavigationBarColor(0);
        }
    }

    public int getBarHeight(Context context) {
        if (this.barHeight == -1) {
            try {
                Class<?> cls = Class.forName("com.android.internal.R$dimen");
                this.barHeight = context.getResources().getDimensionPixelSize(Integer.parseInt(cls.getField("status_bar_height").get(cls.newInstance()).toString()));
            } catch (Exception e) {
                e.printStackTrace();
                return 0;
            }
        }
        return this.barHeight;
    }

    public Bitmap getDrawing(Activity activity) {
        return getDrawing(((ViewGroup) activity.findViewById(16908290)).getChildAt(0));
    }

    public Bitmap getScreenshot(Activity activity, boolean z) {
        try {
            View decorView = activity.getWindow().getDecorView();
            decorView.setDrawingCacheEnabled(true);
            decorView.buildDrawingCache(true);
            Bitmap drawingCache = decorView.getDrawingCache();
            int barHeight = z ? 0 : getBarHeight(activity);
            return Bitmap.createBitmap(drawingCache, 0, barHeight, drawingCache.getWidth(), drawingCache.getHeight() - barHeight);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public Bitmap getDrawing(View view) {
        try {
            view.setDrawingCacheEnabled(true);
            Bitmap createBitmap = Bitmap.createBitmap(view.getDrawingCache());
            view.setDrawingCacheEnabled(false);
            return createBitmap;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
