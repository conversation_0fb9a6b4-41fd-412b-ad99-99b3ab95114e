package com.yfve.ici.appcustomviewlib.blur.util;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.annotation.TargetApi;
import android.view.View;
import android.view.ViewAnimationUtils;
import android.widget.LinearLayout;
import com.yfve.ici.appcustomviewlib.R;

/* loaded from: classes.dex */
public class UtilAnim {

    /* renamed from: com.yfve.ici.appcustomviewlib.blur.util.UtilAnim$2  reason: invalid class name */
    /* loaded from: classes.dex */
    public class AnonymousClass2 implements ValueAnimator.AnimatorUpdateListener {
        public final /* synthetic */ View val$view;

        public AnonymousClass2(View view) {
            this.val$view = view;
        }

        @Override // android.animation.ValueAnimator.AnimatorUpdateListener
        public void onAnimationUpdate(ValueAnimator valueAnimator) {
            float floatValue = ((Float) valueAnimator.getAnimatedValue()).floatValue();
            View view = this.val$view;
            if (view != null) {
                view.setAlpha(floatValue);
                this.val$view.setTranslationY((1.0f - floatValue) * 100.0f);
            }
        }
    }

    /* renamed from: com.yfve.ici.appcustomviewlib.blur.util.UtilAnim$4  reason: invalid class name */
    /* loaded from: classes.dex */
    public class AnonymousClass4 implements ValueAnimator.AnimatorUpdateListener {
        public final /* synthetic */ View val$view;
        public final /* synthetic */ View val$view_back;

        public AnonymousClass4(View view, View view2) {
            this.val$view = view;
            this.val$view_back = view2;
        }

        @Override // android.animation.ValueAnimator.AnimatorUpdateListener
        public void onAnimationUpdate(ValueAnimator valueAnimator) {
            float floatValue = ((Float) valueAnimator.getAnimatedValue()).floatValue();
            View view = this.val$view;
            if (view != null) {
                view.setAlpha(floatValue);
                this.val$view.setTranslationY((1.0f - floatValue) * 100.0f);
            }
            if (floatValue < 0.06d) {
                View view2 = this.val$view_back;
                if (view2 != null) {
                    view2.setVisibility(8);
                }
                View view3 = this.val$view;
                if (view3 != null) {
                    view3.setVisibility(8);
                }
            }
        }
    }

    public void hideIntroduce(View view) {
        hideIntroduce(view, 350);
    }

    @TargetApi(21)
    public void hidePopupWindow(final View view, final View view2, int i) {
        if (view2 == null || view2.getAlpha() != 1.0f) {
            return;
        }
        long j = i;
        ObjectAnimator duration = ObjectAnimator.ofFloat(view2, "Alpha", 1.0f, 0.0f).setDuration(j);
        Animator createCircularReveal = ViewAnimationUtils.createCircularReveal(view, view.getWidth() / 2, (int) (view.getHeight() * 0.85d), (float) Math.hypot(view.getWidth(), view.getHeight()), 0.0f);
        createCircularReveal.setDuration(j);
        createCircularReveal.addListener(new Animator.AnimatorListener() { // from class: com.yfve.ici.appcustomviewlib.blur.util.UtilAnim.3
            @Override // android.animation.Animator.AnimatorListener
            public void onAnimationCancel(Animator animator) {
            }

            @Override // android.animation.Animator.AnimatorListener
            public void onAnimationEnd(Animator animator) {
                view.setVisibility(8);
                view2.setVisibility(8);
            }

            @Override // android.animation.Animator.AnimatorListener
            public void onAnimationRepeat(Animator animator) {
            }

            @Override // android.animation.Animator.AnimatorListener
            public void onAnimationStart(Animator animator) {
            }
        });
        duration.start();
        createCircularReveal.start();
    }

    public void showIntroduce(View view) {
        showIntroduce(view, 350);
    }

    @TargetApi(21)
    public void showPopupWindow(final View view, View view2, final int i) {
        if (view2 == null) {
            return;
        }
        ObjectAnimator duration = ObjectAnimator.ofFloat(view2, "Alpha", 0.0f, 1.0f).setDuration(i);
        view2.setAlpha(0.0f);
        view2.setVisibility(0);
        view.setAlpha(1.0f);
        view.setVisibility(4);
        view.post(new Runnable() { // from class: com.yfve.ici.appcustomviewlib.blur.util.UtilAnim.1
            @Override // java.lang.Runnable
            public void run() {
                int i2 = i;
                view.setVisibility(0);
                View view3 = view;
                ViewAnimationUtils.createCircularReveal(view3, view3.getWidth() / 2, (int) (view.getHeight() * 0.85d), 0.0f, (float) Math.hypot(view.getWidth(), view.getHeight())).setDuration(i2).start();
            }
        });
        duration.start();
    }

    public void hideIntroduce(final View view, int i) {
        LinearLayout linearLayout;
        try {
            linearLayout = (LinearLayout) view.findViewById(R.id.ll_introduce_text);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (linearLayout != null && linearLayout.getAlpha() == 1.0f) {
            linearLayout.animate().alpha(0.0f).y(80.0f).setDuration(i);
            view.animate().alpha(0.0f).setDuration(i).setStartDelay((i * 2) / 3).withEndAction(new Runnable() { // from class: com.yfve.ici.appcustomviewlib.blur.util.UtilAnim.5
                @Override // java.lang.Runnable
                public void run() {
                    view.setVisibility(8);
                }
            });
        }
    }

    @SuppressLint({"WrongViewCast"})
    public void showIntroduce(View view, int i) {
        view.setAlpha(0.0f);
        view.setVisibility(0);
        long j = i;
        view.animate().alpha(1.0f).setDuration(j);
        try {
            LinearLayout linearLayout = (LinearLayout) view.findViewById(R.id.ll_introduce_text);
            linearLayout.setAlpha(0.0f);
            linearLayout.setY(80.0f);
            linearLayout.animate().alpha(1.0f).y(0.0f).setDuration(j).setStartDelay((i * 2) / 3);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void showPopupWindow(View view, View view2) {
        showPopupWindow(view, view2, 300);
    }

    public void hidePopupWindow(View view, View view2) {
        hidePopupWindow(view, view2, 300);
    }
}
