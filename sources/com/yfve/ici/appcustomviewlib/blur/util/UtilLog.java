package com.yfve.ici.appcustomviewlib.blur.util;

import android.os.Environment;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/* loaded from: classes.dex */
public class UtilLog {
    public final String TAG = "bamboyLog";
    public final String LOG_PATH = Environment.getExternalStorageDirectory() + "/bamboy/BamboyLog.txt";
    public final char TOP_LEFT_CORNER = 9556;
    public final char BOTTOM_LEFT_CORNER = 9562;
    public final char MIDDLE_CORNER = 9567;
    public final char HORIZONTAL_DOUBLE_LINE = 9553;
    public final String DOUBLE_DIVIDER = "════════════════════════════════════════════";
    public final String SINGLE_DIVIDER = "────────────────────────────────────────────";
    public final String TOP_BORDER = "╔════════════════════════════════════════════════════════════════════════════════════════";
    public final String BOTTOM_BORDER = "╚════════════════════════════════════════════════════════════════════════════════════════";
    public final String MIDDLE_BORDER = "╟────────────────────────────────────────────────────────────────────────────────────────";

    private String getLoginfo() {
        StackTraceElement stackTraceElement;
        StackTraceElement[] stackTrace = new Exception().getStackTrace();
        if (stackTrace == null) {
            return "包名获取失败 | 方法获取失败| 方法获取失败";
        }
        if (stackTrace.length >= 3) {
            stackTraceElement = stackTrace[2];
        } else {
            stackTraceElement = stackTrace[stackTrace.length - 1];
        }
        StringBuffer stringBuffer = new StringBuffer("包名：");
        stringBuffer.append(stackTraceElement.getClassName());
        stringBuffer.append(" | 行号：");
        stringBuffer.append(stackTraceElement.getLineNumber());
        stringBuffer.append(" | 方法：");
        stringBuffer.append(stackTraceElement.getMethodName());
        stringBuffer.append("()");
        return stringBuffer.toString();
    }

    public void e(String str) {
    }

    public void e(String str, String str2) {
    }

    public void e(String str, String str2, boolean z) {
    }

    public void e(String str, boolean z) {
    }

    public String getFileLineMethod() {
        StackTraceElement stackTraceElement = new Exception().getStackTrace()[1];
        StringBuffer stringBuffer = new StringBuffer("[");
        stringBuffer.append(stackTraceElement.getFileName());
        stringBuffer.append(" | ");
        stringBuffer.append(stackTraceElement.getLineNumber());
        stringBuffer.append(" | ");
        stringBuffer.append(stackTraceElement.getMethodName());
        stringBuffer.append("()");
        stringBuffer.append("]");
        return stringBuffer.toString();
    }

    public String getFileName() {
        return new Exception().getStackTrace()[1].getFileName();
    }

    public int getLineNumber() {
        return new Exception().getStackTrace()[1].getLineNumber();
    }

    public String getMethodName() {
        return new Exception().getStackTrace()[1].getMethodName();
    }

    public String getPackage() {
        return new Exception().getStackTrace()[1].getClassName();
    }

    public String getTime() {
        return new SimpleDateFormat("yyyy.MM.dd.HH:mm:ss", Locale.getDefault()).format(new Date(System.currentTimeMillis()));
    }

    public void i(String str) {
    }

    public void i(String str, String str2) {
    }

    public void i(String str, String str2, boolean z) {
    }

    public void i(String str, boolean z) {
    }

    public void saveToSeverFile(String str, String str2) {
    }

    public void w(String str) {
    }

    public void w(String str, String str2) {
    }

    public void w(String str, String str2, boolean z) {
    }

    public void w(String str, boolean z) {
    }
}
