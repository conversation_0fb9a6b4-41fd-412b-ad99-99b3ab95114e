package com.yfve.ici.appcustomviewlib.blur.util;

/* loaded from: classes.dex */
public class UtilBox {
    public UtilAnim anim;
    public UtilBitmap bitmap;
    public UtilInfo info;
    public UtilLog log;
    public UtilUI ui;
    public UtilWant want;

    /* loaded from: classes.dex */
    public static class StockRemindUtilHolder {
        public static final UtilBox mUtilBox = new UtilBox();
    }

    public static UtilBox getBox() {
        return StockRemindUtilHolder.mUtilBox;
    }

    public void initBox() {
        new Thread(new Runnable() { // from class: com.yfve.ici.appcustomviewlib.blur.util.UtilBox.1
            @Override // java.lang.Runnable
            public void run() {
                UtilBox.this.anim = new UtilAnim();
                UtilBox.this.log = new UtilLog();
                UtilBox.this.bitmap = new UtilBitmap();
            }
        }).start();
    }

    public UtilBox() {
        this.ui = new UtilUI();
        this.want = new UtilWant();
        this.info = new UtilInfo();
        initBox();
    }
}
