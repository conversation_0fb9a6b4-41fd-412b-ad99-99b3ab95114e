package com.yfve.ici.appcustomviewlib.blur.util;

import android.app.Activity;
import android.content.Context;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.TextView;
import androidx.core.app.NotificationManagerCompat;
import java.util.List;
import org.json.JSONException;

/* loaded from: classes.dex */
public class UtilWant {
    public final String CHECK_OP_NO_THROW = NotificationManagerCompat.CHECK_OP_NO_THROW;
    public final String OP_POST_NOTIFICATION = NotificationManagerCompat.OP_POST_NOTIFICATION;

    public void clearList(List list) {
        if (isNull(list)) {
            return;
        }
        for (int size = list.size() - 1; size >= 0; size--) {
            list.remove(size);
        }
    }

    public String getVersion(Context context) {
        try {
            return context.getPackageManager().getPackageInfo(context.getPackageName(), 0).versionName;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    public void hideInput(Activity activity) {
        try {
            ((InputMethodManager) activity.getSystemService("input_method")).hideSoftInputFromWindow(activity.getCurrentFocus().getWindowToken(), 2);
        } catch (Exception unused) {
        }
    }

    public boolean isNotificationEnabled(Context context) {
        return NotificationManagerCompat.from(context).areNotificationsEnabled();
    }

    public boolean isNull(String str) {
        return str == null || "".equals(str) || "null".equals(str) || "[null]".equals(str) || "{null}".equals(str) || "[]".equals(str) || "{}".equals(str);
    }

    public void showException(Error error) {
    }

    public void showException(Exception exc) {
    }

    public void showException(JSONException jSONException) {
    }

    public void showInput(EditText editText) {
        ((InputMethodManager) editText.getContext().getSystemService("input_method")).showSoftInput(editText, 0);
    }

    public boolean isNull(TextView textView) {
        return textView == null || textView.getText() == null || isNull(textView.getText().toString());
    }

    public boolean isNull(List list) {
        return list == null || list.size() == 0;
    }
}
