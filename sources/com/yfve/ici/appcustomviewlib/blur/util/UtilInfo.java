package com.yfve.ici.appcustomviewlib.blur.util;

import android.app.Activity;
import android.os.Build;
import android.util.DisplayMetrics;

/* loaded from: classes.dex */
public class UtilInfo {
    public int phoneWidth = 0;
    public int phoneHeigh = 0;
    public int phoneSDK = 0;

    public int getPhoneHeigh() {
        return this.phoneHeigh;
    }

    public int getPhoneSDK() {
        return this.phoneSDK;
    }

    public int getPhoneWidth() {
        return this.phoneWidth;
    }

    public void initPhoneInfo(Activity activity) {
        DisplayMetrics displayMetrics = new DisplayMetrics();
        activity.getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
        this.phoneWidth = displayMetrics.widthPixels;
        this.phoneHeigh = displayMetrics.heightPixels;
        this.phoneSDK = Build.VERSION.SDK_INT;
    }

    public void setPhoneHeigh(int i) {
        this.phoneHeigh = i;
    }

    public void setPhoneSDK(int i) {
        this.phoneSDK = i;
    }

    public void setPhoneWidth(int i) {
        this.phoneWidth = i;
    }
}
