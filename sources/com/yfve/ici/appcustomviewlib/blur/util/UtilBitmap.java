package com.yfve.ici.appcustomviewlib.blur.util;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.RectF;
import android.graphics.Shader;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.renderscript.Allocation;
import android.renderscript.Element;
import android.renderscript.RenderScript;
import android.renderscript.ScriptIntrinsicBlur;
import android.util.Log;
import android.widget.ImageView;
import androidx.core.view.ViewCompat;

/* loaded from: classes.dex */
public class UtilBitmap {
    public final float BITMAP_SCALE = 0.2f;

    private Bitmap drawableToBitmap(Drawable drawable) {
        Bitmap createBitmap;
        if (drawable instanceof BitmapDrawable) {
            BitmapDrawable bitmapDrawable = (BitmapDrawable) drawable;
            if (bitmapDrawable.getBitmap() != null) {
                return bitmapDrawable.getBitmap();
            }
        }
        if (drawable.getIntrinsicWidth() > 0 && drawable.getIntrinsicHeight() > 0) {
            createBitmap = Bitmap.createBitmap(drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight(), Bitmap.Config.ARGB_8888);
        } else {
            createBitmap = Bitmap.createBitmap(1, 1, Bitmap.Config.ARGB_8888);
        }
        Canvas canvas = new Canvas(createBitmap);
        drawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
        drawable.draw(canvas);
        return createBitmap;
    }

    public Bitmap blurBitmap(Context context, Bitmap bitmap, float f) {
        if (f < 0.0f) {
            f = 0.0f;
        }
        if (f > 25.0f) {
            f = 25.0f;
        }
        try {
            Class.forName("android.renderscript.ScriptIntrinsicBlur");
            Bitmap createScaledBitmap = Bitmap.createScaledBitmap(bitmap, bitmap.getWidth(), bitmap.getHeight(), false);
            Bitmap createBitmap = Bitmap.createBitmap(createScaledBitmap);
            RenderScript create = RenderScript.create(context);
            ScriptIntrinsicBlur create2 = ScriptIntrinsicBlur.create(create, Element.U8_4(create));
            Allocation createFromBitmap = Allocation.createFromBitmap(create, createScaledBitmap);
            Allocation createFromBitmap2 = Allocation.createFromBitmap(create, createBitmap);
            create2.setRadius(f);
            create2.setInput(createFromBitmap);
            create2.forEach(createFromBitmap2);
            createFromBitmap2.copyTo(createBitmap);
            return createBitmap;
        } catch (Exception unused) {
            Log.e("Bemboy_Error", "Android版本过低");
            return null;
        }
    }

    public void blurImageView(Context context, ImageView imageView, Bitmap bitmap, float f) {
        Bitmap blurBitmap = blurBitmap(context, bitmap, f);
        if (blurBitmap != null) {
            Canvas canvas = new Canvas(blurBitmap);
            canvas.drawBitmap(blurBitmap, 0.0f, 0.0f, (Paint) null);
            Paint paint = new Paint();
            paint.setShader(new LinearGradient(0.0f, 0.0f, blurBitmap.getWidth(), 0.0f, 0, (int) ViewCompat.MEASURED_STATE_MASK, Shader.TileMode.CLAMP));
            paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.DST_IN));
            canvas.drawRect(0.0f, blurBitmap.getHeight(), blurBitmap.getWidth(), 0.0f, paint);
            imageView.setImageBitmap(blurBitmap);
        }
    }

    public Drawable coverColor(Context context, Bitmap bitmap, int i) {
        Paint paint = new Paint();
        paint.setColor(i);
        new Canvas(bitmap).drawRoundRect(new RectF(0.0f, 0.0f, bitmap.getWidth(), bitmap.getHeight()), 0.0f, 0.0f, paint);
        return new BitmapDrawable(context.getResources(), bitmap);
    }

    public void blurImageView(Context context, ImageView imageView, float f, int i) {
        Bitmap blurBitmap = blurBitmap(context, drawableToBitmap(imageView.getDrawable()), f);
        if (blurBitmap != null) {
            Drawable coverColor = coverColor(context, blurBitmap, i);
            imageView.setScaleType(ImageView.ScaleType.FIT_XY);
            imageView.setImageDrawable(coverColor);
            return;
        }
        imageView.setImageBitmap(null);
        imageView.setBackgroundColor(i);
    }
}
