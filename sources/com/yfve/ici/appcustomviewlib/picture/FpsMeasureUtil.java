package com.yfve.ici.appcustomviewlib.picture;

import android.os.SystemClock;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Iterator;

/* loaded from: classes.dex */
public class FpsMeasureUtil {
    public ArrayList<Long> mTimes = new ArrayList<>();
    public DecimalFormat mDecimalFormat = new DecimalFormat("0.0 fps");

    /* loaded from: classes.dex */
    public interface OnFpsListener {
        void onFps(String str);
    }

    public String getFpsText() {
        int size = this.mTimes.size();
        if (size <= 1) {
            return "0.0 fps";
        }
        float longValue = (float) (this.mTimes.get(size - 1).longValue() - this.mTimes.get(0).longValue());
        float f = size;
        return this.mDecimalFormat.format(((((1000.0f - longValue) * f) / longValue) + f) - 1.0f);
    }

    public void measureFps() {
        long uptimeMillis = SystemClock.uptimeMillis();
        this.mTimes.add(Long.valueOf(uptimeMillis));
        long j = uptimeMillis - 1000;
        Iterator<Long> it = this.mTimes.iterator();
        while (it.hasNext() && it.next().longValue() < j) {
            it.remove();
        }
    }
}
