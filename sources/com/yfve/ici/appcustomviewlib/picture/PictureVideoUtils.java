package com.yfve.ici.appcustomviewlib.picture;

/* loaded from: classes.dex */
public class PictureVideoUtils {
    public static final int COUNT = 205;
    public static final int FPS = 7;
    public static final String PATH_NAME = "video";
    public static final String TAG = "PictureVideoUtils";
    public static final long mDuration = 29285;
    public static PictureVideoUtils mPictureVideoUtils;
    public String[] mPathUrl = new String[COUNT];

    public PictureVideoUtils() {
        int i = 0;
        while (i < 205) {
            int i2 = i + 1;
            this.mPathUrl[i] = String.format("%1$s/%2$s %3$03d.jpg", PATH_NAME, PATH_NAME, Integer.valueOf(i2));
            i = i2;
        }
    }

    public static PictureVideoUtils getInstance() {
        synchronized (PictureVideoUtils.class) {
            if (mPictureVideoUtils == null) {
                mPictureVideoUtils = new PictureVideoUtils();
            }
        }
        return mPictureVideoUtils;
    }

    public long getDuration() {
        return mDuration;
    }

    public String[] getPaths() {
        return this.mPathUrl;
    }
}
