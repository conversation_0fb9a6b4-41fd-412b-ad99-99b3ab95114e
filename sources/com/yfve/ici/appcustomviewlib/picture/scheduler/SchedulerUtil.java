package com.yfve.ici.appcustomviewlib.picture.scheduler;

/* loaded from: classes.dex */
public class SchedulerUtil {
    public static void join(Thread thread) {
        if (Thread.currentThread().getId() == thread.getId()) {
            return;
        }
        try {
            thread.join();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public static void lockWait(Object obj) {
        try {
            obj.wait();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}
