package com.yfve.ici.appcustomviewlib.picture.scheduler;

import android.annotation.SuppressLint;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.os.SystemClock;
import androidx.annotation.IntRange;
import androidx.annotation.NonNull;

/* loaded from: classes.dex */
public final class Scheduler {
    public static final int MSG_FRAME = 0;
    public static final int MSG_QUIT = -1;
    public static final int MSG_SEEK = 1;
    public double mCurrentUptimeMs;
    public double mDelayTime;
    public long mDuration;
    public long mFrameCount;
    public volatile long mFrameIndex;
    public FrameThread mFrameThread;
    public FrameHandler mHandler;
    public volatile boolean mIsCancel;
    public volatile boolean mIsPaused;
    public volatile boolean mIsRunPause;
    public volatile boolean mIsRunning;
    public volatile boolean mIsSeekToComplete;
    public boolean mIsSkipFrame;
    public volatile boolean mIsStared;
    public volatile boolean mIsWaitResume;
    public final Object mLock;
    public OnFrameListener mOnFrameListener;
    public OnFrameUpdateListener mOnFrameUpdateListener;
    public OnSeekToListener mOnSeekToListener;

    @SuppressLint({"HandlerLeak"})
    /* loaded from: classes.dex */
    public final class FrameHandler extends Handler {
        public FrameHandler() {
        }

        @Override // android.os.Handler
        public void handleMessage(Message message) {
            int i = message.what;
            if (i == -1) {
                Scheduler.this.cancel();
                Scheduler.this.quit();
            } else if (i != 0) {
                if (i != 1) {
                    return;
                }
                Scheduler.this.mOnSeekToListener.onSeekTo(Scheduler.this.mFrameIndex);
                if (!Scheduler.this.mIsWaitResume) {
                    Scheduler.this.mOnSeekToListener.onSeekUpdate(Scheduler.this.mFrameIndex);
                }
                Scheduler.this.mIsSeekToComplete = true;
                if (Scheduler.this.mOnSeekToListener.onSeekToComplete()) {
                    Scheduler.this.mOnSeekToListener = null;
                    if (Scheduler.this.mIsWaitResume) {
                        Scheduler.this.resume();
                        Scheduler.this.mIsWaitResume = false;
                    }
                }
            } else {
                synchronized (Scheduler.this.mLock) {
                    if (Scheduler.this.mIsCancel || !Scheduler.this.mIsPaused) {
                        Scheduler.this.update(Scheduler.this.mFrameIndex);
                        if (Scheduler.this.mIsSkipFrame) {
                            double uptimeMillis = (SystemClock.uptimeMillis() - Scheduler.this.mCurrentUptimeMs) - Scheduler.this.mDelayTime;
                            if (uptimeMillis > 0.0d) {
                                long ceil = (long) Math.ceil(uptimeMillis / Scheduler.this.mDelayTime);
                                Scheduler.this.mFrameIndex += ceil;
                                Scheduler.this.mCurrentUptimeMs = (ceil * Scheduler.this.mDelayTime) + Scheduler.this.mCurrentUptimeMs;
                            }
                        }
                        Scheduler.this.mCurrentUptimeMs += Scheduler.this.mDelayTime;
                        Scheduler.access$908(Scheduler.this);
                        if (!Scheduler.this.mIsCancel) {
                            if (Scheduler.this.mFrameIndex >= Scheduler.this.mFrameCount) {
                                Scheduler.this.nextQuit();
                            } else if (!Scheduler.this.mIsPaused) {
                                Scheduler.this.next(Scheduler.this.mCurrentUptimeMs);
                            }
                        }
                    }
                }
            }
        }
    }

    /* loaded from: classes.dex */
    public final class FrameThread extends HandlerThread {
        public FrameThread(String str) {
            super(str);
        }

        @Override // android.os.HandlerThread
        public void onLooperPrepared() {
            Scheduler scheduler = Scheduler.this;
            scheduler.mHandler = new FrameHandler();
            Scheduler.this.mIsRunning = true;
            Scheduler.this.mCurrentUptimeMs = SystemClock.uptimeMillis();
            Scheduler.this.prepare();
            Scheduler scheduler2 = Scheduler.this;
            scheduler2.next(scheduler2.mCurrentUptimeMs);
        }
    }

    public Scheduler(@IntRange(from = 1) long j, @IntRange(from = 2) long j2, @NonNull OnFrameUpdateListener onFrameUpdateListener) {
        this(j, j2, onFrameUpdateListener, null);
    }

    public static /* synthetic */ long access$908(Scheduler scheduler) {
        long j = scheduler.mFrameIndex;
        scheduler.mFrameIndex = 1 + j;
        return j;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void cancel() {
        OnFrameListener onFrameListener;
        if (!this.mIsCancel || (onFrameListener = this.mOnFrameListener) == null) {
            return;
        }
        onFrameListener.onCancel();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void next(double d) {
        FrameHandler frameHandler = this.mHandler;
        frameHandler.sendMessageAtTime(frameHandler.obtainMessage(0), Math.round(d));
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void nextQuit() {
        FrameHandler frameHandler = this.mHandler;
        frameHandler.sendMessageAtTime(frameHandler.obtainMessage(-1), SystemClock.uptimeMillis());
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void prepare() {
        OnFrameListener onFrameListener = this.mOnFrameListener;
        if (onFrameListener != null) {
            onFrameListener.onStart();
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void quit() {
        this.mFrameThread.quit();
        this.mFrameThread.interrupt();
        this.mIsRunning = false;
        OnFrameListener onFrameListener = this.mOnFrameListener;
        if (onFrameListener != null) {
            onFrameListener.onStop();
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void update(long j) {
        this.mOnFrameUpdateListener.onFrameUpdate(j);
    }

    public long getFrameIndex() {
        return this.mFrameIndex;
    }

    public boolean isCanceled() {
        return this.mIsCancel;
    }

    public boolean isPaused() {
        return this.mIsPaused;
    }

    public boolean isRunning() {
        return this.mIsRunning;
    }

    public boolean isSeekToComplete() {
        return this.mIsSeekToComplete;
    }

    public boolean isStarted() {
        return this.mIsStared;
    }

    public boolean pause() {
        if (!isRunning() || isPaused() || this.mIsCancel) {
            return false;
        }
        this.mIsRunPause = true;
        this.mIsPaused = true;
        this.mHandler.removeMessages(0);
        synchronized (this.mLock) {
            this.mIsRunPause = false;
            this.mLock.notifyAll();
        }
        return true;
    }

    public boolean resume() {
        if (isRunning() && isPaused() && !this.mIsCancel) {
            if (!isSeekToComplete()) {
                this.mIsWaitResume = true;
                return true;
            }
            synchronized (this.mLock) {
                if (this.mIsRunPause) {
                    SchedulerUtil.lockWait(this.mLock);
                }
                if (this.mIsRunning) {
                    this.mIsPaused = false;
                    double uptimeMillis = SystemClock.uptimeMillis();
                    this.mCurrentUptimeMs = uptimeMillis;
                    next(uptimeMillis);
                    return true;
                }
                return false;
            }
        }
        return false;
    }

    public void seekTo(@IntRange(from = 0) long j, @NonNull OnSeekToListener onSeekToListener) {
        if (!isRunning() || this.mIsCancel || j == this.mFrameIndex || !isSeekToComplete()) {
            return;
        }
        this.mIsSeekToComplete = false;
        this.mOnSeekToListener = onSeekToListener;
        if (!isPaused()) {
            pause();
            this.mIsWaitResume = true;
        }
        long j2 = this.mFrameCount;
        if (j >= j2) {
            this.mFrameIndex = j2 - 1;
        } else if (j < 0) {
            this.mFrameIndex = 0L;
        } else {
            this.mFrameIndex = j;
        }
        FrameHandler frameHandler = this.mHandler;
        frameHandler.sendMessageAtTime(frameHandler.obtainMessage(1), SystemClock.uptimeMillis());
    }

    public void setSkipFrame(boolean z) {
        if (!isStarted()) {
            this.mIsSkipFrame = z;
            return;
        }
        throw new RuntimeException("scheduler has been running");
    }

    public void start() {
        if (!isStarted()) {
            this.mIsStared = true;
            FrameThread frameThread = new FrameThread("scheduler");
            this.mFrameThread = frameThread;
            frameThread.start();
            return;
        }
        throw new RuntimeException("scheduler can only run once");
    }

    public void stop() {
        if (isStarted()) {
            if (!this.mIsCancel) {
                if (isRunning()) {
                    this.mIsCancel = true;
                    this.mHandler.removeMessages(0);
                    this.mHandler.removeMessages(1);
                    nextQuit();
                    SchedulerUtil.join(this.mFrameThread);
                    return;
                }
                return;
            }
            throw new RuntimeException("scheduler has stopped");
        }
        throw new RuntimeException("scheduler not yet running");
    }

    public Scheduler(@IntRange(from = 1) long j, @IntRange(from = 2) long j2, @NonNull OnFrameUpdateListener onFrameUpdateListener, OnFrameListener onFrameListener) {
        this.mLock = new Object();
        this.mIsSkipFrame = false;
        this.mIsStared = false;
        this.mIsRunning = false;
        this.mIsPaused = false;
        this.mIsCancel = false;
        this.mIsRunPause = false;
        this.mIsWaitResume = false;
        this.mIsSeekToComplete = true;
        if (j2 > j) {
            throw new RuntimeException("duration must be greater than frameCount");
        }
        if (j < 1) {
            throw new RuntimeException("duration must be greater than 0");
        }
        if (j2 >= 2) {
            this.mDuration = j;
            this.mFrameCount = j2;
            this.mOnFrameUpdateListener = onFrameUpdateListener;
            this.mOnFrameListener = onFrameListener;
            this.mDelayTime = j / (j2 - 1);
            return;
        }
        throw new RuntimeException("frameCount must be greater than 2");
    }
}
