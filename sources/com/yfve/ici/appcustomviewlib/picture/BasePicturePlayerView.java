package com.yfve.ici.appcustomviewlib.picture;

import android.content.Context;
import android.graphics.SurfaceTexture;
import android.util.AttributeSet;
import android.view.TextureView;
import com.yfve.ici.appcustomviewlib.picture.FpsMeasureUtil;

/* loaded from: classes.dex */
public abstract class BasePicturePlayerView extends TextureView implements TextureView.SurfaceTextureListener {
    public FpsMeasureUtil mFpsMeasureUtil;
    public FpsMeasureUtil.OnFpsListener mOnFpsListener;

    public BasePicturePlayerView(Context context) {
        super(context);
    }

    @Override // android.view.TextureView.SurfaceTextureListener
    public void onSurfaceTextureAvailable(SurfaceTexture surfaceTexture, int i, int i2) {
    }

    @Override // android.view.TextureView.SurfaceTextureListener
    public boolean onSurfaceTextureDestroyed(SurfaceTexture surfaceTexture) {
        return false;
    }

    @Override // android.view.TextureView.SurfaceTextureListener
    public void onSurfaceTextureSizeChanged(SurfaceTexture surfaceTexture, int i, int i2) {
    }

    @Override // android.view.TextureView.SurfaceTextureListener
    public void onSurfaceTextureUpdated(SurfaceTexture surfaceTexture) {
        if (this.mFpsMeasureUtil == null) {
            this.mFpsMeasureUtil = new FpsMeasureUtil();
        }
        this.mFpsMeasureUtil.measureFps();
        FpsMeasureUtil.OnFpsListener onFpsListener = this.mOnFpsListener;
        if (onFpsListener != null) {
            onFpsListener.onFps(this.mFpsMeasureUtil.getFpsText());
        }
    }

    public void setOnFpsListener(FpsMeasureUtil.OnFpsListener onFpsListener) {
        this.mOnFpsListener = onFpsListener;
    }

    public abstract void start(String[] strArr, long j);

    public BasePicturePlayerView(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
    }

    public BasePicturePlayerView(Context context, AttributeSet attributeSet, int i) {
        super(context, attributeSet, i);
    }
}
