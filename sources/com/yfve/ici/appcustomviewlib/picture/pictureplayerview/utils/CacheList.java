package com.yfve.ici.appcustomviewlib.picture.pictureplayerview.utils;

import androidx.annotation.NonNull;

/* loaded from: classes.dex */
public class CacheList<T> {
    public int mCount;
    public int mIndex;
    public int mMaxCount;
    public OnRemoveListener<T> mOnRemoveListener;
    public T[] mValues;

    /* loaded from: classes.dex */
    public interface OnRemoveListener<T> {
        void onRemove(boolean z, T t);
    }

    public CacheList(@NonNull T[] tArr) {
        this(tArr, null);
    }

    private int getRealIndex(int i) {
        int i2 = (this.mIndex - this.mCount) + i;
        return i2 < 0 ? this.mMaxCount + i2 : i2;
    }

    public synchronized void add(T t) {
        if (this.mCount == this.mMaxCount) {
            if (this.mOnRemoveListener != null) {
                this.mOnRemoveListener.onRemove(true, this.mValues[this.mIndex]);
            }
            this.mCount--;
        }
        T[] tArr = this.mValues;
        int i = this.mIndex;
        int i2 = i + 1;
        this.mIndex = i2;
        tArr[i] = t;
        if (i2 >= this.mMaxCount) {
            this.mIndex = 0;
        }
        this.mCount++;
    }

    public synchronized void clear() {
        removeCount(this.mCount);
    }

    public synchronized T get(int i) {
        if (i >= 0) {
            if (i < this.mCount) {
                return this.mValues[getRealIndex(i)];
            }
        }
        return null;
    }

    public synchronized T getFirst() {
        return get(0);
    }

    public synchronized boolean isEmpty() {
        return this.mCount == 0;
    }

    public synchronized T remove(int i) {
        if (i >= this.mCount) {
            return null;
        }
        T t = this.mValues[getRealIndex(i)];
        if (i < this.mCount / 2) {
            while (i > 0) {
                this.mValues[getRealIndex(i)] = this.mValues[getRealIndex(i - 1)];
                i--;
            }
        } else {
            while (i < this.mCount) {
                T[] tArr = this.mValues;
                int realIndex = getRealIndex(i);
                i++;
                tArr[realIndex] = this.mValues[getRealIndex(i)];
            }
        }
        this.mValues[getRealIndex(i)] = null;
        this.mCount--;
        if (this.mOnRemoveListener != null) {
            this.mOnRemoveListener.onRemove(false, t);
        }
        return t;
    }

    public synchronized void removeCount(int i) {
        if (i > this.mCount) {
            i = this.mCount;
        }
        for (int i2 = 0; i2 < i; i2++) {
            int realIndex = getRealIndex(i2);
            if (this.mOnRemoveListener != null) {
                this.mOnRemoveListener.onRemove(false, this.mValues[realIndex]);
            }
            this.mValues[realIndex] = null;
        }
        this.mCount -= i;
    }

    public synchronized T removeFirst() {
        return remove(0);
    }

    public synchronized int size() {
        return this.mCount;
    }

    public CacheList(@NonNull T[] tArr, OnRemoveListener<T> onRemoveListener) {
        this.mValues = tArr;
        this.mMaxCount = tArr.length;
        this.mOnRemoveListener = onRemoveListener;
    }
}
