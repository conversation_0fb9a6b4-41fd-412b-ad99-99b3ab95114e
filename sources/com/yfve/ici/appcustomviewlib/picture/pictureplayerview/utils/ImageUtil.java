package com.yfve.ici.appcustomviewlib.picture.pictureplayerview.utils;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;

/* loaded from: classes.dex */
public class ImageUtil {
    public static boolean canUseForInBitmap(Bitmap bitmap, BitmapFactory.Options options) {
        int i = options.outWidth;
        int i2 = options.outHeight;
        int i3 = options.inSampleSize;
        if (i3 > 0) {
            i /= i3;
            i2 /= i3;
        }
        return getBytesPerPixel(bitmap.getConfig()) * (i * i2) <= bitmap.getAllocationByteCount();
    }

    public static int getBytesPerPixel(Bitmap.Config config) {
        if (config == Bitmap.Config.ARGB_8888) {
            return 4;
        }
        if (config == Bitmap.Config.RGB_565 || config == Bitmap.Config.ARGB_4444) {
            return 2;
        }
        if (config == Bitmap.Config.ALPHA_8) {
        }
        return 1;
    }

    public static void recycleBitmap(Bitmap bitmap) {
        if (bitmap == null || bitmap.isRecycled()) {
            return;
        }
        bitmap.recycle();
    }
}
