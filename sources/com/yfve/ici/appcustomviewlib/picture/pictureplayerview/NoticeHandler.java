package com.yfve.ici.appcustomviewlib.picture.pictureplayerview;

import android.os.Handler;
import android.os.Message;
import com.yfve.ici.appcustomviewlib.picture.pictureplayerview.interfaces.OnErrorListener;
import com.yfve.ici.appcustomviewlib.picture.pictureplayerview.interfaces.OnStopListener;
import com.yfve.ici.appcustomviewlib.picture.pictureplayerview.interfaces.OnUpdateListener;

/* loaded from: classes.dex */
public class NoticeHandler extends Handler {
    public static final int ERROR = -1;
    public static final int STOP = 1;
    public static final int UPDATE = 0;
    public OnErrorListener mOnErrorListener;
    public OnStopListener mOnStopListener;
    public OnUpdateListener mOnUpdateListener;

    /* loaded from: classes.dex */
    public static class HandlerObject {
        public Object listener;
        public Object value;

        public HandlerObject(Object obj, Object obj2) {
            this.listener = obj;
            this.value = obj2;
        }
    }

    @Override // android.os.Handler
    public void handleMessage(Message message) {
        super.handleMessage(message);
        HandlerObject handlerObject = (HandlerObject) message.obj;
        int i = message.what;
        if (i == -1) {
            ((OnErrorListener) handlerObject.listener).onError((String) handlerObject.value);
        } else if (i == 0) {
            ((OnUpdateListener) handlerObject.listener).onUpdate(((Integer) handlerObject.value).intValue());
        } else if (i != 1) {
        } else {
            ((OnStopListener) handlerObject.listener).onStop();
        }
    }

    public void noticeError(String str) {
        if (this.mOnErrorListener == null) {
            return;
        }
        Message obtain = Message.obtain();
        obtain.what = -1;
        obtain.obj = new HandlerObject(this.mOnErrorListener, str);
        sendMessage(obtain);
    }

    public void noticeStop() {
        if (this.mOnStopListener == null) {
            return;
        }
        Message obtain = Message.obtain();
        obtain.what = 1;
        obtain.obj = new HandlerObject(this.mOnStopListener, null);
        sendMessage(obtain);
    }

    public void noticeUpdate(int i) {
        if (this.mOnUpdateListener == null) {
            return;
        }
        Message obtain = Message.obtain();
        obtain.what = 0;
        obtain.obj = new HandlerObject(this.mOnUpdateListener, Integer.valueOf(i));
        sendMessage(obtain);
    }

    public void setOnErrorListener(OnErrorListener onErrorListener) {
        this.mOnErrorListener = onErrorListener;
    }

    public void setOnStopListener(OnStopListener onStopListener) {
        this.mOnStopListener = onStopListener;
    }

    public void setOnUpdateListener(OnUpdateListener onUpdateListener) {
        this.mOnUpdateListener = onUpdateListener;
    }
}
