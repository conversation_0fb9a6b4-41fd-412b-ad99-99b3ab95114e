package com.yfve.ici.appcustomviewlib.picture.pictureplayerview;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.SurfaceTexture;
import android.util.AttributeSet;
import android.view.TextureView;
import androidx.annotation.IntRange;
import androidx.annotation.NonNull;
import com.yfve.ici.appcustomviewlib.R;
import com.yfve.ici.appcustomviewlib.picture.pictureplayerview.interfaces.OnChangeListener;
import com.yfve.ici.appcustomviewlib.picture.pictureplayerview.interfaces.OnErrorListener;
import com.yfve.ici.appcustomviewlib.picture.pictureplayerview.interfaces.OnStopListener;
import com.yfve.ici.appcustomviewlib.picture.pictureplayerview.interfaces.OnUpdateListener;

/* loaded from: classes.dex */
public class PicturePlayerView extends TextureView implements TextureView.SurfaceTextureListener {
    public static final int PAUSE = 2;
    public static final int START = 1;
    public static final int STOP = 0;
    public static final String TAG = "PicturePlayerView";
    public int mCacheFrameNumber;
    public boolean mIsAntiAlias;
    public boolean mIsDither;
    public boolean mIsEnabled;
    public boolean mIsFilterBitmap;
    public boolean mIsLoop;
    public boolean mIsOpaque;
    public NoticeHandler mNoticeHandler;
    public OnChangeListener mOnChangeListener;
    public PicturePlayer mPlayer;
    public PictureRenderer mRenderer;
    public int mScaleType;
    public int mSource;
    public int mState;

    public PicturePlayerView(Context context) {
        this(context, null);
    }

    private void findView() {
        this.mNoticeHandler = new NoticeHandler();
        this.mRenderer = new PictureRenderer(this.mIsAntiAlias, this.mIsFilterBitmap, this.mIsDither, this.mScaleType, this);
        this.mPlayer = new PicturePlayer(getContext(), this.mSource, this.mCacheFrameNumber, this.mRenderer);
    }

    private void initAttrs(AttributeSet attributeSet) {
        if (attributeSet == null) {
            return;
        }
        TypedArray obtainStyledAttributes = getContext().obtainStyledAttributes(attributeSet, R.styleable.PicturePlayerView);
        this.mIsLoop = obtainStyledAttributes.getBoolean(R.styleable.PicturePlayerView_picture_loop, false);
        this.mIsOpaque = obtainStyledAttributes.getBoolean(R.styleable.PicturePlayerView_picture_opaque, true);
        this.mIsAntiAlias = obtainStyledAttributes.getBoolean(R.styleable.PicturePlayerView_picture_antiAlias, true);
        this.mIsFilterBitmap = obtainStyledAttributes.getBoolean(R.styleable.PicturePlayerView_picture_filterBitmap, false);
        this.mIsDither = obtainStyledAttributes.getBoolean(R.styleable.PicturePlayerView_picture_dither, false);
        this.mSource = obtainStyledAttributes.getInt(R.styleable.PicturePlayerView_picture_source, 0);
        this.mScaleType = obtainStyledAttributes.getInt(R.styleable.PicturePlayerView_picture_scaleType, 3);
        this.mCacheFrameNumber = obtainStyledAttributes.getInt(R.styleable.PicturePlayerView_picture_cacheFrameNumber, 12);
        obtainStyledAttributes.recycle();
    }

    private void initView() {
        setOpaque(this.mIsOpaque);
    }

    private void setListener() {
        setSurfaceTextureListener(this);
        this.mRenderer.setOnUpdateListener(new OnUpdateListener() { // from class: com.yfve.ici.appcustomviewlib.picture.pictureplayerview.PicturePlayerView.1
            @Override // com.yfve.ici.appcustomviewlib.picture.pictureplayerview.interfaces.OnUpdateListener
            public void onUpdate(int i) {
                PicturePlayerView.this.mNoticeHandler.noticeUpdate(i);
            }
        });
        this.mRenderer.setOnStopListener(new OnStopListener() { // from class: com.yfve.ici.appcustomviewlib.picture.pictureplayerview.PicturePlayerView.2
            @Override // com.yfve.ici.appcustomviewlib.picture.pictureplayerview.interfaces.OnStopListener
            public void onStop() {
                if (PicturePlayerView.this.mState == 0 || !PicturePlayerView.this.mIsLoop) {
                    PicturePlayerView.this.mRenderer.drawClear();
                    PicturePlayerView.this.mState = 0;
                    PicturePlayerView.this.mNoticeHandler.noticeStop();
                    return;
                }
                PicturePlayerView.this.mPlayer.start();
            }
        });
        this.mRenderer.setOnErrorListener(new OnErrorListener() { // from class: com.yfve.ici.appcustomviewlib.picture.pictureplayerview.PicturePlayerView.3
            @Override // com.yfve.ici.appcustomviewlib.picture.pictureplayerview.interfaces.OnErrorListener
            public void onError(String str) {
                PicturePlayerView.this.mNoticeHandler.noticeError(str);
            }
        });
    }

    public int getFrameIndex() {
        return this.mPlayer.getFrameIndex();
    }

    @Override // android.view.View
    public boolean isEnabled() {
        return this.mIsEnabled;
    }

    public boolean isPaused() {
        return this.mState == 2;
    }

    public boolean isPlaying() {
        return this.mState != 0;
    }

    @Override // android.view.TextureView.SurfaceTextureListener
    public void onSurfaceTextureAvailable(SurfaceTexture surfaceTexture, int i, int i2) {
        this.mRenderer.drawClear();
        OnChangeListener onChangeListener = this.mOnChangeListener;
        if (onChangeListener != null) {
            onChangeListener.onCreated();
        }
    }

    @Override // android.view.TextureView.SurfaceTextureListener
    public boolean onSurfaceTextureDestroyed(SurfaceTexture surfaceTexture) {
        stop();
        OnChangeListener onChangeListener = this.mOnChangeListener;
        if (onChangeListener != null) {
            onChangeListener.onDestroyed();
            return true;
        }
        return true;
    }

    @Override // android.view.TextureView.SurfaceTextureListener
    public void onSurfaceTextureSizeChanged(SurfaceTexture surfaceTexture, int i, int i2) {
    }

    @Override // android.view.TextureView.SurfaceTextureListener
    public void onSurfaceTextureUpdated(SurfaceTexture surfaceTexture) {
    }

    public void pause() {
        if (this.mState == 1 && this.mPlayer.isStarted() && this.mPlayer.pause()) {
            this.mState = 2;
        }
    }

    public void release() {
        setOnUpdateListener(null);
        setOnStopListener(null);
        setOnErrorListener(null);
        setOnChangeListener(null);
        stop();
    }

    public void resume() {
        if (this.mState == 2 && this.mPlayer.isStarted() && this.mPlayer.resume()) {
            this.mState = 1;
        }
    }

    public void seekTo(int i) {
        if (this.mState == 0 || getFrameIndex() == i) {
            return;
        }
        this.mPlayer.seekTo(i);
    }

    public void setDataSource(@NonNull String str, @NonNull String[] strArr, @IntRange(from = 1) long j) {
        int length = strArr.length;
        String[] strArr2 = new String[strArr.length];
        for (int i = 0; i < length; i++) {
            strArr2[i] = String.format("%s/%s", str, strArr[i]);
        }
        setDataSource(strArr2, j);
    }

    @Override // android.view.View
    public void setEnabled(boolean z) {
        this.mIsEnabled = z;
    }

    public void setLoop(boolean z) {
        this.mIsLoop = z;
    }

    public void setOnChangeListener(OnChangeListener onChangeListener) {
        this.mOnChangeListener = onChangeListener;
    }

    public void setOnErrorListener(OnErrorListener onErrorListener) {
        this.mNoticeHandler.setOnErrorListener(onErrorListener);
    }

    public void setOnStopListener(OnStopListener onStopListener) {
        this.mNoticeHandler.setOnStopListener(onStopListener);
    }

    public void setOnUpdateListener(OnUpdateListener onUpdateListener) {
        this.mNoticeHandler.setOnUpdateListener(onUpdateListener);
    }

    public void setScaleType(int i) {
        if (this.mState != 0) {
            return;
        }
        this.mRenderer.setScaleType(i);
    }

    public void start() {
        if (this.mIsEnabled && this.mState != 1) {
            this.mState = 1;
            this.mPlayer.start();
        }
    }

    public void stop() {
        if (this.mState == 0) {
            return;
        }
        this.mState = 0;
        this.mPlayer.stop();
    }

    public PicturePlayerView(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, 0);
    }

    public PicturePlayerView(Context context, AttributeSet attributeSet, int i) {
        super(context, attributeSet, i);
        this.mState = 0;
        this.mIsEnabled = true;
        initAttrs(attributeSet);
        findView();
        initView();
        setListener();
    }

    public void setDataSource(@NonNull String[] strArr, @IntRange(from = 1) long j) {
        if (this.mState != 0) {
            return;
        }
        this.mPlayer.setDataSource(strArr, j, strArr.length);
    }
}
