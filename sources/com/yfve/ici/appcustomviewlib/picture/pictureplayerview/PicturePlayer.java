package com.yfve.ici.appcustomviewlib.picture.pictureplayerview;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.SystemClock;
import androidx.annotation.IntRange;
import androidx.annotation.NonNull;
import com.yfve.ici.appcustomviewlib.picture.pictureplayerview.utils.CacheList;
import com.yfve.ici.appcustomviewlib.picture.pictureplayerview.utils.ImageUtil;
import com.yfve.ici.appcustomviewlib.picture.scheduler.OnFrameUpdateListener;
import com.yfve.ici.appcustomviewlib.picture.scheduler.OnSeekToListener;
import com.yfve.ici.appcustomviewlib.picture.scheduler.OnSimpleFrameListener;
import com.yfve.ici.appcustomviewlib.picture.scheduler.Scheduler;
import com.yfve.ici.appcustomviewlib.picture.scheduler.SchedulerUtil;
import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.InputStream;

/* loaded from: classes.dex */
public class PicturePlayer {
    public static final int DEFAULT_MAX_CACHE_NUMBER = 12;
    public CacheList<Bitmap> mCacheBitmaps;
    public final int mCacheFrameNumber;
    public Context mContext;
    public long mDuration;
    public int mFrameCount;
    public volatile boolean mIsCancel;
    public volatile boolean mIsPlayCancel;
    public volatile boolean mIsReadCancel;
    public String[] mPaths;
    public volatile int mReadFrame;
    public ReadThread mReadThread;
    public Renderer mRenderer;
    public CacheList<Bitmap> mReusableBitmaps;
    public final int mReusableFrameNumber;
    public Scheduler mScheduler;
    public int mSource;
    public volatile int mSeekToIndex = -1;
    public final Object mSeekToLock = new Object();
    public final OnSeekToListener mSeekListener = new OnSeekToListener() { // from class: com.yfve.ici.appcustomviewlib.picture.pictureplayerview.PicturePlayer.3
        @Override // com.yfve.ici.appcustomviewlib.picture.scheduler.OnSeekToListener
        public void onSeekTo(long j) {
            synchronized (PicturePlayer.this.mSeekToLock) {
                if (PicturePlayer.this.mCacheBitmaps.isEmpty()) {
                    SchedulerUtil.lockWait(PicturePlayer.this.mSeekToLock);
                }
            }
        }

        @Override // com.yfve.ici.appcustomviewlib.picture.scheduler.OnSeekToListener
        public boolean onSeekToComplete() {
            if (PicturePlayer.this.mSeekToIndex != -1) {
                PicturePlayer picturePlayer = PicturePlayer.this;
                picturePlayer.seekTo(picturePlayer.mSeekToIndex);
                PicturePlayer.this.mSeekToIndex = -1;
                return false;
            }
            return true;
        }

        @Override // com.yfve.ici.appcustomviewlib.picture.scheduler.OnSeekToListener
        public void onSeekUpdate(long j) {
            PicturePlayer.this.update((int) j, -1);
        }
    };

    /* loaded from: classes.dex */
    public class FrameListener extends OnSimpleFrameListener {
        public FrameListener() {
        }

        @Override // com.yfve.ici.appcustomviewlib.picture.scheduler.OnFrameListener
        public void onStop() {
            PicturePlayer.this.mIsPlayCancel = true;
            PicturePlayer.this.threadStop();
        }
    }

    /* loaded from: classes.dex */
    public class FrameUpdateListener implements OnFrameUpdateListener {
        public FrameUpdateListener() {
        }

        @Override // com.yfve.ici.appcustomviewlib.picture.scheduler.OnFrameUpdateListener
        public void onFrameUpdate(long j) {
            int i = (int) j;
            PicturePlayer.this.update(i, i);
        }
    }

    /* loaded from: classes.dex */
    public class ReadThread extends Thread {
        public ReadThread() {
        }

        @Override // java.lang.Thread, java.lang.Runnable
        public void run() {
            while (!PicturePlayer.this.mIsCancel && !PicturePlayer.this.mIsPlayCancel) {
                try {
                    if (PicturePlayer.this.mReadFrame < PicturePlayer.this.mFrameCount) {
                        int size = PicturePlayer.this.mCacheBitmaps.size();
                        if (size < PicturePlayer.this.mCacheFrameNumber && (size < 1 || !PicturePlayer.this.isPaused())) {
                            synchronized (PicturePlayer.this.mSeekToLock) {
                                Bitmap readBitmap = PicturePlayer.this.readBitmap(PicturePlayer.this.mPaths[PicturePlayer.this.mReadFrame]);
                                if (readBitmap != null && !readBitmap.isRecycled()) {
                                    PicturePlayer.this.mCacheBitmaps.add(readBitmap);
                                    PicturePlayer.access$1008(PicturePlayer.this);
                                    PicturePlayer.this.mSeekToLock.notifyAll();
                                } else {
                                    throw new NullPointerException("读取的图片有错误");
                                }
                            }
                            if (PicturePlayer.this.mReadFrame == 1 && !PicturePlayer.this.mIsCancel && !PicturePlayer.this.mScheduler.isStarted()) {
                                PicturePlayer.this.mScheduler.start();
                            }
                        }
                        SystemClock.sleep(1L);
                    } else {
                        SystemClock.sleep(1L);
                    }
                } catch (Throwable th) {
                    PicturePlayer.this.error(th);
                }
            }
            PicturePlayer.this.mIsReadCancel = true;
            PicturePlayer.this.threadStop();
        }
    }

    /* loaded from: classes.dex */
    public interface Renderer {
        void onDraw(int i, Bitmap bitmap);

        void onError(String str);

        void onStop();
    }

    public PicturePlayer(@NonNull Context context, int i, @IntRange(from = 2) int i2, @NonNull Renderer renderer) {
        this.mContext = context;
        this.mSource = i;
        this.mCacheFrameNumber = i2;
        this.mReusableFrameNumber = i2;
        this.mRenderer = renderer;
        this.mCacheBitmaps = new CacheList<>(new Bitmap[i2], new CacheList.OnRemoveListener<Bitmap>() { // from class: com.yfve.ici.appcustomviewlib.picture.pictureplayerview.PicturePlayer.1
            @Override // com.yfve.ici.appcustomviewlib.picture.pictureplayerview.utils.CacheList.OnRemoveListener
            public void onRemove(boolean z, Bitmap bitmap) {
                PicturePlayer.this.mReusableBitmaps.add(bitmap);
            }
        });
        this.mReusableBitmaps = new CacheList<>(new Bitmap[this.mReusableFrameNumber], new CacheList.OnRemoveListener<Bitmap>() { // from class: com.yfve.ici.appcustomviewlib.picture.pictureplayerview.PicturePlayer.2
            @Override // com.yfve.ici.appcustomviewlib.picture.pictureplayerview.utils.CacheList.OnRemoveListener
            public void onRemove(boolean z, Bitmap bitmap) {
                if (z) {
                    ImageUtil.recycleBitmap(bitmap);
                }
            }
        });
    }

    public static /* synthetic */ int access$1008(PicturePlayer picturePlayer) {
        int i = picturePlayer.mReadFrame;
        picturePlayer.mReadFrame = i + 1;
        return i;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void error(Throwable th) {
        th.printStackTrace();
        stop();
        this.mRenderer.onError("读取图片失败");
    }

    private Bitmap getBitmap(int i) {
        if (this.mCacheBitmaps.isEmpty()) {
            return null;
        }
        int size = this.mReadFrame - this.mCacheBitmaps.size();
        if (i == size) {
            return this.mCacheBitmaps.getFirst();
        }
        if (i > size) {
            if (i >= this.mReadFrame) {
                this.mCacheBitmaps.clear();
                this.mReadFrame = i + 1;
                return null;
            }
            this.mCacheBitmaps.removeCount(i - size);
            return this.mCacheBitmaps.getFirst();
        }
        return null;
    }

    private Bitmap getBitmapFromReusableSet(BitmapFactory.Options options) {
        if (this.mReusableBitmaps.isEmpty()) {
            return null;
        }
        int size = this.mReusableBitmaps.size();
        for (int i = 0; i < size; i++) {
            if (ImageUtil.canUseForInBitmap(this.mReusableBitmaps.get(i), options)) {
                return this.mReusableBitmaps.remove(i);
            }
        }
        return null;
    }

    private BitmapFactory.Options getReusableOptions(InputStream inputStream) throws Throwable {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inPreferredConfig = Bitmap.Config.ARGB_8888;
        options.inSampleSize = 1;
        options.inJustDecodeBounds = true;
        inputStream.mark(inputStream.available());
        BitmapFactory.decodeStream(inputStream, null, options);
        options.inJustDecodeBounds = false;
        inputStream.reset();
        Bitmap bitmapFromReusableSet = getBitmapFromReusableSet(options);
        options.inMutable = true;
        if (bitmapFromReusableSet != null) {
            options.inBitmap = bitmapFromReusableSet;
        }
        return options;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public Bitmap readBitmap(String str) throws Throwable {
        InputStream open;
        if (this.mSource == 0) {
            open = new BufferedInputStream(new FileInputStream(str));
        } else {
            open = this.mContext.getResources().getAssets().open(str);
        }
        Bitmap decodeStream = BitmapFactory.decodeStream(open, null, getReusableOptions(open));
        open.close();
        return decodeStream;
    }

    private void reset() {
        this.mCacheBitmaps.clear();
        int size = this.mReusableBitmaps.size();
        for (int i = 0; i < size; i++) {
            ImageUtil.recycleBitmap(this.mReusableBitmaps.removeFirst());
        }
        this.mReadFrame = 0;
        this.mIsReadCancel = false;
        this.mIsPlayCancel = false;
        this.mIsCancel = false;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void threadStop() {
        if (this.mIsReadCancel) {
            if (this.mIsPlayCancel || !this.mScheduler.isStarted()) {
                reset();
                this.mRenderer.onStop();
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void update(int i, int i2) {
        Bitmap bitmap = getBitmap(i);
        this.mRenderer.onDraw(i2, bitmap);
        if (bitmap != null) {
            this.mCacheBitmaps.removeFirst();
        }
    }

    public int getFrameIndex() {
        Scheduler scheduler = this.mScheduler;
        if (scheduler == null) {
            return 0;
        }
        return (int) scheduler.getFrameIndex();
    }

    public boolean isPaused() {
        Scheduler scheduler = this.mScheduler;
        return scheduler != null && scheduler.isPaused();
    }

    public boolean isRunning() {
        Scheduler scheduler = this.mScheduler;
        return scheduler != null && scheduler.isRunning();
    }

    public boolean isStarted() {
        Scheduler scheduler = this.mScheduler;
        return scheduler != null && scheduler.isStarted();
    }

    public boolean pause() {
        return this.mScheduler.pause();
    }

    public boolean resume() {
        return this.mScheduler.resume();
    }

    public void seekTo(int i) {
        if (!this.mScheduler.isStarted() || this.mIsPlayCancel) {
            return;
        }
        if (!this.mScheduler.isSeekToComplete()) {
            this.mSeekToIndex = i;
            return;
        }
        synchronized (this.mSeekToLock) {
            this.mReadFrame = i;
            int size = (i - this.mReadFrame) + this.mCacheBitmaps.size();
            if (size <= 0 || size >= this.mCacheFrameNumber) {
                this.mCacheBitmaps.clear();
            } else {
                for (int i2 = 0; i2 < size; i2++) {
                    this.mCacheBitmaps.removeFirst();
                }
            }
            this.mScheduler.seekTo(i, this.mSeekListener);
        }
    }

    public void setDataSource(String[] strArr, long j, int i) {
        this.mPaths = strArr;
        this.mDuration = j;
        this.mFrameCount = i;
    }

    public void start() {
        reset();
        this.mReadThread = new ReadThread();
        Scheduler scheduler = new Scheduler(this.mDuration, this.mFrameCount, new FrameUpdateListener(), new FrameListener());
        this.mScheduler = scheduler;
        scheduler.setSkipFrame(true);
        this.mReadThread.start();
    }

    public void stop() {
        this.mIsCancel = true;
        this.mReadThread.interrupt();
        if (this.mScheduler.isStarted() && !this.mScheduler.isCanceled()) {
            this.mScheduler.stop();
        }
        SchedulerUtil.join(this.mReadThread);
    }
}
