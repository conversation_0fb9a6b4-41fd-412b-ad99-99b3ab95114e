package com.yfve.ici.appcustomviewlib.picture.pictureplayerview;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.Rect;
import android.view.TextureView;
import com.yfve.ici.appcustomviewlib.picture.pictureplayerview.PicturePlayer;
import com.yfve.ici.appcustomviewlib.picture.pictureplayerview.interfaces.OnErrorListener;
import com.yfve.ici.appcustomviewlib.picture.pictureplayerview.interfaces.OnStopListener;
import com.yfve.ici.appcustomviewlib.picture.pictureplayerview.interfaces.OnUpdateListener;

/* loaded from: classes.dex */
public class PictureRenderer implements PicturePlayer.Renderer {
    public static final int HEIGHT = 1;
    public static final int WIDTH = 0;
    public Rect mDstRect;
    public int mHeight;
    public OnErrorListener mOnErrorListener;
    public OnStopListener mOnStopListener;
    public OnUpdateListener mOnUpdateListener;
    public Paint mPaint;
    public float mScale;
    public int mScaleType;
    public Rect mSrcRect;
    public TextureView mTextureView;
    public int mWidth;
    public int state = 0;

    public PictureRenderer(boolean z, boolean z2, boolean z3, int i, TextureView textureView) {
        this.mScaleType = i;
        this.mTextureView = textureView;
        Paint paint = new Paint();
        this.mPaint = paint;
        if (z) {
            paint.setAntiAlias(true);
        }
        if (z2) {
            this.mPaint.setFilterBitmap(true);
        }
        if (z3) {
            this.mPaint.setDither(true);
        }
        this.mSrcRect = new Rect();
        this.mDstRect = new Rect();
    }

    private int calculateLeft() {
        return (getWidth() - this.mWidth) / 2;
    }

    private void calculateScale(int i, int i2) {
        if (this.mScale != 0.0f) {
            return;
        }
        int i3 = this.mScaleType;
        if (i3 == 0) {
            callWidth(i, i2);
        } else if (i3 == 1) {
            callHeight(i, i2);
        } else if (i3 == 2) {
            if (getWidth() * i2 > getHeight() * i) {
                callHeight(i, i2);
            } else {
                callWidth(i, i2);
            }
        } else if (i3 != 3) {
        } else {
            if (getWidth() * i2 > getHeight() * i) {
                callWidth(i, i2);
            } else {
                callHeight(i, i2);
            }
        }
    }

    private int calculateTop() {
        return (getHeight() - this.mHeight) / 2;
    }

    private void callHeight(int i, int i2) {
        float height = getHeight() / i2;
        this.mScale = height;
        this.mWidth = (int) (i * height);
        this.mHeight = getHeight();
        this.state = 1;
    }

    private void callWidth(int i, int i2) {
        this.mScale = getWidth() / i;
        this.mWidth = getWidth();
        this.mHeight = (int) (i2 * this.mScale);
        this.state = 0;
    }

    private int getHeight() {
        return this.mTextureView.getHeight();
    }

    private int getWidth() {
        return this.mTextureView.getWidth();
    }

    public void drawClear() {
        Canvas lockCanvas;
        if (getWidth() == 0 || getHeight() == 0 || (lockCanvas = this.mTextureView.lockCanvas()) == null) {
            return;
        }
        lockCanvas.drawColor(0, PorterDuff.Mode.CLEAR);
        this.mTextureView.unlockCanvasAndPost(lockCanvas);
    }

    @Override // com.yfve.ici.appcustomviewlib.picture.pictureplayerview.PicturePlayer.Renderer
    public void onDraw(int i, Bitmap bitmap) {
        int calculateLeft;
        int i2;
        OnUpdateListener onUpdateListener = this.mOnUpdateListener;
        if (onUpdateListener != null && i != -1) {
            onUpdateListener.onUpdate(i);
        }
        if (bitmap == null || bitmap.isRecycled()) {
            return;
        }
        calculateScale(bitmap.getWidth(), bitmap.getHeight());
        Canvas lockCanvas = this.mTextureView.lockCanvas();
        if (lockCanvas != null) {
            lockCanvas.drawColor(0, PorterDuff.Mode.CLEAR);
            if (this.state == 0) {
                i2 = calculateTop();
                calculateLeft = 0;
            } else {
                calculateLeft = calculateLeft();
                i2 = 0;
            }
            this.mSrcRect.set(0, 0, bitmap.getWidth(), bitmap.getHeight());
            this.mDstRect.set(calculateLeft, i2, this.mWidth + calculateLeft, this.mHeight + i2);
            lockCanvas.drawBitmap(bitmap, this.mSrcRect, this.mDstRect, this.mPaint);
            this.mTextureView.unlockCanvasAndPost(lockCanvas);
        }
    }

    @Override // com.yfve.ici.appcustomviewlib.picture.pictureplayerview.PicturePlayer.Renderer
    public void onError(String str) {
        OnErrorListener onErrorListener = this.mOnErrorListener;
        if (onErrorListener != null) {
            onErrorListener.onError(str);
        }
    }

    @Override // com.yfve.ici.appcustomviewlib.picture.pictureplayerview.PicturePlayer.Renderer
    public void onStop() {
        this.mScale = 0.0f;
        OnStopListener onStopListener = this.mOnStopListener;
        if (onStopListener != null) {
            onStopListener.onStop();
        }
    }

    public void setOnErrorListener(OnErrorListener onErrorListener) {
        this.mOnErrorListener = onErrorListener;
    }

    public void setOnStopListener(OnStopListener onStopListener) {
        this.mOnStopListener = onStopListener;
    }

    public void setOnUpdateListener(OnUpdateListener onUpdateListener) {
        this.mOnUpdateListener = onUpdateListener;
    }

    public void setScaleType(int i) {
        this.mScaleType = i;
    }
}
