package com.yfve.ici.appcustomviewlib.progressbar;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.Shader;
import android.graphics.drawable.Animatable;
import android.graphics.drawable.Drawable;
import android.os.SystemClock;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.Interpolator;
import androidx.annotation.UiThread;
import com.yfve.ici.appcustomviewlib.R;
import java.util.Locale;

/* loaded from: classes.dex */
public class SmoothProgressDrawable extends Drawable implements Animatable {
    public static final long FRAME_DURATION = 16;
    public static final float OFFSET_PER_FRAME = 0.01f;
    public final Rect fBackgroundRect;
    public Drawable mBackgroundDrawable;
    public Rect mBounds;
    public Callbacks mCallbacks;
    public int[] mColors;
    public int mColorsIndex;
    public float mCurrentOffset;
    public int mCurrentSections;
    public boolean mFinishing;
    public float mFinishingOffset;
    public Interpolator mInterpolator;
    public int[] mLinearGradientColors;
    public float[] mLinearGradientPositions;
    public float mMaxOffset;
    public boolean mMirrorMode;
    public boolean mNewTurn;
    public Paint mPaint;
    public boolean mProgressiveStartActivated;
    public float mProgressiveStartSpeed;
    public float mProgressiveStopSpeed;
    public boolean mReversed;
    public boolean mRunning;
    public int mSectionsCount;
    public int mSeparatorLength;
    public float mSpeed;
    public int mStartSection;
    public float mStrokeWidth;
    public final Runnable mUpdater;
    public boolean mUseGradients;

    /* loaded from: classes.dex */
    public static class Builder {
        public Drawable mBackgroundDrawableWhenHidden;
        public int[] mColors;
        public boolean mGenerateBackgroundUsingColors;
        public boolean mGradients;
        public Interpolator mInterpolator;
        public boolean mMirrorMode;
        public Callbacks mOnProgressiveStopEndedListener;
        public boolean mProgressiveStartActivated;
        public float mProgressiveStartSpeed;
        public float mProgressiveStopSpeed;
        public boolean mReversed;
        public int mSectionsCount;
        public float mSpeed;
        public int mStrokeSeparatorLength;
        public float mStrokeWidth;

        public Builder(Context context) {
            this(context, false);
        }

        private void initValues(Context context, boolean z) {
            Resources resources = context.getResources();
            this.mInterpolator = new AccelerateInterpolator();
            if (!z) {
                this.mSectionsCount = resources.getInteger(R.integer.spb_default_sections_count);
                this.mSpeed = Float.parseFloat(resources.getString(R.string.spb_default_speed));
                this.mReversed = resources.getBoolean(R.bool.spb_default_reversed);
                this.mProgressiveStartActivated = resources.getBoolean(R.bool.spb_default_progressiveStart_activated);
                this.mColors = new int[]{resources.getColor(R.color.spb_default_color)};
                this.mStrokeSeparatorLength = resources.getDimensionPixelSize(R.dimen.spb_default_stroke_separator_length);
                this.mStrokeWidth = resources.getDimensionPixelOffset(R.dimen.spb_default_stroke_width);
            } else {
                this.mSectionsCount = 4;
                this.mSpeed = 1.0f;
                this.mReversed = false;
                this.mProgressiveStartActivated = false;
                this.mColors = new int[]{-13388315};
                this.mStrokeSeparatorLength = 4;
                this.mStrokeWidth = 4.0f;
            }
            float f = this.mSpeed;
            this.mProgressiveStartSpeed = f;
            this.mProgressiveStopSpeed = f;
            this.mGradients = false;
        }

        public Builder backgroundDrawable(Drawable drawable) {
            this.mBackgroundDrawableWhenHidden = drawable;
            return this;
        }

        public SmoothProgressDrawable build() {
            if (this.mGenerateBackgroundUsingColors) {
                this.mBackgroundDrawableWhenHidden = SmoothProgressBarUtils.generateDrawableWithColors(this.mColors, this.mStrokeWidth);
            }
            return new SmoothProgressDrawable(this.mInterpolator, this.mSectionsCount, this.mStrokeSeparatorLength, this.mColors, this.mStrokeWidth, this.mSpeed, this.mProgressiveStartSpeed, this.mProgressiveStopSpeed, this.mReversed, this.mMirrorMode, this.mOnProgressiveStopEndedListener, this.mProgressiveStartActivated, this.mBackgroundDrawableWhenHidden, this.mGradients);
        }

        public Builder callbacks(Callbacks callbacks) {
            this.mOnProgressiveStopEndedListener = callbacks;
            return this;
        }

        public Builder color(int i) {
            this.mColors = new int[]{i};
            return this;
        }

        public Builder colors(int[] iArr) {
            SmoothProgressBarUtils.checkColors(iArr);
            this.mColors = iArr;
            return this;
        }

        public Builder generateBackgroundUsingColors() {
            this.mGenerateBackgroundUsingColors = true;
            return this;
        }

        public Builder gradients() {
            return gradients(true);
        }

        public Builder interpolator(Interpolator interpolator) {
            SmoothProgressBarUtils.checkNotNull(interpolator, "Interpolator");
            this.mInterpolator = interpolator;
            return this;
        }

        public Builder mirrorMode(boolean z) {
            this.mMirrorMode = z;
            return this;
        }

        public Builder progressiveStart(boolean z) {
            this.mProgressiveStartActivated = z;
            return this;
        }

        public Builder progressiveStartSpeed(float f) {
            SmoothProgressBarUtils.checkSpeed(f);
            this.mProgressiveStartSpeed = f;
            return this;
        }

        public Builder progressiveStopSpeed(float f) {
            SmoothProgressBarUtils.checkSpeed(f);
            this.mProgressiveStopSpeed = f;
            return this;
        }

        public Builder reversed(boolean z) {
            this.mReversed = z;
            return this;
        }

        public Builder sectionsCount(int i) {
            SmoothProgressBarUtils.checkPositive(i, "Sections count");
            this.mSectionsCount = i;
            return this;
        }

        public Builder separatorLength(int i) {
            SmoothProgressBarUtils.checkPositiveOrZero(i, "Separator length");
            this.mStrokeSeparatorLength = i;
            return this;
        }

        public Builder speed(float f) {
            SmoothProgressBarUtils.checkSpeed(f);
            this.mSpeed = f;
            return this;
        }

        public Builder strokeWidth(float f) {
            SmoothProgressBarUtils.checkPositiveOrZero(f, "Width");
            this.mStrokeWidth = f;
            return this;
        }

        public Builder(Context context, boolean z) {
            initValues(context, z);
        }

        public Builder gradients(boolean z) {
            this.mGradients = z;
            return this;
        }
    }

    /* loaded from: classes.dex */
    public interface Callbacks {
        void onStart();

        void onStop();
    }

    private void checkColorIndex(int i) {
        if (i < 0 || i >= this.mColors.length) {
            throw new IllegalArgumentException(String.format(Locale.US, "Index %d not valid", Integer.valueOf(i)));
        }
    }

    @UiThread
    private int decrementColor(int i) {
        int i2 = i - 1;
        return i2 < 0 ? this.mColors.length - 1 : i2;
    }

    @UiThread
    private void drawBackground(Canvas canvas, float f, float f2) {
        int save = canvas.save();
        canvas.clipRect(f, (int) ((canvas.getHeight() - this.mStrokeWidth) / 2.0f), f2, (int) ((canvas.getHeight() + this.mStrokeWidth) / 2.0f));
        this.mBackgroundDrawable.draw(canvas);
        canvas.restoreToCount(save);
    }

    @UiThread
    private void drawBackgroundIfNeeded(Canvas canvas, float f, float f2) {
        if (this.mBackgroundDrawable == null) {
            return;
        }
        this.fBackgroundRect.top = (int) ((canvas.getHeight() - this.mStrokeWidth) / 2.0f);
        this.fBackgroundRect.bottom = (int) ((canvas.getHeight() + this.mStrokeWidth) / 2.0f);
        Rect rect = this.fBackgroundRect;
        rect.left = 0;
        rect.right = this.mMirrorMode ? canvas.getWidth() / 2 : canvas.getWidth();
        this.mBackgroundDrawable.setBounds(this.fBackgroundRect);
        if (!isRunning()) {
            if (this.mMirrorMode) {
                canvas.save();
                canvas.translate(canvas.getWidth() / 2, 0.0f);
                drawBackground(canvas, 0.0f, this.fBackgroundRect.width());
                canvas.scale(-1.0f, 1.0f);
                drawBackground(canvas, 0.0f, this.fBackgroundRect.width());
                canvas.restore();
                return;
            }
            drawBackground(canvas, 0.0f, this.fBackgroundRect.width());
        } else if (isFinishing() || isStarting()) {
            if (f > f2) {
                f2 = f;
                f = f2;
            }
            if (f > 0.0f) {
                if (this.mMirrorMode) {
                    canvas.save();
                    canvas.translate(canvas.getWidth() / 2, 0.0f);
                    if (this.mReversed) {
                        drawBackground(canvas, 0.0f, f);
                        canvas.scale(-1.0f, 1.0f);
                        drawBackground(canvas, 0.0f, f);
                    } else {
                        drawBackground(canvas, (canvas.getWidth() / 2) - f, canvas.getWidth() / 2);
                        canvas.scale(-1.0f, 1.0f);
                        drawBackground(canvas, (canvas.getWidth() / 2) - f, canvas.getWidth() / 2);
                    }
                    canvas.restore();
                } else {
                    drawBackground(canvas, 0.0f, f);
                }
            }
            if (f2 <= canvas.getWidth()) {
                if (this.mMirrorMode) {
                    canvas.save();
                    canvas.translate(canvas.getWidth() / 2, 0.0f);
                    if (this.mReversed) {
                        drawBackground(canvas, f2, canvas.getWidth() / 2);
                        canvas.scale(-1.0f, 1.0f);
                        drawBackground(canvas, f2, canvas.getWidth() / 2);
                    } else {
                        drawBackground(canvas, 0.0f, (canvas.getWidth() / 2) - f2);
                        canvas.scale(-1.0f, 1.0f);
                        drawBackground(canvas, 0.0f, (canvas.getWidth() / 2) - f2);
                    }
                    canvas.restore();
                    return;
                }
                drawBackground(canvas, f2, canvas.getWidth());
            }
        }
    }

    @UiThread
    private void drawLine(Canvas canvas, int i, float f, float f2, float f3, float f4, int i2) {
        this.mPaint.setColor(this.mColors[i2]);
        if (!this.mMirrorMode) {
            Path path = new Path();
            path.moveTo(f + 2.0f, 0.0f);
            path.lineTo(f3, 0.0f);
            float f5 = f3 - 2.0f;
            path.lineTo(f5 >= 0.0f ? f5 : 0.0f, 4.0f);
            path.lineTo(f, 4.0f);
            path.close();
            canvas.drawPath(path, this.mPaint);
        } else if (this.mReversed) {
            float f6 = i;
            canvas.drawLine(f6 + f, f2, f6 + f3, f4, this.mPaint);
            canvas.drawLine(f6 - f, f2, f6 - f3, f4, this.mPaint);
        } else {
            canvas.drawLine(f, f2, f3, f4, this.mPaint);
            float f7 = i * 2;
            canvas.drawLine(f7 - f, f2, f7 - f3, f4, this.mPaint);
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:35:0x00f6  */
    /* JADX WARN: Removed duplicated region for block: B:36:0x00f9  */
    @androidx.annotation.UiThread
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private void drawStrokes(android.graphics.Canvas r24) {
        /*
            Method dump skipped, instructions count: 275
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.yfve.ici.appcustomviewlib.progressbar.SmoothProgressDrawable.drawStrokes(android.graphics.Canvas):void");
    }

    @UiThread
    private int incrementColor(int i) {
        int i2 = i + 1;
        if (i2 >= this.mColors.length) {
            return 0;
        }
        return i2;
    }

    @UiThread
    private void prepareGradient() {
        int[] iArr;
        int i;
        int i2;
        float f = 1.0f / this.mSectionsCount;
        int i3 = this.mColorsIndex;
        float[] fArr = this.mLinearGradientPositions;
        int i4 = 0;
        fArr[0] = 0.0f;
        fArr[fArr.length - 1] = 1.0f;
        int i5 = i3 - 1;
        if (i5 < 0) {
            i5 += this.mColors.length;
        }
        this.mLinearGradientColors[0] = this.mColors[i5];
        while (i4 < this.mSectionsCount) {
            float interpolation = this.mInterpolator.getInterpolation((i4 * f) + this.mCurrentOffset);
            i4++;
            this.mLinearGradientPositions[i4] = interpolation;
            int[] iArr2 = this.mLinearGradientColors;
            int[] iArr3 = this.mColors;
            iArr2[i4] = iArr3[i3];
            i3 = (i3 + 1) % iArr3.length;
        }
        this.mLinearGradientColors[iArr.length - 1] = this.mColors[i3];
        if (this.mReversed && this.mMirrorMode) {
            Rect rect = this.mBounds;
            i = Math.abs(rect.left - rect.right) / 2;
        } else {
            i = this.mBounds.left;
        }
        float f2 = i;
        if (!this.mMirrorMode) {
            i2 = this.mBounds.right;
        } else if (this.mReversed) {
            i2 = this.mBounds.left;
        } else {
            Rect rect2 = this.mBounds;
            i2 = Math.abs(rect2.left - rect2.right) / 2;
        }
        this.mPaint.setShader(new LinearGradient(f2, this.mBounds.centerY() - (this.mStrokeWidth / 2.0f), i2, (this.mStrokeWidth / 2.0f) + this.mBounds.centerY(), this.mLinearGradientColors, this.mLinearGradientPositions, this.mMirrorMode ? Shader.TileMode.MIRROR : Shader.TileMode.CLAMP));
    }

    private void refreshLinearGradientOptions() {
        if (this.mUseGradients) {
            int i = this.mSectionsCount;
            this.mLinearGradientColors = new int[i + 2];
            this.mLinearGradientPositions = new float[i + 2];
            return;
        }
        this.mPaint.setShader(null);
        this.mLinearGradientColors = null;
        this.mLinearGradientPositions = null;
    }

    @UiThread
    private void resetProgressiveStart(int i) {
        checkColorIndex(i);
        this.mCurrentOffset = 0.0f;
        this.mFinishing = false;
        this.mFinishingOffset = 0.0f;
        this.mStartSection = 0;
        this.mCurrentSections = 0;
        this.mColorsIndex = i;
    }

    @Override // android.graphics.drawable.Drawable
    public void draw(Canvas canvas) {
        Rect bounds = getBounds();
        this.mBounds = bounds;
        canvas.clipRect(bounds);
        if (this.mNewTurn) {
            this.mColorsIndex = decrementColor(this.mColorsIndex);
            this.mNewTurn = false;
            if (isFinishing()) {
                int i = this.mStartSection + 1;
                this.mStartSection = i;
                if (i > this.mSectionsCount) {
                    stop();
                    return;
                }
            }
            int i2 = this.mCurrentSections;
            if (i2 < this.mSectionsCount) {
                this.mCurrentSections = i2 + 1;
            }
        }
        if (this.mUseGradients) {
            prepareGradient();
        }
        drawStrokes(canvas);
    }

    public Drawable getBackgroundDrawable() {
        return this.mBackgroundDrawable;
    }

    public int[] getColors() {
        return this.mColors;
    }

    @Override // android.graphics.drawable.Drawable
    public int getOpacity() {
        return -2;
    }

    public float getStrokeWidth() {
        return this.mStrokeWidth;
    }

    public boolean isFinishing() {
        return this.mFinishing;
    }

    @Override // android.graphics.drawable.Animatable
    public boolean isRunning() {
        return this.mRunning;
    }

    public boolean isStarting() {
        return this.mCurrentSections < this.mSectionsCount;
    }

    @UiThread
    public void progressiveStart() {
        progressiveStart(0);
    }

    @UiThread
    public void progressiveStop() {
        this.mFinishing = true;
        this.mStartSection = 0;
    }

    @Override // android.graphics.drawable.Drawable
    public void scheduleSelf(Runnable runnable, long j) {
        this.mRunning = true;
        super.scheduleSelf(runnable, j);
    }

    @Override // android.graphics.drawable.Drawable
    public void setAlpha(int i) {
        this.mPaint.setAlpha(i);
    }

    @UiThread
    public void setBackgroundDrawable(Drawable drawable) {
        if (this.mBackgroundDrawable == drawable) {
            return;
        }
        this.mBackgroundDrawable = drawable;
        invalidateSelf();
    }

    public void setCallbacks(Callbacks callbacks) {
        this.mCallbacks = callbacks;
    }

    @UiThread
    public void setColor(int i) {
        setColors(new int[]{i});
    }

    @Override // android.graphics.drawable.Drawable
    public void setColorFilter(ColorFilter colorFilter) {
        this.mPaint.setColorFilter(colorFilter);
    }

    @UiThread
    public void setColors(int[] iArr) {
        if (iArr != null && iArr.length != 0) {
            this.mColorsIndex = 0;
            this.mColors = iArr;
            refreshLinearGradientOptions();
            invalidateSelf();
            return;
        }
        throw new IllegalArgumentException("Colors cannot be null or empty");
    }

    @UiThread
    public void setInterpolator(Interpolator interpolator) {
        if (interpolator != null) {
            this.mInterpolator = interpolator;
            invalidateSelf();
            return;
        }
        throw new IllegalArgumentException("Interpolator cannot be null");
    }

    @UiThread
    public void setMirrorMode(boolean z) {
        if (this.mMirrorMode == z) {
            return;
        }
        this.mMirrorMode = z;
        invalidateSelf();
    }

    @UiThread
    public void setProgressiveStartActivated(boolean z) {
        this.mProgressiveStartActivated = z;
    }

    @UiThread
    public void setProgressiveStartSpeed(float f) {
        if (f >= 0.0f) {
            this.mProgressiveStartSpeed = f;
            invalidateSelf();
            return;
        }
        throw new IllegalArgumentException("SpeedProgressiveStart must be >= 0");
    }

    @UiThread
    public void setProgressiveStopSpeed(float f) {
        if (f >= 0.0f) {
            this.mProgressiveStopSpeed = f;
            invalidateSelf();
            return;
        }
        throw new IllegalArgumentException("SpeedProgressiveStop must be >= 0");
    }

    @UiThread
    public void setReversed(boolean z) {
        if (this.mReversed == z) {
            return;
        }
        this.mReversed = z;
        invalidateSelf();
    }

    @UiThread
    public void setSectionsCount(int i) {
        if (i > 0) {
            this.mSectionsCount = i;
            float f = 1.0f / i;
            this.mMaxOffset = f;
            this.mCurrentOffset %= f;
            refreshLinearGradientOptions();
            invalidateSelf();
            return;
        }
        throw new IllegalArgumentException("SectionsCount must be > 0");
    }

    @UiThread
    public void setSeparatorLength(int i) {
        if (i >= 0) {
            this.mSeparatorLength = i;
            invalidateSelf();
            return;
        }
        throw new IllegalArgumentException("SeparatorLength must be >= 0");
    }

    @UiThread
    public void setSpeed(float f) {
        if (f >= 0.0f) {
            this.mSpeed = f;
            invalidateSelf();
            return;
        }
        throw new IllegalArgumentException("Speed must be >= 0");
    }

    @UiThread
    public void setStrokeWidth(float f) {
        if (f >= 0.0f) {
            this.mPaint.setStrokeWidth(f);
            invalidateSelf();
            return;
        }
        throw new IllegalArgumentException("The strokeWidth must be >= 0");
    }

    @UiThread
    public void setUseGradients(boolean z) {
        if (this.mUseGradients == z) {
            return;
        }
        this.mUseGradients = z;
        refreshLinearGradientOptions();
        invalidateSelf();
    }

    @Override // android.graphics.drawable.Animatable
    public void start() {
        if (this.mProgressiveStartActivated) {
            resetProgressiveStart(0);
        }
        if (isRunning()) {
            return;
        }
        Callbacks callbacks = this.mCallbacks;
        if (callbacks != null) {
            callbacks.onStart();
        }
        scheduleSelf(this.mUpdater, SystemClock.uptimeMillis() + 16);
        invalidateSelf();
    }

    @Override // android.graphics.drawable.Animatable
    public void stop() {
        if (isRunning()) {
            Callbacks callbacks = this.mCallbacks;
            if (callbacks != null) {
                callbacks.onStop();
            }
            this.mRunning = false;
            unscheduleSelf(this.mUpdater);
        }
    }

    public SmoothProgressDrawable(Interpolator interpolator, int i, int i2, int[] iArr, float f, float f2, float f3, float f4, boolean z, boolean z2, Callbacks callbacks, boolean z3, Drawable drawable, boolean z4) {
        this.fBackgroundRect = new Rect();
        this.mUpdater = new Runnable() { // from class: com.yfve.ici.appcustomviewlib.progressbar.SmoothProgressDrawable.1
            @Override // java.lang.Runnable
            public void run() {
                if (SmoothProgressDrawable.this.isFinishing()) {
                    SmoothProgressDrawable smoothProgressDrawable = SmoothProgressDrawable.this;
                    smoothProgressDrawable.mFinishingOffset = (SmoothProgressDrawable.this.mProgressiveStopSpeed * 0.01f) + smoothProgressDrawable.mFinishingOffset;
                    SmoothProgressDrawable smoothProgressDrawable2 = SmoothProgressDrawable.this;
                    smoothProgressDrawable2.mCurrentOffset = (SmoothProgressDrawable.this.mProgressiveStopSpeed * 0.01f) + smoothProgressDrawable2.mCurrentOffset;
                    if (SmoothProgressDrawable.this.mFinishingOffset >= 1.0f) {
                        SmoothProgressDrawable.this.stop();
                    }
                } else if (SmoothProgressDrawable.this.isStarting()) {
                    SmoothProgressDrawable smoothProgressDrawable3 = SmoothProgressDrawable.this;
                    smoothProgressDrawable3.mCurrentOffset = (SmoothProgressDrawable.this.mProgressiveStartSpeed * 0.01f) + smoothProgressDrawable3.mCurrentOffset;
                } else {
                    SmoothProgressDrawable smoothProgressDrawable4 = SmoothProgressDrawable.this;
                    smoothProgressDrawable4.mCurrentOffset = (SmoothProgressDrawable.this.mSpeed * 0.01f) + smoothProgressDrawable4.mCurrentOffset;
                }
                if (SmoothProgressDrawable.this.mCurrentOffset >= SmoothProgressDrawable.this.mMaxOffset) {
                    SmoothProgressDrawable.this.mNewTurn = true;
                    SmoothProgressDrawable.this.mCurrentOffset -= SmoothProgressDrawable.this.mMaxOffset;
                }
                if (SmoothProgressDrawable.this.isRunning()) {
                    SmoothProgressDrawable smoothProgressDrawable5 = SmoothProgressDrawable.this;
                    smoothProgressDrawable5.scheduleSelf(smoothProgressDrawable5.mUpdater, SystemClock.uptimeMillis() + 16);
                }
                SmoothProgressDrawable.this.invalidateSelf();
            }
        };
        this.mRunning = false;
        this.mInterpolator = interpolator;
        this.mSectionsCount = i;
        this.mStartSection = 0;
        this.mCurrentSections = i;
        this.mSeparatorLength = i2;
        this.mSpeed = f2;
        this.mProgressiveStartSpeed = f3;
        this.mProgressiveStopSpeed = f4;
        this.mReversed = z;
        this.mColors = iArr;
        this.mColorsIndex = 0;
        this.mMirrorMode = z2;
        this.mFinishing = false;
        this.mBackgroundDrawable = drawable;
        this.mStrokeWidth = f;
        this.mMaxOffset = 1.0f / i;
        Paint paint = new Paint();
        this.mPaint = paint;
        paint.setStrokeWidth(f);
        this.mPaint.setStyle(Paint.Style.FILL);
        this.mPaint.setDither(false);
        this.mPaint.setAntiAlias(false);
        this.mProgressiveStartActivated = z3;
        this.mCallbacks = callbacks;
        this.mUseGradients = z4;
        refreshLinearGradientOptions();
    }

    @UiThread
    public void progressiveStart(int i) {
        resetProgressiveStart(i);
        start();
    }
}
