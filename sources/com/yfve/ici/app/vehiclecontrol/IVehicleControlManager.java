package com.yfve.ici.app.vehiclecontrol;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface IVehicleControlManager extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IVehicleControlManager {
        @Override // com.yfve.ici.app.vehiclecontrol.IVehicleControlManager
        public void OpenVehicleControl() throws RemoteException {
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IVehicleControlManager {
        public static final String DESCRIPTOR = "com.yfve.ici.app.vehiclecontrol.IVehicleControlManager";
        public static final int TRANSACTION_OpenVehicleControl = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements IVehicleControlManager {
            public static IVehicleControlManager sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // com.yfve.ici.app.vehiclecontrol.IVehicleControlManager
            public void OpenVehicleControl() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.vehiclecontrol.IVehicleControlManager");
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().OpenVehicleControl();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return "com.yfve.ici.app.vehiclecontrol.IVehicleControlManager";
            }
        }

        public Stub() {
            attachInterface(this, "com.yfve.ici.app.vehiclecontrol.IVehicleControlManager");
        }

        public static IVehicleControlManager asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface("com.yfve.ici.app.vehiclecontrol.IVehicleControlManager");
            if (queryLocalInterface != null && (queryLocalInterface instanceof IVehicleControlManager)) {
                return (IVehicleControlManager) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IVehicleControlManager getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IVehicleControlManager iVehicleControlManager) {
            if (Proxy.sDefaultImpl != null || iVehicleControlManager == null) {
                return false;
            }
            Proxy.sDefaultImpl = iVehicleControlManager;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString("com.yfve.ici.app.vehiclecontrol.IVehicleControlManager");
                return true;
            }
            parcel.enforceInterface("com.yfve.ici.app.vehiclecontrol.IVehicleControlManager");
            OpenVehicleControl();
            parcel2.writeNoException();
            return true;
        }
    }

    void OpenVehicleControl() throws RemoteException;
}
