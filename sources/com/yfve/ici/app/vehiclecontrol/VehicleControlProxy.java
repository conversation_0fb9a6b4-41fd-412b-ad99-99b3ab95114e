package com.yfve.ici.app.vehiclecontrol;

import android.os.RemoteException;
import android.util.Log;
import com.yfve.ici.service.base.BaseProxy;
import com.yfve.ici.service.contanst.ServiceConstant;

/* loaded from: classes.dex */
public class VehicleControlProxy extends BaseProxy<IVehicleControlManager> {
    public static final String TAG = "VehicleControl";
    public static VehicleControlProxy VEHICLECONTROL_PROXY;

    public static void d(String str, String str2) {
        Log.d(str, getFileLineMethod() + str2);
    }

    public static void e(String str, String str2) {
        Log.e(str, getFileLineMethod() + str2);
    }

    public static String getFileLineMethod() {
        StackTraceElement stackTraceElement = new Exception().getStackTrace()[2];
        StringBuffer stringBuffer = new StringBuffer("[");
        stringBuffer.append(stackTraceElement.getFileName());
        stringBuffer.append(":");
        stringBuffer.append(stackTraceElement.getLineNumber());
        stringBuffer.append("] ");
        return stringBuffer.toString();
    }

    public static synchronized VehicleControlProxy getInstnce() {
        VehicleControlProxy vehicleControlProxy;
        synchronized (VehicleControlProxy.class) {
            if (VEHICLECONTROL_PROXY == null) {
                VEHICLECONTROL_PROXY = new VehicleControlProxy();
            }
            vehicleControlProxy = VEHICLECONTROL_PROXY;
        }
        return vehicleControlProxy;
    }

    public static void i(String str, String str2) {
        Log.i(str, getFileLineMethod() + str2);
    }

    public void OpenVehicleControl() {
        d(TAG, "OpenVehicleControl --- start");
        try {
            if (!isAvailable()) {
                e(TAG, "OpenVehicleControl~~mInterface is null");
            } else {
                ((IVehicleControlManager) this.mInterface).OpenVehicleControl();
            }
        } catch (RemoteException e) {
            i(TAG, e.toString());
        } catch (Exception e2) {
            i(TAG, e2.toString());
        }
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public String getServiceName() {
        return ServiceConstant.VEHICLECONTROL_BINDER_NAME;
    }
}
