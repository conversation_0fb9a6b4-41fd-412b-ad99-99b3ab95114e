package com.yfve.ici.app.carlife;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;
import b.a.b.a.a;
import com.yfve.ici.app.carlife.IPropertyObserver;
import com.yfve.ici.service.base.BaseProxy;
import com.yfve.ici.service.contanst.ServiceConstant;
import com.yfve.ici.service.servicemgr.IServiceConnChangeListener;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class CarlifeProxy extends BaseProxy<ICarlife> {
    @Deprecated
    public static final int CARLIFE_MEDIA_STATE_PAUSE = -5177342;
    @Deprecated
    public static final int CARLIFE_MEDIA_STATE_PLAY = -5177343;
    public static final int CARLIFE_STATE_CASTING = 4;
    public static final int CARLIFE_STATE_CONNECTED = 3;
    public static final int CARLIFE_STATE_CONNECTING = 2;
    public static final int CARLIFE_STATE_DISCONNECTED = 1;
    public static final String EXTRA_DEV_INFO_NAME = "EXTRA_DEV_INFO_NAME";
    public static final String EXTRA_DEV_INFO_SERIAL = "EXTRA_DEV_INFO_SERIAL";
    public static final int MODULE_ID_MEDIA = -5177344;
    public static final int MODULE_ID_NAVI = -5046272;
    public static final int MODULE_ID_VR = -5111808;
    public static final int MODULE_MEDIA_STATUS_PAUSE = -5177342;
    public static final int MODULE_MEDIA_STATUS_PLAY = -5177343;
    public static final int MODULE_NAVI_STATUS_START = -5046270;
    public static final int MODULE_NAVI_STATUS_STOP = -5046271;
    public static final int MODULE_VR_STATUS_IDLE = -5111807;
    public static final int MODULE_VR_STATUS_STOP = -5111805;
    public static final int MODULE_VR_STATUS_WAKEUP = -5111806;
    public static final String TAG = "CarlifeProxy";
    public static CarlifeProxy mInstance;
    public List<StateChangeListener> mCarlifeStateObs = Collections.synchronizedList(new ArrayList());
    public List<MediaStatusObserver> mCarlifeMediaStatusObs = Collections.synchronizedList(new ArrayList());
    public List<ModuleStatusObserver> mModuleStatusObs = Collections.synchronizedList(new ArrayList());
    public List<TurnByTurnObserver> mTbtObs = Collections.synchronizedList(new ArrayList());
    public List<ConnectionEventObserver> mConnEvtObs = Collections.synchronizedList(new ArrayList());
    public IServiceConnChangeListener mSrvConnChangeListener = new IServiceConnChangeListener() { // from class: com.yfve.ici.app.carlife.CarlifeProxy.1
        @Override // com.yfve.ici.service.servicemgr.IServiceConnChangeListener
        public void onServiceConnChange(boolean z) {
            if (z) {
                Log.i(CarlifeProxy.TAG, "onServiceConnChange connected");
                CarlifeProxy.this.subscribeServiceEvent();
                return;
            }
            Log.i(CarlifeProxy.TAG, "onServiceConnChange disconnected");
            CarlifeProxy.this.unsubscribeServiceEvent();
        }
    };
    public IPropertyObserver mObserver = new IPropertyObserver.Stub() { // from class: com.yfve.ici.app.carlife.CarlifeProxy.2
        @Override // com.yfve.ici.app.carlife.IPropertyObserver
        public void onPropertyChanged(PropertyValue propertyValue) {
            try {
                CarlifeProxy.this.handlePropertyChanged(propertyValue);
            } catch (Exception e) {
                Log.e(CarlifeProxy.TAG, Log.getStackTraceString(e));
                CarlifeProxy.this.mHandler.post(new Runnable() { // from class: com.yfve.ici.app.carlife.CarlifeProxy.2.1
                    @Override // java.lang.Runnable
                    public void run() {
                        throw e;
                    }
                });
            }
        }
    };
    public Handler mHandler = new Handler(Looper.getMainLooper());

    /* loaded from: classes.dex */
    public interface ConnectionEventObserver {
        void onEvent(int i, int i2, int i3, Object obj);
    }

    /* loaded from: classes.dex */
    public interface MediaStatusObserver {
        void onMediaID3Changed(String str);

        void onProgressChanged(int i);
    }

    /* loaded from: classes.dex */
    public interface ModuleStatusObserver {
        void onModuleStatusChange(int i, int i2);
    }

    /* loaded from: classes.dex */
    public interface StateChangeListener {
        void onStateChanged(int i);
    }

    /* loaded from: classes.dex */
    public interface TurnByTurnObserver {
        void onTurnByTurnInfo(String str);
    }

    public CarlifeProxy() {
        subscribeServiceEvent();
        registerConnChangeListener(this.mSrvConnChangeListener);
    }

    private int carlifeMediaCtrl(int i) {
        if (!isAvailable()) {
            Log.e(TAG, "carlifeMediaCtrl(" + i + ") fail, remote service not connect");
            return BaseProxy.RET_ERR_REMOTE_SERVICE_UNCONNECTED;
        }
        try {
            return ((ICarlife) this.mInterface).setProperty(new PropertyValue(i)) == -5308415 ? Integer.MIN_VALUE : -2147418113;
        } catch (RemoteException e) {
            StringBuilder f = a.f("carlifeMediaCtrl(", i, ") fail. ");
            f.append(Log.getStackTraceString(e));
            Log.e(TAG, f.toString());
            return -2147418113;
        }
    }

    public static CarlifeProxy getInstance() {
        if (mInstance == null) {
            synchronized (CarlifeProxy.class) {
                if (mInstance == null) {
                    mInstance = new CarlifeProxy();
                }
            }
        }
        return mInstance;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void handlePropertyChanged(PropertyValue propertyValue) {
        if (propertyValue == null) {
            return;
        }
        switch (propertyValue.getPropertyId()) {
            case Property.PROPID_EVT_CARLIFE_CONNECT_STATE_CHANGED /* -6029311 */:
                int state = propertyValue.getState();
                if (state == 1 || state == 2 || state == 3 || state == 4) {
                    notifyCarlifeStateChange(state);
                    return;
                }
                return;
            case Property.PROPID_EVT_CARLIFE_MEDIA_STATE_CHANGED /* -6029310 */:
            default:
                return;
            case Property.PROPID_EVT_CARLIFE_MEDIA_INFO_CHANGED /* -6029309 */:
                List<MediaStatusObserver> list = this.mCarlifeMediaStatusObs;
                if (list == null || list.isEmpty()) {
                    return;
                }
                String str = (String) propertyValue.getValue();
                if (TextUtils.isEmpty(str)) {
                    Log.e(TAG, "PROPID_EVT_CARLIFE_MEDIA_INFO_CHANGED: empty json string");
                    return;
                }
                try {
                    new JSONObject(str);
                    notifyCarlifeMediaID3Change(str);
                    return;
                } catch (Exception unused) {
                    Log.e(TAG, "PROPID_EVT_CARLIFE_MEDIA_INFO_CHANGED: invalid json string");
                    return;
                }
            case Property.PROPID_EVT_CARLIFE_MEDIA_PROGRESS_CHANGED /* -6029308 */:
                List<MediaStatusObserver> list2 = this.mCarlifeMediaStatusObs;
                if (list2 == null || list2.isEmpty()) {
                    return;
                }
                int intVal = propertyValue.getIntVal();
                if (intVal < 0) {
                    Log.e(TAG, "PROPID_EVT_CARLIFE_MEDIA_PROGRESS_CHANGED: invalid progress value");
                    return;
                } else {
                    notifyCarlifeMediaProgressChange(intVal);
                    return;
                }
            case Property.PROPID_EVT_CARLIFE_MODULE_STATUS_CHANGED /* -6029307 */:
                List<ModuleStatusObserver> list3 = this.mModuleStatusObs;
                if (list3 == null || list3.isEmpty()) {
                    return;
                }
                notifyModuleStatus(propertyValue.getIntVal(), propertyValue.getState());
                return;
            case Property.PROPID_EVT_CARLIFE_TBT_INFO /* -6029306 */:
                List<TurnByTurnObserver> list4 = this.mTbtObs;
                if (list4 == null || list4.isEmpty()) {
                    return;
                }
                String str2 = (String) propertyValue.getValue();
                if (TextUtils.isEmpty(str2)) {
                    Log.e(TAG, "PROPID_EVT_CARLIFE_TBT_INFO: empty json string");
                    return;
                }
                try {
                    new JSONObject(str2);
                    notifyTbtInfo(str2);
                    return;
                } catch (Exception unused2) {
                    Log.e(TAG, "PROPID_EVT_CARLIFE_TBT_INFO: invalid json string");
                    return;
                }
            case Property.PROPID_EVT_CARLIFE_CONNECTION_EVENT /* -6029305 */:
                List<ConnectionEventObserver> list5 = this.mConnEvtObs;
                if (list5 == null || list5.isEmpty()) {
                    return;
                }
                notifyConnectionEvents(propertyValue.getState(), propertyValue.getArea(), propertyValue.getIntVal(), propertyValue.getValue());
                return;
        }
    }

    private void notifyCarlifeMediaID3Change(String str) {
        if (TextUtils.isEmpty(str)) {
            return;
        }
        for (MediaStatusObserver mediaStatusObserver : this.mCarlifeMediaStatusObs) {
            mediaStatusObserver.onMediaID3Changed(str);
        }
    }

    private void notifyCarlifeMediaProgressChange(int i) {
        for (MediaStatusObserver mediaStatusObserver : this.mCarlifeMediaStatusObs) {
            mediaStatusObserver.onProgressChanged(i);
        }
    }

    private void notifyCarlifeStateChange(int i) {
        if (this.mCarlifeStateObs.isEmpty()) {
            return;
        }
        Iterator it = new ArrayList(this.mCarlifeStateObs).iterator();
        while (it.hasNext()) {
            try {
                ((StateChangeListener) it.next()).onStateChanged(i);
            } catch (Exception e) {
                StringBuilder e2 = a.e("notifyCarlifeStateChange fail:");
                e2.append(Log.getStackTraceString(e));
                Log.e(TAG, e2.toString());
            }
        }
    }

    private void notifyModuleStatus(int i, int i2) {
        for (ModuleStatusObserver moduleStatusObserver : this.mModuleStatusObs) {
            moduleStatusObserver.onModuleStatusChange(i, i2);
        }
    }

    private void notifyTbtInfo(String str) {
        for (TurnByTurnObserver turnByTurnObserver : this.mTbtObs) {
            turnByTurnObserver.onTurnByTurnInfo(str);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void subscribeServiceEvent() {
        Log.i(TAG, "-- subscribeServiceEvent");
        try {
            if (isAvailable()) {
                ((ICarlife) this.mInterface).subscribe(-1, this.mObserver);
            }
        } catch (Exception e) {
            Log.e(TAG, Log.getStackTraceString(e));
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void unsubscribeServiceEvent() {
        Log.i(TAG, "-- unsubscribeServiceEvent");
        try {
            if (isAvailable()) {
                ((ICarlife) this.mInterface).unsubscribe(-1, this.mObserver);
            }
        } catch (Exception e) {
            Log.e(TAG, Log.getStackTraceString(e));
        }
    }

    public int carlifeMediaNext() {
        return carlifeMediaCtrl(Property.PROPID_CTRL_MEDIA_NEXT);
    }

    public int carlifeMediaPause() {
        return carlifeMediaCtrl(Property.PROPID_CTRL_MEDIA_PAUSE);
    }

    public int carlifeMediaPlay() {
        return carlifeMediaCtrl(Property.PROPID_CTRL_MEDIA_PLAY);
    }

    public int carlifeMediaPrev() {
        return carlifeMediaCtrl(Property.PROPID_CTRL_MEDIA_PREV);
    }

    public void connect(String str, int i) {
        if (TextUtils.isEmpty(str)) {
            Log.e(TAG, "invalid device serial:" + str);
        } else if (!isAvailable()) {
            Log.e(TAG, "carlife service not connected");
        } else {
            try {
                ((ICarlife) this.mInterface).setProperty(new PropertyValue(Property.PROPID_CTRL_CONNECT_CARLIFE, 0, i, str));
            } catch (RemoteException e) {
                StringBuilder e2 = a.e("connectCarlife fail. ");
                e2.append(Log.getStackTraceString(e));
                Log.e(TAG, e2.toString());
            }
        }
    }

    public void exitCarlife() {
        if (!isAvailable()) {
            Log.e(TAG, "carlife service not connected");
            return;
        }
        try {
            ((ICarlife) this.mInterface).setProperty(new PropertyValue((int) Property.PROPID_CTRL_EXIT_CARLIFE, 0));
        } catch (RemoteException e) {
            StringBuilder e2 = a.e("exit carlife fail. ");
            e2.append(Log.getStackTraceString(e));
            Log.e(TAG, e2.toString());
        }
    }

    public void exitCarlifeNavi() {
        if (!isAvailable()) {
            Log.e(TAG, "carlife service not connected");
            return;
        }
        try {
            ((ICarlife) this.mInterface).setProperty(new PropertyValue((int) Property.PROPID_CTRL_EXIT_CARLIFE_NAVI, 0));
        } catch (RemoteException e) {
            StringBuilder e2 = a.e("exit carlife navi fail. ");
            e2.append(Log.getStackTraceString(e));
            Log.e(TAG, e2.toString());
        }
    }

    public int getCarlifeConnectState() {
        if (!isAvailable()) {
            Log.e(TAG, "getCarlifeConnectState fail, remote service not connect");
            return BaseProxy.RET_ERR_REMOTE_SERVICE_UNCONNECTED;
        }
        PropertyValue propertyValue = null;
        try {
            propertyValue = ((ICarlife) this.mInterface).getProperty(Property.PROPID_GET_CARLIFE_STATE);
        } catch (RemoteException e) {
            StringBuilder e2 = a.e("getCarlifeState fail. ");
            e2.append(Log.getStackTraceString(e));
            Log.e(TAG, e2.toString());
        }
        if (propertyValue == null) {
            Log.e(TAG, "getCarlifeState fail. service return invalid PropertyValue");
            return -2147418113;
        }
        return propertyValue.getState();
    }

    public String getCarlifeMediaInfo() {
        PropertyValue propertyValue;
        if (!isAvailable()) {
            Log.e(TAG, "getCarlifeMediaInfo fail, remote service not connect");
            return null;
        }
        try {
            propertyValue = ((ICarlife) this.mInterface).getProperty(Property.PROPID_GET_CARLIFE_MEDIA_INFO);
        } catch (RemoteException e) {
            StringBuilder e2 = a.e("getCarlifeMediaInfo fail. ");
            e2.append(Log.getStackTraceString(e));
            Log.e(TAG, e2.toString());
            propertyValue = null;
        }
        if (propertyValue == null) {
            Log.d(TAG, "getCarlifeMediaInfo fail. service return invalid PropertyValue");
            return null;
        }
        String str = (String) propertyValue.getValue();
        if (TextUtils.isEmpty(str)) {
            Log.e(TAG, "getCarlifeMediaInfo fail: empty json string");
            return null;
        }
        try {
            new JSONObject(str);
            return (String) propertyValue.getValue();
        } catch (Exception unused) {
            Log.e(TAG, "getCarlifeMediaInfo fail: invalid json string");
            return null;
        }
    }

    public int getCarlifeMediaProgress() {
        if (!isAvailable()) {
            Log.e(TAG, "getCarlifeMediaProgress fail, remote service not connect");
            return BaseProxy.RET_ERR_REMOTE_SERVICE_UNCONNECTED;
        }
        PropertyValue propertyValue = null;
        try {
            propertyValue = ((ICarlife) this.mInterface).getProperty(Property.PROPID_GET_CARLIFE_MEDIA_PROGRESS);
        } catch (RemoteException e) {
            StringBuilder e2 = a.e("getCarlifeMediaProgress fail. ");
            e2.append(Log.getStackTraceString(e));
            Log.e(TAG, e2.toString());
        }
        if (propertyValue == null) {
            Log.d(TAG, "getCarlifeMediaProgress fail. service return invalid PropertyValue");
            return -2147418113;
        }
        return propertyValue.getIntVal();
    }

    public int getCarlifeMediaState() {
        return getModuleStatus(-5177344);
    }

    public int getCarlifeVrStatus() {
        if (!isAvailable()) {
            Log.e(TAG, "getCarlifeVrStatus fail, remote service not connect");
            return BaseProxy.RET_ERR_REMOTE_SERVICE_UNCONNECTED;
        }
        return getModuleStatus(-5111808);
    }

    public String getConnectDeviceName() {
        Bundle deviceInfo = getDeviceInfo();
        if (deviceInfo == null || !deviceInfo.containsKey(EXTRA_DEV_INFO_NAME)) {
            return null;
        }
        return deviceInfo.getString(EXTRA_DEV_INFO_NAME, null);
    }

    public Bundle getDeviceInfo() {
        Bundle bundle = new Bundle();
        bundle.putString(EXTRA_DEV_INFO_NAME, "");
        bundle.putString(EXTRA_DEV_INFO_SERIAL, "");
        if (!isAvailable()) {
            Log.e(TAG, "carlife service not connected");
            return bundle;
        }
        try {
            PropertyValue property = ((ICarlife) this.mInterface).getProperty(Property.PROPID_GET_CARLIFE_DEVICE_INFO);
            if (property == null) {
                Log.e(TAG, "getDeviceInfo fail. service return invalid PropertyValue");
                return bundle;
            }
            String str = (String) property.getValue();
            if (TextUtils.isEmpty(str)) {
                Log.e(TAG, "getDeviceInfo fail. invalid value");
                return bundle;
            }
            try {
                JSONObject jSONObject = new JSONObject(str);
                bundle.putString(EXTRA_DEV_INFO_NAME, jSONObject.getString(EXTRA_DEV_INFO_NAME));
                bundle.putString(EXTRA_DEV_INFO_SERIAL, jSONObject.getString(EXTRA_DEV_INFO_SERIAL));
            } catch (JSONException e) {
                StringBuilder e2 = a.e("getDeviceInfo fail. parse json string error:");
                e2.append(e.getMessage());
                Log.e(TAG, e2.toString());
            }
            return bundle;
        } catch (RemoteException e3) {
            StringBuilder e4 = a.e("getDeviceInfo fail. ");
            e4.append(Log.getStackTraceString(e3));
            Log.e(TAG, e4.toString());
            return bundle;
        }
    }

    public int getModuleStatus(int i) {
        if (i != -5177344 && i != -5046272 && i != -5111808) {
            Log.e(TAG, "getModuleStatus fail, invalid moduleId:" + i);
            return BaseProxy.RET_ERR_INVALID_COMMAND;
        } else if (!isAvailable()) {
            Log.e(TAG, "getModuleStatus fail, remote service not connect");
            return BaseProxy.RET_ERR_REMOTE_SERVICE_UNCONNECTED;
        } else {
            PropertyValue propertyValue = null;
            try {
                propertyValue = ((ICarlife) this.mInterface).getPropertyByValue(new PropertyValue(Property.PROPID_GET_CARLIFE_MODULE_STATE, 0, i, 0));
            } catch (RemoteException e) {
                StringBuilder e2 = a.e("getModuleStatus fail. ");
                e2.append(Log.getStackTraceString(e));
                Log.e(TAG, e2.toString());
            }
            if (propertyValue == null) {
                Log.d(TAG, "getModuleStatus fail. service return invalid PropertyValue");
                return -2147418113;
            }
            return propertyValue.getState();
        }
    }

    public PropertyValue getPropertyValue(int i) {
        if (!isAvailable()) {
            Log.e(TAG, "carlife service not connected");
            return null;
        }
        try {
            PropertyValue property = ((ICarlife) this.mInterface).getProperty(i);
            if (property == null) {
                Log.e(TAG, "getProperty fail. service return invalid PropertyValue");
                return null;
            }
            return property;
        } catch (RemoteException e) {
            StringBuilder f = a.f("getProperty ", i, " fail. ");
            f.append(Log.getStackTraceString(e));
            Log.e(TAG, f.toString());
            return null;
        }
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public String getServiceName() {
        return ServiceConstant.CARLIFE_BINDER_NAME;
    }

    public String getTurnByTurnInfo() {
        PropertyValue propertyValue;
        if (!isAvailable()) {
            Log.e(TAG, "getTurnByTurnInfo fail, remote service not connect");
            return null;
        }
        try {
            propertyValue = ((ICarlife) this.mInterface).getProperty(Property.PROPID_GET_CARLIFE_TBT_INFO);
        } catch (RemoteException e) {
            StringBuilder e2 = a.e("getTurnByTurnInfo fail. ");
            e2.append(Log.getStackTraceString(e));
            Log.e(TAG, e2.toString());
            propertyValue = null;
        }
        if (propertyValue == null) {
            Log.d(TAG, "getTurnByTurnInfo fail. service return invalid PropertyValue");
            return null;
        }
        String str = (String) propertyValue.getValue();
        if (TextUtils.isEmpty(str)) {
            Log.e(TAG, "getTurnByTurnInfo fail: empty json string");
            return null;
        }
        try {
            new JSONObject(str);
            return (String) propertyValue.getValue();
        } catch (Exception unused) {
            Log.e(TAG, "getTurnByTurnInfo fail: invalid json string");
            return null;
        }
    }

    public boolean isCarLifeSessionActived() {
        if (!isAvailable()) {
            Log.e(TAG, "isCarLifeSessionActived fail, remote service not connect");
            return false;
        }
        PropertyValue propertyValue = null;
        try {
            propertyValue = ((ICarlife) this.mInterface).getProperty(Property.PROPID_GET_HAS_ACTIVE_CARLIFE_SESSION);
        } catch (RemoteException e) {
            StringBuilder e2 = a.e("isCarLifeSessionActived fail. ");
            e2.append(Log.getStackTraceString(e));
            Log.e(TAG, e2.toString());
        }
        if (propertyValue == null) {
            Log.e(TAG, "isCarLifeSessionActived fail. service return invalid PropertyValue");
            return false;
        }
        try {
            return ((Boolean) propertyValue.getValue()).booleanValue();
        } catch (Exception unused) {
            Log.e(TAG, "isCarLifeSessionActived value to boolean fail");
            return false;
        }
    }

    public boolean isCarlifeNaviRunning() {
        if (!isAvailable()) {
            Log.e(TAG, "carlife service not connected");
            return false;
        }
        PropertyValue propertyValue = null;
        try {
            propertyValue = ((ICarlife) this.mInterface).getProperty(Property.PROPID_GET_CARLIFE_NAVI_IS_RUNNING);
        } catch (RemoteException e) {
            StringBuilder e2 = a.e("isCarlifeNaviRunning fail. ");
            e2.append(Log.getStackTraceString(e));
            Log.e(TAG, e2.toString());
        }
        if (propertyValue == null) {
            Log.e(TAG, "isCarlifeNaviRunning fail. service return invalid PropertyValue");
            return false;
        }
        return ((Boolean) propertyValue.getValue()).booleanValue();
    }

    public boolean isCarlifeVrRunning() {
        if (!isAvailable()) {
            Log.e(TAG, "carlife service not connected");
            return false;
        }
        PropertyValue propertyValue = null;
        try {
            propertyValue = ((ICarlife) this.mInterface).getProperty(Property.PROPID_GET_CARLIFE_VR_IS_RUNNING);
        } catch (RemoteException e) {
            StringBuilder e2 = a.e("isCarlifeVrRunning fail. ");
            e2.append(Log.getStackTraceString(e));
            Log.e(TAG, e2.toString());
        }
        if (propertyValue == null) {
            Log.e(TAG, "isCarlifeVrRunning fail. service return invalid PropertyValue");
            return false;
        }
        return ((Boolean) propertyValue.getValue()).booleanValue();
    }

    public void notifyConnectionEvents(int i, int i2, int i3, Object obj) {
        for (ConnectionEventObserver connectionEventObserver : this.mConnEvtObs) {
            connectionEventObserver.onEvent(i, i2, i3, obj);
        }
    }

    public void registerConnectStateChangeListener(StateChangeListener stateChangeListener) {
        if (this.mCarlifeStateObs.contains(stateChangeListener)) {
            return;
        }
        this.mCarlifeStateObs.add(stateChangeListener);
    }

    public void registerConnectionEventObserver(ConnectionEventObserver connectionEventObserver) {
        if (this.mConnEvtObs.contains(connectionEventObserver)) {
            return;
        }
        this.mConnEvtObs.add(connectionEventObserver);
    }

    public void registerMediaStatusObserver(MediaStatusObserver mediaStatusObserver) {
        if (this.mCarlifeMediaStatusObs.contains(mediaStatusObserver)) {
            return;
        }
        this.mCarlifeMediaStatusObs.add(mediaStatusObserver);
    }

    public void registerModuleStatusObserver(ModuleStatusObserver moduleStatusObserver) {
        if (this.mModuleStatusObs.contains(moduleStatusObserver)) {
            return;
        }
        this.mModuleStatusObs.add(moduleStatusObserver);
    }

    public void registerTurnByTurnObserver(TurnByTurnObserver turnByTurnObserver) {
        if (this.mTbtObs.contains(turnByTurnObserver)) {
            return;
        }
        this.mTbtObs.add(turnByTurnObserver);
    }

    public void setPropertyValue(PropertyValue propertyValue) {
        if (propertyValue == null) {
            Log.e(TAG, "setPropertyValue fail, invalid args");
        } else if (!isAvailable()) {
            Log.e(TAG, "setPropertyValue fail, carlife service not connected");
        } else {
            try {
                ((ICarlife) this.mInterface).setProperty(propertyValue);
            } catch (RemoteException e) {
                StringBuilder e2 = a.e("setPropertyValue fail. ");
                e2.append(Log.getStackTraceString(e));
                Log.e(TAG, e2.toString());
            }
        }
    }

    public void stopCarlifeVr() {
        if (!isAvailable()) {
            Log.e(TAG, "carlife service not connected");
            return;
        }
        try {
            ((ICarlife) this.mInterface).setProperty(new PropertyValue((int) Property.PROPID_CTRL_EXIT_CARLIFE_VR, 0));
        } catch (RemoteException e) {
            StringBuilder e2 = a.e("stopCarlifeVr fail. ");
            e2.append(Log.getStackTraceString(e));
            Log.e(TAG, e2.toString());
        }
    }

    public void unregisterConnectStateChangeListener(StateChangeListener stateChangeListener) {
        this.mCarlifeStateObs.remove(stateChangeListener);
    }

    public void unregisterConnectionEventObserver(ConnectionEventObserver connectionEventObserver) {
        this.mConnEvtObs.remove(connectionEventObserver);
    }

    public void unregisterMediaStatusObserver(MediaStatusObserver mediaStatusObserver) {
        this.mCarlifeMediaStatusObs.remove(mediaStatusObserver);
    }

    public void unregisterModuleStatusObserver(ModuleStatusObserver moduleStatusObserver) {
        this.mModuleStatusObs.remove(moduleStatusObserver);
    }

    public void unregisterTurnByTurnObserver(TurnByTurnObserver turnByTurnObserver) {
        this.mTbtObs.remove(turnByTurnObserver);
    }

    public void updateSubscribe() {
        Log.i(TAG, "-- updateSubscribe");
        subscribeServiceEvent();
    }

    public <T> PropertyValue<T> getPropertyValue(PropertyValue<?> propertyValue) {
        if (!isAvailable()) {
            Log.e(TAG, "carlife service not connected");
            return null;
        }
        try {
            return ((ICarlife) this.mInterface).getPropertyByValue(propertyValue);
        } catch (Exception e) {
            StringBuilder e2 = a.e("getPropertyValue ");
            e2.append(propertyValue.getPropertyId());
            e2.append(" fail. ");
            e2.append(Log.getStackTraceString(e));
            Log.e(TAG, e2.toString());
            return null;
        }
    }
}
