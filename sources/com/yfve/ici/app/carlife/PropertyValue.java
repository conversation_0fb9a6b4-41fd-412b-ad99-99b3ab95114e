package com.yfve.ici.app.carlife;

import android.os.Parcel;
import android.os.Parcelable;
import androidx.annotation.NonNull;
import b.a.b.a.a;
import java.nio.charset.Charset;
import java.util.Objects;

/* loaded from: classes.dex */
public class PropertyValue<T> implements Parcelable, Comparable<PropertyValue> {
    public final int mArea;
    public final int mIntVal;
    public final int mPropertyId;
    public final int mState;
    public final T mValue;
    public static final Charset DEFAULT_CHARSET = Charset.forName("UTF-8");
    public static final Parcelable.Creator<PropertyValue> CREATOR = new Parcelable.Creator<PropertyValue>() { // from class: com.yfve.ici.app.carlife.PropertyValue.1
        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public PropertyValue createFromParcel(Parcel parcel) {
            return new PropertyValue(parcel);
        }

        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public PropertyValue[] newArray(int i) {
            return new PropertyValue[i];
        }
    };

    public PropertyValue(int i, int i2) {
        this(i, i2, null);
    }

    @Override // android.os.Parcelable
    public int describeContents() {
        return 0;
    }

    public String dump() {
        StringBuilder e = a.e("PropertyID:");
        e.append(this.mPropertyId);
        e.append(" State:");
        e.append(this.mState);
        e.append(" IntVal:");
        e.append(this.mIntVal);
        e.append(" Area:");
        e.append(this.mArea);
        e.append(" Value:");
        e.append(this.mValue);
        return e.toString();
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || PropertyValue.class != obj.getClass()) {
            return false;
        }
        PropertyValue propertyValue = (PropertyValue) obj;
        return this.mPropertyId == propertyValue.mPropertyId && this.mState == propertyValue.mState && this.mIntVal == propertyValue.mIntVal && this.mArea == propertyValue.mArea && Objects.equals(this.mValue, propertyValue.mValue);
    }

    public int getArea() {
        return this.mArea;
    }

    public int getIntVal() {
        return this.mIntVal;
    }

    public int getIntValue() {
        return ((Integer) this.mValue).intValue();
    }

    public int getPropertyId() {
        return this.mPropertyId;
    }

    public int getState() {
        return this.mState;
    }

    public T getValue() {
        return this.mValue;
    }

    public int hashCode() {
        return Objects.hash(Integer.valueOf(this.mPropertyId), Integer.valueOf(this.mState), Integer.valueOf(this.mIntVal), this.mValue, Integer.valueOf(this.mArea));
    }

    public boolean isMatchProperty(PropertyValue propertyValue) {
        if (propertyValue.mPropertyId == this.mPropertyId && this.mArea == 0) {
            if (propertyValue.mArea == 0) {
                return true;
            }
        } else if ((propertyValue.mArea & this.mArea) != 0) {
            return true;
        }
        return false;
    }

    public boolean isSameProperty(PropertyValue propertyValue) {
        return propertyValue.mPropertyId == this.mPropertyId && propertyValue.mArea == this.mArea;
    }

    public String toString() {
        StringBuilder e = a.e("PropertyValue{mPropertyId=");
        e.append(this.mPropertyId);
        e.append(", mState=");
        e.append(this.mState);
        e.append(", mIntVal=");
        e.append(this.mIntVal);
        e.append(", mValue=");
        e.append(this.mValue);
        e.append(", Area:");
        e.append(this.mArea);
        e.append('}');
        return e.toString();
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeInt(this.mPropertyId);
        parcel.writeInt(this.mState);
        parcel.writeInt(this.mIntVal);
        parcel.writeInt(this.mArea);
        T t = this.mValue;
        Class<?> cls = t == null ? null : t.getClass();
        parcel.writeString(cls != null ? cls.getName() : null);
        if (String.class.equals(cls)) {
            parcel.writeByteArray(((String) this.mValue).getBytes(DEFAULT_CHARSET));
        } else if (byte[].class.equals(cls)) {
            parcel.writeByteArray((byte[]) this.mValue);
        } else if (boolean[].class.equals(cls)) {
            parcel.writeBooleanArray((boolean[]) this.mValue);
        } else {
            parcel.writeValue(this.mValue);
        }
    }

    public PropertyValue(int i, int i2, T t) {
        this(i, i2, 0, 0, t);
    }

    @Override // java.lang.Comparable
    public int compareTo(@NonNull PropertyValue propertyValue) {
        int propertyId;
        int propertyId2;
        if (propertyValue.getPropertyId() == getPropertyId()) {
            propertyId = getArea();
            propertyId2 = propertyValue.getArea();
        } else {
            propertyId = getPropertyId();
            propertyId2 = propertyValue.getPropertyId();
        }
        return propertyId - propertyId2;
    }

    public PropertyValue(int i) {
        this(i, 0, null);
    }

    public PropertyValue(int i, T t) {
        this(i, 0, t);
    }

    public PropertyValue(int i, int i2, int i3, T t) {
        this(i, i2, i3, 0, t);
    }

    public PropertyValue(int i, int i2, int i3, int i4, T t) {
        this.mPropertyId = i;
        this.mState = i2;
        this.mIntVal = i3;
        if (t == null) {
            this.mValue = "";
        } else {
            this.mValue = t;
        }
        this.mArea = i4;
    }

    public PropertyValue(Parcel parcel) {
        this.mPropertyId = parcel.readInt();
        this.mState = parcel.readInt();
        this.mIntVal = parcel.readInt();
        this.mArea = parcel.readInt();
        String readString = parcel.readString();
        try {
            Class<?> cls = Class.forName(readString);
            if (String.class.equals(cls)) {
                this.mValue = (T) new String(parcel.createByteArray(), DEFAULT_CHARSET);
            } else if (byte[].class.equals(cls)) {
                this.mValue = (T) parcel.createByteArray();
            } else if (boolean[].class.equals(cls)) {
                this.mValue = (T) parcel.createBooleanArray();
            } else {
                this.mValue = (T) parcel.readValue(cls.getClassLoader());
            }
        } catch (ClassNotFoundException unused) {
            throw new IllegalArgumentException(a.x("Class not found: ", readString));
        }
    }
}
