package com.yfve.ici.app.carlife;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import com.yfve.ici.app.carlife.IPropertyObserver;

/* loaded from: classes.dex */
public interface ICarlife extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements ICarlife {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.carlife.ICarlife
        public PropertyValue getProperty(int i) throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.carlife.ICarlife
        public PropertyValue getPropertyByValue(PropertyValue propertyValue) throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.carlife.ICarlife
        public int setProperty(PropertyValue propertyValue) throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.carlife.ICarlife
        public void subscribe(int i, IPropertyObserver iPropertyObserver) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carlife.ICarlife
        public void unsubscribe(int i, IPropertyObserver iPropertyObserver) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements ICarlife {
        public static final String DESCRIPTOR = "com.yfve.ici.app.carlife.ICarlife";
        public static final int TRANSACTION_getProperty = 2;
        public static final int TRANSACTION_getPropertyByValue = 3;
        public static final int TRANSACTION_setProperty = 1;
        public static final int TRANSACTION_subscribe = 4;
        public static final int TRANSACTION_unsubscribe = 5;

        /* loaded from: classes.dex */
        public static class Proxy implements ICarlife {
            public static ICarlife sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return "com.yfve.ici.app.carlife.ICarlife";
            }

            @Override // com.yfve.ici.app.carlife.ICarlife
            public PropertyValue getProperty(int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carlife.ICarlife");
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getProperty(i);
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0 ? PropertyValue.CREATOR.createFromParcel(obtain2) : null;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carlife.ICarlife
            public PropertyValue getPropertyByValue(PropertyValue propertyValue) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carlife.ICarlife");
                    if (propertyValue != null) {
                        obtain.writeInt(1);
                        propertyValue.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    if (!this.mRemote.transact(3, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getPropertyByValue(propertyValue);
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0 ? PropertyValue.CREATOR.createFromParcel(obtain2) : null;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carlife.ICarlife
            public int setProperty(PropertyValue propertyValue) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carlife.ICarlife");
                    if (propertyValue != null) {
                        obtain.writeInt(1);
                        propertyValue.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().setProperty(propertyValue);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carlife.ICarlife
            public void subscribe(int i, IPropertyObserver iPropertyObserver) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carlife.ICarlife");
                    obtain.writeInt(i);
                    obtain.writeStrongBinder(iPropertyObserver != null ? iPropertyObserver.asBinder() : null);
                    if (!this.mRemote.transact(4, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().subscribe(i, iPropertyObserver);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carlife.ICarlife
            public void unsubscribe(int i, IPropertyObserver iPropertyObserver) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carlife.ICarlife");
                    obtain.writeInt(i);
                    obtain.writeStrongBinder(iPropertyObserver != null ? iPropertyObserver.asBinder() : null);
                    if (!this.mRemote.transact(5, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unsubscribe(i, iPropertyObserver);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, "com.yfve.ici.app.carlife.ICarlife");
        }

        public static ICarlife asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface("com.yfve.ici.app.carlife.ICarlife");
            if (queryLocalInterface != null && (queryLocalInterface instanceof ICarlife)) {
                return (ICarlife) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static ICarlife getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(ICarlife iCarlife) {
            if (Proxy.sDefaultImpl != null || iCarlife == null) {
                return false;
            }
            Proxy.sDefaultImpl = iCarlife;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i == 1) {
                parcel.enforceInterface("com.yfve.ici.app.carlife.ICarlife");
                int property = setProperty(parcel.readInt() != 0 ? PropertyValue.CREATOR.createFromParcel(parcel) : null);
                parcel2.writeNoException();
                parcel2.writeInt(property);
                return true;
            } else if (i == 2) {
                parcel.enforceInterface("com.yfve.ici.app.carlife.ICarlife");
                PropertyValue property2 = getProperty(parcel.readInt());
                parcel2.writeNoException();
                if (property2 != null) {
                    parcel2.writeInt(1);
                    property2.writeToParcel(parcel2, 1);
                } else {
                    parcel2.writeInt(0);
                }
                return true;
            } else if (i == 3) {
                parcel.enforceInterface("com.yfve.ici.app.carlife.ICarlife");
                PropertyValue propertyByValue = getPropertyByValue(parcel.readInt() != 0 ? PropertyValue.CREATOR.createFromParcel(parcel) : null);
                parcel2.writeNoException();
                if (propertyByValue != null) {
                    parcel2.writeInt(1);
                    propertyByValue.writeToParcel(parcel2, 1);
                } else {
                    parcel2.writeInt(0);
                }
                return true;
            } else if (i == 4) {
                parcel.enforceInterface("com.yfve.ici.app.carlife.ICarlife");
                subscribe(parcel.readInt(), IPropertyObserver.Stub.asInterface(parcel.readStrongBinder()));
                parcel2.writeNoException();
                return true;
            } else if (i != 5) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString("com.yfve.ici.app.carlife.ICarlife");
                return true;
            } else {
                parcel.enforceInterface("com.yfve.ici.app.carlife.ICarlife");
                unsubscribe(parcel.readInt(), IPropertyObserver.Stub.asInterface(parcel.readStrongBinder()));
                parcel2.writeNoException();
                return true;
            }
        }
    }

    PropertyValue getProperty(int i) throws RemoteException;

    PropertyValue getPropertyByValue(PropertyValue propertyValue) throws RemoteException;

    int setProperty(PropertyValue propertyValue) throws RemoteException;

    void subscribe(int i, IPropertyObserver iPropertyObserver) throws RemoteException;

    void unsubscribe(int i, IPropertyObserver iPropertyObserver) throws RemoteException;
}
