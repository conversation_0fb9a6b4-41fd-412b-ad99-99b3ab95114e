package com.yfve.ici.app.carlife;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface IPropertyObserver extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IPropertyObserver {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.carlife.IPropertyObserver
        public void onPropertyChanged(PropertyValue propertyValue) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IPropertyObserver {
        public static final String DESCRIPTOR = "com.yfve.ici.app.carlife.IPropertyObserver";
        public static final int TRANSACTION_onPropertyChanged = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements IPropertyObserver {
            public static IPropertyObserver sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            @Override // com.yfve.ici.app.carlife.IPropertyObserver
            public void onPropertyChanged(PropertyValue propertyValue) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (propertyValue != null) {
                        obtain.writeInt(1);
                        propertyValue.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onPropertyChanged(propertyValue);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static IPropertyObserver asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof IPropertyObserver)) {
                return (IPropertyObserver) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IPropertyObserver getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IPropertyObserver iPropertyObserver) {
            if (Proxy.sDefaultImpl != null || iPropertyObserver == null) {
                return false;
            }
            Proxy.sDefaultImpl = iPropertyObserver;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString(DESCRIPTOR);
                return true;
            }
            parcel.enforceInterface(DESCRIPTOR);
            onPropertyChanged(parcel.readInt() != 0 ? PropertyValue.CREATOR.createFromParcel(parcel) : null);
            parcel2.writeNoException();
            return true;
        }
    }

    void onPropertyChanged(PropertyValue propertyValue) throws RemoteException;
}
