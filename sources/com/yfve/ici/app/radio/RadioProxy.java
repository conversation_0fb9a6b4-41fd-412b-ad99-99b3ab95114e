package com.yfve.ici.app.radio;

import android.os.RemoteException;
import android.util.Log;
import b.a.b.a.a;
import com.yfve.ici.app.radio.IRadioCallBack;
import com.yfve.ici.app.radio.IRadioScanStateChangeListener;
import com.yfve.ici.service.base.BaseProxy;
import com.yfve.ici.service.contanst.ServiceConstant;
import java.util.ArrayList;
import java.util.List;

/* loaded from: classes.dex */
public class RadioProxy extends BaseProxy<IRadio> {
    public static final int BAND_AM = 0;
    public static final int BAND_FM = 1;
    public static final String TAG = "RadioProxy";
    public static volatile RadioProxy mRadioProxy;
    public IRadioScanStateChangeListener.Stub mIRadioScanStateChangeListener;
    public IRadioCallBack.Stub mRadioCallback;
    public List<IRadioCallBack> radioCallBacks = new ArrayList();
    public List<IRadioScanStateChangeListener> radioScanStateChangeListeners = new ArrayList();

    public static RadioProxy getInstance() {
        if (mRadioProxy == null) {
            synchronized (RadioProxy.class) {
                if (mRadioProxy == null) {
                    mRadioProxy = new RadioProxy();
                }
            }
        }
        return mRadioProxy;
    }

    public RadioInfo getCurrentRadio() {
        Log.i(TAG, "  getCurrentRadio ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "radio~~mInterface is null");
                return null;
            }
            return ((IRadio) this.mInterface).getCurrentRadio();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return null;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return null;
        }
    }

    public List<RadioInfo> getRadioList(int i) {
        Log.i(TAG, "  getRadioList ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "radio~~mInterface is null");
                return null;
            }
            return ((IRadio) this.mInterface).getRadioList(i);
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return null;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return null;
        }
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public String getServiceName() {
        return ServiceConstant.RADIO_BINDER_NAME;
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public void onServiceConnectStatusChanged(boolean z) {
        Log.e(TAG, "  onServiceConnectStatusChanged   status:" + z);
        if (z) {
            return;
        }
        List<IRadioCallBack> list = this.radioCallBacks;
        if (list != null) {
            list.clear();
        }
        List<IRadioScanStateChangeListener> list2 = this.radioScanStateChangeListeners;
        if (list2 != null) {
            list2.clear();
        }
    }

    public void openAM() {
        Log.i(TAG, " open am");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "radio~~mInterface is null");
            } else {
                ((IRadio) this.mInterface).openAM();
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public void openFM() {
        Log.i(TAG, " open fm ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "radio~~mInterface is null");
            } else {
                ((IRadio) this.mInterface).openFM();
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public void playRadio(int i, double d, int i2, boolean z) {
        Log.i(TAG, "  playRadio  freq:" + d + "   isStart:" + z);
        try {
            if (!isAvailable()) {
                Log.e(TAG, "radio~~mInterface is null");
            } else {
                ((IRadio) this.mInterface).playRadio(i, d, i2, z);
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public int registerIRadioScanStateChangeListener(IRadioScanStateChangeListener iRadioScanStateChangeListener) {
        Log.i(TAG, "registerIRadioScanStateChangeListener in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerIRadioScanStateChangeListener~~mInterface is null");
                return -2147483646;
            }
            if (iRadioScanStateChangeListener != null && !this.radioScanStateChangeListeners.contains(iRadioScanStateChangeListener)) {
                this.radioScanStateChangeListeners.add(iRadioScanStateChangeListener);
                if (this.radioScanStateChangeListeners.size() == 1) {
                    IRadioScanStateChangeListener.Stub stub = new IRadioScanStateChangeListener.Stub() { // from class: com.yfve.ici.app.radio.RadioProxy.2
                        @Override // com.yfve.ici.app.radio.IRadioScanStateChangeListener
                        public void onRadioScanStateChange(int i) throws RemoteException {
                            Log.d(RadioProxy.TAG, "onRadioScanStateChange is called. state: " + i);
                            for (IRadioScanStateChangeListener iRadioScanStateChangeListener2 : RadioProxy.this.radioScanStateChangeListeners) {
                                iRadioScanStateChangeListener2.onRadioScanStateChange(i);
                            }
                        }
                    };
                    this.mIRadioScanStateChangeListener = stub;
                    ((IRadio) this.mInterface).registerRadioScanStateChangeListener(stub);
                    return Integer.MIN_VALUE;
                }
            }
            return -2147418113;
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int registerRadioCallback(IRadioCallBack iRadioCallBack) {
        Log.i(TAG, "registerPhoneInfoListener in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerPhoneInfoListener~~mInterface is null");
                return -2147483646;
            }
            if (iRadioCallBack != null && !this.radioCallBacks.contains(iRadioCallBack)) {
                this.radioCallBacks.add(iRadioCallBack);
                if (this.radioCallBacks.size() == 1) {
                    IRadioCallBack.Stub stub = new IRadioCallBack.Stub() { // from class: com.yfve.ici.app.radio.RadioProxy.1
                        @Override // com.yfve.ici.app.radio.IRadioCallBack
                        public void onProgramChange(RadioInfo radioInfo) throws RemoteException {
                            StringBuilder e = a.e("onProgramChange is called. info:");
                            e.append(radioInfo.toString());
                            Log.d(RadioProxy.TAG, e.toString());
                            for (IRadioCallBack iRadioCallBack2 : RadioProxy.this.radioCallBacks) {
                                iRadioCallBack2.onProgramChange(radioInfo);
                            }
                        }
                    };
                    this.mRadioCallback = stub;
                    ((IRadio) this.mInterface).registerRadioCallback(stub);
                    return Integer.MIN_VALUE;
                }
            }
            return -2147418113;
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }

    public void scanRadio() {
        Log.i(TAG, "scan radio ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "radio~~mInterface is null");
            } else {
                ((IRadio) this.mInterface).scan();
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public void seekDown() {
        Log.i(TAG, "seek down");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "radio~~mInterface is null");
            } else {
                ((IRadio) this.mInterface).seekDown();
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public void seekUp() {
        Log.i(TAG, " seek  up");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "radio~~mInterface is null");
            } else {
                ((IRadio) this.mInterface).seekUp();
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public int unRegisterRadioCallback(IRadioCallBack iRadioCallBack) {
        Log.i(TAG, "unRegisterRadioCallback in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unRegisterRadioCallback~~mInterface is null");
                return -2147483646;
            }
            if (iRadioCallBack != null && this.radioCallBacks.contains(iRadioCallBack)) {
                this.radioCallBacks.remove(iRadioCallBack);
                if (this.radioCallBacks.size() == 0) {
                    ((IRadio) this.mInterface).unregisterRadioCallback(iRadioCallBack);
                    this.mRadioCallback = null;
                    return Integer.MIN_VALUE;
                }
            }
            return -2147418113;
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int unRegisterRadioScanStateChangeListener(IRadioScanStateChangeListener iRadioScanStateChangeListener) {
        Log.i(TAG, "unRegisterRadioScanStateChangeListener in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unRegisterRadioScanStateChangeListener~~mInterface is null");
                return -2147483646;
            }
            if (iRadioScanStateChangeListener != null && this.radioScanStateChangeListeners.contains(iRadioScanStateChangeListener)) {
                this.radioScanStateChangeListeners.remove(iRadioScanStateChangeListener);
                if (this.radioScanStateChangeListeners.size() == 0) {
                    ((IRadio) this.mInterface).unregisterRadioScanStateChangeListener(iRadioScanStateChangeListener);
                    this.mIRadioScanStateChangeListener = null;
                    return Integer.MIN_VALUE;
                }
            }
            return -2147418113;
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }
}
