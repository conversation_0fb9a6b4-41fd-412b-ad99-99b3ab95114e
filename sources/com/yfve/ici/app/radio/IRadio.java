package com.yfve.ici.app.radio;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import com.yfve.ici.app.radio.IRadioCallBack;
import com.yfve.ici.app.radio.IRadioScanStateChangeListener;
import java.util.List;

/* loaded from: classes.dex */
public interface IRadio extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IRadio {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.radio.IRadio
        public RadioInfo getCurrentRadio() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.radio.IRadio
        public List<RadioInfo> getRadioList(int i) throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.radio.IRadio
        public void openAM() throws RemoteException {
        }

        @Override // com.yfve.ici.app.radio.IRadio
        public void openFM() throws RemoteException {
        }

        @Override // com.yfve.ici.app.radio.IRadio
        public void playRadio(int i, double d, int i2, boolean z) throws RemoteException {
        }

        @Override // com.yfve.ici.app.radio.IRadio
        public void registerRadioCallback(IRadioCallBack iRadioCallBack) throws RemoteException {
        }

        @Override // com.yfve.ici.app.radio.IRadio
        public void registerRadioScanStateChangeListener(IRadioScanStateChangeListener iRadioScanStateChangeListener) throws RemoteException {
        }

        @Override // com.yfve.ici.app.radio.IRadio
        public void scan() throws RemoteException {
        }

        @Override // com.yfve.ici.app.radio.IRadio
        public void seekDown() throws RemoteException {
        }

        @Override // com.yfve.ici.app.radio.IRadio
        public void seekUp() throws RemoteException {
        }

        @Override // com.yfve.ici.app.radio.IRadio
        public void unregisterRadioCallback(IRadioCallBack iRadioCallBack) throws RemoteException {
        }

        @Override // com.yfve.ici.app.radio.IRadio
        public void unregisterRadioScanStateChangeListener(IRadioScanStateChangeListener iRadioScanStateChangeListener) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IRadio {
        public static final String DESCRIPTOR = "com.yfve.ici.app.radio.IRadio";
        public static final int TRANSACTION_getCurrentRadio = 1;
        public static final int TRANSACTION_getRadioList = 12;
        public static final int TRANSACTION_openAM = 2;
        public static final int TRANSACTION_openFM = 3;
        public static final int TRANSACTION_playRadio = 6;
        public static final int TRANSACTION_registerRadioCallback = 8;
        public static final int TRANSACTION_registerRadioScanStateChangeListener = 10;
        public static final int TRANSACTION_scan = 7;
        public static final int TRANSACTION_seekDown = 5;
        public static final int TRANSACTION_seekUp = 4;
        public static final int TRANSACTION_unregisterRadioCallback = 9;
        public static final int TRANSACTION_unregisterRadioScanStateChangeListener = 11;

        /* loaded from: classes.dex */
        public static class Proxy implements IRadio {
            public static IRadio sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            @Override // com.yfve.ici.app.radio.IRadio
            public RadioInfo getCurrentRadio() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.radio.IRadio");
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getCurrentRadio();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0 ? RadioInfo.CREATOR.createFromParcel(obtain2) : null;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            public String getInterfaceDescriptor() {
                return "com.yfve.ici.app.radio.IRadio";
            }

            @Override // com.yfve.ici.app.radio.IRadio
            public List<RadioInfo> getRadioList(int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.radio.IRadio");
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(12, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getRadioList(i);
                    }
                    obtain2.readException();
                    return obtain2.createTypedArrayList(RadioInfo.CREATOR);
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.radio.IRadio
            public void openAM() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.radio.IRadio");
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().openAM();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.radio.IRadio
            public void openFM() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.radio.IRadio");
                    if (!this.mRemote.transact(3, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().openFM();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.radio.IRadio
            public void playRadio(int i, double d, int i2, boolean z) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.radio.IRadio");
                    obtain.writeInt(i);
                    obtain.writeDouble(d);
                    obtain.writeInt(i2);
                    obtain.writeInt(z ? 1 : 0);
                    if (!this.mRemote.transact(6, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().playRadio(i, d, i2, z);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.radio.IRadio
            public void registerRadioCallback(IRadioCallBack iRadioCallBack) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.radio.IRadio");
                    obtain.writeStrongBinder(iRadioCallBack != null ? iRadioCallBack.asBinder() : null);
                    if (!this.mRemote.transact(8, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerRadioCallback(iRadioCallBack);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.radio.IRadio
            public void registerRadioScanStateChangeListener(IRadioScanStateChangeListener iRadioScanStateChangeListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.radio.IRadio");
                    obtain.writeStrongBinder(iRadioScanStateChangeListener != null ? iRadioScanStateChangeListener.asBinder() : null);
                    if (!this.mRemote.transact(10, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerRadioScanStateChangeListener(iRadioScanStateChangeListener);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.radio.IRadio
            public void scan() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.radio.IRadio");
                    if (!this.mRemote.transact(7, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().scan();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.radio.IRadio
            public void seekDown() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.radio.IRadio");
                    if (!this.mRemote.transact(5, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().seekDown();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.radio.IRadio
            public void seekUp() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.radio.IRadio");
                    if (!this.mRemote.transact(4, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().seekUp();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.radio.IRadio
            public void unregisterRadioCallback(IRadioCallBack iRadioCallBack) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.radio.IRadio");
                    obtain.writeStrongBinder(iRadioCallBack != null ? iRadioCallBack.asBinder() : null);
                    if (!this.mRemote.transact(9, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterRadioCallback(iRadioCallBack);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.radio.IRadio
            public void unregisterRadioScanStateChangeListener(IRadioScanStateChangeListener iRadioScanStateChangeListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.radio.IRadio");
                    obtain.writeStrongBinder(iRadioScanStateChangeListener != null ? iRadioScanStateChangeListener.asBinder() : null);
                    if (!this.mRemote.transact(11, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterRadioScanStateChangeListener(iRadioScanStateChangeListener);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, "com.yfve.ici.app.radio.IRadio");
        }

        public static IRadio asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface("com.yfve.ici.app.radio.IRadio");
            if (queryLocalInterface != null && (queryLocalInterface instanceof IRadio)) {
                return (IRadio) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IRadio getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IRadio iRadio) {
            if (Proxy.sDefaultImpl != null || iRadio == null) {
                return false;
            }
            Proxy.sDefaultImpl = iRadio;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1598968902) {
                switch (i) {
                    case 1:
                        parcel.enforceInterface("com.yfve.ici.app.radio.IRadio");
                        RadioInfo currentRadio = getCurrentRadio();
                        parcel2.writeNoException();
                        if (currentRadio != null) {
                            parcel2.writeInt(1);
                            currentRadio.writeToParcel(parcel2, 1);
                        } else {
                            parcel2.writeInt(0);
                        }
                        return true;
                    case 2:
                        parcel.enforceInterface("com.yfve.ici.app.radio.IRadio");
                        openAM();
                        parcel2.writeNoException();
                        return true;
                    case 3:
                        parcel.enforceInterface("com.yfve.ici.app.radio.IRadio");
                        openFM();
                        parcel2.writeNoException();
                        return true;
                    case 4:
                        parcel.enforceInterface("com.yfve.ici.app.radio.IRadio");
                        seekUp();
                        parcel2.writeNoException();
                        return true;
                    case 5:
                        parcel.enforceInterface("com.yfve.ici.app.radio.IRadio");
                        seekDown();
                        parcel2.writeNoException();
                        return true;
                    case 6:
                        parcel.enforceInterface("com.yfve.ici.app.radio.IRadio");
                        playRadio(parcel.readInt(), parcel.readDouble(), parcel.readInt(), parcel.readInt() != 0);
                        parcel2.writeNoException();
                        return true;
                    case 7:
                        parcel.enforceInterface("com.yfve.ici.app.radio.IRadio");
                        scan();
                        parcel2.writeNoException();
                        return true;
                    case 8:
                        parcel.enforceInterface("com.yfve.ici.app.radio.IRadio");
                        registerRadioCallback(IRadioCallBack.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 9:
                        parcel.enforceInterface("com.yfve.ici.app.radio.IRadio");
                        unregisterRadioCallback(IRadioCallBack.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 10:
                        parcel.enforceInterface("com.yfve.ici.app.radio.IRadio");
                        registerRadioScanStateChangeListener(IRadioScanStateChangeListener.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 11:
                        parcel.enforceInterface("com.yfve.ici.app.radio.IRadio");
                        unregisterRadioScanStateChangeListener(IRadioScanStateChangeListener.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 12:
                        parcel.enforceInterface("com.yfve.ici.app.radio.IRadio");
                        List<RadioInfo> radioList = getRadioList(parcel.readInt());
                        parcel2.writeNoException();
                        parcel2.writeTypedList(radioList);
                        return true;
                    default:
                        return super.onTransact(i, parcel, parcel2, i2);
                }
            }
            parcel2.writeString("com.yfve.ici.app.radio.IRadio");
            return true;
        }
    }

    RadioInfo getCurrentRadio() throws RemoteException;

    List<RadioInfo> getRadioList(int i) throws RemoteException;

    void openAM() throws RemoteException;

    void openFM() throws RemoteException;

    void playRadio(int i, double d, int i2, boolean z) throws RemoteException;

    void registerRadioCallback(IRadioCallBack iRadioCallBack) throws RemoteException;

    void registerRadioScanStateChangeListener(IRadioScanStateChangeListener iRadioScanStateChangeListener) throws RemoteException;

    void scan() throws RemoteException;

    void seekDown() throws RemoteException;

    void seekUp() throws RemoteException;

    void unregisterRadioCallback(IRadioCallBack iRadioCallBack) throws RemoteException;

    void unregisterRadioScanStateChangeListener(IRadioScanStateChangeListener iRadioScanStateChangeListener) throws RemoteException;
}
