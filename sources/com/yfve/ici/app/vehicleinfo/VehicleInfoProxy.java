package com.yfve.ici.app.vehicleinfo;

import android.os.RemoteException;
import android.util.Log;
import com.yfve.ici.service.base.BaseProxy;
import com.yfve.ici.service.contanst.ServiceConstant;

/* loaded from: classes.dex */
public class VehicleInfoProxy extends BaseProxy<IVehicleInfoManager> {
    public static final String TAG = "VehicleInfoProxy";
    public static VehicleInfoProxy mVehicleInfoProxy;

    public static synchronized VehicleInfoProxy getInstance() {
        VehicleInfoProxy vehicleInfoProxy;
        synchronized (VehicleInfoProxy.class) {
            Log.d(TAG, "getInstance --- start");
            if (mVehicleInfoProxy == null) {
                mVehicleInfoProxy = new VehicleInfoProxy();
            }
            vehicleInfoProxy = mVehicleInfoProxy;
        }
        return vehicleInfoProxy;
    }

    public void OpenVehicleInfo() {
        Log.d(TAG, "OpenVehicleInfo --- start");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "OpenVehicleInfo~~mInterface is null");
            } else {
                ((IVehicleInfoManager) this.mInterface).OpenVehicleInfo();
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public String getServiceName() {
        return ServiceConstant.VEHICLEINFO_BINDER_NAME;
    }
}
