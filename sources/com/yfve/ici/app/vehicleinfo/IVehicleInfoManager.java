package com.yfve.ici.app.vehicleinfo;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface IVehicleInfoManager extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IVehicleInfoManager {
        @Override // com.yfve.ici.app.vehicleinfo.IVehicleInfoManager
        public void OpenVehicleInfo() throws RemoteException {
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IVehicleInfoManager {
        public static final String DESCRIPTOR = "com.yfve.ici.app.vehicleinfo.IVehicleInfoManager";
        public static final int TRANSACTION_OpenVehicleInfo = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements IVehicleInfoManager {
            public static IVehicleInfoManager sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // com.yfve.ici.app.vehicleinfo.IVehicleInfoManager
            public void OpenVehicleInfo() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.vehicleinfo.IVehicleInfoManager");
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().OpenVehicleInfo();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return "com.yfve.ici.app.vehicleinfo.IVehicleInfoManager";
            }
        }

        public Stub() {
            attachInterface(this, "com.yfve.ici.app.vehicleinfo.IVehicleInfoManager");
        }

        public static IVehicleInfoManager asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface("com.yfve.ici.app.vehicleinfo.IVehicleInfoManager");
            if (queryLocalInterface != null && (queryLocalInterface instanceof IVehicleInfoManager)) {
                return (IVehicleInfoManager) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IVehicleInfoManager getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IVehicleInfoManager iVehicleInfoManager) {
            if (Proxy.sDefaultImpl != null || iVehicleInfoManager == null) {
                return false;
            }
            Proxy.sDefaultImpl = iVehicleInfoManager;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString("com.yfve.ici.app.vehicleinfo.IVehicleInfoManager");
                return true;
            }
            parcel.enforceInterface("com.yfve.ici.app.vehicleinfo.IVehicleInfoManager");
            OpenVehicleInfo();
            parcel2.writeNoException();
            return true;
        }
    }

    void OpenVehicleInfo() throws RemoteException;
}
