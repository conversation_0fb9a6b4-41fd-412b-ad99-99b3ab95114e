package com.yfve.ici.app.ADASSetting;

import android.os.RemoteException;
import android.util.Log;
import com.yfve.ici.service.base.BaseProxy;
import com.yfve.ici.service.contanst.ServiceConstant;

/* loaded from: classes.dex */
public class ADASSettingProxy extends BaseProxy<IADASSettingManager> {
    public static ADASSettingProxy ADAS_PROXY = null;
    public static final String TAG = "ICIADAS";

    public static void d(String str, String str2) {
        Log.d(str, getFileLineMethod() + str2);
    }

    public static String getFileLineMethod() {
        StackTraceElement stackTraceElement = new Exception().getStackTrace()[2];
        StringBuffer stringBuffer = new StringBuffer("[");
        stringBuffer.append(stackTraceElement.getFileName());
        stringBuffer.append(":");
        stringBuffer.append(stackTraceElement.getLineNumber());
        stringBuffer.append("] ");
        return stringBuffer.toString();
    }

    public static synchronized ADASSettingProxy getInstance() {
        ADASSettingProxy aDASSettingProxy;
        synchronized (ADASSettingProxy.class) {
            if (ADAS_PROXY == null) {
                ADAS_PROXY = new ADASSettingProxy();
            }
            aDASSettingProxy = ADAS_PROXY;
        }
        return aDASSettingProxy;
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public String getServiceName() {
        return ServiceConstant.ADAS_BINDER_NAME;
    }

    public void openADAS() {
        d(TAG, "打开ADASSetting app");
        if (isAvailable()) {
            d(TAG, " mInterface.openADASSetting(): ");
            try {
                ((IADASSettingManager) this.mInterface).openADASSetting();
                return;
            } catch (RemoteException e) {
                e.printStackTrace();
                return;
            }
        }
        d(TAG, "openADASSetting: sevice is not bind");
    }
}
