package com.yfve.ici.app.ADASSetting;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface IADASSettingManager extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IADASSettingManager {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.ADASSetting.IADASSettingManager
        public void openADASSetting() throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IADASSettingManager {
        public static final String DESCRIPTOR = "com.yfve.ici.app.ADASSetting.IADASSettingManager";
        public static final int TRANSACTION_openADASSetting = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements IADASSettingManager {
            public static IADASSettingManager sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return "com.yfve.ici.app.ADASSetting.IADASSettingManager";
            }

            @Override // com.yfve.ici.app.ADASSetting.IADASSettingManager
            public void openADASSetting() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.ADASSetting.IADASSettingManager");
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().openADASSetting();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, "com.yfve.ici.app.ADASSetting.IADASSettingManager");
        }

        public static IADASSettingManager asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface("com.yfve.ici.app.ADASSetting.IADASSettingManager");
            if (queryLocalInterface != null && (queryLocalInterface instanceof IADASSettingManager)) {
                return (IADASSettingManager) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IADASSettingManager getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IADASSettingManager iADASSettingManager) {
            if (Proxy.sDefaultImpl != null || iADASSettingManager == null) {
                return false;
            }
            Proxy.sDefaultImpl = iADASSettingManager;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString("com.yfve.ici.app.ADASSetting.IADASSettingManager");
                return true;
            }
            parcel.enforceInterface("com.yfve.ici.app.ADASSetting.IADASSettingManager");
            openADASSetting();
            parcel2.writeNoException();
            return true;
        }
    }

    void openADASSetting() throws RemoteException;
}
