package com.yfve.ici.app.connectivity;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface IConnectivityProxy extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IConnectivityProxy {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IConnectivityProxy {
        public static final String DESCRIPTOR = "com.yfve.ici.app.connectivity.IConnectivityProxy";

        /* loaded from: classes.dex */
        public static class Proxy implements IConnectivityProxy {
            public static IConnectivityProxy sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static IConnectivityProxy asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof IConnectivityProxy)) {
                return (IConnectivityProxy) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IConnectivityProxy getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IConnectivityProxy iConnectivityProxy) {
            if (Proxy.sDefaultImpl != null || iConnectivityProxy == null) {
                return false;
            }
            Proxy.sDefaultImpl = iConnectivityProxy;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1598968902) {
                return super.onTransact(i, parcel, parcel2, i2);
            }
            parcel2.writeString(DESCRIPTOR);
            return true;
        }
    }
}
