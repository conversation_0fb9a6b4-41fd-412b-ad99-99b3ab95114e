package com.yfve.ici.app.connectivity;

import com.yfve.ici.app.btsetting.BtSettingProxy;
import com.yfve.ici.service.base.BaseProxy;
import com.yfve.ici.service.contanst.ServiceConstant;

/* loaded from: classes.dex */
public class ConnectivityProxy extends BaseProxy<IConnectivityProxy> {
    public static final String TAG = "ConnectivityProxy";
    public static final int VR_RESULT_DISABLE = 3;
    public static final int VR_RESULT_OPENED = 1;
    public static final int VR_RESULT_OPENING = 2;
    public static final int VR_RESULT_UNKNOW = 0;
    public static ConnectivityProxy mInstance;

    public static ConnectivityProxy getInstance() {
        if (mInstance == null) {
            synchronized (ConnectivityProxy.class) {
                if (mInstance == null) {
                    mInstance = new ConnectivityProxy();
                }
            }
        }
        return mInstance;
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public String getServiceName() {
        return ServiceConstant.CONNECTIVITY_BINDER_NAME;
    }

    public int openCarPlay() {
        return BtSettingProxy.getInstance().openCarPlay();
    }

    public int openCarlife() {
        return BtSettingProxy.getInstance().openCarlife();
    }
}
