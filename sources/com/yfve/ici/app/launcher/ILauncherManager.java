package com.yfve.ici.app.launcher;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import com.yfve.ici.app.launcher.IApptrayListener;
import com.yfve.ici.app.launcher.IWidgetListener;

/* loaded from: classes.dex */
public interface ILauncherManager extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements ILauncherManager {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.launcher.ILauncherManager
        public void changeIcon(String str, int i) throws RemoteException {
        }

        @Override // com.yfve.ici.app.launcher.ILauncherManager
        public boolean isAppiconShow(String str) throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.launcher.ILauncherManager
        public boolean isWidgetShow(String str) throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.launcher.ILauncherManager
        public void registerApptrayListener(IApptrayListener iApptrayListener) throws RemoteException {
        }

        @Override // com.yfve.ici.app.launcher.ILauncherManager
        public void registerWidgetListener(String str, IWidgetListener iWidgetListener) throws RemoteException {
        }

        @Override // com.yfve.ici.app.launcher.ILauncherManager
        public void setBadgetType(int i) throws RemoteException {
        }

        @Override // com.yfve.ici.app.launcher.ILauncherManager
        public void showAppTray(boolean z) throws RemoteException {
        }

        @Override // com.yfve.ici.app.launcher.ILauncherManager
        public void unregisterApptrayListener(IApptrayListener iApptrayListener) throws RemoteException {
        }

        @Override // com.yfve.ici.app.launcher.ILauncherManager
        public void unregisterWidgetListener(IWidgetListener iWidgetListener) throws RemoteException {
        }

        @Override // com.yfve.ici.app.launcher.ILauncherManager
        public void updateBadge(String str, int i) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements ILauncherManager {
        public static final String DESCRIPTOR = "com.yfve.ici.app.launcher.ILauncherManager";
        public static final int TRANSACTION_changeIcon = 2;
        public static final int TRANSACTION_isAppiconShow = 7;
        public static final int TRANSACTION_isWidgetShow = 10;
        public static final int TRANSACTION_registerApptrayListener = 5;
        public static final int TRANSACTION_registerWidgetListener = 8;
        public static final int TRANSACTION_setBadgetType = 3;
        public static final int TRANSACTION_showAppTray = 1;
        public static final int TRANSACTION_unregisterApptrayListener = 6;
        public static final int TRANSACTION_unregisterWidgetListener = 9;
        public static final int TRANSACTION_updateBadge = 4;

        /* loaded from: classes.dex */
        public static class Proxy implements ILauncherManager {
            public static ILauncherManager sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            @Override // com.yfve.ici.app.launcher.ILauncherManager
            public void changeIcon(String str, int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.launcher.ILauncherManager");
                    obtain.writeString(str);
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().changeIcon(str, i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            public String getInterfaceDescriptor() {
                return "com.yfve.ici.app.launcher.ILauncherManager";
            }

            @Override // com.yfve.ici.app.launcher.ILauncherManager
            public boolean isAppiconShow(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.launcher.ILauncherManager");
                    obtain.writeString(str);
                    if (!this.mRemote.transact(7, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().isAppiconShow(str);
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.launcher.ILauncherManager
            public boolean isWidgetShow(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.launcher.ILauncherManager");
                    obtain.writeString(str);
                    if (!this.mRemote.transact(10, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().isWidgetShow(str);
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.launcher.ILauncherManager
            public void registerApptrayListener(IApptrayListener iApptrayListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.launcher.ILauncherManager");
                    obtain.writeStrongBinder(iApptrayListener != null ? iApptrayListener.asBinder() : null);
                    if (!this.mRemote.transact(5, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerApptrayListener(iApptrayListener);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.launcher.ILauncherManager
            public void registerWidgetListener(String str, IWidgetListener iWidgetListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.launcher.ILauncherManager");
                    obtain.writeString(str);
                    obtain.writeStrongBinder(iWidgetListener != null ? iWidgetListener.asBinder() : null);
                    if (!this.mRemote.transact(8, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerWidgetListener(str, iWidgetListener);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.launcher.ILauncherManager
            public void setBadgetType(int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.launcher.ILauncherManager");
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(3, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().setBadgetType(i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.launcher.ILauncherManager
            public void showAppTray(boolean z) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.launcher.ILauncherManager");
                    obtain.writeInt(z ? 1 : 0);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().showAppTray(z);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.launcher.ILauncherManager
            public void unregisterApptrayListener(IApptrayListener iApptrayListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.launcher.ILauncherManager");
                    obtain.writeStrongBinder(iApptrayListener != null ? iApptrayListener.asBinder() : null);
                    if (!this.mRemote.transact(6, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterApptrayListener(iApptrayListener);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.launcher.ILauncherManager
            public void unregisterWidgetListener(IWidgetListener iWidgetListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.launcher.ILauncherManager");
                    obtain.writeStrongBinder(iWidgetListener != null ? iWidgetListener.asBinder() : null);
                    if (!this.mRemote.transact(9, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterWidgetListener(iWidgetListener);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.launcher.ILauncherManager
            public void updateBadge(String str, int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.launcher.ILauncherManager");
                    obtain.writeString(str);
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(4, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().updateBadge(str, i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, "com.yfve.ici.app.launcher.ILauncherManager");
        }

        public static ILauncherManager asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface("com.yfve.ici.app.launcher.ILauncherManager");
            if (queryLocalInterface != null && (queryLocalInterface instanceof ILauncherManager)) {
                return (ILauncherManager) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static ILauncherManager getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(ILauncherManager iLauncherManager) {
            if (Proxy.sDefaultImpl != null || iLauncherManager == null) {
                return false;
            }
            Proxy.sDefaultImpl = iLauncherManager;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1598968902) {
                switch (i) {
                    case 1:
                        parcel.enforceInterface("com.yfve.ici.app.launcher.ILauncherManager");
                        showAppTray(parcel.readInt() != 0);
                        parcel2.writeNoException();
                        return true;
                    case 2:
                        parcel.enforceInterface("com.yfve.ici.app.launcher.ILauncherManager");
                        changeIcon(parcel.readString(), parcel.readInt());
                        parcel2.writeNoException();
                        return true;
                    case 3:
                        parcel.enforceInterface("com.yfve.ici.app.launcher.ILauncherManager");
                        setBadgetType(parcel.readInt());
                        parcel2.writeNoException();
                        return true;
                    case 4:
                        parcel.enforceInterface("com.yfve.ici.app.launcher.ILauncherManager");
                        updateBadge(parcel.readString(), parcel.readInt());
                        parcel2.writeNoException();
                        return true;
                    case 5:
                        parcel.enforceInterface("com.yfve.ici.app.launcher.ILauncherManager");
                        registerApptrayListener(IApptrayListener.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 6:
                        parcel.enforceInterface("com.yfve.ici.app.launcher.ILauncherManager");
                        unregisterApptrayListener(IApptrayListener.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 7:
                        parcel.enforceInterface("com.yfve.ici.app.launcher.ILauncherManager");
                        boolean isAppiconShow = isAppiconShow(parcel.readString());
                        parcel2.writeNoException();
                        parcel2.writeInt(isAppiconShow ? 1 : 0);
                        return true;
                    case 8:
                        parcel.enforceInterface("com.yfve.ici.app.launcher.ILauncherManager");
                        registerWidgetListener(parcel.readString(), IWidgetListener.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 9:
                        parcel.enforceInterface("com.yfve.ici.app.launcher.ILauncherManager");
                        unregisterWidgetListener(IWidgetListener.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 10:
                        parcel.enforceInterface("com.yfve.ici.app.launcher.ILauncherManager");
                        boolean isWidgetShow = isWidgetShow(parcel.readString());
                        parcel2.writeNoException();
                        parcel2.writeInt(isWidgetShow ? 1 : 0);
                        return true;
                    default:
                        return super.onTransact(i, parcel, parcel2, i2);
                }
            }
            parcel2.writeString("com.yfve.ici.app.launcher.ILauncherManager");
            return true;
        }
    }

    void changeIcon(String str, int i) throws RemoteException;

    boolean isAppiconShow(String str) throws RemoteException;

    boolean isWidgetShow(String str) throws RemoteException;

    void registerApptrayListener(IApptrayListener iApptrayListener) throws RemoteException;

    void registerWidgetListener(String str, IWidgetListener iWidgetListener) throws RemoteException;

    void setBadgetType(int i) throws RemoteException;

    void showAppTray(boolean z) throws RemoteException;

    void unregisterApptrayListener(IApptrayListener iApptrayListener) throws RemoteException;

    void unregisterWidgetListener(IWidgetListener iWidgetListener) throws RemoteException;

    void updateBadge(String str, int i) throws RemoteException;
}
