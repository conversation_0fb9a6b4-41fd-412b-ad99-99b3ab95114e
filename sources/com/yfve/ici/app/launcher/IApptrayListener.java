package com.yfve.ici.app.launcher;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface IApptrayListener extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IApptrayListener {
        @Override // com.yfve.ici.app.launcher.IApptrayListener
        public void apptrayStatusChanged(boolean z) throws RemoteException {
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.launcher.IApptrayListener
        public void onApptrayIconClick(int i) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IApptrayListener {
        public static final String DESCRIPTOR = "com.yfve.ici.app.launcher.IApptrayListener";
        public static final int TRANSACTION_apptrayStatusChanged = 2;
        public static final int TRANSACTION_onApptrayIconClick = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements IApptrayListener {
            public static IApptrayListener sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // com.yfve.ici.app.launcher.IApptrayListener
            public void apptrayStatusChanged(boolean z) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeInt(z ? 1 : 0);
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().apptrayStatusChanged(z);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            @Override // com.yfve.ici.app.launcher.IApptrayListener
            public void onApptrayIconClick(int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onApptrayIconClick(i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static IApptrayListener asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof IApptrayListener)) {
                return (IApptrayListener) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IApptrayListener getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IApptrayListener iApptrayListener) {
            if (Proxy.sDefaultImpl != null || iApptrayListener == null) {
                return false;
            }
            Proxy.sDefaultImpl = iApptrayListener;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i == 1) {
                parcel.enforceInterface(DESCRIPTOR);
                onApptrayIconClick(parcel.readInt());
                parcel2.writeNoException();
                return true;
            } else if (i != 2) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString(DESCRIPTOR);
                return true;
            } else {
                parcel.enforceInterface(DESCRIPTOR);
                apptrayStatusChanged(parcel.readInt() != 0);
                parcel2.writeNoException();
                return true;
            }
        }
    }

    void apptrayStatusChanged(boolean z) throws RemoteException;

    void onApptrayIconClick(int i) throws RemoteException;
}
