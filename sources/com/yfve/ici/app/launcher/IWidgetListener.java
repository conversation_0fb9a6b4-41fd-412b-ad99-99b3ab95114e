package com.yfve.ici.app.launcher;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface IWidgetListener extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IWidgetListener {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.launcher.IWidgetListener
        public void widgetStatusChanged(boolean z) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IWidgetListener {
        public static final String DESCRIPTOR = "com.yfve.ici.app.launcher.IWidgetListener";
        public static final int TRANSACTION_widgetStatusChanged = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements IWidgetListener {
            public static IWidgetListener sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            @Override // com.yfve.ici.app.launcher.IWidgetListener
            public void widgetStatusChanged(boolean z) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeInt(z ? 1 : 0);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().widgetStatusChanged(z);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static IWidgetListener asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof IWidgetListener)) {
                return (IWidgetListener) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IWidgetListener getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IWidgetListener iWidgetListener) {
            if (Proxy.sDefaultImpl != null || iWidgetListener == null) {
                return false;
            }
            Proxy.sDefaultImpl = iWidgetListener;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString(DESCRIPTOR);
                return true;
            }
            parcel.enforceInterface(DESCRIPTOR);
            widgetStatusChanged(parcel.readInt() != 0);
            parcel2.writeNoException();
            return true;
        }
    }

    void widgetStatusChanged(boolean z) throws RemoteException;
}
