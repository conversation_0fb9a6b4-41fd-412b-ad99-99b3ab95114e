package com.yfve.ici.app.launcher;

import android.os.RemoteException;
import android.util.Log;
import com.yfve.ici.app.launcher.IApptrayListener;
import com.yfve.ici.app.launcher.IWidgetListener;
import com.yfve.ici.service.base.BaseProxy;
import com.yfve.ici.service.contanst.ServiceConstant;

/* loaded from: classes.dex */
public class ICILauncherProxy extends BaseProxy<ILauncherManager> {
    public static ICILauncherProxy PROXY = null;
    public static final String TAG = "ICILauncherProxy";

    public static synchronized ICILauncherProxy getInstance() {
        ICILauncherProxy iCILauncherProxy;
        synchronized (ICILauncherProxy.class) {
            if (PROXY == null) {
                PROXY = new ICILauncherProxy();
            }
            iCILauncherProxy = PROXY;
        }
        return iCILauncherProxy;
    }

    public void changeIcon(String str, int i) {
        Log.d(TAG, "changeIcon: " + str + i);
        if (isAvailable()) {
            try {
                ((ILauncherManager) this.mInterface).changeIcon(str, i);
                return;
            } catch (RemoteException e) {
                e.printStackTrace();
                return;
            }
        }
        Log.d(TAG, "changeIcon: service is not bind");
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public String getServiceName() {
        return ServiceConstant.LAUNCHER_BINDER_NAME;
    }

    public boolean isAppiconShow(String str) {
        if (isAvailable()) {
            try {
                return ((ILauncherManager) this.mInterface).isAppiconShow(str);
            } catch (RemoteException e) {
                e.printStackTrace();
                return false;
            }
        }
        Log.d(TAG, "isAppiconShow: service is not bind");
        return false;
    }

    public boolean isWidgetShow(String str) {
        if (isAvailable()) {
            try {
                return ((ILauncherManager) this.mInterface).isWidgetShow(str);
            } catch (RemoteException e) {
                e.printStackTrace();
                return false;
            }
        }
        Log.d(TAG, "isWidgetShow: service is not bind");
        return false;
    }

    public void registerApptrayListener(IApptrayListener.Stub stub) {
        if (isAvailable()) {
            Log.d(TAG, "registerApptrayListener: ");
            try {
                ((ILauncherManager) this.mInterface).registerApptrayListener(stub);
                return;
            } catch (RemoteException e) {
                e.printStackTrace();
                return;
            }
        }
        Log.d(TAG, "registerApptrayListener: sevice is not bind");
    }

    public void registerWidgetListener(String str, IWidgetListener.Stub stub) {
        if (isAvailable()) {
            Log.d(TAG, "registerWidgetListener: ");
            try {
                ((ILauncherManager) this.mInterface).registerWidgetListener(str, stub);
                return;
            } catch (RemoteException e) {
                e.printStackTrace();
                return;
            }
        }
        Log.d(TAG, "registerWidgetListener: sevice is not bind");
    }

    public void setBadgetType(int i) {
        Log.d(TAG, "setBadgetType: " + i);
        if (isAvailable()) {
            try {
                ((ILauncherManager) this.mInterface).setBadgetType(i);
                return;
            } catch (RemoteException e) {
                e.printStackTrace();
                return;
            }
        }
        Log.d(TAG, "setBadgetType: service is not bind");
    }

    public void showAppTray(boolean z) {
        Log.d(TAG, "showAppTray: " + z);
        if (isAvailable()) {
            try {
                ((ILauncherManager) this.mInterface).showAppTray(z);
                return;
            } catch (RemoteException e) {
                e.printStackTrace();
                return;
            }
        }
        Log.d(TAG, "showAppTray: service is not bind");
    }

    public void unregisterApptrayListener(IApptrayListener.Stub stub) {
        if (isAvailable()) {
            Log.d(TAG, "registerApptrayListener: ");
            try {
                ((ILauncherManager) this.mInterface).unregisterApptrayListener(stub);
                return;
            } catch (RemoteException e) {
                e.printStackTrace();
                return;
            }
        }
        Log.d(TAG, "unregisterApptrayListener: sevice is not bind");
    }

    public void unregisterWidgetListener(IWidgetListener.Stub stub) {
        if (isAvailable()) {
            Log.d(TAG, "unregisterWidgetListener: ");
            try {
                ((ILauncherManager) this.mInterface).unregisterWidgetListener(stub);
                return;
            } catch (RemoteException e) {
                e.printStackTrace();
                return;
            }
        }
        Log.d(TAG, "unregisterWidgetListener: sevice is not bind");
    }

    public void updateBadge(String str, int i) {
        Log.d(TAG, "updateBadge: " + str + i);
        if (isAvailable()) {
            try {
                ((ILauncherManager) this.mInterface).updateBadge(str, i);
                return;
            } catch (RemoteException e) {
                e.printStackTrace();
                return;
            }
        }
        Log.d(TAG, "updateBadge: service is not bind");
    }
}
