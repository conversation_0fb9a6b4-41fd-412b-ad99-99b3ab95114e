package com.yfve.ici.app.ambientlighting;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface IAmbientLightingManager extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IAmbientLightingManager {
        @Override // com.yfve.ici.app.ambientlighting.IAmbientLightingManager
        public void OpenAmbientLighting() throws RemoteException {
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IAmbientLightingManager {
        public static final String DESCRIPTOR = "com.yfve.ici.app.ambientlighting.IAmbientLightingManager";
        public static final int TRANSACTION_OpenAmbientLighting = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements IAmbientLightingManager {
            public static IAmbientLightingManager sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // com.yfve.ici.app.ambientlighting.IAmbientLightingManager
            public void OpenAmbientLighting() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.ambientlighting.IAmbientLightingManager");
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().OpenAmbientLighting();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return "com.yfve.ici.app.ambientlighting.IAmbientLightingManager";
            }
        }

        public Stub() {
            attachInterface(this, "com.yfve.ici.app.ambientlighting.IAmbientLightingManager");
        }

        public static IAmbientLightingManager asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface("com.yfve.ici.app.ambientlighting.IAmbientLightingManager");
            if (queryLocalInterface != null && (queryLocalInterface instanceof IAmbientLightingManager)) {
                return (IAmbientLightingManager) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IAmbientLightingManager getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IAmbientLightingManager iAmbientLightingManager) {
            if (Proxy.sDefaultImpl != null || iAmbientLightingManager == null) {
                return false;
            }
            Proxy.sDefaultImpl = iAmbientLightingManager;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString("com.yfve.ici.app.ambientlighting.IAmbientLightingManager");
                return true;
            }
            parcel.enforceInterface("com.yfve.ici.app.ambientlighting.IAmbientLightingManager");
            OpenAmbientLighting();
            parcel2.writeNoException();
            return true;
        }
    }

    void OpenAmbientLighting() throws RemoteException;
}
