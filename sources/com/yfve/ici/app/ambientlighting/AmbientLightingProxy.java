package com.yfve.ici.app.ambientlighting;

import android.os.RemoteException;
import android.util.Log;
import com.yfve.ici.service.base.BaseProxy;
import com.yfve.ici.service.contanst.ServiceConstant;

/* loaded from: classes.dex */
public class AmbientLightingProxy extends BaseProxy<IAmbientLightingManager> {
    public static final String TAG = "AmbientLightingProxy";
    public static AmbientLightingProxy mAmbientLightingProxy;

    public static synchronized AmbientLightingProxy getInstance() {
        AmbientLightingProxy ambientLightingProxy;
        synchronized (AmbientLightingProxy.class) {
            if (mAmbientLightingProxy == null) {
                mAmbientLightingProxy = new AmbientLightingProxy();
            }
            ambientLightingProxy = mAmbientLightingProxy;
        }
        return ambientLightingProxy;
    }

    public void OpenAmbientLighting() {
        Log.d(TAG, "OpenAmbientLighting --- start");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "OpenAmbientLighting~~mInterface is null");
            } else {
                ((IAmbientLightingManager) this.mInterface).OpenAmbientLighting();
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public String getServiceName() {
        return ServiceConstant.AMBIENTLIGHTING_BINDER_NAME;
    }
}
