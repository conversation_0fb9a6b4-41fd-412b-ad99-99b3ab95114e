package com.yfve.ici.app.wallpapertheme;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import com.yfve.ici.app.wallpapertheme.IThemeListener;
import com.yfve.ici.app.wallpapertheme.IWallpaperListener;

/* loaded from: classes.dex */
public interface IWallpaperThemeManager extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IWallpaperThemeManager {
        @Override // com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager
        public void OpenWallpaperTheme() throws RemoteException {
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager
        public void registerThemeListener(IThemeListener iThemeListener) throws RemoteException {
        }

        @Override // com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager
        public void registerWallpaperListener(IWallpaperListener iWallpaperListener) throws RemoteException {
        }

        @Override // com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager
        public void setCurrent(String str, String str2, String str3) throws RemoteException {
        }

        @Override // com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager
        public void unregisterThemeListener(IThemeListener iThemeListener) throws RemoteException {
        }

        @Override // com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager
        public void unregisterWallpaperListener(IWallpaperListener iWallpaperListener) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IWallpaperThemeManager {
        public static final String DESCRIPTOR = "com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager";
        public static final int TRANSACTION_OpenWallpaperTheme = 6;
        public static final int TRANSACTION_registerThemeListener = 1;
        public static final int TRANSACTION_registerWallpaperListener = 3;
        public static final int TRANSACTION_setCurrent = 5;
        public static final int TRANSACTION_unregisterThemeListener = 2;
        public static final int TRANSACTION_unregisterWallpaperListener = 4;

        /* loaded from: classes.dex */
        public static class Proxy implements IWallpaperThemeManager {
            public static IWallpaperThemeManager sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager
            public void OpenWallpaperTheme() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager");
                    if (!this.mRemote.transact(6, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().OpenWallpaperTheme();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return "com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager";
            }

            @Override // com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager
            public void registerThemeListener(IThemeListener iThemeListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager");
                    obtain.writeStrongBinder(iThemeListener != null ? iThemeListener.asBinder() : null);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerThemeListener(iThemeListener);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager
            public void registerWallpaperListener(IWallpaperListener iWallpaperListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager");
                    obtain.writeStrongBinder(iWallpaperListener != null ? iWallpaperListener.asBinder() : null);
                    if (!this.mRemote.transact(3, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerWallpaperListener(iWallpaperListener);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager
            public void setCurrent(String str, String str2, String str3) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager");
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeString(str3);
                    if (!this.mRemote.transact(5, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().setCurrent(str, str2, str3);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager
            public void unregisterThemeListener(IThemeListener iThemeListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager");
                    obtain.writeStrongBinder(iThemeListener != null ? iThemeListener.asBinder() : null);
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterThemeListener(iThemeListener);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager
            public void unregisterWallpaperListener(IWallpaperListener iWallpaperListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager");
                    obtain.writeStrongBinder(iWallpaperListener != null ? iWallpaperListener.asBinder() : null);
                    if (!this.mRemote.transact(4, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterWallpaperListener(iWallpaperListener);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, "com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager");
        }

        public static IWallpaperThemeManager asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface("com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager");
            if (queryLocalInterface != null && (queryLocalInterface instanceof IWallpaperThemeManager)) {
                return (IWallpaperThemeManager) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IWallpaperThemeManager getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IWallpaperThemeManager iWallpaperThemeManager) {
            if (Proxy.sDefaultImpl != null || iWallpaperThemeManager == null) {
                return false;
            }
            Proxy.sDefaultImpl = iWallpaperThemeManager;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1598968902) {
                switch (i) {
                    case 1:
                        parcel.enforceInterface("com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager");
                        registerThemeListener(IThemeListener.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 2:
                        parcel.enforceInterface("com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager");
                        unregisterThemeListener(IThemeListener.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 3:
                        parcel.enforceInterface("com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager");
                        registerWallpaperListener(IWallpaperListener.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 4:
                        parcel.enforceInterface("com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager");
                        unregisterWallpaperListener(IWallpaperListener.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 5:
                        parcel.enforceInterface("com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager");
                        setCurrent(parcel.readString(), parcel.readString(), parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 6:
                        parcel.enforceInterface("com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager");
                        OpenWallpaperTheme();
                        parcel2.writeNoException();
                        return true;
                    default:
                        return super.onTransact(i, parcel, parcel2, i2);
                }
            }
            parcel2.writeString("com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager");
            return true;
        }
    }

    void OpenWallpaperTheme() throws RemoteException;

    void registerThemeListener(IThemeListener iThemeListener) throws RemoteException;

    void registerWallpaperListener(IWallpaperListener iWallpaperListener) throws RemoteException;

    void setCurrent(String str, String str2, String str3) throws RemoteException;

    void unregisterThemeListener(IThemeListener iThemeListener) throws RemoteException;

    void unregisterWallpaperListener(IWallpaperListener iWallpaperListener) throws RemoteException;
}
