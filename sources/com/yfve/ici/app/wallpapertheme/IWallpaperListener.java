package com.yfve.ici.app.wallpapertheme;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface IWallpaperListener extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IWallpaperListener {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.wallpapertheme.IWallpaperListener
        public void onWallpaperChanged(String str) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IWallpaperListener {
        public static final String DESCRIPTOR = "com.yfve.ici.app.wallpapertheme.IWallpaperListener";
        public static final int TRANSACTION_onWallpaperChanged = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements IWallpaperListener {
            public static IWallpaperListener sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            @Override // com.yfve.ici.app.wallpapertheme.IWallpaperListener
            public void onWallpaperChanged(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onWallpaperChanged(str);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static IWallpaperListener asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof IWallpaperListener)) {
                return (IWallpaperListener) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IWallpaperListener getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IWallpaperListener iWallpaperListener) {
            if (Proxy.sDefaultImpl != null || iWallpaperListener == null) {
                return false;
            }
            Proxy.sDefaultImpl = iWallpaperListener;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString(DESCRIPTOR);
                return true;
            }
            parcel.enforceInterface(DESCRIPTOR);
            onWallpaperChanged(parcel.readString());
            parcel2.writeNoException();
            return true;
        }
    }

    void onWallpaperChanged(String str) throws RemoteException;
}
