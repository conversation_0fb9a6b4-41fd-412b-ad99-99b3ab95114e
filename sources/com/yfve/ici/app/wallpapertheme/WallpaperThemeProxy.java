package com.yfve.ici.app.wallpapertheme;

import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;
import com.yfve.ici.app.wallpapertheme.IThemeListener;
import com.yfve.ici.app.wallpapertheme.IWallpaperListener;
import com.yfve.ici.service.base.BaseProxy;
import com.yfve.ici.service.contanst.ServiceConstant;

/* loaded from: classes.dex */
public class WallpaperThemeProxy extends BaseProxy<IWallpaperThemeManager> {
    public static final String TAG = "WallpaperTheme";
    public static WallpaperThemeProxy WALLPAPERTHEME_PROXY;

    public static void d(String str, String str2) {
        Log.d(str, getFileLineMethod() + str2);
    }

    public static void e(String str, String str2) {
        Log.e(str, getFileLineMethod() + str2);
    }

    public static String getFileLineMethod() {
        StackTraceElement stackTraceElement = new Exception().getStackTrace()[2];
        StringBuffer stringBuffer = new StringBuffer("[");
        stringBuffer.append(stackTraceElement.getFileName());
        stringBuffer.append(":");
        stringBuffer.append(stackTraceElement.getLineNumber());
        stringBuffer.append("] ");
        return stringBuffer.toString();
    }

    public static synchronized WallpaperThemeProxy getInstnce() {
        WallpaperThemeProxy wallpaperThemeProxy;
        synchronized (WallpaperThemeProxy.class) {
            if (WALLPAPERTHEME_PROXY == null) {
                WALLPAPERTHEME_PROXY = new WallpaperThemeProxy();
            }
            wallpaperThemeProxy = WALLPAPERTHEME_PROXY;
        }
        return wallpaperThemeProxy;
    }

    public static void i(String str, String str2) {
        Log.i(str, getFileLineMethod() + str2);
    }

    public void OpenWallpaperTheme() {
        d(TAG, "OpenWallpaperTheme --- start");
        try {
            if (!isAvailable()) {
                e(TAG, "OpenWallpaperTheme~~mInterface is null");
            } else {
                ((IWallpaperThemeManager) this.mInterface).OpenWallpaperTheme();
            }
        } catch (RemoteException e) {
            i(TAG, e.toString());
        } catch (Exception e2) {
            i(TAG, e2.toString());
        }
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public String getServiceName() {
        return ServiceConstant.WALLPAPERTHEME_BINDER_NAME;
    }

    public String getThemeFolderPath(Context context) {
        Cursor query = context.getContentResolver().query(Uri.parse("content://com.ici.wallpapertheme.provider/current"), null, "name = ?", new String[]{"TopicSetting"}, null);
        if (query == null || !query.moveToFirst()) {
            return "";
        }
        int columnIndex = query.getColumnIndex("path");
        String string = columnIndex >= 0 ? query.getString(columnIndex) : null;
        query.close();
        return !TextUtils.isEmpty(string) ? string : "";
    }

    public String getWallpaperPath(Context context) {
        Cursor query = context.getContentResolver().query(Uri.parse("content://com.ici.wallpapertheme.provider/current"), null, "name = ?", new String[]{"WallpaperSettingIPC"}, null);
        if (query == null || !query.moveToFirst()) {
            return "";
        }
        int columnIndex = query.getColumnIndex("path");
        String string = columnIndex >= 0 ? query.getString(columnIndex) : null;
        query.close();
        return !TextUtils.isEmpty(string) ? string : "";
    }

    public void registerThemeListenerListener(IThemeListener.Stub stub) {
        d(TAG, "注册主题变化监听: " + stub);
        if (isAvailable()) {
            d(TAG, "mInterface.registerThemeListenerListener");
            try {
                ((IWallpaperThemeManager) this.mInterface).registerThemeListener(stub);
                return;
            } catch (RemoteException e) {
                e.printStackTrace();
                return;
            }
        }
        d(TAG, "registerThemeListenerListener: sevice is not bind");
    }

    public void registerWallpaperListener(IWallpaperListener.Stub stub) {
        d(TAG, "注册壁纸变化监听: " + stub);
        if (isAvailable()) {
            d(TAG, "mInterface.registerWallpaperListener");
            try {
                ((IWallpaperThemeManager) this.mInterface).registerWallpaperListener(stub);
                return;
            } catch (RemoteException e) {
                e.printStackTrace();
                return;
            }
        }
        d(TAG, "registerWallpaperListener: sevice is not bind");
    }

    public void setCurrent(String str, String str2, String str3) {
        d(TAG, "setCustomerSetting");
        if (isAvailable()) {
            d(TAG, " mInterface.setCurrent(" + str + "," + str2 + "," + str3 + ")");
            try {
                ((IWallpaperThemeManager) this.mInterface).setCurrent(str, str2, str3);
                return;
            } catch (RemoteException e) {
                e.printStackTrace();
                return;
            }
        }
        d(TAG, "setCurrent: sevice is not bind");
    }

    public void unregisterThemeListenerListener(IThemeListener.Stub stub) {
        d(TAG, "注销主题变化监听： " + stub);
        if (isAvailable()) {
            d(TAG, "mInterface.unregisterThemeListenerListener");
            try {
                ((IWallpaperThemeManager) this.mInterface).unregisterThemeListener(stub);
                return;
            } catch (RemoteException e) {
                e.printStackTrace();
                return;
            }
        }
        d(TAG, "unregisterThemeListenerListener: sevice is not bind");
    }

    public void unregisterWallpaperListener(IWallpaperListener.Stub stub) {
        d(TAG, "注销壁纸变化监听： " + stub);
        if (isAvailable()) {
            d(TAG, "mInterface.unregisterWallpaperListener");
            try {
                ((IWallpaperThemeManager) this.mInterface).unregisterWallpaperListener(stub);
                return;
            } catch (RemoteException e) {
                e.printStackTrace();
                return;
            }
        }
        d(TAG, "unregisterWallpaperListener: sevice is not bind");
    }
}
