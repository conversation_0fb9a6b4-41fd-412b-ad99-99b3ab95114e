package com.yfve.ici.app.account;

import android.os.RemoteException;
import android.util.Log;
import b.a.b.a.a;
import com.yfve.ici.app.account.ISynchronizationListener;
import com.yfve.ici.service.base.BaseProxy;
import com.yfve.ici.service.contanst.ServiceConstant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/* loaded from: classes.dex */
public class SettingSyncProxy extends BaseProxy<ISettingSyncProxy> {
    public static final String TAG = "SettingSyncProxy";
    public static volatile SettingSyncProxy mInstance;
    public Map<String, ISynchronizationCallback> mSynchronizationCallbackList = new HashMap();
    public ISynchronizationListener mSynchronizationListenerAIDL;

    /* loaded from: classes.dex */
    public abstract class SynchronizationListener extends ISynchronizationListener.Stub {
        public final String settingName;

        public SynchronizationListener(String str) {
            this.settingName = str;
        }

        @Override // com.yfve.ici.app.account.ISynchronizationListener
        public String getSettingName() {
            return this.settingName;
        }
    }

    public static SettingSyncProxy getInstance() {
        if (mInstance == null) {
            synchronized (SettingSyncProxy.class) {
                if (mInstance == null) {
                    mInstance = new SettingSyncProxy();
                }
            }
        }
        return mInstance;
    }

    public List<SyncData> getCustomerSetting(String str) {
        String str2 = TAG;
        Log.e(str2, "getCustomerSetting. " + str);
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getCustomerSetting~~mInterface is null");
                return null;
            }
            return ((ISettingSyncProxy) this.mInterface).getCustomerSetting(str);
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return null;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return null;
        }
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public String getServiceName() {
        return ServiceConstant.SETTING_SYNC_BINDER_NAME;
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public void onServiceConnectStatusChanged(boolean z) {
        Map<String, ISynchronizationCallback> map;
        if (z || (map = this.mSynchronizationCallbackList) == null) {
            return;
        }
        map.clear();
    }

    public int registerSynchronizationListener(final String str, ISynchronizationCallback iSynchronizationCallback) {
        String str2 = TAG;
        Log.e(str2, "registerSynchronizationListener in." + str);
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerSynchronizationListener~~mInterface is null");
                return -2147483646;
            }
            if (iSynchronizationCallback != null && this.mSynchronizationCallbackList.get(str) == null) {
                String str3 = TAG;
                Log.e(str3, "registerSynchronizationListener~~put1 " + str + "success");
                this.mSynchronizationCallbackList.put(str, iSynchronizationCallback);
                String str4 = TAG;
                Log.e(str4, "registerSynchronizationListener~~put2 " + str + "success");
                if (this.mSynchronizationCallbackList.size() == 1) {
                    String str5 = TAG;
                    Log.e(str5, "registerSynchronizationListener~~put3 " + str + "success");
                    SynchronizationListener synchronizationListener = new SynchronizationListener(str) { // from class: com.yfve.ici.app.account.SettingSyncProxy.1
                        @Override // com.yfve.ici.app.account.ISynchronizationListener
                        public void onSynchronization(List<SyncData> list) throws RemoteException {
                            String str6 = SettingSyncProxy.TAG;
                            StringBuilder e = a.e("onSynchronization is called. syncData:");
                            e.append(list.toString());
                            Log.d(str6, e.toString());
                            ISynchronizationCallback iSynchronizationCallback2 = (ISynchronizationCallback) SettingSyncProxy.this.mSynchronizationCallbackList.get(str);
                            if (iSynchronizationCallback2 != null) {
                                iSynchronizationCallback2.onSynchronization(list);
                            }
                        }
                    };
                    this.mSynchronizationListenerAIDL = synchronizationListener;
                    ((ISettingSyncProxy) this.mInterface).registerSynchronizationListener(synchronizationListener);
                    return Integer.MIN_VALUE;
                }
            }
            return -2147418113;
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }

    public void setCustomerSetting(String str, List<SyncData> list) {
        try {
            if (!isAvailable()) {
                Log.e(TAG, "setCustomerSetting~~mInterface is null");
                return;
            }
            String str2 = TAG;
            Log.e(str2, "setCustomerSetting. " + str);
            ((ISettingSyncProxy) this.mInterface).setCustomerSetting(str, list);
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public void setVisitorSetting(String str, List<SyncData> list) {
        try {
            if (!isAvailable()) {
                Log.e(TAG, "setVisitorSetting~~mInterface is null");
                return;
            }
            String str2 = TAG;
            Log.e(str2, "setVisitorSetting. " + str);
            ((ISettingSyncProxy) this.mInterface).setVisitorSetting(str, list);
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public int unregisterSynchronizationListener(String str, ISynchronizationCallback iSynchronizationCallback) {
        String str2 = TAG;
        Log.e(str2, "unregisterSynchronizationListener in." + str);
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unregisterSynchronizationListener~~mInterface is null");
                return -2147483646;
            }
            if (iSynchronizationCallback != null && this.mSynchronizationCallbackList.get(str) != null) {
                this.mSynchronizationCallbackList.remove(str);
                if (this.mSynchronizationCallbackList.size() == 0) {
                    ((ISettingSyncProxy) this.mInterface).unregisterSynchronizationListener(this.mSynchronizationListenerAIDL);
                    this.mSynchronizationListenerAIDL = null;
                    return Integer.MIN_VALUE;
                }
            }
            return -2147418113;
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }
}
