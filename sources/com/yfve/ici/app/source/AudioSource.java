package com.yfve.ici.app.source;

import android.graphics.Bitmap;
import android.graphics.drawable.Icon;
import android.os.Parcel;
import android.os.Parcelable;
import b.a.b.a.a;

/* loaded from: classes.dex */
public class AudioSource implements Parcelable, Cloneable {
    public static final Parcelable.Creator<AudioSource> CREATOR = new Parcelable.Creator<AudioSource>() { // from class: com.yfve.ici.app.source.AudioSource.1
        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public AudioSource createFromParcel(Parcel parcel) {
            return new AudioSource(parcel);
        }

        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public AudioSource[] newArray(int i) {
            return new AudioSource[i];
        }
    };
    public Icon icon;
    public boolean isSelected;
    public Icon miniIcon;
    public String packageName;
    public String preSourceType;
    public String searchId;
    public String title;
    public String type;

    public AudioSource(Parcel parcel) {
        this.preSourceType = ICISourceConstant.TYPE_FM;
        this.searchId = "";
        this.packageName = parcel.readString();
        this.title = parcel.readString();
        this.type = parcel.readString();
        this.isSelected = parcel.readByte() != 0;
        this.icon = (Icon) parcel.readParcelable(Bitmap.class.getClassLoader());
        this.miniIcon = (Icon) parcel.readParcelable(Bitmap.class.getClassLoader());
        this.preSourceType = parcel.readString();
        this.searchId = parcel.readString();
    }

    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

    @Override // android.os.Parcelable
    public int describeContents() {
        return 0;
    }

    public Icon getIcon() {
        return this.icon;
    }

    public Icon getMiniIcon() {
        return this.miniIcon;
    }

    public String getPackageName() {
        return this.packageName;
    }

    public String getPreSourceType() {
        return this.preSourceType;
    }

    public String getSearchId() {
        return this.searchId;
    }

    public String getTitle() {
        return this.title;
    }

    public String getType() {
        return this.type;
    }

    public boolean isSelected() {
        return this.isSelected;
    }

    public void setIcon(Icon icon) {
        this.icon = icon;
    }

    public void setMiniIcon(Icon icon) {
        this.miniIcon = icon;
    }

    public void setPackageName(String str) {
        this.packageName = str;
    }

    public void setPreSourceType(String str) {
        this.preSourceType = str;
    }

    public void setSearchId(String str) {
        this.searchId = str;
    }

    public void setSelected(boolean z) {
        this.isSelected = z;
    }

    public void setTitle(String str) {
        this.title = str;
    }

    public void setType(String str) {
        this.type = str;
    }

    public String toString() {
        StringBuilder e = a.e("AudioSource{packageName='");
        a.o(e, this.packageName, '\'', ", title='");
        a.o(e, this.title, '\'', ", type='");
        a.o(e, this.type, '\'', ", isSelected=");
        e.append(this.isSelected);
        e.append(", icon=");
        e.append(this.icon);
        e.append(", miniIcon=");
        e.append(this.miniIcon);
        e.append(", preSourceType='");
        a.o(e, this.preSourceType, '\'', ", searchId='");
        e.append(this.searchId);
        e.append('\'');
        e.append('}');
        return e.toString();
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeString(this.packageName);
        parcel.writeString(this.title);
        parcel.writeString(this.type);
        parcel.writeByte(this.isSelected ? (byte) 1 : (byte) 0);
        parcel.writeParcelable(this.icon, i);
        parcel.writeParcelable(this.miniIcon, i);
        parcel.writeString(this.preSourceType);
        parcel.writeString(this.searchId);
    }

    public AudioSource(String str, String str2, String str3, boolean z, Icon icon, Icon icon2) {
        this.preSourceType = ICISourceConstant.TYPE_FM;
        this.searchId = "";
        this.packageName = str;
        this.title = str2;
        this.type = str3;
        this.isSelected = z;
        this.icon = icon;
        this.miniIcon = icon2;
    }
}
