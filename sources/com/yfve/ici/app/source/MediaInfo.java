package com.yfve.ici.app.source;

import android.os.Parcel;
import android.os.Parcelable;
import android.util.Log;
import androidx.annotation.Nullable;
import b.a.b.a.a;

/* loaded from: classes.dex */
public class MediaInfo implements Parcelable {
    public static final Parcelable.Creator<MediaInfo> CREATOR = new Parcelable.Creator<MediaInfo>() { // from class: com.yfve.ici.app.source.MediaInfo.1
        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public MediaInfo createFromParcel(Parcel parcel) {
            return new MediaInfo(parcel);
        }

        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public MediaInfo[] newArray(int i) {
            return new MediaInfo[i];
        }
    };
    public static final int ORDER = 2;
    public static final int RANDOM = 1;
    public static final int REPEAT = 3;
    public static final int WIDGET_VIEW_TYPE_BT = 2;
    public static final int WIDGET_VIEW_TYPE_BT_NOT_CONNECT = 10;
    public static final int WIDGET_VIEW_TYPE_EMPTY = 7;
    public static final int WIDGET_VIEW_TYPE_LOADING = 8;
    public static final int WIDGET_VIEW_TYPE_MUSIC = 3;
    public static final int WIDGET_VIEW_TYPE_NET_RADIO = 4;
    public static final int WIDGET_VIEW_TYPE_NET_READ = 5;
    public static final int WIDGET_VIEW_TYPE_NEWS = 6;
    public static final int WIDGET_VIEW_TYPE_NO_LYRIC = 12;
    public static final int WIDGET_VIEW_TYPE_RADIO = 1;
    public static final int WIDGET_VIEW_TYPE_SELECT_SOURCE = 9;
    public static final int WIDGET_VIEW_TYPE_VIDEO = 11;
    public String album;
    public String artist;
    public String cover;
    public long duration;
    public int identifier;
    public boolean isPlaying;
    public String lyric;
    public String mediaId;
    public String mediaStatus;
    public int mediaViewType;
    public int mode;
    public String name;
    public String playListTitle;
    public int showType;
    public String sourceType;
    public boolean star;

    public MediaInfo(String str, int i) {
        this.sourceType = str;
        this.mediaViewType = i;
        this.mediaId = "0";
        this.isPlaying = false;
        this.star = false;
        this.name = "";
        this.artist = "";
        this.album = "";
        this.lyric = "";
        this.cover = "";
        this.mode = 2;
        this.playListTitle = "";
        this.mediaStatus = "";
    }

    public static void sendEmptyInfo(String str) {
        Log.i("MediaInfo", "sendEmptyInfo   sourceType:" + str);
        ICISourceProxy.getInstance().setMediaInfo(new MediaInfo(str, 7));
    }

    @Override // android.os.Parcelable
    public int describeContents() {
        return 0;
    }

    public boolean equals(@Nullable Object obj) {
        String str;
        String str2;
        String str3;
        String str4;
        String str5;
        String str6;
        String str7;
        if (obj instanceof MediaInfo) {
            MediaInfo mediaInfo = (MediaInfo) obj;
            if (mediaInfo.sourceType.equals(this.sourceType) && mediaInfo.mediaViewType == this.mediaViewType && mediaInfo.mode == this.mode && (str = mediaInfo.cover) != null && str.equals(this.cover) && (str2 = mediaInfo.lyric) != null && str2.equals(this.lyric) && (str3 = mediaInfo.album) != null && str3.equals(this.album) && (str4 = mediaInfo.artist) != null && str4.equals(this.artist) && (str5 = mediaInfo.name) != null && str5.equals(this.name) && (str6 = mediaInfo.mediaId) != null && str6.equals(this.mediaId) && mediaInfo.isPlaying == this.isPlaying && mediaInfo.star == this.star && mediaInfo.identifier == this.identifier && mediaInfo.showType == this.showType && (str7 = mediaInfo.playListTitle) != null && str7.equals(this.playListTitle)) {
                return true;
            }
        }
        return false;
    }

    public String getAlbum() {
        return this.album;
    }

    public String getArtist() {
        return this.artist;
    }

    public String getCover() {
        return this.cover;
    }

    public long getDuration() {
        return this.duration;
    }

    public int getIdentifier() {
        return this.identifier;
    }

    public String getLyric() {
        return this.lyric;
    }

    public String getMediaId() {
        return this.mediaId;
    }

    public String getMediaStatus() {
        return this.mediaStatus;
    }

    public int getMediaViewType() {
        return this.mediaViewType;
    }

    public int getMode() {
        return this.mode;
    }

    public String getName() {
        return this.name;
    }

    public String getPlayListTitle() {
        return this.playListTitle;
    }

    public int getShowType() {
        return this.showType;
    }

    public String getSourceType() {
        return this.sourceType;
    }

    public boolean isPlaying() {
        return this.isPlaying;
    }

    public boolean isStar() {
        return this.star;
    }

    public void readFromParcel(Parcel parcel) {
        parcel.readString();
        parcel.readInt();
        parcel.readString();
        parcel.readByte();
        parcel.readString();
        parcel.readString();
        parcel.readString();
        parcel.readString();
        parcel.readString();
        parcel.readLong();
        parcel.readByte();
        parcel.readInt();
        parcel.readInt();
        parcel.readString();
        parcel.readInt();
        parcel.readString();
    }

    public void setAlbum(String str) {
        this.album = str;
    }

    public void setArtist(String str) {
        this.artist = str;
    }

    public void setCover(String str) {
        this.cover = str;
    }

    public void setDuration(long j) {
        this.duration = j;
    }

    public void setIdentifier(int i) {
        this.identifier = i;
    }

    public void setLyric(String str) {
        this.lyric = str;
    }

    public void setMediaId(String str) {
        this.mediaId = str;
    }

    public void setMediaStatus(String str) {
        this.mediaStatus = str;
    }

    public void setMediaViewType(int i) {
        this.mediaViewType = i;
    }

    public void setMode(int i) {
        this.mode = i;
    }

    public void setName(String str) {
        this.name = str;
    }

    public void setPlayListTitle(String str) {
        this.playListTitle = str;
    }

    public void setPlaying(boolean z) {
        this.isPlaying = z;
    }

    public void setShowType(int i) {
        this.showType = i;
    }

    public void setSourceType(String str) {
        this.sourceType = str;
    }

    public void setStar(boolean z) {
        this.star = z;
    }

    public String toString() {
        StringBuilder e = a.e("MediaInfo{sourceType='");
        a.o(e, this.sourceType, '\'', ", mediaViewType=");
        e.append(this.mediaViewType);
        e.append(", mediaId='");
        a.o(e, this.mediaId, '\'', ", isPlaying=");
        e.append(this.isPlaying);
        e.append(", duration=");
        e.append(this.duration);
        e.append(", star=");
        e.append(this.star);
        e.append(", mode=");
        e.append(this.mode);
        e.append(", identifier=");
        e.append(this.identifier);
        e.append(", playListTitle='");
        a.o(e, this.playListTitle, '\'', ", showType=");
        e.append(this.showType);
        e.append(", mediaStatus='");
        a.o(e, this.mediaStatus, '\'', ", name='");
        a.o(e, this.name, '\'', ", artist='");
        a.o(e, this.artist, '\'', ", album='");
        a.o(e, this.album, '\'', ", cover='");
        a.o(e, this.cover, '\'', ", lyric='");
        e.append(this.lyric);
        e.append('\'');
        e.append('}');
        return e.toString();
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeString(this.sourceType);
        parcel.writeInt(this.mediaViewType);
        parcel.writeString(this.mediaId);
        parcel.writeByte(this.isPlaying ? (byte) 1 : (byte) 0);
        parcel.writeString(this.name);
        parcel.writeString(this.artist);
        parcel.writeString(this.album);
        parcel.writeString(this.lyric);
        parcel.writeString(this.cover);
        parcel.writeLong(this.duration);
        parcel.writeByte(this.star ? (byte) 1 : (byte) 0);
        parcel.writeInt(this.mode);
        parcel.writeInt(this.identifier);
        parcel.writeString(this.playListTitle);
        parcel.writeInt(this.showType);
        parcel.writeString(this.mediaStatus);
    }

    public MediaInfo(Parcel parcel) {
        this.sourceType = parcel.readString();
        this.mediaViewType = parcel.readInt();
        this.mediaId = parcel.readString();
        this.isPlaying = parcel.readByte() != 0;
        this.name = parcel.readString();
        this.artist = parcel.readString();
        this.album = parcel.readString();
        this.lyric = parcel.readString();
        this.cover = parcel.readString();
        this.duration = parcel.readLong();
        this.star = parcel.readByte() != 0;
        this.mode = parcel.readInt();
        this.identifier = parcel.readInt();
        this.playListTitle = parcel.readString();
        this.showType = parcel.readInt();
        this.mediaStatus = parcel.readString();
    }
}
