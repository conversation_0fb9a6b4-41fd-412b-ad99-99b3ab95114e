package com.yfve.ici.app.source;

import android.graphics.Bitmap;
import android.os.Parcel;
import android.os.RemoteException;
import android.util.Log;
import com.yfve.ici.app.source.IMediaInfoChangeListener;
import com.yfve.ici.app.source.ISourceControlListener;
import com.yfve.ici.service.base.BaseProxy;
import com.yfve.ici.service.contanst.ServiceConstant;
import java.util.List;

/* loaded from: classes.dex */
public class ICISourceProxy extends BaseProxy<ISourceManagerInterface> {
    public static volatile ICISourceProxy PROXY = null;
    public static final String TAG = "ICISourceProxy";
    public ICISourceInstructListener mICIInstructListener;
    public ISourceControlListener mSourceControlInterface;

    /* loaded from: classes.dex */
    public interface ICISourceInstructListener {
        void onPlayInstructChange(AudioSource audioSource, String str);

        void onSourceInstructChange(AudioSource audioSource, String str, String str2);
    }

    public static synchronized ICISourceProxy getInstance() {
        ICISourceProxy iCISourceProxy;
        synchronized (ICISourceProxy.class) {
            if (PROXY == null) {
                synchronized (ICISourceProxy.class) {
                    if (PROXY == null) {
                        PROXY = new ICISourceProxy();
                    }
                }
            }
            iCISourceProxy = PROXY;
        }
        return iCISourceProxy;
    }

    public void dispatchInstruct(String str, String str2) {
        Log.i(TAG, "dispatchInstruct: type=" + str + "    instruct:" + str2);
        if (!isAvailable()) {
            Log.i(TAG, "dispatchInstruct: mInterface==null");
            return;
        }
        try {
            ((ISourceManagerInterface) this.mInterface).dispatchInstruct(str, str2, "");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public MediaInfo getCurrentMediaInfo() {
        Log.i(TAG, "getCurrentMediaInfo: ");
        if (!isAvailable()) {
            Log.e(TAG, "  getCurrentMediaInfo  mInterface  null ");
            return null;
        }
        try {
            return ((ISourceManagerInterface) this.mInterface).getCurrentMediaInfo();
        } catch (RemoteException e) {
            e.printStackTrace();
            return null;
        }
    }

    public AudioSource getLastSource() {
        Log.i(TAG, "getLastSource");
        if (!isAvailable()) {
            Log.i(TAG, "getLastSource: mInterface==null");
            return null;
        }
        try {
            return ((ISourceManagerInterface) this.mInterface).getLastSource();
        } catch (RemoteException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public String getServiceName() {
        return ServiceConstant.SOURCE_VR_BINDER_NAME;
    }

    public List<AudioSource> getSourceList() {
        Log.i(TAG, "getSourceList: ");
        if (!isAvailable()) {
            Log.e(TAG, "  getSourceList  mInterface  null ");
            return null;
        }
        try {
            return ((ISourceManagerInterface) this.mInterface).getSourceList();
        } catch (RemoteException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public void onServiceConnectStatusChanged(boolean z) {
        Log.e(TAG, "onServiceConnectStatusChanged~~status:" + z);
        if (z) {
            return;
        }
        this.mICIInstructListener = null;
        this.mSourceControlInterface = null;
    }

    public void registerMediaInfoChangedListener(IMediaInfoChangeListener.Stub stub) {
        if (!isAvailable()) {
            Log.i(TAG, "registerMediaInfoChangedListener  interface  null");
            return;
        }
        try {
            ((ISourceManagerInterface) this.mInterface).registerMediaInfoChangeListener(stub);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void registerSourceInstructListener(ICISourceInstructListener iCISourceInstructListener) {
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerSourceInstructListener~~iciInstructListener is null");
                return;
            }
            this.mICIInstructListener = iCISourceInstructListener;
            if (iCISourceInstructListener == null || this.mSourceControlInterface != null) {
                return;
            }
            ISourceControlListener.Stub stub = new ISourceControlListener.Stub() { // from class: com.yfve.ici.app.source.ICISourceProxy.1
                @Override // com.yfve.ici.app.source.ISourceControlListener
                public void onPlayInstructChange(AudioSource audioSource, String str) throws RemoteException {
                    Log.i(ICISourceProxy.TAG, "onPlayInstructChange: curSourceType = " + audioSource + "      instruct = " + str);
                    if (ICISourceProxy.this.mICIInstructListener != null) {
                        ICISourceProxy.this.mICIInstructListener.onPlayInstructChange(audioSource, str);
                    }
                }

                @Override // com.yfve.ici.app.source.ISourceControlListener
                public void onSourceInstructChange(AudioSource audioSource, String str, String str2) throws RemoteException {
                    Log.i(ICISourceProxy.TAG, "onSourceInstructChange: control = " + str + "      arg = " + str2);
                    if (ICISourceProxy.this.mICIInstructListener != null) {
                        ICISourceProxy.this.mICIInstructListener.onSourceInstructChange(audioSource, str, str2);
                    }
                }

                @Override // com.yfve.ici.app.source.ISourceControlListener.Stub, android.os.Binder
                public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
                    try {
                        return super.onTransact(i, parcel, parcel2, i2);
                    } catch (RuntimeException e) {
                        Log.i(ICISourceProxy.TAG, "Unexpected remote exception", e);
                        throw e;
                    }
                }
            };
            this.mSourceControlInterface = stub;
            ((ISourceManagerInterface) this.mInterface).registerSourceListener(stub);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void setBitmap(Bitmap bitmap, String str) {
        Log.i(TAG, "setBitmap: url:" + str + "    bitmap:" + bitmap);
        if (!isAvailable()) {
            Log.e(TAG, "  setBitmap  mInterface  null ");
            return;
        }
        try {
            ((ISourceManagerInterface) this.mInterface).setBitmap(bitmap, str);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void setFavorite(boolean z) {
        Log.i(TAG, "setFavorite: isFavorite:" + z);
        if (!isAvailable()) {
            Log.e(TAG, "  setFavorite  mInterface  null ");
            return;
        }
        try {
            ((ISourceManagerInterface) this.mInterface).setFavorite(z);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void setLyricShow(boolean z) {
        Log.i(TAG, "setLyricShow: isShow : " + z);
        if (!isAvailable()) {
            Log.e(TAG, "  setLyricShow  mInterface  null ");
            return;
        }
        try {
            ((ISourceManagerInterface) this.mInterface).setLyricShow(z);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void setMediaInfo(MediaInfo mediaInfo) {
        Log.i(TAG, "setMediaInfo: mediaInfo:" + mediaInfo);
        if (!isAvailable()) {
            Log.i(TAG, "setMediaInfo: mInterface==null");
            return;
        }
        try {
            ((ISourceManagerInterface) this.mInterface).setMediaInfo(mediaInfo);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void setPlayLyric(String str) {
        Log.i(TAG, "setPlayLyric: lyric:" + str);
        if (!isAvailable()) {
            Log.i(TAG, "setPlayLyric: mInterface==null");
            return;
        }
        try {
            ((ISourceManagerInterface) this.mInterface).setPlatLyric(str);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void setPlayMode(int i) {
        Log.i(TAG, "setPlayMode: type:" + i);
        if (!isAvailable()) {
            Log.i(TAG, "setPlayMode: mInterface==null");
            return;
        }
        try {
            ((ISourceManagerInterface) this.mInterface).setPlayMode(i);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void setPlayProgress(int i) {
        Log.i(TAG, "setPlayProgress: progress:" + i);
        if (!isAvailable()) {
            Log.i(TAG, "setPlayProgress: mInterface==null");
            return;
        }
        try {
            ((ISourceManagerInterface) this.mInterface).setPlayProgress(i);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void setPlaying(boolean z) {
        Log.i(TAG, "setPlaying: isPlaying:" + z);
        if (!isAvailable()) {
            Log.e(TAG, "  setPlaying  mInterface  null ");
            return;
        }
        try {
            ((ISourceManagerInterface) this.mInterface).setPlaying(z);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void switchSource(String str, boolean z) {
        Log.i(TAG, "switchSource: type=" + str + "     isShowView:" + z);
        if (!isAvailable()) {
            Log.i(TAG, "switchSource: mInterface==null");
            return;
        }
        try {
            ((ISourceManagerInterface) this.mInterface).switchSource(str, z);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void unRegisterSourceInstructListener() {
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unRegisterSourceInstructListener~~iciInstructListener is null");
            } else if (this.mSourceControlInterface != null) {
                ((ISourceManagerInterface) this.mInterface).unRegisterSourceListener(this.mSourceControlInterface);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void unregisterMediaInfoChangedListener(IMediaInfoChangeListener.Stub stub) {
        if (!isAvailable()) {
            Log.i(TAG, "registerMediaInfoChangedListener  interface  null");
            return;
        }
        try {
            ((ISourceManagerInterface) this.mInterface).unregisteronMediaInfoChangeListener(stub);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void dispatchInstruct(String str, String str2, String str3) {
        Log.i(TAG, "dispatchInstruct: type=" + str + "    instruct:" + str2 + "    arg :" + str3);
        if (!isAvailable()) {
            Log.i(TAG, "dispatchInstruct: mInterface==null");
            return;
        }
        try {
            ((ISourceManagerInterface) this.mInterface).dispatchInstruct(str, str2, str3);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }
}
