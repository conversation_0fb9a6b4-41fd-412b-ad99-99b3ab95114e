package com.yfve.ici.app.source;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import com.yfve.ici.app.source.IClientInterface;
import com.yfve.ici.app.source.ITitleInterface;

/* loaded from: classes.dex */
public interface IAudioSourceInterface extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IAudioSourceInterface {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.source.IAudioSourceInterface
        public void registerClient(IClientInterface iClientInterface) throws RemoteException {
        }

        @Override // com.yfve.ici.app.source.IAudioSourceInterface
        public void registerOnTitleClick(ITitleInterface iTitleInterface) throws RemoteException {
        }

        @Override // com.yfve.ici.app.source.IAudioSourceInterface
        public void setTitleClick(boolean z) throws RemoteException {
        }

        @Override // com.yfve.ici.app.source.IAudioSourceInterface
        public void setUpdateView() throws RemoteException {
        }

        @Override // com.yfve.ici.app.source.IAudioSourceInterface
        public void startSearchActivity() throws RemoteException {
        }

        @Override // com.yfve.ici.app.source.IAudioSourceInterface
        public void startSettingActivity() throws RemoteException {
        }

        @Override // com.yfve.ici.app.source.IAudioSourceInterface
        public void switchSource(int i) throws RemoteException {
        }

        @Override // com.yfve.ici.app.source.IAudioSourceInterface
        public void switchSourceItem(AudioSource audioSource) throws RemoteException {
        }

        @Override // com.yfve.ici.app.source.IAudioSourceInterface
        public void unRegisterClient(IClientInterface iClientInterface) throws RemoteException {
        }

        @Override // com.yfve.ici.app.source.IAudioSourceInterface
        public void unregisterOnTitleClick(ITitleInterface iTitleInterface) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IAudioSourceInterface {
        public static final String DESCRIPTOR = "com.yfve.ici.app.source.IAudioSourceInterface";
        public static final int TRANSACTION_registerClient = 1;
        public static final int TRANSACTION_registerOnTitleClick = 3;
        public static final int TRANSACTION_setTitleClick = 8;
        public static final int TRANSACTION_setUpdateView = 7;
        public static final int TRANSACTION_startSearchActivity = 9;
        public static final int TRANSACTION_startSettingActivity = 10;
        public static final int TRANSACTION_switchSource = 5;
        public static final int TRANSACTION_switchSourceItem = 6;
        public static final int TRANSACTION_unRegisterClient = 2;
        public static final int TRANSACTION_unregisterOnTitleClick = 4;

        /* loaded from: classes.dex */
        public static class Proxy implements IAudioSourceInterface {
            public static IAudioSourceInterface sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return "com.yfve.ici.app.source.IAudioSourceInterface";
            }

            @Override // com.yfve.ici.app.source.IAudioSourceInterface
            public void registerClient(IClientInterface iClientInterface) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.source.IAudioSourceInterface");
                    obtain.writeStrongBinder(iClientInterface != null ? iClientInterface.asBinder() : null);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerClient(iClientInterface);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.source.IAudioSourceInterface
            public void registerOnTitleClick(ITitleInterface iTitleInterface) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.source.IAudioSourceInterface");
                    obtain.writeStrongBinder(iTitleInterface != null ? iTitleInterface.asBinder() : null);
                    if (!this.mRemote.transact(3, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerOnTitleClick(iTitleInterface);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.source.IAudioSourceInterface
            public void setTitleClick(boolean z) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.source.IAudioSourceInterface");
                    obtain.writeInt(z ? 1 : 0);
                    if (!this.mRemote.transact(8, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().setTitleClick(z);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.source.IAudioSourceInterface
            public void setUpdateView() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.source.IAudioSourceInterface");
                    if (!this.mRemote.transact(7, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().setUpdateView();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.source.IAudioSourceInterface
            public void startSearchActivity() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.source.IAudioSourceInterface");
                    if (!this.mRemote.transact(9, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().startSearchActivity();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.source.IAudioSourceInterface
            public void startSettingActivity() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.source.IAudioSourceInterface");
                    if (!this.mRemote.transact(10, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().startSettingActivity();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.source.IAudioSourceInterface
            public void switchSource(int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.source.IAudioSourceInterface");
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(5, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().switchSource(i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.source.IAudioSourceInterface
            public void switchSourceItem(AudioSource audioSource) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.source.IAudioSourceInterface");
                    if (audioSource != null) {
                        obtain.writeInt(1);
                        audioSource.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    if (!this.mRemote.transact(6, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().switchSourceItem(audioSource);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.source.IAudioSourceInterface
            public void unRegisterClient(IClientInterface iClientInterface) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.source.IAudioSourceInterface");
                    obtain.writeStrongBinder(iClientInterface != null ? iClientInterface.asBinder() : null);
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unRegisterClient(iClientInterface);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.source.IAudioSourceInterface
            public void unregisterOnTitleClick(ITitleInterface iTitleInterface) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.source.IAudioSourceInterface");
                    obtain.writeStrongBinder(iTitleInterface != null ? iTitleInterface.asBinder() : null);
                    if (!this.mRemote.transact(4, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterOnTitleClick(iTitleInterface);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, "com.yfve.ici.app.source.IAudioSourceInterface");
        }

        public static IAudioSourceInterface asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface("com.yfve.ici.app.source.IAudioSourceInterface");
            if (queryLocalInterface != null && (queryLocalInterface instanceof IAudioSourceInterface)) {
                return (IAudioSourceInterface) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IAudioSourceInterface getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IAudioSourceInterface iAudioSourceInterface) {
            if (Proxy.sDefaultImpl != null || iAudioSourceInterface == null) {
                return false;
            }
            Proxy.sDefaultImpl = iAudioSourceInterface;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1598968902) {
                switch (i) {
                    case 1:
                        parcel.enforceInterface("com.yfve.ici.app.source.IAudioSourceInterface");
                        registerClient(IClientInterface.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 2:
                        parcel.enforceInterface("com.yfve.ici.app.source.IAudioSourceInterface");
                        unRegisterClient(IClientInterface.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 3:
                        parcel.enforceInterface("com.yfve.ici.app.source.IAudioSourceInterface");
                        registerOnTitleClick(ITitleInterface.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 4:
                        parcel.enforceInterface("com.yfve.ici.app.source.IAudioSourceInterface");
                        unregisterOnTitleClick(ITitleInterface.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 5:
                        parcel.enforceInterface("com.yfve.ici.app.source.IAudioSourceInterface");
                        switchSource(parcel.readInt());
                        parcel2.writeNoException();
                        return true;
                    case 6:
                        parcel.enforceInterface("com.yfve.ici.app.source.IAudioSourceInterface");
                        switchSourceItem(parcel.readInt() != 0 ? AudioSource.CREATOR.createFromParcel(parcel) : null);
                        parcel2.writeNoException();
                        return true;
                    case 7:
                        parcel.enforceInterface("com.yfve.ici.app.source.IAudioSourceInterface");
                        setUpdateView();
                        parcel2.writeNoException();
                        return true;
                    case 8:
                        parcel.enforceInterface("com.yfve.ici.app.source.IAudioSourceInterface");
                        setTitleClick(parcel.readInt() != 0);
                        parcel2.writeNoException();
                        return true;
                    case 9:
                        parcel.enforceInterface("com.yfve.ici.app.source.IAudioSourceInterface");
                        startSearchActivity();
                        parcel2.writeNoException();
                        return true;
                    case 10:
                        parcel.enforceInterface("com.yfve.ici.app.source.IAudioSourceInterface");
                        startSettingActivity();
                        parcel2.writeNoException();
                        return true;
                    default:
                        return super.onTransact(i, parcel, parcel2, i2);
                }
            }
            parcel2.writeString("com.yfve.ici.app.source.IAudioSourceInterface");
            return true;
        }
    }

    void registerClient(IClientInterface iClientInterface) throws RemoteException;

    void registerOnTitleClick(ITitleInterface iTitleInterface) throws RemoteException;

    void setTitleClick(boolean z) throws RemoteException;

    void setUpdateView() throws RemoteException;

    void startSearchActivity() throws RemoteException;

    void startSettingActivity() throws RemoteException;

    void switchSource(int i) throws RemoteException;

    void switchSourceItem(AudioSource audioSource) throws RemoteException;

    void unRegisterClient(IClientInterface iClientInterface) throws RemoteException;

    void unregisterOnTitleClick(ITitleInterface iTitleInterface) throws RemoteException;
}
