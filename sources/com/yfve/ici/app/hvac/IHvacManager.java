package com.yfve.ici.app.hvac;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface IHvacManager extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IHvacManager {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.hvac.IHvacManager
        public void openHvac() throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IHvacManager {
        public static final String DESCRIPTOR = "com.yfve.ici.app.hvac.IHvacManager";
        public static final int TRANSACTION_openHvac = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements IHvacManager {
            public static IHvacManager sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return "com.yfve.ici.app.hvac.IHvacManager";
            }

            @Override // com.yfve.ici.app.hvac.IHvacManager
            public void openHvac() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.hvac.IHvacManager");
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().openHvac();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, "com.yfve.ici.app.hvac.IHvacManager");
        }

        public static IHvacManager asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface("com.yfve.ici.app.hvac.IHvacManager");
            if (queryLocalInterface != null && (queryLocalInterface instanceof IHvacManager)) {
                return (IHvacManager) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IHvacManager getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IHvacManager iHvacManager) {
            if (Proxy.sDefaultImpl != null || iHvacManager == null) {
                return false;
            }
            Proxy.sDefaultImpl = iHvacManager;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString("com.yfve.ici.app.hvac.IHvacManager");
                return true;
            }
            parcel.enforceInterface("com.yfve.ici.app.hvac.IHvacManager");
            openHvac();
            parcel2.writeNoException();
            return true;
        }
    }

    void openHvac() throws RemoteException;
}
