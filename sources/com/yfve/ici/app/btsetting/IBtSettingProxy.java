package com.yfve.ici.app.btsetting;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import com.yfve.ici.app.btsetting.IBtSettingListenerAIDL;
import com.yfve.ici.app.btsetting.IConnectionDeviceBatteryChangeAIDL;

/* loaded from: classes.dex */
public interface IBtSettingProxy extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IBtSettingProxy {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public void closeBtDiscoveryPopUp() throws RemoteException {
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public void closeCarBluetooth() throws RemoteException {
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public void connectBluetooth(String str) throws RemoteException {
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public void connectNewPhoneDevice() throws RemoteException {
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public boolean connectPbapProtocal() throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public void disConnectCP() throws RemoteException {
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public void disconnectBluetooth(String str) throws RemoteException {
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public String getAllPairedDeviceNames() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public String getAllSearchedDeviceNames() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public String getBluetoothAddress() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public String getBluetoothName() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public String getCurrentConnectedDeviceName() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public void ignoreBluetooth(String str) throws RemoteException {
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public boolean isBluetoothConnected() throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public boolean isBluetoothEnable() throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public boolean isCarBluetoothInDiscoveryMode() throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public boolean isDeviceConnected(String str) throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public boolean isDeviceInDiscoveryList(String str) throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public boolean isDevicePaired(String str) throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public boolean isDiscoveryPopUpVisibility() throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public boolean isHfpConnected() throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public boolean isPbapConnected() throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public void openBluetoothSetting() throws RemoteException {
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public void openCarBluetooth() throws RemoteException {
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public int openCarPlay() throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public int openCarlife() throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public void pairBluetooth(String str) throws RemoteException {
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public void registerBtSettingListener(IBtSettingListenerAIDL iBtSettingListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public void registerConnectionDeviceBatteryChangeListener(IConnectionDeviceBatteryChangeAIDL iConnectionDeviceBatteryChangeAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public boolean setBluetoothName(String str) throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public void showBtDiscoveryPopUp() throws RemoteException {
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public void unregisterBtSettingListener(IBtSettingListenerAIDL iBtSettingListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
        public void unregisterConnectionDeviceBatteryChangeListener(IConnectionDeviceBatteryChangeAIDL iConnectionDeviceBatteryChangeAIDL) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IBtSettingProxy {
        public static final String DESCRIPTOR = "com.yfve.ici.app.btsetting.IBtSettingProxy";
        public static final int TRANSACTION_closeBtDiscoveryPopUp = 32;
        public static final int TRANSACTION_closeCarBluetooth = 6;
        public static final int TRANSACTION_connectBluetooth = 9;
        public static final int TRANSACTION_connectNewPhoneDevice = 10;
        public static final int TRANSACTION_connectPbapProtocal = 29;
        public static final int TRANSACTION_disConnectCP = 33;
        public static final int TRANSACTION_disconnectBluetooth = 7;
        public static final int TRANSACTION_getAllPairedDeviceNames = 25;
        public static final int TRANSACTION_getAllSearchedDeviceNames = 26;
        public static final int TRANSACTION_getBluetoothAddress = 19;
        public static final int TRANSACTION_getBluetoothName = 18;
        public static final int TRANSACTION_getCurrentConnectedDeviceName = 12;
        public static final int TRANSACTION_ignoreBluetooth = 11;
        public static final int TRANSACTION_isBluetoothConnected = 13;
        public static final int TRANSACTION_isBluetoothEnable = 4;
        public static final int TRANSACTION_isCarBluetoothInDiscoveryMode = 22;
        public static final int TRANSACTION_isDeviceConnected = 14;
        public static final int TRANSACTION_isDeviceInDiscoveryList = 16;
        public static final int TRANSACTION_isDevicePaired = 15;
        public static final int TRANSACTION_isDiscoveryPopUpVisibility = 20;
        public static final int TRANSACTION_isHfpConnected = 23;
        public static final int TRANSACTION_isPbapConnected = 24;
        public static final int TRANSACTION_openBluetoothSetting = 3;
        public static final int TRANSACTION_openCarBluetooth = 5;
        public static final int TRANSACTION_openCarPlay = 28;
        public static final int TRANSACTION_openCarlife = 27;
        public static final int TRANSACTION_pairBluetooth = 8;
        public static final int TRANSACTION_registerBtSettingListener = 1;
        public static final int TRANSACTION_registerConnectionDeviceBatteryChangeListener = 30;
        public static final int TRANSACTION_setBluetoothName = 17;
        public static final int TRANSACTION_showBtDiscoveryPopUp = 21;
        public static final int TRANSACTION_unregisterBtSettingListener = 2;
        public static final int TRANSACTION_unregisterConnectionDeviceBatteryChangeListener = 31;

        /* loaded from: classes.dex */
        public static class Proxy implements IBtSettingProxy {
            public static IBtSettingProxy sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public void closeBtDiscoveryPopUp() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(32, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().closeBtDiscoveryPopUp();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public void closeCarBluetooth() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(6, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().closeCarBluetooth();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public void connectBluetooth(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    obtain.writeString(str);
                    if (!this.mRemote.transact(9, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().connectBluetooth(str);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public void connectNewPhoneDevice() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(10, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().connectNewPhoneDevice();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public boolean connectPbapProtocal() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(29, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().connectPbapProtocal();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public void disConnectCP() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(33, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().disConnectCP();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public void disconnectBluetooth(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    obtain.writeString(str);
                    if (!this.mRemote.transact(7, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().disconnectBluetooth(str);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public String getAllPairedDeviceNames() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(25, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getAllPairedDeviceNames();
                    }
                    obtain2.readException();
                    return obtain2.readString();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public String getAllSearchedDeviceNames() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(26, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getAllSearchedDeviceNames();
                    }
                    obtain2.readException();
                    return obtain2.readString();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public String getBluetoothAddress() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(19, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getBluetoothAddress();
                    }
                    obtain2.readException();
                    return obtain2.readString();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public String getBluetoothName() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(18, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getBluetoothName();
                    }
                    obtain2.readException();
                    return obtain2.readString();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public String getCurrentConnectedDeviceName() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(12, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getCurrentConnectedDeviceName();
                    }
                    obtain2.readException();
                    return obtain2.readString();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            public String getInterfaceDescriptor() {
                return "com.yfve.ici.app.btsetting.IBtSettingProxy";
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public void ignoreBluetooth(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    obtain.writeString(str);
                    if (!this.mRemote.transact(11, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().ignoreBluetooth(str);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public boolean isBluetoothConnected() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(13, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().isBluetoothConnected();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public boolean isBluetoothEnable() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(4, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().isBluetoothEnable();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public boolean isCarBluetoothInDiscoveryMode() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(22, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().isCarBluetoothInDiscoveryMode();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public boolean isDeviceConnected(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    obtain.writeString(str);
                    if (!this.mRemote.transact(14, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().isDeviceConnected(str);
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public boolean isDeviceInDiscoveryList(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    obtain.writeString(str);
                    if (!this.mRemote.transact(16, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().isDeviceInDiscoveryList(str);
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public boolean isDevicePaired(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    obtain.writeString(str);
                    if (!this.mRemote.transact(15, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().isDevicePaired(str);
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public boolean isDiscoveryPopUpVisibility() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(20, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().isDiscoveryPopUpVisibility();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public boolean isHfpConnected() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(23, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().isHfpConnected();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public boolean isPbapConnected() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(24, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().isPbapConnected();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public void openBluetoothSetting() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(3, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().openBluetoothSetting();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public void openCarBluetooth() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(5, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().openCarBluetooth();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public int openCarPlay() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(28, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().openCarPlay();
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public int openCarlife() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(27, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().openCarlife();
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public void pairBluetooth(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    obtain.writeString(str);
                    if (!this.mRemote.transact(8, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().pairBluetooth(str);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public void registerBtSettingListener(IBtSettingListenerAIDL iBtSettingListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    obtain.writeStrongBinder(iBtSettingListenerAIDL != null ? iBtSettingListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerBtSettingListener(iBtSettingListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public void registerConnectionDeviceBatteryChangeListener(IConnectionDeviceBatteryChangeAIDL iConnectionDeviceBatteryChangeAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    obtain.writeStrongBinder(iConnectionDeviceBatteryChangeAIDL != null ? iConnectionDeviceBatteryChangeAIDL.asBinder() : null);
                    if (!this.mRemote.transact(30, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerConnectionDeviceBatteryChangeListener(iConnectionDeviceBatteryChangeAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public boolean setBluetoothName(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    obtain.writeString(str);
                    if (!this.mRemote.transact(17, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().setBluetoothName(str);
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public void showBtDiscoveryPopUp() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    if (!this.mRemote.transact(21, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().showBtDiscoveryPopUp();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public void unregisterBtSettingListener(IBtSettingListenerAIDL iBtSettingListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    obtain.writeStrongBinder(iBtSettingListenerAIDL != null ? iBtSettingListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterBtSettingListener(iBtSettingListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingProxy
            public void unregisterConnectionDeviceBatteryChangeListener(IConnectionDeviceBatteryChangeAIDL iConnectionDeviceBatteryChangeAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btsetting.IBtSettingProxy");
                    obtain.writeStrongBinder(iConnectionDeviceBatteryChangeAIDL != null ? iConnectionDeviceBatteryChangeAIDL.asBinder() : null);
                    if (!this.mRemote.transact(31, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterConnectionDeviceBatteryChangeListener(iConnectionDeviceBatteryChangeAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, "com.yfve.ici.app.btsetting.IBtSettingProxy");
        }

        public static IBtSettingProxy asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
            if (queryLocalInterface != null && (queryLocalInterface instanceof IBtSettingProxy)) {
                return (IBtSettingProxy) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IBtSettingProxy getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IBtSettingProxy iBtSettingProxy) {
            if (Proxy.sDefaultImpl != null || iBtSettingProxy == null) {
                return false;
            }
            Proxy.sDefaultImpl = iBtSettingProxy;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1598968902) {
                switch (i) {
                    case 1:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        registerBtSettingListener(IBtSettingListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 2:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        unregisterBtSettingListener(IBtSettingListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 3:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        openBluetoothSetting();
                        parcel2.writeNoException();
                        return true;
                    case 4:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        boolean isBluetoothEnable = isBluetoothEnable();
                        parcel2.writeNoException();
                        parcel2.writeInt(isBluetoothEnable ? 1 : 0);
                        return true;
                    case 5:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        openCarBluetooth();
                        parcel2.writeNoException();
                        return true;
                    case 6:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        closeCarBluetooth();
                        parcel2.writeNoException();
                        return true;
                    case 7:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        disconnectBluetooth(parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 8:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        pairBluetooth(parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 9:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        connectBluetooth(parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 10:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        connectNewPhoneDevice();
                        parcel2.writeNoException();
                        return true;
                    case 11:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        ignoreBluetooth(parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 12:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        String currentConnectedDeviceName = getCurrentConnectedDeviceName();
                        parcel2.writeNoException();
                        parcel2.writeString(currentConnectedDeviceName);
                        return true;
                    case 13:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        boolean isBluetoothConnected = isBluetoothConnected();
                        parcel2.writeNoException();
                        parcel2.writeInt(isBluetoothConnected ? 1 : 0);
                        return true;
                    case 14:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        boolean isDeviceConnected = isDeviceConnected(parcel.readString());
                        parcel2.writeNoException();
                        parcel2.writeInt(isDeviceConnected ? 1 : 0);
                        return true;
                    case 15:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        boolean isDevicePaired = isDevicePaired(parcel.readString());
                        parcel2.writeNoException();
                        parcel2.writeInt(isDevicePaired ? 1 : 0);
                        return true;
                    case 16:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        boolean isDeviceInDiscoveryList = isDeviceInDiscoveryList(parcel.readString());
                        parcel2.writeNoException();
                        parcel2.writeInt(isDeviceInDiscoveryList ? 1 : 0);
                        return true;
                    case 17:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        boolean bluetoothName = setBluetoothName(parcel.readString());
                        parcel2.writeNoException();
                        parcel2.writeInt(bluetoothName ? 1 : 0);
                        return true;
                    case 18:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        String bluetoothName2 = getBluetoothName();
                        parcel2.writeNoException();
                        parcel2.writeString(bluetoothName2);
                        return true;
                    case 19:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        String bluetoothAddress = getBluetoothAddress();
                        parcel2.writeNoException();
                        parcel2.writeString(bluetoothAddress);
                        return true;
                    case 20:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        boolean isDiscoveryPopUpVisibility = isDiscoveryPopUpVisibility();
                        parcel2.writeNoException();
                        parcel2.writeInt(isDiscoveryPopUpVisibility ? 1 : 0);
                        return true;
                    case 21:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        showBtDiscoveryPopUp();
                        parcel2.writeNoException();
                        return true;
                    case 22:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        boolean isCarBluetoothInDiscoveryMode = isCarBluetoothInDiscoveryMode();
                        parcel2.writeNoException();
                        parcel2.writeInt(isCarBluetoothInDiscoveryMode ? 1 : 0);
                        return true;
                    case 23:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        boolean isHfpConnected = isHfpConnected();
                        parcel2.writeNoException();
                        parcel2.writeInt(isHfpConnected ? 1 : 0);
                        return true;
                    case 24:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        boolean isPbapConnected = isPbapConnected();
                        parcel2.writeNoException();
                        parcel2.writeInt(isPbapConnected ? 1 : 0);
                        return true;
                    case 25:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        String allPairedDeviceNames = getAllPairedDeviceNames();
                        parcel2.writeNoException();
                        parcel2.writeString(allPairedDeviceNames);
                        return true;
                    case 26:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        String allSearchedDeviceNames = getAllSearchedDeviceNames();
                        parcel2.writeNoException();
                        parcel2.writeString(allSearchedDeviceNames);
                        return true;
                    case 27:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        int openCarlife = openCarlife();
                        parcel2.writeNoException();
                        parcel2.writeInt(openCarlife);
                        return true;
                    case 28:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        int openCarPlay = openCarPlay();
                        parcel2.writeNoException();
                        parcel2.writeInt(openCarPlay);
                        return true;
                    case 29:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        boolean connectPbapProtocal = connectPbapProtocal();
                        parcel2.writeNoException();
                        parcel2.writeInt(connectPbapProtocal ? 1 : 0);
                        return true;
                    case 30:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        registerConnectionDeviceBatteryChangeListener(IConnectionDeviceBatteryChangeAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 31:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        unregisterConnectionDeviceBatteryChangeListener(IConnectionDeviceBatteryChangeAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 32:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        closeBtDiscoveryPopUp();
                        parcel2.writeNoException();
                        return true;
                    case 33:
                        parcel.enforceInterface("com.yfve.ici.app.btsetting.IBtSettingProxy");
                        disConnectCP();
                        parcel2.writeNoException();
                        return true;
                    default:
                        return super.onTransact(i, parcel, parcel2, i2);
                }
            }
            parcel2.writeString("com.yfve.ici.app.btsetting.IBtSettingProxy");
            return true;
        }
    }

    void closeBtDiscoveryPopUp() throws RemoteException;

    void closeCarBluetooth() throws RemoteException;

    void connectBluetooth(String str) throws RemoteException;

    void connectNewPhoneDevice() throws RemoteException;

    boolean connectPbapProtocal() throws RemoteException;

    void disConnectCP() throws RemoteException;

    void disconnectBluetooth(String str) throws RemoteException;

    String getAllPairedDeviceNames() throws RemoteException;

    String getAllSearchedDeviceNames() throws RemoteException;

    String getBluetoothAddress() throws RemoteException;

    String getBluetoothName() throws RemoteException;

    String getCurrentConnectedDeviceName() throws RemoteException;

    void ignoreBluetooth(String str) throws RemoteException;

    boolean isBluetoothConnected() throws RemoteException;

    boolean isBluetoothEnable() throws RemoteException;

    boolean isCarBluetoothInDiscoveryMode() throws RemoteException;

    boolean isDeviceConnected(String str) throws RemoteException;

    boolean isDeviceInDiscoveryList(String str) throws RemoteException;

    boolean isDevicePaired(String str) throws RemoteException;

    boolean isDiscoveryPopUpVisibility() throws RemoteException;

    boolean isHfpConnected() throws RemoteException;

    boolean isPbapConnected() throws RemoteException;

    void openBluetoothSetting() throws RemoteException;

    void openCarBluetooth() throws RemoteException;

    int openCarPlay() throws RemoteException;

    int openCarlife() throws RemoteException;

    void pairBluetooth(String str) throws RemoteException;

    void registerBtSettingListener(IBtSettingListenerAIDL iBtSettingListenerAIDL) throws RemoteException;

    void registerConnectionDeviceBatteryChangeListener(IConnectionDeviceBatteryChangeAIDL iConnectionDeviceBatteryChangeAIDL) throws RemoteException;

    boolean setBluetoothName(String str) throws RemoteException;

    void showBtDiscoveryPopUp() throws RemoteException;

    void unregisterBtSettingListener(IBtSettingListenerAIDL iBtSettingListenerAIDL) throws RemoteException;

    void unregisterConnectionDeviceBatteryChangeListener(IConnectionDeviceBatteryChangeAIDL iConnectionDeviceBatteryChangeAIDL) throws RemoteException;
}
