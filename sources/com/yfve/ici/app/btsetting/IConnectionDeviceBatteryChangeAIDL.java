package com.yfve.ici.app.btsetting;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface IConnectionDeviceBatteryChangeAIDL extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IConnectionDeviceBatteryChangeAIDL {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.btsetting.IConnectionDeviceBatteryChangeAIDL
        public void onBatteryChanged(int i) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IConnectionDeviceBatteryChangeAIDL {
        public static final String DESCRIPTOR = "com.yfve.ici.app.btsetting.IConnectionDeviceBatteryChangeAIDL";
        public static final int TRANSACTION_onBatteryChanged = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements IConnectionDeviceBatteryChangeAIDL {
            public static IConnectionDeviceBatteryChangeAIDL sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            @Override // com.yfve.ici.app.btsetting.IConnectionDeviceBatteryChangeAIDL
            public void onBatteryChanged(int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onBatteryChanged(i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static IConnectionDeviceBatteryChangeAIDL asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof IConnectionDeviceBatteryChangeAIDL)) {
                return (IConnectionDeviceBatteryChangeAIDL) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IConnectionDeviceBatteryChangeAIDL getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IConnectionDeviceBatteryChangeAIDL iConnectionDeviceBatteryChangeAIDL) {
            if (Proxy.sDefaultImpl != null || iConnectionDeviceBatteryChangeAIDL == null) {
                return false;
            }
            Proxy.sDefaultImpl = iConnectionDeviceBatteryChangeAIDL;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString(DESCRIPTOR);
                return true;
            }
            parcel.enforceInterface(DESCRIPTOR);
            onBatteryChanged(parcel.readInt());
            parcel2.writeNoException();
            return true;
        }
    }

    void onBatteryChanged(int i) throws RemoteException;
}
