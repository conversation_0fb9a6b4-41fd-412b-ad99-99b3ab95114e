package com.yfve.ici.app.btsetting;

import android.os.RemoteException;
import android.util.Log;
import b.a.b.a.a;
import com.yfve.ici.app.btsetting.IBtSettingListenerAIDL;
import com.yfve.ici.app.btsetting.IConnectionDeviceBatteryChangeAIDL;
import com.yfve.ici.service.base.BaseProxy;
import com.yfve.ici.service.contanst.ServiceConstant;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/* loaded from: classes.dex */
public class BtSettingProxy extends BaseProxy<IBtSettingProxy> {
    public static final String TAG = "BtSettingProxy";
    public static BtSettingProxy mInstance;
    public IBtSettingListenerAIDL mBtSettingListenerAIDL;
    public IConnectionDeviceBatteryChangeAIDL mConnentionAIDL;
    public List<IBtSettingCallback> mBtSettingCallbackList = new CopyOnWriteArrayList();
    public List<IConnectionDeviceBatteryChangeListener> mIConnentionDeviceBatteryChangeListeners = new CopyOnWriteArrayList();

    public static BtSettingProxy getInstance() {
        if (mInstance == null) {
            synchronized (BtSettingProxy.class) {
                if (mInstance == null) {
                    mInstance = new BtSettingProxy();
                }
            }
        }
        return mInstance;
    }

    public void closeBtDiscoveryPopUp() {
        Log.i(TAG, "closeBtDiscoveryPopUp in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "closeBtDiscoveryPopUp~~mInterface is null");
            }
            ((IBtSettingProxy) this.mInterface).closeBtDiscoveryPopUp();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public void closeCarBluetooth() {
        Log.i(TAG, "closeCarBluetooth in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "closeCarBluetooth~~mInterface is null");
            }
            ((IBtSettingProxy) this.mInterface).closeCarBluetooth();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public void connectBluetooth(String str) {
        Log.i(TAG, "connectBluetooth in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "connectBluetooth~~mInterface is null");
            }
            ((IBtSettingProxy) this.mInterface).connectBluetooth(str);
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public void connectNewPhoneDevice() {
        Log.i(TAG, "connectNewPhoneDevice in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "connectNewPhoneDevice~~mInterface is null");
            }
            ((IBtSettingProxy) this.mInterface).connectNewPhoneDevice();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public boolean connectPbapProtocal() {
        Log.i(TAG, "connectPbapProtocal in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "connectPbapProtocal ~~mInterface is null");
                return false;
            }
            return ((IBtSettingProxy) this.mInterface).connectPbapProtocal();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return false;
        }
    }

    public void disConnectCP() {
        Log.i(TAG, "disConnectCP in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "disConnectCP ~~mInterface is null");
            } else {
                ((IBtSettingProxy) this.mInterface).disConnectCP();
            }
        } catch (Exception e) {
            Log.i(TAG, e.toString());
        }
    }

    public void disconnectBluetooth(String str) {
        Log.i(TAG, "disconnectBluetooth in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "disconnectBluetooth~~mInterface is null");
            }
            ((IBtSettingProxy) this.mInterface).disconnectBluetooth(str);
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public String getAllPairedDeviceNames() {
        Log.i(TAG, "getAllPairedDeviceNames in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getAllPairedDeviceNames~~mInterface is null");
                return null;
            }
            return ((IBtSettingProxy) this.mInterface).getAllPairedDeviceNames();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return null;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return null;
        }
    }

    public String getAllSearchedDeviceNames() {
        Log.i(TAG, "getAllSearchedDeviceNames in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getAllSearchedDeviceNames~~mInterface is null");
                return null;
            }
            return ((IBtSettingProxy) this.mInterface).getAllSearchedDeviceNames();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return null;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return null;
        }
    }

    public String getBluetoothAddress() {
        Log.i(TAG, "getBluetoothAddress in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getBluetoothAddress~~mInterface is null");
                return null;
            }
            return ((IBtSettingProxy) this.mInterface).getBluetoothAddress();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return null;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return null;
        }
    }

    public String getBluetoothName() {
        Log.i(TAG, "getBluetoothName in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getBluetoothName~~mInterface is null");
                return null;
            }
            return ((IBtSettingProxy) this.mInterface).getBluetoothName();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return null;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return null;
        }
    }

    public String getCurrentConnectedDeviceName() {
        Log.i(TAG, "getCurrentConnectedDeviceName in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getCurrentConnectedDeviceName~~mInterface is null");
                return null;
            }
            return ((IBtSettingProxy) this.mInterface).getCurrentConnectedDeviceName();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return null;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return null;
        }
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public String getServiceName() {
        return ServiceConstant.CONNECTIVITY_BINDER_NAME;
    }

    public void ignoreBluetooth(String str) {
        Log.i(TAG, "ignoreBluetooth in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "ignoreBluetooth~~mInterface is null");
            }
            ((IBtSettingProxy) this.mInterface).ignoreBluetooth(str);
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public boolean isBluetoothConnected() {
        Log.i(TAG, "isBluetoothConnected in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "isBluetoothConnected~~mInterface is null");
                return false;
            }
            return ((IBtSettingProxy) this.mInterface).isBluetoothConnected();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return false;
        }
    }

    public boolean isBluetoothEnable() {
        Log.i(TAG, "isBluetoothEnable in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "isBluetoothEnable~~mInterface is null");
                return false;
            }
            return ((IBtSettingProxy) this.mInterface).isBluetoothEnable();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return false;
        }
    }

    public boolean isCarBluetoothInDiscoveryMode() {
        Log.i(TAG, "isCarBluetoothInDiscoveryMode in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "isCarBluetoothInDiscoveryMode~~mInterface is null");
                return false;
            }
            return ((IBtSettingProxy) this.mInterface).isCarBluetoothInDiscoveryMode();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return false;
        }
    }

    public boolean isDeviceConnected(String str) {
        Log.i(TAG, "isDeviceConnected in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "isDeviceConnected~~mInterface is null");
                return false;
            }
            return ((IBtSettingProxy) this.mInterface).isDeviceConnected(str);
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return false;
        }
    }

    public boolean isDeviceInDiscoveryList(String str) {
        Log.i(TAG, "isDeviceInDiscoveryList in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "isDeviceInDiscoveryList~~mInterface is null");
                return false;
            }
            return ((IBtSettingProxy) this.mInterface).isDeviceInDiscoveryList(str);
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return false;
        }
    }

    public boolean isDevicePaired(String str) {
        Log.i(TAG, "isDevicePaired in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "isDevicePaired~~mInterface is null");
                return false;
            }
            return ((IBtSettingProxy) this.mInterface).isDevicePaired(str);
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return false;
        }
    }

    public boolean isDiscoveryPopUpVisibility() {
        Log.i(TAG, "isDiscoveryPopUpVisibility in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "isDiscoveryPopUpVisibility~~mInterface is null");
                return false;
            }
            return ((IBtSettingProxy) this.mInterface).isDiscoveryPopUpVisibility();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return false;
        }
    }

    public boolean isHfpConnected() {
        Log.i(TAG, "isHfpConnected in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "isHfpConnected~~mInterface is null");
                return false;
            }
            return ((IBtSettingProxy) this.mInterface).isHfpConnected();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return false;
        }
    }

    public boolean isPbapConnected() {
        Log.i(TAG, "isPbapConnected in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "isPbapConnected~~mInterface is null");
                return false;
            }
            return ((IBtSettingProxy) this.mInterface).isPbapConnected();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return false;
        }
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public void onServiceConnectStatusChanged(boolean z) {
        List<IBtSettingCallback> list;
        super.onServiceConnectStatusChanged(z);
        a.l("onServiceConnectStatusChanged onServiceConnectStatusChanged status : ", z, TAG);
        if (z || (list = this.mBtSettingCallbackList) == null) {
            return;
        }
        list.clear();
    }

    public void openBluetoothSetting() {
        Log.i(TAG, "openBluetoothSetting in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "openBluetoothSetting~~mInterface is null");
            } else {
                ((IBtSettingProxy) this.mInterface).openBluetoothSetting();
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public void openCarBluetooth() {
        Log.i(TAG, "openCarBluetooth in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "openCarBluetooth~~mInterface is null");
            }
            ((IBtSettingProxy) this.mInterface).openCarBluetooth();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public int openCarPlay() {
        Log.i(TAG, "openCarPlay in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "openCarPlay~~mInterface is null");
                return 0;
            }
            return ((IBtSettingProxy) this.mInterface).openCarPlay();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return 0;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return 0;
        }
    }

    public int openCarlife() {
        Log.i(TAG, "openCarlife in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "openCarlife~~mInterface is null");
                return 0;
            }
            return ((IBtSettingProxy) this.mInterface).openCarlife();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return 0;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return 0;
        }
    }

    public void pairBluetooth(String str) {
        Log.i(TAG, "pairBluetooth in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "pairBluetooth~~mInterface is null");
            }
            ((IBtSettingProxy) this.mInterface).pairBluetooth(str);
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public int registerBtSettingListener(IBtSettingCallback iBtSettingCallback) {
        Log.d(TAG, "registerBtSettingListener in");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerBtSettingListener~~mInterface is null");
                return -2147483646;
            }
            if (iBtSettingCallback != null && !this.mBtSettingCallbackList.contains(iBtSettingCallback)) {
                this.mBtSettingCallbackList.add(iBtSettingCallback);
                if (this.mBtSettingCallbackList.size() == 1) {
                    IBtSettingListenerAIDL.Stub stub = new IBtSettingListenerAIDL.Stub() { // from class: com.yfve.ici.app.btsetting.BtSettingProxy.1
                        @Override // com.yfve.ici.app.btsetting.IBtSettingListenerAIDL
                        public void onConnectStateChanged(String str, int i) throws RemoteException {
                            String str2 = BtSettingProxy.TAG;
                            Log.d(str2, "onConnectStateChanged  deviceName -- " + str + "  connectState -- " + i);
                            for (IBtSettingCallback iBtSettingCallback2 : BtSettingProxy.this.mBtSettingCallbackList) {
                                iBtSettingCallback2.onConnectStateChanged(str, i);
                            }
                        }

                        @Override // com.yfve.ici.app.btsetting.IBtSettingListenerAIDL
                        public void onDeviceBondStateChanged(String str, int i) throws RemoteException {
                            String str2 = BtSettingProxy.TAG;
                            Log.d(str2, "onDeviceBondStateChanged  deviceName -- " + str + "  bondState -- " + i);
                            for (IBtSettingCallback iBtSettingCallback2 : BtSettingProxy.this.mBtSettingCallbackList) {
                                iBtSettingCallback2.onDeviceBondStateChanged(str, i);
                            }
                        }
                    };
                    this.mBtSettingListenerAIDL = stub;
                    ((IBtSettingProxy) this.mInterface).registerBtSettingListener(stub);
                    return Integer.MIN_VALUE;
                }
            }
            return -2147418113;
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int registerConnectionListener(IConnectionDeviceBatteryChangeListener iConnectionDeviceBatteryChangeListener) {
        Log.d(TAG, "registerConnectionListener in");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerConnectionListener~~mInterface is null");
                return -2147483646;
            }
            if (iConnectionDeviceBatteryChangeListener != null && !this.mIConnentionDeviceBatteryChangeListeners.contains(iConnectionDeviceBatteryChangeListener)) {
                this.mIConnentionDeviceBatteryChangeListeners.add(iConnectionDeviceBatteryChangeListener);
                if (this.mIConnentionDeviceBatteryChangeListeners.size() == 1) {
                    IConnectionDeviceBatteryChangeAIDL.Stub stub = new IConnectionDeviceBatteryChangeAIDL.Stub() { // from class: com.yfve.ici.app.btsetting.BtSettingProxy.2
                        @Override // com.yfve.ici.app.btsetting.IConnectionDeviceBatteryChangeAIDL
                        public void onBatteryChanged(int i) throws RemoteException {
                            String str = BtSettingProxy.TAG;
                            Log.d(str, "onBatteryChanged  batteryLevel -- " + i);
                            for (IConnectionDeviceBatteryChangeListener iConnectionDeviceBatteryChangeListener2 : BtSettingProxy.this.mIConnentionDeviceBatteryChangeListeners) {
                                iConnectionDeviceBatteryChangeListener2.onBatteryChanged(i);
                            }
                        }
                    };
                    this.mConnentionAIDL = stub;
                    ((IBtSettingProxy) this.mInterface).registerConnectionDeviceBatteryChangeListener(stub);
                    return Integer.MIN_VALUE;
                }
            }
            return -2147418113;
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }

    public boolean setBluetoothName(String str) {
        Log.i(TAG, "setBluetoothName in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "setBluetoothName~~mInterface is null");
                return false;
            }
            return ((IBtSettingProxy) this.mInterface).setBluetoothName(str);
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return false;
        }
    }

    public void showBtDiscoveryPopUp() {
        Log.i(TAG, "showBtDiscoveryPopUp in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "showBtDiscoveryPopUp~~mInterface is null");
            }
            ((IBtSettingProxy) this.mInterface).showBtDiscoveryPopUp();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public int unregisterBtSettingListener(IBtSettingCallback iBtSettingCallback) {
        Log.i(TAG, "unregisterBtSettingListener in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unregisterBtSettingListener~~mInterface is null");
                return -2147483646;
            }
            if (iBtSettingCallback != null && this.mBtSettingCallbackList.contains(iBtSettingCallback)) {
                this.mBtSettingCallbackList.remove(iBtSettingCallback);
                if (this.mBtSettingCallbackList.size() == 0) {
                    ((IBtSettingProxy) this.mInterface).unregisterBtSettingListener(this.mBtSettingListenerAIDL);
                    this.mBtSettingListenerAIDL = null;
                    return Integer.MIN_VALUE;
                }
            }
            return -2147418113;
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int unregisterConnectionListener(IConnectionDeviceBatteryChangeListener iConnectionDeviceBatteryChangeListener) {
        Log.i(TAG, "unregisterConnectionListener in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unregisterConnectionListener~~mInterface is null");
                return -2147483646;
            }
            if (iConnectionDeviceBatteryChangeListener != null && this.mIConnentionDeviceBatteryChangeListeners.contains(iConnectionDeviceBatteryChangeListener)) {
                this.mIConnentionDeviceBatteryChangeListeners.remove(iConnectionDeviceBatteryChangeListener);
                if (this.mIConnentionDeviceBatteryChangeListeners.size() == 0) {
                    ((IBtSettingProxy) this.mInterface).unregisterConnectionDeviceBatteryChangeListener(this.mConnentionAIDL);
                    this.mConnentionAIDL = null;
                    return Integer.MIN_VALUE;
                }
            }
            return -2147418113;
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }
}
