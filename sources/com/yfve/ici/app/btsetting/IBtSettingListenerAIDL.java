package com.yfve.ici.app.btsetting;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface IBtSettingListenerAIDL extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IBtSettingListenerAIDL {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingListenerAIDL
        public void onConnectStateChanged(String str, int i) throws RemoteException {
        }

        @Override // com.yfve.ici.app.btsetting.IBtSettingListenerAIDL
        public void onDeviceBondStateChanged(String str, int i) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IBtSettingListenerAIDL {
        public static final String DESCRIPTOR = "com.yfve.ici.app.btsetting.IBtSettingListenerAIDL";
        public static final int TRANSACTION_onConnectStateChanged = 1;
        public static final int TRANSACTION_onDeviceBondStateChanged = 2;

        /* loaded from: classes.dex */
        public static class Proxy implements IBtSettingListenerAIDL {
            public static IBtSettingListenerAIDL sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingListenerAIDL
            public void onConnectStateChanged(String str, int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onConnectStateChanged(str, i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btsetting.IBtSettingListenerAIDL
            public void onDeviceBondStateChanged(String str, int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onDeviceBondStateChanged(str, i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static IBtSettingListenerAIDL asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof IBtSettingListenerAIDL)) {
                return (IBtSettingListenerAIDL) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IBtSettingListenerAIDL getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IBtSettingListenerAIDL iBtSettingListenerAIDL) {
            if (Proxy.sDefaultImpl != null || iBtSettingListenerAIDL == null) {
                return false;
            }
            Proxy.sDefaultImpl = iBtSettingListenerAIDL;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i == 1) {
                parcel.enforceInterface(DESCRIPTOR);
                onConnectStateChanged(parcel.readString(), parcel.readInt());
                parcel2.writeNoException();
                return true;
            } else if (i != 2) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString(DESCRIPTOR);
                return true;
            } else {
                parcel.enforceInterface(DESCRIPTOR);
                onDeviceBondStateChanged(parcel.readString(), parcel.readInt());
                parcel2.writeNoException();
                return true;
            }
        }
    }

    void onConnectStateChanged(String str, int i) throws RemoteException;

    void onDeviceBondStateChanged(String str, int i) throws RemoteException;
}
