package com.yfve.ici.app.btsetting;

import android.os.Parcel;
import android.os.Parcelable;

/* loaded from: classes.dex */
public class UiBluetoothInfo implements Parcelable {
    public static final Parcelable.Creator<UiBluetoothInfo> CREATOR = new Parcelable.Creator<UiBluetoothInfo>() { // from class: com.yfve.ici.app.btsetting.UiBluetoothInfo.1
        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public UiBluetoothInfo createFromParcel(Parcel parcel) {
            return new UiBluetoothInfo(parcel);
        }

        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public UiBluetoothInfo[] newArray(int i) {
            return new UiBluetoothInfo[i];
        }
    };
    public int connectState;
    public String deviceName;

    @Override // android.os.Parcelable
    public int describeContents() {
        return 0;
    }

    public int getConnectState() {
        return this.connectState;
    }

    public String getDeviceName() {
        return this.deviceName;
    }

    public void setConnectState(int i) {
        this.connectState = i;
    }

    public void setDeviceName(String str) {
        this.deviceName = str;
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeString(this.deviceName);
        parcel.writeInt(this.connectState);
    }

    public UiBluetoothInfo(String str, int i) {
        this.deviceName = str;
        this.connectState = i;
    }

    public UiBluetoothInfo(Parcel parcel) {
        this.deviceName = parcel.readString();
        this.connectState = parcel.readInt();
    }
}
