package com.yfve.ici.app.onstar;

import android.os.Parcel;
import android.os.Parcelable;
import b.a.b.a.a;
import java.util.ArrayList;
import java.util.List;

/* loaded from: classes.dex */
public class TrafficDataInfo implements Parcelable {
    public static final Parcelable.Creator<TrafficDataInfo> CREATOR = new Parcelable.Creator<TrafficDataInfo>() { // from class: com.yfve.ici.app.onstar.TrafficDataInfo.1
        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public TrafficDataInfo createFromParcel(Parcel parcel) {
            return new TrafficDataInfo(parcel);
        }

        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public TrafficDataInfo[] newArray(int i) {
            return new TrafficDataInfo[i];
        }
    };
    public String annualPriceMonth;
    public String basePriceMonth;
    public String cancellationDate;
    public String duration;
    public String durationLowerLimitMonths;
    public String durationUpperLimitMonths;
    public String endDate;
    public String expireDate;
    public String id;
    public String packageBase;
    public String packageDesc;
    public String packageName;
    public String packageendDate;
    public String packagestartDate;
    public List<Pcgservices> pcgservices;
    public String pkgQTY;
    public String pricePlan;
    public String productNumber;
    public String productofferingId;
    public String remainQTY;
    public String serviceLevelDesc;
    public String startDate;
    public String submitOrderId;
    public String type;
    public String typeDesc;

    public TrafficDataInfo(Parcel parcel) {
        readFromParcel(parcel);
    }

    @Override // android.os.Parcelable
    public int describeContents() {
        return 0;
    }

    public String getAnnualPriceMonth() {
        return this.annualPriceMonth;
    }

    public String getBasePriceMonth() {
        return this.basePriceMonth;
    }

    public String getCancellationDate() {
        return this.cancellationDate;
    }

    public String getDuration() {
        return this.duration;
    }

    public String getDurationLowerLimitMonths() {
        return this.durationLowerLimitMonths;
    }

    public String getDurationUpperLimitMonths() {
        return this.durationUpperLimitMonths;
    }

    public String getEndDate() {
        return this.endDate;
    }

    public String getExpireDate() {
        return this.expireDate;
    }

    public String getId() {
        return this.id;
    }

    public String getPackageBase() {
        return this.packageBase;
    }

    public String getPackageDesc() {
        return this.packageDesc;
    }

    public String getPackageName() {
        return this.packageName;
    }

    public String getPackageendDate() {
        return this.packageendDate;
    }

    public String getPackagestartDate() {
        return this.packagestartDate;
    }

    public List<Pcgservices> getPcgservices() {
        return this.pcgservices;
    }

    public String getPkgQTY() {
        return this.pkgQTY;
    }

    public String getPricePlan() {
        return this.pricePlan;
    }

    public String getProductNumber() {
        return this.productNumber;
    }

    public String getProductofferingId() {
        return this.productofferingId;
    }

    public String getRemainQTY() {
        return this.remainQTY;
    }

    public String getServiceLevelDesc() {
        return this.serviceLevelDesc;
    }

    public String getStartDate() {
        return this.startDate;
    }

    public String getSubmitOrderId() {
        return this.submitOrderId;
    }

    public String getType() {
        return this.type;
    }

    public String getTypeDesc() {
        return this.typeDesc;
    }

    public void readFromParcel(Parcel parcel) {
        this.productNumber = parcel.readString();
        this.packagestartDate = parcel.readString();
        this.packageendDate = parcel.readString();
        this.packageDesc = parcel.readString();
        this.remainQTY = parcel.readString();
        this.cancellationDate = parcel.readString();
        ArrayList arrayList = new ArrayList();
        this.pcgservices = arrayList;
        parcel.readList(arrayList, Pcgservices.class.getClassLoader());
        this.basePriceMonth = parcel.readString();
        this.annualPriceMonth = parcel.readString();
        this.expireDate = parcel.readString();
        this.serviceLevelDesc = parcel.readString();
        this.packageBase = parcel.readString();
        this.submitOrderId = parcel.readString();
        this.productofferingId = parcel.readString();
        this.durationUpperLimitMonths = parcel.readString();
        this.durationLowerLimitMonths = parcel.readString();
        this.pricePlan = parcel.readString();
        this.pkgQTY = parcel.readString();
        this.typeDesc = parcel.readString();
        this.startDate = parcel.readString();
        this.endDate = parcel.readString();
        this.duration = parcel.readString();
        this.packageName = parcel.readString();
        this.id = parcel.readString();
        this.type = parcel.readString();
    }

    public void setAnnualPriceMonth(String str) {
        this.annualPriceMonth = str;
    }

    public void setBasePriceMonth(String str) {
        this.basePriceMonth = str;
    }

    public void setCancellationDate(String str) {
        this.cancellationDate = str;
    }

    public void setDuration(String str) {
        this.duration = str;
    }

    public void setDurationLowerLimitMonths(String str) {
        this.durationLowerLimitMonths = str;
    }

    public void setDurationUpperLimitMonths(String str) {
        this.durationUpperLimitMonths = str;
    }

    public void setEndDate(String str) {
        this.endDate = str;
    }

    public void setExpireDate(String str) {
        this.expireDate = str;
    }

    public void setId(String str) {
        this.id = str;
    }

    public void setPackageBase(String str) {
        this.packageBase = str;
    }

    public void setPackageDesc(String str) {
        this.packageDesc = str;
    }

    public void setPackageName(String str) {
        this.packageName = str;
    }

    public void setPackageendDate(String str) {
        this.packageendDate = str;
    }

    public void setPackagestartDate(String str) {
        this.packagestartDate = str;
    }

    public void setPcgservices(List<Pcgservices> list) {
        this.pcgservices = list;
    }

    public void setPkgQTY(String str) {
        this.pkgQTY = str;
    }

    public void setPricePlan(String str) {
        this.pricePlan = str;
    }

    public void setProductNumber(String str) {
        this.productNumber = str;
    }

    public void setProductofferingId(String str) {
        this.productofferingId = str;
    }

    public void setRemainQTY(String str) {
        this.remainQTY = str;
    }

    public void setServiceLevelDesc(String str) {
        this.serviceLevelDesc = str;
    }

    public void setStartDate(String str) {
        this.startDate = str;
    }

    public void setSubmitOrderId(String str) {
        this.submitOrderId = str;
    }

    public void setType(String str) {
        this.type = str;
    }

    public void setTypeDesc(String str) {
        this.typeDesc = str;
    }

    public String toString() {
        StringBuilder e = a.e("TrafficDataInfo{productNumber='");
        a.o(e, this.productNumber, '\'', ", packagestartDate='");
        a.o(e, this.packagestartDate, '\'', ", packageendDate='");
        a.o(e, this.packageendDate, '\'', ", packageDesc='");
        a.o(e, this.packageDesc, '\'', ", remainQTY='");
        a.o(e, this.remainQTY, '\'', ", cancellationDate='");
        a.o(e, this.cancellationDate, '\'', ", pcgservices=");
        e.append(this.pcgservices);
        e.append(", basePriceMonth='");
        a.o(e, this.basePriceMonth, '\'', ", annualPriceMonth='");
        a.o(e, this.annualPriceMonth, '\'', ", expireDate='");
        a.o(e, this.expireDate, '\'', ", serviceLevelDesc='");
        a.o(e, this.serviceLevelDesc, '\'', ", packageBase='");
        a.o(e, this.packageBase, '\'', ", submitOrderId='");
        a.o(e, this.submitOrderId, '\'', ", productofferingId='");
        a.o(e, this.productofferingId, '\'', ", durationUpperLimitMonths='");
        a.o(e, this.durationUpperLimitMonths, '\'', ", durationLowerLimitMonths='");
        a.o(e, this.durationLowerLimitMonths, '\'', ", pricePlan='");
        a.o(e, this.pricePlan, '\'', ", pkgQTY='");
        a.o(e, this.pkgQTY, '\'', ", typeDesc='");
        a.o(e, this.typeDesc, '\'', ", startDate='");
        a.o(e, this.startDate, '\'', ", endDate='");
        a.o(e, this.endDate, '\'', ", duration='");
        a.o(e, this.duration, '\'', ", packageName='");
        a.o(e, this.packageName, '\'', ", id='");
        a.o(e, this.id, '\'', ", type='");
        e.append(this.type);
        e.append('\'');
        e.append('}');
        return e.toString();
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeString(this.productNumber);
        parcel.writeString(this.packagestartDate);
        parcel.writeString(this.packageendDate);
        parcel.writeString(this.packageDesc);
        parcel.writeString(this.remainQTY);
        parcel.writeString(this.cancellationDate);
        parcel.writeList(this.pcgservices);
        parcel.writeString(this.basePriceMonth);
        parcel.writeString(this.annualPriceMonth);
        parcel.writeString(this.expireDate);
        parcel.writeString(this.serviceLevelDesc);
        parcel.writeString(this.packageBase);
        parcel.writeString(this.submitOrderId);
        parcel.writeString(this.productofferingId);
        parcel.writeString(this.durationUpperLimitMonths);
        parcel.writeString(this.durationLowerLimitMonths);
        parcel.writeString(this.pricePlan);
        parcel.writeString(this.pkgQTY);
        parcel.writeString(this.typeDesc);
        parcel.writeString(this.startDate);
        parcel.writeString(this.endDate);
        parcel.writeString(this.duration);
        parcel.writeString(this.packageName);
        parcel.writeString(this.id);
        parcel.writeString(this.type);
    }

    /* loaded from: classes.dex */
    public static class Pcgservices implements Parcelable {
        public static final Parcelable.Creator<Pcgservices> CREATOR = new Parcelable.Creator<Pcgservices>() { // from class: com.yfve.ici.app.onstar.TrafficDataInfo.Pcgservices.1
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // android.os.Parcelable.Creator
            public Pcgservices createFromParcel(Parcel parcel) {
                return new Pcgservices(parcel);
            }

            /* JADX WARN: Can't rename method to resolve collision */
            @Override // android.os.Parcelable.Creator
            public Pcgservices[] newArray(int i) {
                return new Pcgservices[i];
            }
        };
        public String id;
        public String serviceDesc;
        public String serviceName;

        public Pcgservices(Parcel parcel) {
            readFromParcel(parcel);
        }

        @Override // android.os.Parcelable
        public int describeContents() {
            return 0;
        }

        public String getId() {
            return this.id;
        }

        public String getServiceDesc() {
            return this.serviceDesc;
        }

        public String getServiceName() {
            return this.serviceName;
        }

        public void readFromParcel(Parcel parcel) {
            this.serviceDesc = parcel.readString();
            this.serviceName = parcel.readString();
            this.id = parcel.readString();
        }

        public void setId(String str) {
            this.id = str;
        }

        public void setServiceDesc(String str) {
            this.serviceDesc = str;
        }

        public void setServiceName(String str) {
            this.serviceName = str;
        }

        @Override // android.os.Parcelable
        public void writeToParcel(Parcel parcel, int i) {
            parcel.writeString(this.serviceDesc);
            parcel.writeString(this.serviceName);
            parcel.writeString(this.id);
        }

        public Pcgservices() {
        }
    }

    public TrafficDataInfo() {
    }
}
