package com.yfve.ici.app.onstar;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import com.yfve.ici.app.onstar.ITboxListener;

/* loaded from: classes.dex */
public interface ITboxManager extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements ITboxManager {
        @Override // com.yfve.ici.app.onstar.ITboxManager
        public void OpenOnStar() throws RemoteException {
        }

        @Override // com.yfve.ici.app.onstar.ITboxManager
        public void OpenOnStarCallPopup() throws RemoteException {
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.onstar.ITboxManager
        public int getCurrentTboxCallState() throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.onstar.ITboxManager
        public int getCurrentTboxCallType() throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.onstar.ITboxManager
        public int getTBoxState() throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.onstar.ITboxManager
        public boolean isActiveTboxCallScreenInBackground() throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.onstar.ITboxManager
        public int registCallBack(ITboxListener iTboxListener) throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.onstar.ITboxManager
        public int unregistCallBack(ITboxListener iTboxListener) throws RemoteException {
            return 0;
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements ITboxManager {
        public static final String DESCRIPTOR = "com.yfve.ici.app.onstar.ITboxManager";
        public static final int TRANSACTION_OpenOnStar = 7;
        public static final int TRANSACTION_OpenOnStarCallPopup = 8;
        public static final int TRANSACTION_getCurrentTboxCallState = 4;
        public static final int TRANSACTION_getCurrentTboxCallType = 5;
        public static final int TRANSACTION_getTBoxState = 3;
        public static final int TRANSACTION_isActiveTboxCallScreenInBackground = 6;
        public static final int TRANSACTION_registCallBack = 1;
        public static final int TRANSACTION_unregistCallBack = 2;

        /* loaded from: classes.dex */
        public static class Proxy implements ITboxManager {
            public static ITboxManager sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // com.yfve.ici.app.onstar.ITboxManager
            public void OpenOnStar() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.onstar.ITboxManager");
                    if (!this.mRemote.transact(7, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().OpenOnStar();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.onstar.ITboxManager
            public void OpenOnStarCallPopup() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.onstar.ITboxManager");
                    if (!this.mRemote.transact(8, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().OpenOnStarCallPopup();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            @Override // com.yfve.ici.app.onstar.ITboxManager
            public int getCurrentTboxCallState() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.onstar.ITboxManager");
                    if (!this.mRemote.transact(4, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getCurrentTboxCallState();
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.onstar.ITboxManager
            public int getCurrentTboxCallType() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.onstar.ITboxManager");
                    if (!this.mRemote.transact(5, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getCurrentTboxCallType();
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            public String getInterfaceDescriptor() {
                return "com.yfve.ici.app.onstar.ITboxManager";
            }

            @Override // com.yfve.ici.app.onstar.ITboxManager
            public int getTBoxState() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.onstar.ITboxManager");
                    if (!this.mRemote.transact(3, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getTBoxState();
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.onstar.ITboxManager
            public boolean isActiveTboxCallScreenInBackground() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.onstar.ITboxManager");
                    if (!this.mRemote.transact(6, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().isActiveTboxCallScreenInBackground();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.onstar.ITboxManager
            public int registCallBack(ITboxListener iTboxListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.onstar.ITboxManager");
                    obtain.writeStrongBinder(iTboxListener != null ? iTboxListener.asBinder() : null);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().registCallBack(iTboxListener);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.onstar.ITboxManager
            public int unregistCallBack(ITboxListener iTboxListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.onstar.ITboxManager");
                    obtain.writeStrongBinder(iTboxListener != null ? iTboxListener.asBinder() : null);
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().unregistCallBack(iTboxListener);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, "com.yfve.ici.app.onstar.ITboxManager");
        }

        public static ITboxManager asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface("com.yfve.ici.app.onstar.ITboxManager");
            if (queryLocalInterface != null && (queryLocalInterface instanceof ITboxManager)) {
                return (ITboxManager) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static ITboxManager getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(ITboxManager iTboxManager) {
            if (Proxy.sDefaultImpl != null || iTboxManager == null) {
                return false;
            }
            Proxy.sDefaultImpl = iTboxManager;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1598968902) {
                switch (i) {
                    case 1:
                        parcel.enforceInterface("com.yfve.ici.app.onstar.ITboxManager");
                        int registCallBack = registCallBack(ITboxListener.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        parcel2.writeInt(registCallBack);
                        return true;
                    case 2:
                        parcel.enforceInterface("com.yfve.ici.app.onstar.ITboxManager");
                        int unregistCallBack = unregistCallBack(ITboxListener.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        parcel2.writeInt(unregistCallBack);
                        return true;
                    case 3:
                        parcel.enforceInterface("com.yfve.ici.app.onstar.ITboxManager");
                        int tBoxState = getTBoxState();
                        parcel2.writeNoException();
                        parcel2.writeInt(tBoxState);
                        return true;
                    case 4:
                        parcel.enforceInterface("com.yfve.ici.app.onstar.ITboxManager");
                        int currentTboxCallState = getCurrentTboxCallState();
                        parcel2.writeNoException();
                        parcel2.writeInt(currentTboxCallState);
                        return true;
                    case 5:
                        parcel.enforceInterface("com.yfve.ici.app.onstar.ITboxManager");
                        int currentTboxCallType = getCurrentTboxCallType();
                        parcel2.writeNoException();
                        parcel2.writeInt(currentTboxCallType);
                        return true;
                    case 6:
                        parcel.enforceInterface("com.yfve.ici.app.onstar.ITboxManager");
                        boolean isActiveTboxCallScreenInBackground = isActiveTboxCallScreenInBackground();
                        parcel2.writeNoException();
                        parcel2.writeInt(isActiveTboxCallScreenInBackground ? 1 : 0);
                        return true;
                    case 7:
                        parcel.enforceInterface("com.yfve.ici.app.onstar.ITboxManager");
                        OpenOnStar();
                        parcel2.writeNoException();
                        return true;
                    case 8:
                        parcel.enforceInterface("com.yfve.ici.app.onstar.ITboxManager");
                        OpenOnStarCallPopup();
                        parcel2.writeNoException();
                        return true;
                    default:
                        return super.onTransact(i, parcel, parcel2, i2);
                }
            }
            parcel2.writeString("com.yfve.ici.app.onstar.ITboxManager");
            return true;
        }
    }

    void OpenOnStar() throws RemoteException;

    void OpenOnStarCallPopup() throws RemoteException;

    int getCurrentTboxCallState() throws RemoteException;

    int getCurrentTboxCallType() throws RemoteException;

    int getTBoxState() throws RemoteException;

    boolean isActiveTboxCallScreenInBackground() throws RemoteException;

    int registCallBack(ITboxListener iTboxListener) throws RemoteException;

    int unregistCallBack(ITboxListener iTboxListener) throws RemoteException;
}
