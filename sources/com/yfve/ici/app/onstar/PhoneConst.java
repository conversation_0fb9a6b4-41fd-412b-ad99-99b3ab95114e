package com.yfve.ici.app.onstar;

/* loaded from: classes.dex */
public interface PhoneConst {
    public static final String ACTION_CALLSERVICE_NAME = "com.ici.onstarlib.service";
    public static final int CALLSTATUS_END = 0;
    public static final int CALLSTATUS_HELD = -2;
    public static final int CALLSTATUS_HELDSTATUSCHANGE = 9;
    public static final int CALLSTATUS_IDEL = -1;
    public static final int CALLSTATUS_INCALL = 2;
    public static final int CALLSTATUS_INCOMING = 1;
    public static final int CALLSTATUS_OUTGOING = 3;
    public static final int CALLSTATUS_THREEWAYL = 4;
    public static final int CALLSTATUS_VOICEDIALACTIVE = 5;
    public static final int CALLSTATUS_VOICEDIALTERMINATE = 7;
    public static final int CALLSTATUS_VOICEDIALTIMEOUT = 6;
    public static final int CALL_TYPE_ADVISOR = 2;
    public static final int CALL_TYPE_COLLISION = 0;
    public static final int CALL_TYPE_EMERGENCY = 1;
    public static final int CALL_TYPE_ENROLLMENT = 6;
    public static final int CALL_TYPE_OCCINITIATED = 7;
    public static final int CALL_TYPE_OCCINITIATED_ADVISOR = 9;
    public static final int CALL_TYPE_OCCINITIATED_EMERGENCY = 8;
    public static final int CALL_TYPE_UNITADD = 4;
    public static final int CALL_TYPE_UNKNOWN = 10;
    public static final int CALL_TYPE_VEHICLEDATAUPLOAD = 5;
    public static final int CALL_TYPE_VIRTUALADVISOR = 3;
    public static final String DATA_TRAFFIC_SERVICE_ACTION = "ici.intent.action.DATATRAFFIC";
    public static final int FREE_TRAFFIC_LEVEL_CRITICAL_LOW = 2;
    public static final int FREE_TRAFFIC_LEVEL_NORMAL = 1;
    public static final boolean IS_APP_IN_FRONT = false;
    public static final String PACKAGE_CALLSERVICE_NAME = "com.ici.onstarlib.service";
    public static final int RETURN_ERR_COMMAND_AMBIGUOUS = -2147483643;
    public static final int RETURN_ERR_DEVICE_UNCONNECTED = -2147483645;
    public static final int RETURN_ERR_FUNCTION_UNINIT = -2147483644;
    public static final int RETURN_ERR_LOCAL_SERVICE_UNCONNECTED = -2147483646;
    public static final int RETURN_ERR_PARAMETER_EXCEPTION = -2147483647;
    public static final int RETURN_ERR_UNKNOWN = -2147418113;
    public static final int RETURN_OK = Integer.MIN_VALUE;
    public static final String TBOX_CALL_SERVICE_ACTION = "ici.intent.action.ONSTAR";
    public static final int WIFI_TRAFFIC_LEVEL_CRITICAL_LOW = 4;
    public static final int WIFI_TRAFFIC_LEVEL_LOW = 2;
    public static final int WIFI_TRAFFIC_LEVEL_NORMAL = 1;
    public static final int WIFI_TRAFFIC_LEVEL_QUERY_ERR = -1;
    public static final int WIFI_TRAFFIC_LEVEL_RUN_OUT = 5;
    public static final int WIFI_TRAFFIC_LEVEL_SHORTAGE = 3;
}
