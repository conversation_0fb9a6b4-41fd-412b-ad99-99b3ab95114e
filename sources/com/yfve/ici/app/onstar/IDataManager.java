package com.yfve.ici.app.onstar;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import com.yfve.ici.app.onstar.IDataListener;

/* loaded from: classes.dex */
public interface IDataManager extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IDataManager {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.onstar.IDataManager
        public TrafficDataInfo getFreeDataTrafficData() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.onstar.IDataManager
        public int getFreeDataTrafficLevel() throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.onstar.IDataManager
        public TrafficDataInfo getWIFIDataTrafficData() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.onstar.IDataManager
        public int getWifiDataTrafficLevel() throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.onstar.IDataManager
        public boolean registerTrafficDataRanges(IDataListener iDataListener) throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.onstar.IDataManager
        public void unRegisterTrafficDataRanges(IDataListener iDataListener) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IDataManager {
        public static final String DESCRIPTOR = "com.yfve.ici.app.onstar.IDataManager";
        public static final int TRANSACTION_getFreeDataTrafficData = 2;
        public static final int TRANSACTION_getFreeDataTrafficLevel = 3;
        public static final int TRANSACTION_getWIFIDataTrafficData = 1;
        public static final int TRANSACTION_getWifiDataTrafficLevel = 4;
        public static final int TRANSACTION_registerTrafficDataRanges = 5;
        public static final int TRANSACTION_unRegisterTrafficDataRanges = 6;

        /* loaded from: classes.dex */
        public static class Proxy implements IDataManager {
            public static IDataManager sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            @Override // com.yfve.ici.app.onstar.IDataManager
            public TrafficDataInfo getFreeDataTrafficData() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.onstar.IDataManager");
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getFreeDataTrafficData();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0 ? TrafficDataInfo.CREATOR.createFromParcel(obtain2) : null;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.onstar.IDataManager
            public int getFreeDataTrafficLevel() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.onstar.IDataManager");
                    if (!this.mRemote.transact(3, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getFreeDataTrafficLevel();
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            public String getInterfaceDescriptor() {
                return "com.yfve.ici.app.onstar.IDataManager";
            }

            @Override // com.yfve.ici.app.onstar.IDataManager
            public TrafficDataInfo getWIFIDataTrafficData() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.onstar.IDataManager");
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getWIFIDataTrafficData();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0 ? TrafficDataInfo.CREATOR.createFromParcel(obtain2) : null;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.onstar.IDataManager
            public int getWifiDataTrafficLevel() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.onstar.IDataManager");
                    if (!this.mRemote.transact(4, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getWifiDataTrafficLevel();
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.onstar.IDataManager
            public boolean registerTrafficDataRanges(IDataListener iDataListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.onstar.IDataManager");
                    obtain.writeStrongBinder(iDataListener != null ? iDataListener.asBinder() : null);
                    if (!this.mRemote.transact(5, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().registerTrafficDataRanges(iDataListener);
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.onstar.IDataManager
            public void unRegisterTrafficDataRanges(IDataListener iDataListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.onstar.IDataManager");
                    obtain.writeStrongBinder(iDataListener != null ? iDataListener.asBinder() : null);
                    if (!this.mRemote.transact(6, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unRegisterTrafficDataRanges(iDataListener);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, "com.yfve.ici.app.onstar.IDataManager");
        }

        public static IDataManager asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface("com.yfve.ici.app.onstar.IDataManager");
            if (queryLocalInterface != null && (queryLocalInterface instanceof IDataManager)) {
                return (IDataManager) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IDataManager getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IDataManager iDataManager) {
            if (Proxy.sDefaultImpl != null || iDataManager == null) {
                return false;
            }
            Proxy.sDefaultImpl = iDataManager;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1598968902) {
                switch (i) {
                    case 1:
                        parcel.enforceInterface("com.yfve.ici.app.onstar.IDataManager");
                        TrafficDataInfo wIFIDataTrafficData = getWIFIDataTrafficData();
                        parcel2.writeNoException();
                        if (wIFIDataTrafficData != null) {
                            parcel2.writeInt(1);
                            wIFIDataTrafficData.writeToParcel(parcel2, 1);
                        } else {
                            parcel2.writeInt(0);
                        }
                        return true;
                    case 2:
                        parcel.enforceInterface("com.yfve.ici.app.onstar.IDataManager");
                        TrafficDataInfo freeDataTrafficData = getFreeDataTrafficData();
                        parcel2.writeNoException();
                        if (freeDataTrafficData != null) {
                            parcel2.writeInt(1);
                            freeDataTrafficData.writeToParcel(parcel2, 1);
                        } else {
                            parcel2.writeInt(0);
                        }
                        return true;
                    case 3:
                        parcel.enforceInterface("com.yfve.ici.app.onstar.IDataManager");
                        int freeDataTrafficLevel = getFreeDataTrafficLevel();
                        parcel2.writeNoException();
                        parcel2.writeInt(freeDataTrafficLevel);
                        return true;
                    case 4:
                        parcel.enforceInterface("com.yfve.ici.app.onstar.IDataManager");
                        int wifiDataTrafficLevel = getWifiDataTrafficLevel();
                        parcel2.writeNoException();
                        parcel2.writeInt(wifiDataTrafficLevel);
                        return true;
                    case 5:
                        parcel.enforceInterface("com.yfve.ici.app.onstar.IDataManager");
                        boolean registerTrafficDataRanges = registerTrafficDataRanges(IDataListener.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        parcel2.writeInt(registerTrafficDataRanges ? 1 : 0);
                        return true;
                    case 6:
                        parcel.enforceInterface("com.yfve.ici.app.onstar.IDataManager");
                        unRegisterTrafficDataRanges(IDataListener.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    default:
                        return super.onTransact(i, parcel, parcel2, i2);
                }
            }
            parcel2.writeString("com.yfve.ici.app.onstar.IDataManager");
            return true;
        }
    }

    TrafficDataInfo getFreeDataTrafficData() throws RemoteException;

    int getFreeDataTrafficLevel() throws RemoteException;

    TrafficDataInfo getWIFIDataTrafficData() throws RemoteException;

    int getWifiDataTrafficLevel() throws RemoteException;

    boolean registerTrafficDataRanges(IDataListener iDataListener) throws RemoteException;

    void unRegisterTrafficDataRanges(IDataListener iDataListener) throws RemoteException;
}
