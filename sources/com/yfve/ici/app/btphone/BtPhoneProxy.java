package com.yfve.ici.app.btphone;

import android.os.RemoteException;
import android.util.Log;
import b.a.b.a.a;
import com.yfve.ici.app.btphone.IBtPhoneActiveCallScreenChangedAIDL;
import com.yfve.ici.app.btphone.IBtPhoneCurrentCallStateChangedAIDL;
import com.yfve.ici.app.btphone.IBtPhoneDialPhoneWindowChangedAIDL;
import com.yfve.ici.service.base.BaseProxy;
import com.yfve.ici.service.contanst.ServiceConstant;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/* loaded from: classes.dex */
public class BtPhoneProxy extends BaseProxy<IBtPhoneProxy> {
    public static final String TAG = "BtPhoneProxy";
    public static BtPhoneProxy mInstance;
    public IBtPhoneActiveCallScreenChangedAIDL mBtPhoneActiveCallScreenChangedAIDL;
    public IBtPhoneCurrentCallStateChangedAIDL mBtPhoneCurrentCallStateChangedAIDL;
    public IBtPhoneDialPhoneWindowChangedAIDL mIBtPhoneDialPhoneWindowChangedAIDL;
    public List<IBtPhoneActiveCallScreenChangedCallback> mBtPhoneActiveCallScreenChangedCallbackList = new CopyOnWriteArrayList();
    public List<IBtPhoneCurrentCallStateChangedCallback> mBtPhoneCurrentCallStateChangedCallbackList = new CopyOnWriteArrayList();
    public List<IBtPhoneDialPhoneWindowChangedCallback> mBtPhoneDialPhoneWindowChangedCallbackList = new CopyOnWriteArrayList();

    public static BtPhoneProxy getInstance() {
        if (mInstance == null) {
            synchronized (BtPhoneProxy.class) {
                if (mInstance == null) {
                    mInstance = new BtPhoneProxy();
                }
            }
        }
        return mInstance;
    }

    public void answerCall() {
        Log.i(TAG, "answerCall in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "answerCall~~mInterface is null");
            } else {
                ((IBtPhoneProxy) this.mInterface).answerCall();
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public int callBackLatestMissedCall() {
        Log.i(TAG, "callBackLatestMissedCall in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "callBackLatestMissedCall~~mInterface is null");
                return -2147418113;
            }
            return ((IBtPhoneProxy) this.mInterface).callBackLatestMissedCall();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int callByName(String str) {
        Log.i(TAG, "callByName in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "callByName~~mInterface is null");
            }
            return ((IBtPhoneProxy) this.mInterface).callByName(str);
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }

    public boolean checkContactDetail(String str) {
        Log.i(TAG, "checkContactDetail in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "checkContactDetail~~mInterface is null");
                return false;
            }
            return ((IBtPhoneProxy) this.mInterface).checkContactDetail(str);
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return false;
        }
    }

    public void confirmConnectDevice() {
        Log.i(TAG, "confirmConnectDevice in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "confirmConnectDevice~~mInterface is null");
            } else {
                ((IBtPhoneProxy) this.mInterface).confirmConnectDevice();
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public void confirmDialPhone() {
        Log.i(TAG, "confirmDialPhone in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "confirmDialPhone~~mInterface is null");
            } else {
                ((IBtPhoneProxy) this.mInterface).confirmDialPhone();
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public void dialPhoneNumber(String str) {
        Log.i(TAG, "dialPhoneNumber in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "dialPhoneNumber~~mInterface is null");
            } else {
                ((IBtPhoneProxy) this.mInterface).dialPhoneNumber(str);
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public void dialPhoneNumberFromOtherApplication(String str) {
        Log.i(TAG, "dialPhoneNumberFromOtherApplication in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "dialPhoneNumberFromOtherApplication~~mInterface is null");
            } else {
                ((IBtPhoneProxy) this.mInterface).dialPhoneNumberFromOtherApplication(str);
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public void disconnectCall() {
        Log.i(TAG, "disconnectCall in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "disconnectCall~~mInterface is null");
            } else {
                ((IBtPhoneProxy) this.mInterface).disconnectCall();
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public void dismissConnectDeviceWindow() {
        Log.i(TAG, "dismissConnectDeviceWindow in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "dismissConnectDeviceWindow~~mInterface is null");
            } else {
                ((IBtPhoneProxy) this.mInterface).dismissConnectDeviceWindow();
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public void dismissDialPhoneWindow() {
        Log.i(TAG, "dismissDialPhoneWindow in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "dismissDialPhoneWindow~~mInterface is null");
            } else {
                ((IBtPhoneProxy) this.mInterface).dismissDialPhoneWindow();
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public boolean downloadPhoneBook() {
        Log.i(TAG, "downloadPhoneBook in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "downloadPhoneBook~~mInterface is null");
                return false;
            }
            return ((IBtPhoneProxy) this.mInterface).downloadPhoneBook();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return false;
        }
    }

    public String getCurrentBtDeviceName() {
        Log.i(TAG, "getCurrentBtDeviceName in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getCurrentBtDeviceName~~mInterface is null");
                return "";
            }
            return ((IBtPhoneProxy) this.mInterface).getCurrentBtDeviceName();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return "";
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return "";
        }
    }

    public String getCurrentCallName() {
        Log.i(TAG, "getCurrentCallName in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getCurrentCallName~~mInterface is null");
                return null;
            }
            return ((IBtPhoneProxy) this.mInterface).getCurrentCallName();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return null;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return null;
        }
    }

    public String getCurrentCallNumber() {
        Log.i(TAG, "getCurrentCallNumber in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getCurrentCallNumber~~mInterface is null");
                return null;
            }
            return ((IBtPhoneProxy) this.mInterface).getCurrentCallNumber();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return null;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return null;
        }
    }

    public int getCurrentCallState() {
        Log.i(TAG, "getCurrentCallState in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getCurrentCallState~~mInterface is null");
                return 0;
            }
            return ((IBtPhoneProxy) this.mInterface).getCurrentCallState();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return 0;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return 0;
        }
    }

    public long getCurrentCallTime() {
        Log.i(TAG, "getCurrentCallTime in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getCurrentCallTime~~mInterface is null");
                return 0L;
            }
            return ((IBtPhoneProxy) this.mInterface).getCurrentCallTime();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return 0L;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return 0L;
        }
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public String getServiceName() {
        return ServiceConstant.BT_PHONE_BINDER_NAME;
    }

    public void incClearMissCall(String str) {
        Log.i(TAG, "incClearMissCall in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "incClearMissCall~~mInterface is null");
            } else {
                ((IBtPhoneProxy) this.mInterface).incClearMissCall(str);
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public boolean isActiveCallScreenInBackground() {
        Log.i(TAG, "isActiveCallScreenInBackground in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "isActiveCallScreenInBackground~~mInterface is null");
                return false;
            }
            return ((IBtPhoneProxy) this.mInterface).isActiveCallScreenInBackground();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return false;
        }
    }

    public boolean isConnectDeviceWindowShowing() {
        Log.i(TAG, "isConnectDeviceWindowShowing in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "isConnectDeviceWindowShowing~~mInterface is null");
                return false;
            }
            return ((IBtPhoneProxy) this.mInterface).isConnectDeviceWindowShowing();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return false;
        }
    }

    public boolean isDialPhoneWindowShowing() {
        Log.i(TAG, "isDialPhoneWindowShowing in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "isDialPhoneWindowShowing~~mInterface is null");
                return false;
            }
            return ((IBtPhoneProxy) this.mInterface).isDialPhoneWindowShowing();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return false;
        }
    }

    public boolean launchBtPhoneApp(String str) {
        Log.i(TAG, "launchBtPhoneApp in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "launchBtPhoneApp~~mInterface is null");
                return false;
            }
            return ((IBtPhoneProxy) this.mInterface).launchBtPhoneApp(str);
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return false;
        }
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public void onServiceConnectStatusChanged(boolean z) {
        super.onServiceConnectStatusChanged(z);
        a.l("onServiceConnectStatusChanged onServiceConnectStatusChanged status : ", z, TAG);
        if (z) {
            return;
        }
        List<IBtPhoneActiveCallScreenChangedCallback> list = this.mBtPhoneActiveCallScreenChangedCallbackList;
        if (list != null) {
            list.clear();
        }
        List<IBtPhoneCurrentCallStateChangedCallback> list2 = this.mBtPhoneCurrentCallStateChangedCallbackList;
        if (list2 != null) {
            list2.clear();
        }
    }

    public void openContacts() {
        Log.i(TAG, "openContacts in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "openContacts~~mInterface is null");
            } else {
                ((IBtPhoneProxy) this.mInterface).openContacts();
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public void openRecentCall() {
        Log.i(TAG, "openRecentCall in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "openRecentCall~~mInterface is null");
            } else {
                ((IBtPhoneProxy) this.mInterface).openRecentCall();
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public String queryLatestMissedCallNumber() {
        Log.i(TAG, "queryLatestMissedCallNumber in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "queryLatestMissedCallNumber~~mInterface is null");
                return null;
            }
            return ((IBtPhoneProxy) this.mInterface).queryLatestMissedCallNumber();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return null;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return null;
        }
    }

    public String queryLatestOutGoingCallNumber() {
        Log.i(TAG, "queryLatestOutGoingCallNumber in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "queryLatestOutGoingCallNumber~~mInterface is null");
                return null;
            }
            return ((IBtPhoneProxy) this.mInterface).queryLatestOutGoingCallNumber();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return null;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return null;
        }
    }

    public int redialLatestOutGoingCall() {
        Log.i(TAG, "redialLatestOutGoingCall in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "redialLatestOutGoingCall~~mInterface is null");
                return -2147418113;
            }
            return ((IBtPhoneProxy) this.mInterface).redialLatestOutGoingCall();
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int registerBtPhoneActiveCallScreenChangedListener(IBtPhoneActiveCallScreenChangedCallback iBtPhoneActiveCallScreenChangedCallback) {
        Log.d(TAG, "registerBtPhoneActiveCallScreenChanged in");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerBtPhoneActiveCallScreenChanged~~mInterface is null");
                return -2147483646;
            }
            if (iBtPhoneActiveCallScreenChangedCallback != null && !this.mBtPhoneActiveCallScreenChangedCallbackList.contains(iBtPhoneActiveCallScreenChangedCallback)) {
                this.mBtPhoneActiveCallScreenChangedCallbackList.add(iBtPhoneActiveCallScreenChangedCallback);
                if (this.mBtPhoneActiveCallScreenChangedCallbackList.size() == 1) {
                    IBtPhoneActiveCallScreenChangedAIDL.Stub stub = new IBtPhoneActiveCallScreenChangedAIDL.Stub() { // from class: com.yfve.ici.app.btphone.BtPhoneProxy.1
                        @Override // com.yfve.ici.app.btphone.IBtPhoneActiveCallScreenChangedAIDL
                        public void onActiveCallScreenChanged(boolean z) throws RemoteException {
                            String str = BtPhoneProxy.TAG;
                            Log.d(str, "onActiveCallScreenChanged  isBackgroundState -- " + z);
                            for (IBtPhoneActiveCallScreenChangedCallback iBtPhoneActiveCallScreenChangedCallback2 : BtPhoneProxy.this.mBtPhoneActiveCallScreenChangedCallbackList) {
                                iBtPhoneActiveCallScreenChangedCallback2.onActiveCallScreenChanged(z);
                            }
                        }
                    };
                    this.mBtPhoneActiveCallScreenChangedAIDL = stub;
                    ((IBtPhoneProxy) this.mInterface).registerBtPhoneActiveCallScreenChangedListener(stub);
                    return Integer.MIN_VALUE;
                }
            }
            return -2147418113;
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int registerBtPhoneCurrentCallStateChangedListener(IBtPhoneCurrentCallStateChangedCallback iBtPhoneCurrentCallStateChangedCallback) {
        Log.d(TAG, "registerBtPhoneCurrentCallStateChanged in");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerBtPhoneCurrentCallStateChanged~~mInterface is null");
                return -2147483646;
            }
            if (iBtPhoneCurrentCallStateChangedCallback != null && !this.mBtPhoneCurrentCallStateChangedCallbackList.contains(iBtPhoneCurrentCallStateChangedCallback)) {
                this.mBtPhoneCurrentCallStateChangedCallbackList.add(iBtPhoneCurrentCallStateChangedCallback);
                if (this.mBtPhoneCurrentCallStateChangedCallbackList.size() == 1) {
                    IBtPhoneCurrentCallStateChangedAIDL.Stub stub = new IBtPhoneCurrentCallStateChangedAIDL.Stub() { // from class: com.yfve.ici.app.btphone.BtPhoneProxy.2
                        @Override // com.yfve.ici.app.btphone.IBtPhoneCurrentCallStateChangedAIDL
                        public void onCurrentCallStateChanged(int i) throws RemoteException {
                            String str = BtPhoneProxy.TAG;
                            Log.d(str, "onCurrentCallStateChanged  state -- " + i);
                            for (IBtPhoneCurrentCallStateChangedCallback iBtPhoneCurrentCallStateChangedCallback2 : BtPhoneProxy.this.mBtPhoneCurrentCallStateChangedCallbackList) {
                                iBtPhoneCurrentCallStateChangedCallback2.onCurrentCallStateChanged(i);
                            }
                        }
                    };
                    this.mBtPhoneCurrentCallStateChangedAIDL = stub;
                    ((IBtPhoneProxy) this.mInterface).registerBtPhoneCurrentCallStateChangedListener(stub);
                    return Integer.MIN_VALUE;
                }
            }
            return -2147418113;
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int registerBtPhoneDialPhoneWindowChangedListener(IBtPhoneDialPhoneWindowChangedCallback iBtPhoneDialPhoneWindowChangedCallback) {
        Log.d(TAG, "registerBtPhoneActiveCallScreenChanged in");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerBtPhoneActiveCallScreenChanged~~mInterface is null");
                return -2147483646;
            }
            if (iBtPhoneDialPhoneWindowChangedCallback != null && !this.mBtPhoneDialPhoneWindowChangedCallbackList.contains(iBtPhoneDialPhoneWindowChangedCallback)) {
                this.mBtPhoneDialPhoneWindowChangedCallbackList.add(iBtPhoneDialPhoneWindowChangedCallback);
                if (this.mBtPhoneDialPhoneWindowChangedCallbackList.size() == 1) {
                    IBtPhoneDialPhoneWindowChangedAIDL.Stub stub = new IBtPhoneDialPhoneWindowChangedAIDL.Stub() { // from class: com.yfve.ici.app.btphone.BtPhoneProxy.3
                        @Override // com.yfve.ici.app.btphone.IBtPhoneDialPhoneWindowChangedAIDL
                        public void onConnectDeviceWindowChanged(boolean z) throws RemoteException {
                            String str = BtPhoneProxy.TAG;
                            Log.d(str, "onConnectDeviceWindowChanged   -- " + z);
                            for (IBtPhoneDialPhoneWindowChangedCallback iBtPhoneDialPhoneWindowChangedCallback2 : BtPhoneProxy.this.mBtPhoneDialPhoneWindowChangedCallbackList) {
                                iBtPhoneDialPhoneWindowChangedCallback2.onConnectDeviceWindowChanged(z);
                            }
                        }

                        @Override // com.yfve.ici.app.btphone.IBtPhoneDialPhoneWindowChangedAIDL
                        public void onDialPhoneWindowChanged(boolean z) throws RemoteException {
                            String str = BtPhoneProxy.TAG;
                            Log.d(str, "onDialPhoneWindowChanged   -- " + z);
                            for (IBtPhoneDialPhoneWindowChangedCallback iBtPhoneDialPhoneWindowChangedCallback2 : BtPhoneProxy.this.mBtPhoneDialPhoneWindowChangedCallbackList) {
                                iBtPhoneDialPhoneWindowChangedCallback2.onDialPhoneWindowChanged(z);
                            }
                        }
                    };
                    this.mIBtPhoneDialPhoneWindowChangedAIDL = stub;
                    ((IBtPhoneProxy) this.mInterface).registerBtPhoneDialPhoneWindowChangedListener(stub);
                    return Integer.MIN_VALUE;
                }
            }
            return -2147418113;
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }

    public void rejectCall() {
        Log.i(TAG, "rejectCall in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "rejectCall~~mInterface is null");
            } else {
                ((IBtPhoneProxy) this.mInterface).rejectCall();
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public int unRegisterBtPhoneDialPhoneWindowChangedListener(IBtPhoneDialPhoneWindowChangedCallback iBtPhoneDialPhoneWindowChangedCallback) {
        Log.i(TAG, "unregisterBtPhoneActiveCallScreenChanged in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unregisterBtPhoneActiveCallScreenChanged~~mInterface is null");
                return -2147483646;
            }
            if (iBtPhoneDialPhoneWindowChangedCallback != null && this.mBtPhoneDialPhoneWindowChangedCallbackList.contains(iBtPhoneDialPhoneWindowChangedCallback)) {
                this.mBtPhoneDialPhoneWindowChangedCallbackList.remove(iBtPhoneDialPhoneWindowChangedCallback);
                if (this.mBtPhoneDialPhoneWindowChangedCallbackList.size() == 0) {
                    ((IBtPhoneProxy) this.mInterface).unRegisterBtPhoneDialPhoneWindowChangedListener(this.mIBtPhoneDialPhoneWindowChangedAIDL);
                    this.mIBtPhoneDialPhoneWindowChangedAIDL = null;
                    return Integer.MIN_VALUE;
                }
            }
            return -2147418113;
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int unregisterBtPhoneActiveCallScreenChangedListener(IBtPhoneActiveCallScreenChangedCallback iBtPhoneActiveCallScreenChangedCallback) {
        Log.i(TAG, "unregisterBtPhoneActiveCallScreenChanged in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unregisterBtPhoneActiveCallScreenChanged~~mInterface is null");
                return -2147483646;
            }
            if (iBtPhoneActiveCallScreenChangedCallback != null && this.mBtPhoneActiveCallScreenChangedCallbackList.contains(iBtPhoneActiveCallScreenChangedCallback)) {
                this.mBtPhoneActiveCallScreenChangedCallbackList.remove(iBtPhoneActiveCallScreenChangedCallback);
                if (this.mBtPhoneActiveCallScreenChangedCallbackList.size() == 0) {
                    ((IBtPhoneProxy) this.mInterface).unRegisterBtPhoneActiveCallScreenChangedListener(this.mBtPhoneActiveCallScreenChangedAIDL);
                    this.mBtPhoneActiveCallScreenChangedAIDL = null;
                    return Integer.MIN_VALUE;
                }
            }
            return -2147418113;
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int unregisterBtPhoneCurrentCallStateChangedListener(IBtPhoneCurrentCallStateChangedCallback iBtPhoneCurrentCallStateChangedCallback) {
        Log.i(TAG, "unregisterBtPhoneCurrentCallStateChanged in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unregisterBtPhoneCurrentCallStateChanged~~mInterface is null");
                return -2147483646;
            }
            if (iBtPhoneCurrentCallStateChangedCallback != null && this.mBtPhoneCurrentCallStateChangedCallbackList.contains(iBtPhoneCurrentCallStateChangedCallback)) {
                this.mBtPhoneCurrentCallStateChangedCallbackList.remove(iBtPhoneCurrentCallStateChangedCallback);
                if (this.mBtPhoneCurrentCallStateChangedCallbackList.size() == 0) {
                    ((IBtPhoneProxy) this.mInterface).unRegisterBtPhoneCurrentCallStateChangedListener(this.mBtPhoneCurrentCallStateChangedAIDL);
                    this.mBtPhoneCurrentCallStateChangedAIDL = null;
                    return Integer.MIN_VALUE;
                }
            }
            return -2147418113;
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }
}
