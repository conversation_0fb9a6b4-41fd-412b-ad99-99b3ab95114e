package com.yfve.ici.app.btphone;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import com.yfve.ici.app.btphone.IBtPhoneActiveCallScreenChangedAIDL;
import com.yfve.ici.app.btphone.IBtPhoneCurrentCallStateChangedAIDL;
import com.yfve.ici.app.btphone.IBtPhoneDialPhoneWindowChangedAIDL;

/* loaded from: classes.dex */
public interface IBtPhoneProxy extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IBtPhoneProxy {
        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public void answerCall() throws RemoteException {
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public int callBackLatestMissedCall() throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public int callByName(String str) throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public boolean checkContactDetail(String str) throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public void confirmConnectDevice() throws RemoteException {
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public void confirmDialPhone() throws RemoteException {
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public void dialPhoneNumber(String str) throws RemoteException {
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public void dialPhoneNumberFromOtherApplication(String str) throws RemoteException {
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public void disconnectCall() throws RemoteException {
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public void dismissConnectDeviceWindow() throws RemoteException {
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public void dismissDialPhoneWindow() throws RemoteException {
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public boolean downloadPhoneBook() throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public String getCurrentBtDeviceName() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public String getCurrentCallName() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public String getCurrentCallNumber() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public int getCurrentCallState() throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public long getCurrentCallTime() throws RemoteException {
            return 0L;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public void incClearMissCall(String str) throws RemoteException {
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public boolean isActiveCallScreenInBackground() throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public boolean isConnectDeviceWindowShowing() throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public boolean isDialPhoneWindowShowing() throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public boolean launchBtPhoneApp(String str) throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public void openContacts() throws RemoteException {
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public void openRecentCall() throws RemoteException {
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public String queryLatestMissedCallNumber() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public String queryLatestOutGoingCallNumber() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public int redialLatestOutGoingCall() throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public int registerBtPhoneActiveCallScreenChangedListener(IBtPhoneActiveCallScreenChangedAIDL iBtPhoneActiveCallScreenChangedAIDL) throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public int registerBtPhoneCurrentCallStateChangedListener(IBtPhoneCurrentCallStateChangedAIDL iBtPhoneCurrentCallStateChangedAIDL) throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public int registerBtPhoneDialPhoneWindowChangedListener(IBtPhoneDialPhoneWindowChangedAIDL iBtPhoneDialPhoneWindowChangedAIDL) throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public void rejectCall() throws RemoteException {
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public int unRegisterBtPhoneActiveCallScreenChangedListener(IBtPhoneActiveCallScreenChangedAIDL iBtPhoneActiveCallScreenChangedAIDL) throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public int unRegisterBtPhoneCurrentCallStateChangedListener(IBtPhoneCurrentCallStateChangedAIDL iBtPhoneCurrentCallStateChangedAIDL) throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
        public int unRegisterBtPhoneDialPhoneWindowChangedListener(IBtPhoneDialPhoneWindowChangedAIDL iBtPhoneDialPhoneWindowChangedAIDL) throws RemoteException {
            return 0;
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IBtPhoneProxy {
        public static final String DESCRIPTOR = "com.yfve.ici.app.btphone.IBtPhoneProxy";
        public static final int TRANSACTION_answerCall = 10;
        public static final int TRANSACTION_callBackLatestMissedCall = 16;
        public static final int TRANSACTION_callByName = 13;
        public static final int TRANSACTION_checkContactDetail = 21;
        public static final int TRANSACTION_confirmConnectDevice = 32;
        public static final int TRANSACTION_confirmDialPhone = 31;
        public static final int TRANSACTION_dialPhoneNumber = 9;
        public static final int TRANSACTION_dialPhoneNumberFromOtherApplication = 26;
        public static final int TRANSACTION_disconnectCall = 12;
        public static final int TRANSACTION_dismissConnectDeviceWindow = 28;
        public static final int TRANSACTION_dismissDialPhoneWindow = 27;
        public static final int TRANSACTION_downloadPhoneBook = 19;
        public static final int TRANSACTION_getCurrentBtDeviceName = 25;
        public static final int TRANSACTION_getCurrentCallName = 6;
        public static final int TRANSACTION_getCurrentCallNumber = 7;
        public static final int TRANSACTION_getCurrentCallState = 5;
        public static final int TRANSACTION_getCurrentCallTime = 8;
        public static final int TRANSACTION_incClearMissCall = 24;
        public static final int TRANSACTION_isActiveCallScreenInBackground = 22;
        public static final int TRANSACTION_isConnectDeviceWindowShowing = 30;
        public static final int TRANSACTION_isDialPhoneWindowShowing = 29;
        public static final int TRANSACTION_launchBtPhoneApp = 23;
        public static final int TRANSACTION_openContacts = 20;
        public static final int TRANSACTION_openRecentCall = 14;
        public static final int TRANSACTION_queryLatestMissedCallNumber = 15;
        public static final int TRANSACTION_queryLatestOutGoingCallNumber = 17;
        public static final int TRANSACTION_redialLatestOutGoingCall = 18;
        public static final int TRANSACTION_registerBtPhoneActiveCallScreenChangedListener = 3;
        public static final int TRANSACTION_registerBtPhoneCurrentCallStateChangedListener = 1;
        public static final int TRANSACTION_registerBtPhoneDialPhoneWindowChangedListener = 33;
        public static final int TRANSACTION_rejectCall = 11;
        public static final int TRANSACTION_unRegisterBtPhoneActiveCallScreenChangedListener = 4;
        public static final int TRANSACTION_unRegisterBtPhoneCurrentCallStateChangedListener = 2;
        public static final int TRANSACTION_unRegisterBtPhoneDialPhoneWindowChangedListener = 34;

        /* loaded from: classes.dex */
        public static class Proxy implements IBtPhoneProxy {
            public static IBtPhoneProxy sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public void answerCall() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(10, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().answerCall();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public int callBackLatestMissedCall() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(16, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().callBackLatestMissedCall();
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public int callByName(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    obtain.writeString(str);
                    if (!this.mRemote.transact(13, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().callByName(str);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public boolean checkContactDetail(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    obtain.writeString(str);
                    if (!this.mRemote.transact(21, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().checkContactDetail(str);
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public void confirmConnectDevice() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(32, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().confirmConnectDevice();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public void confirmDialPhone() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(31, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().confirmDialPhone();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public void dialPhoneNumber(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    obtain.writeString(str);
                    if (!this.mRemote.transact(9, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().dialPhoneNumber(str);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public void dialPhoneNumberFromOtherApplication(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    obtain.writeString(str);
                    if (!this.mRemote.transact(26, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().dialPhoneNumberFromOtherApplication(str);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public void disconnectCall() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(12, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().disconnectCall();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public void dismissConnectDeviceWindow() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(28, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().dismissConnectDeviceWindow();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public void dismissDialPhoneWindow() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(27, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().dismissDialPhoneWindow();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public boolean downloadPhoneBook() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(19, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().downloadPhoneBook();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public String getCurrentBtDeviceName() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(25, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getCurrentBtDeviceName();
                    }
                    obtain2.readException();
                    return obtain2.readString();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public String getCurrentCallName() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(6, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getCurrentCallName();
                    }
                    obtain2.readException();
                    return obtain2.readString();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public String getCurrentCallNumber() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(7, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getCurrentCallNumber();
                    }
                    obtain2.readException();
                    return obtain2.readString();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public int getCurrentCallState() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(5, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getCurrentCallState();
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public long getCurrentCallTime() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(8, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getCurrentCallTime();
                    }
                    obtain2.readException();
                    return obtain2.readLong();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            public String getInterfaceDescriptor() {
                return "com.yfve.ici.app.btphone.IBtPhoneProxy";
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public void incClearMissCall(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    obtain.writeString(str);
                    if (!this.mRemote.transact(24, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().incClearMissCall(str);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public boolean isActiveCallScreenInBackground() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(22, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().isActiveCallScreenInBackground();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public boolean isConnectDeviceWindowShowing() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(30, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().isConnectDeviceWindowShowing();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public boolean isDialPhoneWindowShowing() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(29, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().isDialPhoneWindowShowing();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public boolean launchBtPhoneApp(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    obtain.writeString(str);
                    if (!this.mRemote.transact(23, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().launchBtPhoneApp(str);
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public void openContacts() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(20, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().openContacts();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public void openRecentCall() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(14, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().openRecentCall();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public String queryLatestMissedCallNumber() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(15, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().queryLatestMissedCallNumber();
                    }
                    obtain2.readException();
                    return obtain2.readString();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public String queryLatestOutGoingCallNumber() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(17, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().queryLatestOutGoingCallNumber();
                    }
                    obtain2.readException();
                    return obtain2.readString();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public int redialLatestOutGoingCall() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(18, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().redialLatestOutGoingCall();
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public int registerBtPhoneActiveCallScreenChangedListener(IBtPhoneActiveCallScreenChangedAIDL iBtPhoneActiveCallScreenChangedAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    obtain.writeStrongBinder(iBtPhoneActiveCallScreenChangedAIDL != null ? iBtPhoneActiveCallScreenChangedAIDL.asBinder() : null);
                    if (!this.mRemote.transact(3, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().registerBtPhoneActiveCallScreenChangedListener(iBtPhoneActiveCallScreenChangedAIDL);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public int registerBtPhoneCurrentCallStateChangedListener(IBtPhoneCurrentCallStateChangedAIDL iBtPhoneCurrentCallStateChangedAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    obtain.writeStrongBinder(iBtPhoneCurrentCallStateChangedAIDL != null ? iBtPhoneCurrentCallStateChangedAIDL.asBinder() : null);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().registerBtPhoneCurrentCallStateChangedListener(iBtPhoneCurrentCallStateChangedAIDL);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public int registerBtPhoneDialPhoneWindowChangedListener(IBtPhoneDialPhoneWindowChangedAIDL iBtPhoneDialPhoneWindowChangedAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    obtain.writeStrongBinder(iBtPhoneDialPhoneWindowChangedAIDL != null ? iBtPhoneDialPhoneWindowChangedAIDL.asBinder() : null);
                    if (!this.mRemote.transact(33, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().registerBtPhoneDialPhoneWindowChangedListener(iBtPhoneDialPhoneWindowChangedAIDL);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public void rejectCall() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    if (!this.mRemote.transact(11, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().rejectCall();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public int unRegisterBtPhoneActiveCallScreenChangedListener(IBtPhoneActiveCallScreenChangedAIDL iBtPhoneActiveCallScreenChangedAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    obtain.writeStrongBinder(iBtPhoneActiveCallScreenChangedAIDL != null ? iBtPhoneActiveCallScreenChangedAIDL.asBinder() : null);
                    if (!this.mRemote.transact(4, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().unRegisterBtPhoneActiveCallScreenChangedListener(iBtPhoneActiveCallScreenChangedAIDL);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public int unRegisterBtPhoneCurrentCallStateChangedListener(IBtPhoneCurrentCallStateChangedAIDL iBtPhoneCurrentCallStateChangedAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    obtain.writeStrongBinder(iBtPhoneCurrentCallStateChangedAIDL != null ? iBtPhoneCurrentCallStateChangedAIDL.asBinder() : null);
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().unRegisterBtPhoneCurrentCallStateChangedListener(iBtPhoneCurrentCallStateChangedAIDL);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneProxy
            public int unRegisterBtPhoneDialPhoneWindowChangedListener(IBtPhoneDialPhoneWindowChangedAIDL iBtPhoneDialPhoneWindowChangedAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.btphone.IBtPhoneProxy");
                    obtain.writeStrongBinder(iBtPhoneDialPhoneWindowChangedAIDL != null ? iBtPhoneDialPhoneWindowChangedAIDL.asBinder() : null);
                    if (!this.mRemote.transact(34, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().unRegisterBtPhoneDialPhoneWindowChangedListener(iBtPhoneDialPhoneWindowChangedAIDL);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, "com.yfve.ici.app.btphone.IBtPhoneProxy");
        }

        public static IBtPhoneProxy asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
            if (queryLocalInterface != null && (queryLocalInterface instanceof IBtPhoneProxy)) {
                return (IBtPhoneProxy) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IBtPhoneProxy getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IBtPhoneProxy iBtPhoneProxy) {
            if (Proxy.sDefaultImpl != null || iBtPhoneProxy == null) {
                return false;
            }
            Proxy.sDefaultImpl = iBtPhoneProxy;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1598968902) {
                switch (i) {
                    case 1:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        int registerBtPhoneCurrentCallStateChangedListener = registerBtPhoneCurrentCallStateChangedListener(IBtPhoneCurrentCallStateChangedAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        parcel2.writeInt(registerBtPhoneCurrentCallStateChangedListener);
                        return true;
                    case 2:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        int unRegisterBtPhoneCurrentCallStateChangedListener = unRegisterBtPhoneCurrentCallStateChangedListener(IBtPhoneCurrentCallStateChangedAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        parcel2.writeInt(unRegisterBtPhoneCurrentCallStateChangedListener);
                        return true;
                    case 3:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        int registerBtPhoneActiveCallScreenChangedListener = registerBtPhoneActiveCallScreenChangedListener(IBtPhoneActiveCallScreenChangedAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        parcel2.writeInt(registerBtPhoneActiveCallScreenChangedListener);
                        return true;
                    case 4:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        int unRegisterBtPhoneActiveCallScreenChangedListener = unRegisterBtPhoneActiveCallScreenChangedListener(IBtPhoneActiveCallScreenChangedAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        parcel2.writeInt(unRegisterBtPhoneActiveCallScreenChangedListener);
                        return true;
                    case 5:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        int currentCallState = getCurrentCallState();
                        parcel2.writeNoException();
                        parcel2.writeInt(currentCallState);
                        return true;
                    case 6:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        String currentCallName = getCurrentCallName();
                        parcel2.writeNoException();
                        parcel2.writeString(currentCallName);
                        return true;
                    case 7:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        String currentCallNumber = getCurrentCallNumber();
                        parcel2.writeNoException();
                        parcel2.writeString(currentCallNumber);
                        return true;
                    case 8:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        long currentCallTime = getCurrentCallTime();
                        parcel2.writeNoException();
                        parcel2.writeLong(currentCallTime);
                        return true;
                    case 9:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        dialPhoneNumber(parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 10:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        answerCall();
                        parcel2.writeNoException();
                        return true;
                    case 11:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        rejectCall();
                        parcel2.writeNoException();
                        return true;
                    case 12:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        disconnectCall();
                        parcel2.writeNoException();
                        return true;
                    case 13:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        int callByName = callByName(parcel.readString());
                        parcel2.writeNoException();
                        parcel2.writeInt(callByName);
                        return true;
                    case 14:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        openRecentCall();
                        parcel2.writeNoException();
                        return true;
                    case 15:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        String queryLatestMissedCallNumber = queryLatestMissedCallNumber();
                        parcel2.writeNoException();
                        parcel2.writeString(queryLatestMissedCallNumber);
                        return true;
                    case 16:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        int callBackLatestMissedCall = callBackLatestMissedCall();
                        parcel2.writeNoException();
                        parcel2.writeInt(callBackLatestMissedCall);
                        return true;
                    case 17:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        String queryLatestOutGoingCallNumber = queryLatestOutGoingCallNumber();
                        parcel2.writeNoException();
                        parcel2.writeString(queryLatestOutGoingCallNumber);
                        return true;
                    case 18:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        int redialLatestOutGoingCall = redialLatestOutGoingCall();
                        parcel2.writeNoException();
                        parcel2.writeInt(redialLatestOutGoingCall);
                        return true;
                    case 19:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        boolean downloadPhoneBook = downloadPhoneBook();
                        parcel2.writeNoException();
                        parcel2.writeInt(downloadPhoneBook ? 1 : 0);
                        return true;
                    case 20:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        openContacts();
                        parcel2.writeNoException();
                        return true;
                    case 21:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        boolean checkContactDetail = checkContactDetail(parcel.readString());
                        parcel2.writeNoException();
                        parcel2.writeInt(checkContactDetail ? 1 : 0);
                        return true;
                    case 22:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        boolean isActiveCallScreenInBackground = isActiveCallScreenInBackground();
                        parcel2.writeNoException();
                        parcel2.writeInt(isActiveCallScreenInBackground ? 1 : 0);
                        return true;
                    case 23:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        boolean launchBtPhoneApp = launchBtPhoneApp(parcel.readString());
                        parcel2.writeNoException();
                        parcel2.writeInt(launchBtPhoneApp ? 1 : 0);
                        return true;
                    case 24:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        incClearMissCall(parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 25:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        String currentBtDeviceName = getCurrentBtDeviceName();
                        parcel2.writeNoException();
                        parcel2.writeString(currentBtDeviceName);
                        return true;
                    case 26:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        dialPhoneNumberFromOtherApplication(parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 27:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        dismissDialPhoneWindow();
                        parcel2.writeNoException();
                        return true;
                    case 28:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        dismissConnectDeviceWindow();
                        parcel2.writeNoException();
                        return true;
                    case 29:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        boolean isDialPhoneWindowShowing = isDialPhoneWindowShowing();
                        parcel2.writeNoException();
                        parcel2.writeInt(isDialPhoneWindowShowing ? 1 : 0);
                        return true;
                    case 30:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        boolean isConnectDeviceWindowShowing = isConnectDeviceWindowShowing();
                        parcel2.writeNoException();
                        parcel2.writeInt(isConnectDeviceWindowShowing ? 1 : 0);
                        return true;
                    case 31:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        confirmDialPhone();
                        parcel2.writeNoException();
                        return true;
                    case 32:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        confirmConnectDevice();
                        parcel2.writeNoException();
                        return true;
                    case 33:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        int registerBtPhoneDialPhoneWindowChangedListener = registerBtPhoneDialPhoneWindowChangedListener(IBtPhoneDialPhoneWindowChangedAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        parcel2.writeInt(registerBtPhoneDialPhoneWindowChangedListener);
                        return true;
                    case 34:
                        parcel.enforceInterface("com.yfve.ici.app.btphone.IBtPhoneProxy");
                        int unRegisterBtPhoneDialPhoneWindowChangedListener = unRegisterBtPhoneDialPhoneWindowChangedListener(IBtPhoneDialPhoneWindowChangedAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        parcel2.writeInt(unRegisterBtPhoneDialPhoneWindowChangedListener);
                        return true;
                    default:
                        return super.onTransact(i, parcel, parcel2, i2);
                }
            }
            parcel2.writeString("com.yfve.ici.app.btphone.IBtPhoneProxy");
            return true;
        }
    }

    void answerCall() throws RemoteException;

    int callBackLatestMissedCall() throws RemoteException;

    int callByName(String str) throws RemoteException;

    boolean checkContactDetail(String str) throws RemoteException;

    void confirmConnectDevice() throws RemoteException;

    void confirmDialPhone() throws RemoteException;

    void dialPhoneNumber(String str) throws RemoteException;

    void dialPhoneNumberFromOtherApplication(String str) throws RemoteException;

    void disconnectCall() throws RemoteException;

    void dismissConnectDeviceWindow() throws RemoteException;

    void dismissDialPhoneWindow() throws RemoteException;

    boolean downloadPhoneBook() throws RemoteException;

    String getCurrentBtDeviceName() throws RemoteException;

    String getCurrentCallName() throws RemoteException;

    String getCurrentCallNumber() throws RemoteException;

    int getCurrentCallState() throws RemoteException;

    long getCurrentCallTime() throws RemoteException;

    void incClearMissCall(String str) throws RemoteException;

    boolean isActiveCallScreenInBackground() throws RemoteException;

    boolean isConnectDeviceWindowShowing() throws RemoteException;

    boolean isDialPhoneWindowShowing() throws RemoteException;

    boolean launchBtPhoneApp(String str) throws RemoteException;

    void openContacts() throws RemoteException;

    void openRecentCall() throws RemoteException;

    String queryLatestMissedCallNumber() throws RemoteException;

    String queryLatestOutGoingCallNumber() throws RemoteException;

    int redialLatestOutGoingCall() throws RemoteException;

    int registerBtPhoneActiveCallScreenChangedListener(IBtPhoneActiveCallScreenChangedAIDL iBtPhoneActiveCallScreenChangedAIDL) throws RemoteException;

    int registerBtPhoneCurrentCallStateChangedListener(IBtPhoneCurrentCallStateChangedAIDL iBtPhoneCurrentCallStateChangedAIDL) throws RemoteException;

    int registerBtPhoneDialPhoneWindowChangedListener(IBtPhoneDialPhoneWindowChangedAIDL iBtPhoneDialPhoneWindowChangedAIDL) throws RemoteException;

    void rejectCall() throws RemoteException;

    int unRegisterBtPhoneActiveCallScreenChangedListener(IBtPhoneActiveCallScreenChangedAIDL iBtPhoneActiveCallScreenChangedAIDL) throws RemoteException;

    int unRegisterBtPhoneCurrentCallStateChangedListener(IBtPhoneCurrentCallStateChangedAIDL iBtPhoneCurrentCallStateChangedAIDL) throws RemoteException;

    int unRegisterBtPhoneDialPhoneWindowChangedListener(IBtPhoneDialPhoneWindowChangedAIDL iBtPhoneDialPhoneWindowChangedAIDL) throws RemoteException;
}
