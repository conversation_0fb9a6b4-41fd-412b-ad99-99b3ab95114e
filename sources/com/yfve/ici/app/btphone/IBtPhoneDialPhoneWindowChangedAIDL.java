package com.yfve.ici.app.btphone;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface IBtPhoneDialPhoneWindowChangedAIDL extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IBtPhoneDialPhoneWindowChangedAIDL {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneDialPhoneWindowChangedAIDL
        public void onConnectDeviceWindowChanged(boolean z) throws RemoteException {
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneDialPhoneWindowChangedAIDL
        public void onDialPhoneWindowChanged(boolean z) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IBtPhoneDialPhoneWindowChangedAIDL {
        public static final String DESCRIPTOR = "com.yfve.ici.app.btphone.IBtPhoneDialPhoneWindowChangedAIDL";
        public static final int TRANSACTION_onConnectDeviceWindowChanged = 2;
        public static final int TRANSACTION_onDialPhoneWindowChanged = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements IBtPhoneDialPhoneWindowChangedAIDL {
            public static IBtPhoneDialPhoneWindowChangedAIDL sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneDialPhoneWindowChangedAIDL
            public void onConnectDeviceWindowChanged(boolean z) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeInt(z ? 1 : 0);
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onConnectDeviceWindowChanged(z);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneDialPhoneWindowChangedAIDL
            public void onDialPhoneWindowChanged(boolean z) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeInt(z ? 1 : 0);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onDialPhoneWindowChanged(z);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static IBtPhoneDialPhoneWindowChangedAIDL asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof IBtPhoneDialPhoneWindowChangedAIDL)) {
                return (IBtPhoneDialPhoneWindowChangedAIDL) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IBtPhoneDialPhoneWindowChangedAIDL getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IBtPhoneDialPhoneWindowChangedAIDL iBtPhoneDialPhoneWindowChangedAIDL) {
            if (Proxy.sDefaultImpl != null || iBtPhoneDialPhoneWindowChangedAIDL == null) {
                return false;
            }
            Proxy.sDefaultImpl = iBtPhoneDialPhoneWindowChangedAIDL;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i == 1) {
                parcel.enforceInterface(DESCRIPTOR);
                onDialPhoneWindowChanged(parcel.readInt() != 0);
                parcel2.writeNoException();
                return true;
            } else if (i != 2) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString(DESCRIPTOR);
                return true;
            } else {
                parcel.enforceInterface(DESCRIPTOR);
                onConnectDeviceWindowChanged(parcel.readInt() != 0);
                parcel2.writeNoException();
                return true;
            }
        }
    }

    void onConnectDeviceWindowChanged(boolean z) throws RemoteException;

    void onDialPhoneWindowChanged(boolean z) throws RemoteException;
}
