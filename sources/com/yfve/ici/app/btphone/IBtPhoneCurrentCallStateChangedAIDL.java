package com.yfve.ici.app.btphone;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface IBtPhoneCurrentCallStateChangedAIDL extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IBtPhoneCurrentCallStateChangedAIDL {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneCurrentCallStateChangedAIDL
        public void onCurrentCallStateChanged(int i) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IBtPhoneCurrentCallStateChangedAIDL {
        public static final String DESCRIPTOR = "com.yfve.ici.app.btphone.IBtPhoneCurrentCallStateChangedAIDL";
        public static final int TRANSACTION_onCurrentCallStateChanged = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements IBtPhoneCurrentCallStateChangedAIDL {
            public static IBtPhoneCurrentCallStateChangedAIDL sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneCurrentCallStateChangedAIDL
            public void onCurrentCallStateChanged(int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onCurrentCallStateChanged(i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static IBtPhoneCurrentCallStateChangedAIDL asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof IBtPhoneCurrentCallStateChangedAIDL)) {
                return (IBtPhoneCurrentCallStateChangedAIDL) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IBtPhoneCurrentCallStateChangedAIDL getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IBtPhoneCurrentCallStateChangedAIDL iBtPhoneCurrentCallStateChangedAIDL) {
            if (Proxy.sDefaultImpl != null || iBtPhoneCurrentCallStateChangedAIDL == null) {
                return false;
            }
            Proxy.sDefaultImpl = iBtPhoneCurrentCallStateChangedAIDL;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString(DESCRIPTOR);
                return true;
            }
            parcel.enforceInterface(DESCRIPTOR);
            onCurrentCallStateChanged(parcel.readInt());
            parcel2.writeNoException();
            return true;
        }
    }

    void onCurrentCallStateChanged(int i) throws RemoteException;
}
