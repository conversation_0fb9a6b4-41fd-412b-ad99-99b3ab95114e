package com.yfve.ici.app.btphone;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface IBtPhoneActiveCallScreenChangedAIDL extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IBtPhoneActiveCallScreenChangedAIDL {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneActiveCallScreenChangedAIDL
        public void onActiveCallScreenChanged(boolean z) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IBtPhoneActiveCallScreenChangedAIDL {
        public static final String DESCRIPTOR = "com.yfve.ici.app.btphone.IBtPhoneActiveCallScreenChangedAIDL";
        public static final int TRANSACTION_onActiveCallScreenChanged = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements IBtPhoneActiveCallScreenChangedAIDL {
            public static IBtPhoneActiveCallScreenChangedAIDL sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            @Override // com.yfve.ici.app.btphone.IBtPhoneActiveCallScreenChangedAIDL
            public void onActiveCallScreenChanged(boolean z) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeInt(z ? 1 : 0);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onActiveCallScreenChanged(z);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static IBtPhoneActiveCallScreenChangedAIDL asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof IBtPhoneActiveCallScreenChangedAIDL)) {
                return (IBtPhoneActiveCallScreenChangedAIDL) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IBtPhoneActiveCallScreenChangedAIDL getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IBtPhoneActiveCallScreenChangedAIDL iBtPhoneActiveCallScreenChangedAIDL) {
            if (Proxy.sDefaultImpl != null || iBtPhoneActiveCallScreenChangedAIDL == null) {
                return false;
            }
            Proxy.sDefaultImpl = iBtPhoneActiveCallScreenChangedAIDL;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString(DESCRIPTOR);
                return true;
            }
            parcel.enforceInterface(DESCRIPTOR);
            onActiveCallScreenChanged(parcel.readInt() != 0);
            parcel2.writeNoException();
            return true;
        }
    }

    void onActiveCallScreenChanged(boolean z) throws RemoteException;
}
