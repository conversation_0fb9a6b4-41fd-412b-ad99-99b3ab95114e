package com.yfve.ici.app.carplay;

import android.os.Parcel;
import android.os.Parcelable;
import b.a.b.a.a;

/* loaded from: classes.dex */
public class CPDeviceInfo implements Parcelable {
    public static final int CONNECT_TYPE_BT_CLIENT = 3;
    public static final int CONNECT_TYPE_BT_SERVER = 2;
    public static final int CONNECT_TYPE_CARPLAY_SESSION = 4;
    public static final int CONNECT_TYPE_NONE = 0;
    public static final int CONNECT_TYPE_USB_HOST = 1;
    public static final Parcelable.Creator<CPDeviceInfo> CREATOR = new Parcelable.Creator<CPDeviceInfo>() { // from class: com.yfve.ici.app.carplay.CPDeviceInfo.1
        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public CPDeviceInfo createFromParcel(Parcel parcel) {
            return new CPDeviceInfo(parcel);
        }

        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public CPDeviceInfo[] newArray(int i) {
            return new CPDeviceInfo[i];
        }
    };
    public static final String DEFAULT_INFO = "UNKNOWN";
    public static final int DEFAULT_VALUE = 0;
    public static final int UPDATE_ALL = -1;
    public static final int UPDATE_BATTERY_LEVEL = 32;
    public static final int UPDATE_BT_MAC_ADDRESS = 1;
    public static final int UPDATE_CONNECT_TYPE = 8;
    public static final int UPDATE_DEVICE_NAME = 4;
    public static final int UPDATE_NONE = 0;
    public static final int UPDATE_SIGNAL_STRENGTH = 16;
    public static final int UPDATE_USB_SERIAL_NUMBER = 2;
    public static final int UPDATE_WLAN_ADDRESS = 64;
    public int batteryLevel;
    public String btMacAddress;
    public int connectType;
    public String deviceName;
    public int signalStrength;
    public int updateFlag;
    public String usbSerialNumber;
    public String wlanAddress;

    public CPDeviceInfo() {
        this.btMacAddress = "UNKNOWN";
        this.wlanAddress = "UNKNOWN";
        this.usbSerialNumber = "UNKNOWN";
        this.deviceName = "UNKNOWN";
        this.connectType = 0;
        this.signalStrength = 0;
        this.batteryLevel = 0;
        this.updateFlag = 0;
    }

    @Override // android.os.Parcelable
    public int describeContents() {
        return 0;
    }

    public int getBatteryLevel() {
        return this.batteryLevel;
    }

    public String getBtMacAddress() {
        return this.btMacAddress;
    }

    public int getConnectType() {
        return this.connectType;
    }

    public String getDeviceName() {
        return this.deviceName;
    }

    public int getSignalStrength() {
        return this.signalStrength;
    }

    public int getUpdateFlag() {
        return this.updateFlag;
    }

    public String getUsbSerialNumber() {
        return this.usbSerialNumber;
    }

    public String getWlanAddress() {
        return this.wlanAddress;
    }

    public boolean isBatteryLevelUpdate() {
        return (this.updateFlag & 32) != 0;
    }

    public boolean isBtMacAddressUpdate() {
        return (this.updateFlag & 1) != 0;
    }

    public boolean isConnectTypeUpdate() {
        return (this.updateFlag & 8) != 0;
    }

    public boolean isDeviceNameUpdate() {
        return (this.updateFlag & 4) != 0;
    }

    public boolean isSignalStrengthUpdate() {
        return (this.updateFlag & 16) != 0;
    }

    public boolean isUsbSerialNumberUpdate() {
        return (this.updateFlag & 2) != 0;
    }

    public boolean isWlanAddressUpdate() {
        return (this.updateFlag & 64) != 0;
    }

    public void setBatteryLevel(int i) {
        this.batteryLevel = i;
        setUpdateFlag(32);
    }

    public void setBtMacAddress(String str) {
        this.btMacAddress = str;
        setUpdateFlag(1);
    }

    public void setConnectType(int i) {
        this.connectType = i;
        setUpdateFlag(8);
    }

    public void setDeviceName(String str) {
        this.deviceName = str;
        setUpdateFlag(4);
    }

    public void setSignalStrength(int i) {
        this.signalStrength = i;
        setUpdateFlag(16);
    }

    public void setUpdateFlag(int i) {
        this.updateFlag = i | this.updateFlag;
    }

    public void setUsbSerialNumber(String str) {
        this.usbSerialNumber = str;
        setUpdateFlag(2);
    }

    public void setWlanAddress(String str) {
        this.wlanAddress = str;
        setUpdateFlag(64);
    }

    public String toString() {
        StringBuilder e = a.e("CPDeviceInfo{btMacAddress=");
        e.append(this.btMacAddress);
        e.append(", wlanAddress=");
        e.append(this.wlanAddress);
        e.append(", usbSerialNumber=");
        e.append(this.usbSerialNumber);
        e.append(", deviceName=");
        e.append(this.deviceName);
        e.append(", connectType=");
        e.append(this.connectType);
        e.append(", signalStrength=");
        e.append(this.signalStrength);
        e.append(", batteryLevel=");
        e.append(this.batteryLevel);
        e.append(", updateFlag=");
        e.append(Integer.toHexString(this.updateFlag));
        e.append('}');
        return e.toString();
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeString(this.btMacAddress);
        parcel.writeString(this.wlanAddress);
        parcel.writeString(this.usbSerialNumber);
        parcel.writeString(this.deviceName);
        parcel.writeInt(this.connectType);
        parcel.writeInt(this.signalStrength);
        parcel.writeInt(this.batteryLevel);
        parcel.writeInt(this.updateFlag);
    }

    public CPDeviceInfo(Parcel parcel) {
        this.btMacAddress = parcel.readString();
        this.wlanAddress = parcel.readString();
        this.usbSerialNumber = parcel.readString();
        this.deviceName = parcel.readString();
        this.connectType = parcel.readInt();
        this.signalStrength = parcel.readInt();
        this.batteryLevel = parcel.readInt();
        this.updateFlag = parcel.readInt();
    }
}
