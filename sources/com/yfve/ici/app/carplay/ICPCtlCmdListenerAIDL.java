package com.yfve.ici.app.carplay;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface ICPCtlCmdListenerAIDL extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements ICPCtlCmdListenerAIDL {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.carplay.ICPCtlCmdListenerAIDL
        public void onControl(int i, byte[] bArr) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements ICPCtlCmdListenerAIDL {
        public static final String DESCRIPTOR = "com.yfve.ici.app.carplay.ICPCtlCmdListenerAIDL";
        public static final int TRANSACTION_onControl = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements ICPCtlCmdListenerAIDL {
            public static ICPCtlCmdListenerAIDL sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            @Override // com.yfve.ici.app.carplay.ICPCtlCmdListenerAIDL
            public void onControl(int i, byte[] bArr) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeInt(i);
                    obtain.writeByteArray(bArr);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onControl(i, bArr);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static ICPCtlCmdListenerAIDL asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof ICPCtlCmdListenerAIDL)) {
                return (ICPCtlCmdListenerAIDL) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static ICPCtlCmdListenerAIDL getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(ICPCtlCmdListenerAIDL iCPCtlCmdListenerAIDL) {
            if (Proxy.sDefaultImpl != null || iCPCtlCmdListenerAIDL == null) {
                return false;
            }
            Proxy.sDefaultImpl = iCPCtlCmdListenerAIDL;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString(DESCRIPTOR);
                return true;
            }
            parcel.enforceInterface(DESCRIPTOR);
            onControl(parcel.readInt(), parcel.createByteArray());
            parcel2.writeNoException();
            return true;
        }
    }

    void onControl(int i, byte[] bArr) throws RemoteException;
}
