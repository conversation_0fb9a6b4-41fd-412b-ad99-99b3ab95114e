package com.yfve.ici.app.carplay;

import android.bluetooth.BluetoothDevice;
import android.hardware.usb.UsbDevice;
import android.os.RemoteException;
import android.util.Log;
import b.a.b.a.a;
import com.yfve.ici.app.carplay.ICPConnectStatusListenerAIDL;
import com.yfve.ici.app.carplay.ICPCtlCmdListenerAIDL;
import com.yfve.ici.app.carplay.ICPDeviceInfoListenerAIDL;
import com.yfve.ici.app.carplay.ICPExitListenerAIDL;
import com.yfve.ici.app.carplay.ICPOOBPairingInfoListenerAIDL;
import com.yfve.ici.app.carplay.ICPPhoneInfoListenerAIDL;
import com.yfve.ici.app.carplay.IMediaListenerAIDL;
import com.yfve.ici.app.carplay.ISiriStatusListenerAIDL;
import com.yfve.ici.app.carplay.IUICtlListenerAIDL;
import com.yfve.ici.app.carplay.IWirelessCPBTServerConnectListenerAIDL;
import com.yfve.ici.service.base.BaseProxy;
import com.yfve.ici.service.contanst.ServiceConstant;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/* loaded from: classes.dex */
public class CarPlayProxy extends BaseProxy<ICarPlayProxy> {
    public static final int CP_CTL_CMD_DISABLE_BLUETOOTH = 2;
    public static final int CP_STATUS_CONNECTED = 2;
    public static final int CP_STATUS_CONNECTING = 1;
    public static final int CP_STATUS_CONNECT_FAILED = 3;
    public static final int CP_STATUS_CONNECT_TIMEOUT = 4;
    public static final int CP_STATUS_DISCONNECTED = 5;
    public static final int CP_STATUS_NONE = 0;
    public static final int CP_STATUS_USER_REFUSE = 6;
    public static final String CP_URL_DEFAULT = "default";
    public static final String CP_URL_MAPS = "maps:";
    public static final String CP_URL_MESSAGES = "messages:";
    public static final String CP_URL_MOBILEPHONE = "mobilephone:";
    public static final String CP_URL_MUSIC = "music:";
    public static final String CP_URL_NOWPLAYING = "nowplaying:";
    public static final String CP_URL_TEL = "tel:";
    public static final String ID3_INFO_DEF_STR_VALUE = "unkown";
    public static final String ID3_INFO_KEY_ABLUM = "ablum";
    public static final String ID3_INFO_KEY_ARTIST = "artist";
    public static final String ID3_INFO_KEY_ARTWORK = "artwork";
    public static final String ID3_INFO_KEY_DURATION = "duration";
    public static final String ID3_INFO_KEY_TITLE = "title";
    public static final int MEDIA_BUTTON_DOWN = 1;
    public static final int MEDIA_BUTTON_DOWN_AND_UP = 0;
    public static final int MEDIA_BUTTON_STATUS_MASK = 3;
    public static final int MEDIA_BUTTON_UP = 2;
    public static final int MEDIA_CTL_COMMAND_MASK = 1073741823;
    public static final int MEDIA_CTL_NEXT = 4;
    public static final int MEDIA_CTL_PAUSE = 2;
    public static final int MEDIA_CTL_PLAY = 1;
    public static final int MEDIA_CTL_PLAY_PAUSE = 64;
    public static final int MEDIA_CTL_PREV = 8;
    public static final int MEDIA_CTL_REPEAT = 32;
    public static final int MEDIA_CTL_SHUFFLE = 16;
    public static final int MEDIA_PLAY_STATE_PAUSED = 2;
    public static final int MEDIA_PLAY_STATE_PLAYING = 1;
    public static final int MEDIA_PLAY_STATE_SEEKBACKWARD = 4;
    public static final int MEDIA_PLAY_STATE_SEEKFORWARD = 3;
    public static final int MEDIA_PLAY_STATE_STOPED = 0;
    public static final int MEDIA_REPEAT_MODE_ALL = 2;
    public static final int MEDIA_REPEAT_MODE_OFF = 0;
    public static final int MEDIA_REPEAT_MODE_ONE = 1;
    public static final int MEDIA_SHUFFLE_MODE_ALBUMS = 2;
    public static final int MEDIA_SHUFFLE_MODE_OFF = 0;
    public static final int MEDIA_SHUFFLE_MODE_SONGS = 1;
    public static final String TAG = "CarPlayProxy";
    public static CarPlayProxy mInstance;
    public ICPConnectStatusListenerAIDL mCPConnectStatusListenerAIDL;
    public ICPCtlCmdListenerAIDL mCPCtlCmdListenerAIDL;
    public ICPOOBPairingInfoListenerAIDL mCPOOBPairingInfoListenerAIDL;
    public ICPExitListenerAIDL mCarPlayExitListenerAIDL;
    public ICPDeviceInfoListenerAIDL mDeviceInfoListenerAIDL;
    public IMediaListenerAIDL mMediaListenerAIDL;
    public ICPPhoneInfoListenerAIDL mPhoneInfoListenerAIDL;
    public ISiriStatusListenerAIDL mSiriStatusListenerAIDL;
    public IUICtlListenerAIDL mUICtlListenerAIDL;
    public IWirelessCPBTServerConnectListenerAIDL mWirelessCPBTServerConnectListenerAIDL;
    public List<ICPPhoneInfoCallback> mPhoneInfoCallbackList = new CopyOnWriteArrayList();
    public List<ICPExitCallback> mCarPlayExitCallbackList = new CopyOnWriteArrayList();
    public List<ICPConnectStatusCallback> mCPConnectStatusCallbackList = new CopyOnWriteArrayList();
    public List<ICPDeviceInfoCallback> mDeviceInfoCallbackList = new CopyOnWriteArrayList();
    public List<ISiriStatusCallback> mSiriStatusCallbackList = new CopyOnWriteArrayList();
    public List<IMediaCallback> mMediaCallbackList = new CopyOnWriteArrayList();
    public List<ICPCtlCmdCallback> mCPCtlCmdCallbackList = new CopyOnWriteArrayList();
    public List<ICPOOBPairingInfoCallback> mCPOOBPairingInfoCallbackList = new CopyOnWriteArrayList();
    public List<IWirelessCPBTServerConnectCallback> mWirelessCPBTServerConnectCallbackList = new CopyOnWriteArrayList();
    public List<IUICtlCallback> mUICtlCallbackList = new CopyOnWriteArrayList();

    public static CarPlayProxy getInstance() {
        if (mInstance == null) {
            synchronized (CarPlayProxy.class) {
                if (mInstance == null) {
                    mInstance = new CarPlayProxy();
                }
            }
        }
        return mInstance;
    }

    public int cancelConnectWirelessCPServer() {
        Log.i(TAG, "cancelConnectWirelessCPServer in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "disconnect~~mInterface is null");
                return -2147483646;
            }
            ((ICarPlayProxy) this.mInterface).cancelConnectWirelessCPServer();
            return Integer.MIN_VALUE;
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public boolean confirmConnectWirelessCPServer(boolean z) {
        Log.i(TAG, "confirmConnectWirelessCPServer in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "connect~~mInterface is null");
                return false;
            }
            return ((ICarPlayProxy) this.mInterface).confirmConnectWirelessCPServer(z);
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return false;
        }
    }

    public boolean connect(UsbDevice usbDevice, int i, boolean z) {
        Log.i(TAG, "connect wired carplay in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "connect~~mInterface is null");
                return false;
            }
            return ((ICarPlayProxy) this.mInterface).connectWiredCP(usbDevice, i, z);
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return false;
        }
    }

    public int disconnectCP() {
        Log.i(TAG, "disconnectCP in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "disconnectCP~~mInterface is null");
                return -2147483646;
            }
            ((ICarPlayProxy) this.mInterface).disconnectCP();
            return Integer.MIN_VALUE;
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public String getAblum() {
        Log.i(TAG, "getAblum in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getAblum~~mInterface is null");
                return null;
            }
            return ((ICarPlayProxy) this.mInterface).getAblum();
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return null;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return null;
        }
    }

    public String getArtist() {
        Log.i(TAG, "getArtist in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getArtist~~mInterface is null");
                return null;
            }
            return ((ICarPlayProxy) this.mInterface).getArtist();
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return null;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return null;
        }
    }

    public String getArtwork() {
        Log.i(TAG, "getArtwork in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getArtwork~~mInterface is null");
                return null;
            }
            return ((ICarPlayProxy) this.mInterface).getArtwork();
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return null;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return null;
        }
    }

    public int getCPConnectStatus() {
        Log.i(TAG, "getCPConnectStatus in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getCPConnectStatus~~mInterface is null");
                return -2147483646;
            }
            return ((ICarPlayProxy) this.mInterface).getCPConnectStatus();
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public long getCurrentPosition() {
        Log.i(TAG, "getCurrentPosition in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getCurrentPosition~~mInterface is null");
                return -2147483646L;
            }
            return ((ICarPlayProxy) this.mInterface).getCurrentPosition();
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113L;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113L;
        }
    }

    public CPDeviceInfo getDeviceInfo() {
        Log.i(TAG, "getDeviceInfo in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getDeviceInfo~~mInterface is null");
                return null;
            }
            return ((ICarPlayProxy) this.mInterface).getDeviceInfo();
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return null;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return null;
        }
    }

    public long getDuration() {
        Log.i(TAG, "getDuration in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getDuration~~mInterface is null");
                return -2147483646L;
            }
            return ((ICarPlayProxy) this.mInterface).getDuration();
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113L;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113L;
        }
    }

    public String getID3Info() {
        Log.i(TAG, "getID3Info in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getID3Info~~mInterface is null");
                return null;
            }
            return ((ICarPlayProxy) this.mInterface).getID3Info();
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return null;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return null;
        }
    }

    public CPPhoneInfo getPhoneInfo() {
        Log.i(TAG, "getPhoneInfo in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getPhoneInfo~~mInterface is null");
                return null;
            }
            return ((ICarPlayProxy) this.mInterface).getPhoneInfo();
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return null;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return null;
        }
    }

    public int getPlayState() {
        Log.i(TAG, "getPlayState in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getPlayState~~mInterface is null");
                return -2147483646;
            }
            return ((ICarPlayProxy) this.mInterface).getPlayState();
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int getRepeatMode() {
        Log.i(TAG, "getRepeatMode in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getRepeatMode~~mInterface is null");
                return -2147483646;
            }
            return ((ICarPlayProxy) this.mInterface).getRepeatMode();
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public String getServiceName() {
        return ServiceConstant.CARPLAY_BINDER_NAME;
    }

    public int getShuffleMode() {
        Log.i(TAG, "getShuffleMode in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getShuffleMode~~mInterface is null");
                return -2147483646;
            }
            return ((ICarPlayProxy) this.mInterface).getShuffleMode();
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public String getTitle() {
        Log.i(TAG, "getTitle in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getTitle~~mInterface is null");
                return null;
            }
            return ((ICarPlayProxy) this.mInterface).getTitle();
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return null;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return null;
        }
    }

    public boolean isCarPlaySessionActived() {
        Log.i(TAG, "isCarPlaySessionActived in...... ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "isCarPlaySessionActived~~mInterface is null");
                return false;
            }
            return ((ICarPlayProxy) this.mInterface).isCarPlaySessionActived();
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return false;
        }
    }

    public boolean isSiriActived() {
        Log.i(TAG, "isSiriActived in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "isSiriActived~~mInterface is null");
                return false;
            }
            return ((ICarPlayProxy) this.mInterface).isSiriActived();
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return false;
        }
    }

    public int mediaPlayControl(int i, int i2) {
        Log.i(TAG, "mediaPlayControl in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "mediaPlayControl~~mInterface is null");
                return -2147483646;
            }
            ((ICarPlayProxy) this.mInterface).mediaPlayControl(((i & 3) << 30) | (i2 & MEDIA_CTL_COMMAND_MASK));
            return Integer.MIN_VALUE;
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int next() {
        Log.i(TAG, "next in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "next~~mInterface is null");
                return -2147483646;
            }
            ((ICarPlayProxy) this.mInterface).mediaPlayControl(4);
            return Integer.MIN_VALUE;
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int notifyUIHided(String str, int i) {
        Log.i(TAG, "notifyUIHided in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "notifyUIHided~~mInterface is null");
                return -2147483646;
            }
            ((ICarPlayProxy) this.mInterface).notifyUIHided(str, i);
            return Integer.MIN_VALUE;
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int notifyUIShowForVR(String str, int i) {
        Log.i(TAG, "notifyUIShowForVR in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "notifyUIShowForVR~~mInterface is null");
                return -2147483646;
            }
            ((ICarPlayProxy) this.mInterface).notifyUIShowForVR(str, i);
            return Integer.MIN_VALUE;
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int notifyUIShowed(String str, int i) {
        Log.i(TAG, "notifyUIShowed in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "notifyUIShowed~~mInterface is null");
                return -2147483646;
            }
            ((ICarPlayProxy) this.mInterface).notifyUIShowForVR(str, i);
            return Integer.MIN_VALUE;
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public void onServiceConnectStatusChanged(boolean z) {
        String str = TAG;
        Log.d(str, "notifyServiceConnStatus status: " + z);
        if (z) {
            return;
        }
        List<ICPPhoneInfoCallback> list = this.mPhoneInfoCallbackList;
        if (list != null) {
            list.clear();
        }
        List<ICPExitCallback> list2 = this.mCarPlayExitCallbackList;
        if (list2 != null) {
            list2.clear();
        }
        List<ICPConnectStatusCallback> list3 = this.mCPConnectStatusCallbackList;
        if (list3 != null) {
            list3.clear();
        }
        List<ISiriStatusCallback> list4 = this.mSiriStatusCallbackList;
        if (list4 != null) {
            list4.clear();
        }
        List<ICPDeviceInfoCallback> list5 = this.mDeviceInfoCallbackList;
        if (list5 != null) {
            list5.clear();
        }
        List<IMediaCallback> list6 = this.mMediaCallbackList;
        if (list6 != null) {
            list6.clear();
        }
        List<ICPCtlCmdCallback> list7 = this.mCPCtlCmdCallbackList;
        if (list7 != null) {
            list7.clear();
        }
        List<ICPOOBPairingInfoCallback> list8 = this.mCPOOBPairingInfoCallbackList;
        if (list8 != null) {
            list8.clear();
        }
        List<IWirelessCPBTServerConnectCallback> list9 = this.mWirelessCPBTServerConnectCallbackList;
        if (list9 != null) {
            list9.clear();
        }
        List<IUICtlCallback> list10 = this.mUICtlCallbackList;
        if (list10 != null) {
            list10.clear();
        }
    }

    public int pause() {
        Log.i(TAG, "pause in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "pause~~mInterface is null");
                return -2147483646;
            }
            ((ICarPlayProxy) this.mInterface).mediaPlayControl(2);
            return Integer.MIN_VALUE;
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int play() {
        Log.i(TAG, "play in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "play~~mInterface is null");
                return -2147483646;
            }
            ((ICarPlayProxy) this.mInterface).mediaPlayControl(1);
            return Integer.MIN_VALUE;
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int playPause() {
        Log.i(TAG, "playPause in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "playPause~~mInterface is null");
                return -2147483646;
            }
            ((ICarPlayProxy) this.mInterface).mediaPlayControl(64);
            return Integer.MIN_VALUE;
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int prev() {
        Log.i(TAG, "prev in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "prev~~mInterface is null");
                return -2147483646;
            }
            ((ICarPlayProxy) this.mInterface).mediaPlayControl(8);
            return Integer.MIN_VALUE;
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int registerCPConnectStatusCallback(ICPConnectStatusCallback iCPConnectStatusCallback) {
        Log.i(TAG, "registerCPConnectStatusCallback in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerCPConnectStatusCallback~~mInterface is null");
                return -2147483646;
            } else if (iCPConnectStatusCallback == null || this.mCPConnectStatusCallbackList.contains(iCPConnectStatusCallback)) {
                return -2147418113;
            } else {
                this.mCPConnectStatusCallbackList.add(iCPConnectStatusCallback);
                if (this.mCPConnectStatusCallbackList.size() == 1) {
                    ICPConnectStatusListenerAIDL.Stub stub = new ICPConnectStatusListenerAIDL.Stub() { // from class: com.yfve.ici.app.carplay.CarPlayProxy.3
                        @Override // com.yfve.ici.app.carplay.ICPConnectStatusListenerAIDL
                        public void onConnectStatusChanged(int i) {
                            Log.d(CarPlayProxy.TAG, "onConnectStatusChanged is called. ");
                            for (ICPConnectStatusCallback iCPConnectStatusCallback2 : CarPlayProxy.this.mCPConnectStatusCallbackList) {
                                iCPConnectStatusCallback2.onConnectStatusChanged(i);
                            }
                        }
                    };
                    this.mCPConnectStatusListenerAIDL = stub;
                    ((ICarPlayProxy) this.mInterface).registerCPConnectStatusListener(stub);
                    return Integer.MIN_VALUE;
                }
                return Integer.MIN_VALUE;
            }
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int registerCPCtlCmdCallback(ICPCtlCmdCallback iCPCtlCmdCallback) {
        Log.i(TAG, "registerCPCtlCmdCallback in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerCPCtlCmdCallback~~mInterface is null");
                return -2147483646;
            } else if (iCPCtlCmdCallback == null || this.mCPCtlCmdCallbackList.contains(iCPCtlCmdCallback)) {
                return -2147418113;
            } else {
                this.mCPCtlCmdCallbackList.add(iCPCtlCmdCallback);
                if (this.mCPCtlCmdCallbackList.size() == 1) {
                    ICPCtlCmdListenerAIDL.Stub stub = new ICPCtlCmdListenerAIDL.Stub() { // from class: com.yfve.ici.app.carplay.CarPlayProxy.7
                        @Override // com.yfve.ici.app.carplay.ICPCtlCmdListenerAIDL
                        public void onControl(int i, byte[] bArr) {
                            String str = CarPlayProxy.TAG;
                            Log.d(str, "onControl is called. cmd:" + i);
                            for (ICPCtlCmdCallback iCPCtlCmdCallback2 : CarPlayProxy.this.mCPCtlCmdCallbackList) {
                                iCPCtlCmdCallback2.onControl(i, bArr);
                            }
                        }
                    };
                    this.mCPCtlCmdListenerAIDL = stub;
                    ((ICarPlayProxy) this.mInterface).registerCPCtlCmdListenerListener(stub);
                    return Integer.MIN_VALUE;
                }
                return Integer.MIN_VALUE;
            }
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int registerCPOOBPairingInfoCallback(ICPOOBPairingInfoCallback iCPOOBPairingInfoCallback) {
        Log.i(TAG, "registerCPOOBPairingInfoCallback in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerCPOOBPairingInfoCallback~~mInterface is null");
                return -2147483646;
            } else if (iCPOOBPairingInfoCallback == null || this.mCPOOBPairingInfoCallbackList.contains(iCPOOBPairingInfoCallback)) {
                return -2147418113;
            } else {
                this.mCPOOBPairingInfoCallbackList.add(iCPOOBPairingInfoCallback);
                if (this.mCPOOBPairingInfoCallbackList.size() == 1) {
                    ICPOOBPairingInfoListenerAIDL.Stub stub = new ICPOOBPairingInfoListenerAIDL.Stub() { // from class: com.yfve.ici.app.carplay.CarPlayProxy.8
                        @Override // com.yfve.ici.app.carplay.ICPOOBPairingInfoListenerAIDL
                        public void onCarPlayOOBPairingInfo(String str) throws RemoteException {
                            String str2 = CarPlayProxy.TAG;
                            Log.d(str2, "onCarPlayOOBPairingInfo is called. macAddress:" + str);
                            for (ICPOOBPairingInfoCallback iCPOOBPairingInfoCallback2 : CarPlayProxy.this.mCPOOBPairingInfoCallbackList) {
                                iCPOOBPairingInfoCallback2.onCarPlayOOBPairingInfo(str);
                            }
                        }
                    };
                    this.mCPOOBPairingInfoListenerAIDL = stub;
                    ((ICarPlayProxy) this.mInterface).registerCPOOBPairingInfoListener(stub);
                    return Integer.MIN_VALUE;
                }
                return Integer.MIN_VALUE;
            }
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int registerCarPlayExitCallback(ICPExitCallback iCPExitCallback) {
        Log.i(TAG, "registerCarPlayExitCallback in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerCarPlayExitCallback~~mInterface is null");
                return -2147483646;
            } else if (iCPExitCallback == null || this.mCarPlayExitCallbackList.contains(iCPExitCallback)) {
                return -2147418113;
            } else {
                this.mCarPlayExitCallbackList.add(iCPExitCallback);
                if (this.mCarPlayExitCallbackList.size() == 1) {
                    ICPExitListenerAIDL.Stub stub = new ICPExitListenerAIDL.Stub() { // from class: com.yfve.ici.app.carplay.CarPlayProxy.2
                        @Override // com.yfve.ici.app.carplay.ICPExitListenerAIDL
                        public void onExit() {
                            Log.d(CarPlayProxy.TAG, "onExit is called. ");
                            for (ICPExitCallback iCPExitCallback2 : CarPlayProxy.this.mCarPlayExitCallbackList) {
                                iCPExitCallback2.onExit();
                            }
                        }
                    };
                    this.mCarPlayExitListenerAIDL = stub;
                    ((ICarPlayProxy) this.mInterface).registerCPExitListener(stub);
                    return Integer.MIN_VALUE;
                }
                return Integer.MIN_VALUE;
            }
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int registerDeviceInfoCallback(ICPDeviceInfoCallback iCPDeviceInfoCallback) {
        Log.i(TAG, "registerDeviceInfoCallback in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerDeviceInfoCallback~~mInterface is null");
                return -2147483646;
            } else if (iCPDeviceInfoCallback == null || this.mDeviceInfoCallbackList.contains(iCPDeviceInfoCallback)) {
                return -2147418113;
            } else {
                this.mDeviceInfoCallbackList.add(iCPDeviceInfoCallback);
                if (this.mDeviceInfoCallbackList.size() == 1) {
                    ICPDeviceInfoListenerAIDL.Stub stub = new ICPDeviceInfoListenerAIDL.Stub() { // from class: com.yfve.ici.app.carplay.CarPlayProxy.4
                        @Override // com.yfve.ici.app.carplay.ICPDeviceInfoListenerAIDL
                        public void onDeviceInfoChanged(CPDeviceInfo cPDeviceInfo) {
                            String str = CarPlayProxy.TAG;
                            StringBuilder e = a.e("onDeviceInfoChanged is called. info:");
                            e.append(cPDeviceInfo.toString());
                            Log.d(str, e.toString());
                            for (ICPDeviceInfoCallback iCPDeviceInfoCallback2 : CarPlayProxy.this.mDeviceInfoCallbackList) {
                                iCPDeviceInfoCallback2.onDeviceInfoChanged(cPDeviceInfo);
                            }
                        }
                    };
                    this.mDeviceInfoListenerAIDL = stub;
                    ((ICarPlayProxy) this.mInterface).registerCPDeviceInfoListener(stub);
                    return Integer.MIN_VALUE;
                }
                return Integer.MIN_VALUE;
            }
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int registerMediaCallback(IMediaCallback iMediaCallback) {
        Log.i(TAG, "registerMediaCallback in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerMediaCallback~~mInterface is null");
                return -2147483646;
            } else if (iMediaCallback == null || this.mMediaCallbackList.contains(iMediaCallback)) {
                return -2147418113;
            } else {
                this.mMediaCallbackList.add(iMediaCallback);
                if (this.mMediaCallbackList.size() == 1) {
                    IMediaListenerAIDL.Stub stub = new IMediaListenerAIDL.Stub() { // from class: com.yfve.ici.app.carplay.CarPlayProxy.6
                        @Override // com.yfve.ici.app.carplay.IMediaListenerAIDL
                        public void onCurPositionChanged(long j) {
                            for (IMediaCallback iMediaCallback2 : CarPlayProxy.this.mMediaCallbackList) {
                                iMediaCallback2.onCurPositionChanged(j);
                            }
                        }

                        @Override // com.yfve.ici.app.carplay.IMediaListenerAIDL
                        public void onMetadataChanged(String str) {
                            for (IMediaCallback iMediaCallback2 : CarPlayProxy.this.mMediaCallbackList) {
                                iMediaCallback2.onMetadataChanged(str);
                            }
                        }

                        @Override // com.yfve.ici.app.carplay.IMediaListenerAIDL
                        public void onPlayStateChanged(int i) {
                            for (IMediaCallback iMediaCallback2 : CarPlayProxy.this.mMediaCallbackList) {
                                iMediaCallback2.onPlayStateChanged(i);
                            }
                        }

                        @Override // com.yfve.ici.app.carplay.IMediaListenerAIDL
                        public void onRepeatModeChanged(int i) {
                            for (IMediaCallback iMediaCallback2 : CarPlayProxy.this.mMediaCallbackList) {
                                iMediaCallback2.onRepeatModeChanged(i);
                            }
                        }

                        @Override // com.yfve.ici.app.carplay.IMediaListenerAIDL
                        public void onShuffleModeChanged(int i) {
                            for (IMediaCallback iMediaCallback2 : CarPlayProxy.this.mMediaCallbackList) {
                                iMediaCallback2.onShuffleModeChanged(i);
                            }
                        }
                    };
                    this.mMediaListenerAIDL = stub;
                    ((ICarPlayProxy) this.mInterface).registerMediaListener(stub);
                    return Integer.MIN_VALUE;
                }
                return Integer.MIN_VALUE;
            }
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int registerPhoneInfoCallback(ICPPhoneInfoCallback iCPPhoneInfoCallback) {
        Log.i(TAG, "registerPhoneInfoListener in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerPhoneInfoListener~~mInterface is null");
                return -2147483646;
            } else if (iCPPhoneInfoCallback == null || this.mPhoneInfoCallbackList.contains(iCPPhoneInfoCallback)) {
                return -2147418113;
            } else {
                this.mPhoneInfoCallbackList.add(iCPPhoneInfoCallback);
                if (this.mPhoneInfoCallbackList.size() == 1) {
                    ICPPhoneInfoListenerAIDL.Stub stub = new ICPPhoneInfoListenerAIDL.Stub() { // from class: com.yfve.ici.app.carplay.CarPlayProxy.1
                        @Override // com.yfve.ici.app.carplay.ICPPhoneInfoListenerAIDL
                        public void onPhoneInfoChanged(CPPhoneInfo cPPhoneInfo) {
                            String str = CarPlayProxy.TAG;
                            StringBuilder e = a.e("onPhoneInfoChanged is called. info:");
                            e.append(cPPhoneInfo.toString());
                            Log.d(str, e.toString());
                            for (ICPPhoneInfoCallback iCPPhoneInfoCallback2 : CarPlayProxy.this.mPhoneInfoCallbackList) {
                                iCPPhoneInfoCallback2.onPhoneInfoChanged(cPPhoneInfo);
                            }
                        }
                    };
                    this.mPhoneInfoListenerAIDL = stub;
                    ((ICarPlayProxy) this.mInterface).registerPhoneInfoListener(stub);
                    return Integer.MIN_VALUE;
                }
                return Integer.MIN_VALUE;
            }
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int registerSiriStatusCallback(ISiriStatusCallback iSiriStatusCallback) {
        Log.i(TAG, "registerSiriStatusCallback in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerSiriStatusCallback~~mInterface is null");
                return -2147483646;
            } else if (iSiriStatusCallback == null || this.mSiriStatusCallbackList.contains(iSiriStatusCallback)) {
                return -2147418113;
            } else {
                this.mSiriStatusCallbackList.add(iSiriStatusCallback);
                if (this.mSiriStatusCallbackList.size() == 1) {
                    ISiriStatusListenerAIDL.Stub stub = new ISiriStatusListenerAIDL.Stub() { // from class: com.yfve.ici.app.carplay.CarPlayProxy.5
                        @Override // com.yfve.ici.app.carplay.ISiriStatusListenerAIDL
                        public void onSiriStatusChanged(boolean z) {
                            String str = CarPlayProxy.TAG;
                            Log.d(str, "onSiriStatusChanged is called. isActived:" + z);
                            for (ISiriStatusCallback iSiriStatusCallback2 : CarPlayProxy.this.mSiriStatusCallbackList) {
                                iSiriStatusCallback2.onSiriStatusChanged(z);
                            }
                        }
                    };
                    this.mSiriStatusListenerAIDL = stub;
                    ((ICarPlayProxy) this.mInterface).registerSiriStatusListener(stub);
                    return Integer.MIN_VALUE;
                }
                return Integer.MIN_VALUE;
            }
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int registerUICtlCallback(IUICtlCallback iUICtlCallback) {
        Log.i(TAG, "registerUICtlCallback in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerUICtlCallback~~mInterface is null");
                return -2147483646;
            } else if (iUICtlCallback == null || this.mUICtlCallbackList.contains(iUICtlCallback)) {
                return -2147418113;
            } else {
                this.mUICtlCallbackList.add(iUICtlCallback);
                if (this.mUICtlCallbackList.size() == 1) {
                    IUICtlListenerAIDL.Stub stub = new IUICtlListenerAIDL.Stub() { // from class: com.yfve.ici.app.carplay.CarPlayProxy.10
                        @Override // com.yfve.ici.app.carplay.IUICtlListenerAIDL
                        public void onUICtlStatusChanged(String str, int i, boolean z) {
                            String str2 = CarPlayProxy.TAG;
                            Log.d(str2, "onUICtlStatusChanged. pkg:" + str + " wndType:" + i + " isShow:" + z);
                            for (IUICtlCallback iUICtlCallback2 : CarPlayProxy.this.mUICtlCallbackList) {
                                iUICtlCallback2.onUICtlStatusChanged(str, i, z);
                            }
                        }
                    };
                    this.mUICtlListenerAIDL = stub;
                    ((ICarPlayProxy) this.mInterface).registerUICtlListener(stub);
                    return Integer.MIN_VALUE;
                }
                return Integer.MIN_VALUE;
            }
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int registerWirelessCPBTServerConnectCallback(IWirelessCPBTServerConnectCallback iWirelessCPBTServerConnectCallback) {
        Log.i(TAG, "registerWirelessCPBTServerConnectCallback in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerWirelessCPBTServerConnectCallback~~mInterface is null");
                return -2147483646;
            } else if (iWirelessCPBTServerConnectCallback == null || this.mWirelessCPBTServerConnectCallbackList.contains(iWirelessCPBTServerConnectCallback)) {
                return -2147418113;
            } else {
                this.mWirelessCPBTServerConnectCallbackList.add(iWirelessCPBTServerConnectCallback);
                if (this.mWirelessCPBTServerConnectCallbackList.size() == 1) {
                    IWirelessCPBTServerConnectListenerAIDL.Stub stub = new IWirelessCPBTServerConnectListenerAIDL.Stub() { // from class: com.yfve.ici.app.carplay.CarPlayProxy.9
                        @Override // com.yfve.ici.app.carplay.IWirelessCPBTServerConnectListenerAIDL
                        public void onConnectStatusChanged(boolean z, String str) throws RemoteException {
                            String str2 = CarPlayProxy.TAG;
                            Log.d(str2, "onConnectStatusChanged is called. isConnected:" + z + " macAddress" + str);
                            for (IWirelessCPBTServerConnectCallback iWirelessCPBTServerConnectCallback2 : CarPlayProxy.this.mWirelessCPBTServerConnectCallbackList) {
                                iWirelessCPBTServerConnectCallback2.onConnectStatusChanged(z, str);
                            }
                        }
                    };
                    this.mWirelessCPBTServerConnectListenerAIDL = stub;
                    ((ICarPlayProxy) this.mInterface).registerWirelessCPBTServerConnectListener(stub);
                    return Integer.MIN_VALUE;
                }
                return Integer.MIN_VALUE;
            }
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int repeat() {
        Log.i(TAG, "repeat in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "repeat~~mInterface is null");
                return -2147483646;
            }
            ((ICarPlayProxy) this.mInterface).mediaPlayControl(32);
            return Integer.MIN_VALUE;
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int requestHideUI(String str, int i) {
        Log.i(TAG, "requestHideUI in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "requestHideUI~~mInterface is null");
                return -2147483646;
            }
            ((ICarPlayProxy) this.mInterface).requestHideUI(str, i);
            return Integer.MIN_VALUE;
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int requestShowUI(String str, int i) {
        Log.i(TAG, "requestShowUI in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "requestShowUI~~mInterface is null");
                return -2147483646;
            }
            ((ICarPlayProxy) this.mInterface).notifyUIShowForVR(str, i);
            return Integer.MIN_VALUE;
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public void setPlayingTime(int i) {
        Log.i(TAG, "setPlayingTime in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "setPlayingTime~~mInterface is null");
            }
            ((ICarPlayProxy) this.mInterface).setPlayingTime(i);
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public int shuffle() {
        Log.i(TAG, "shuffle in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "shuffle~~mInterface is null");
                return -2147483646;
            }
            ((ICarPlayProxy) this.mInterface).mediaPlayControl(16);
            return Integer.MIN_VALUE;
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int startCarPlayByUrl(String str) {
        Log.i(TAG, "startCarPlayByUrl in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "startCarPlayByUrl~~mInterface is null");
                return -2147483646;
            }
            ((ICarPlayProxy) this.mInterface).startCarPlayByUrl(str);
            return Integer.MIN_VALUE;
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int startCarPlayUI() {
        Log.i(TAG, "startCarPlayUI in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "startCarPlayUI~~mInterface is null");
                return -2147483646;
            }
            ((ICarPlayProxy) this.mInterface).startCarPlayUI();
            return Integer.MIN_VALUE;
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147418113;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int unregisterCPConnectStatusCallback(ICPConnectStatusCallback iCPConnectStatusCallback) {
        Log.i(TAG, "unregisterCPConnectStatusCallback in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unregisterCPConnectStatusCallback~~mInterface is null");
                return -2147483646;
            } else if (iCPConnectStatusCallback == null || !this.mCPConnectStatusCallbackList.contains(iCPConnectStatusCallback)) {
                return -2147418113;
            } else {
                this.mCPConnectStatusCallbackList.remove(iCPConnectStatusCallback);
                if (this.mCPConnectStatusCallbackList.size() == 0) {
                    ((ICarPlayProxy) this.mInterface).unregisterCPConnectStatusListener(this.mCPConnectStatusListenerAIDL);
                    this.mCPConnectStatusListenerAIDL = null;
                    return Integer.MIN_VALUE;
                }
                return Integer.MIN_VALUE;
            }
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int unregisterCPCtlCmdCallback(ICPCtlCmdCallback iCPCtlCmdCallback) {
        Log.i(TAG, "unregisterCPCtlCmdCallback in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unregisterCPCtlCmdCallback~~mInterface is null");
                return -2147483646;
            } else if (iCPCtlCmdCallback == null || !this.mCPCtlCmdCallbackList.contains(iCPCtlCmdCallback)) {
                return -2147418113;
            } else {
                this.mCPCtlCmdCallbackList.remove(iCPCtlCmdCallback);
                if (this.mCPCtlCmdCallbackList.size() == 0) {
                    ((ICarPlayProxy) this.mInterface).unregisterCPCtlCmdListenerListener(this.mCPCtlCmdListenerAIDL);
                    this.mCPCtlCmdListenerAIDL = null;
                    return Integer.MIN_VALUE;
                }
                return Integer.MIN_VALUE;
            }
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int unregisterCPOOBPairingInfoCallback(ICPOOBPairingInfoCallback iCPOOBPairingInfoCallback) {
        Log.i(TAG, "unregisterCPOOBPairingInfoCallback in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unregisterCPOOBPairingInfoCallback~~mInterface is null");
                return -2147483646;
            } else if (iCPOOBPairingInfoCallback == null || !this.mCPOOBPairingInfoCallbackList.contains(iCPOOBPairingInfoCallback)) {
                return -2147418113;
            } else {
                this.mCPOOBPairingInfoCallbackList.remove(iCPOOBPairingInfoCallback);
                if (this.mCPOOBPairingInfoCallbackList.size() == 0) {
                    ((ICarPlayProxy) this.mInterface).unregisterCPOOBPairingInfoListener(this.mCPOOBPairingInfoListenerAIDL);
                    this.mCPOOBPairingInfoListenerAIDL = null;
                    return Integer.MIN_VALUE;
                }
                return Integer.MIN_VALUE;
            }
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int unregisterCarPlayExitCallback(ICPExitCallback iCPExitCallback) {
        Log.i(TAG, "unregisterCarPlayExitCallback in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unregisterCarPlayExitCallback~~mInterface is null");
                return -2147483646;
            } else if (iCPExitCallback == null || !this.mCarPlayExitCallbackList.contains(iCPExitCallback)) {
                return -2147418113;
            } else {
                this.mCarPlayExitCallbackList.remove(iCPExitCallback);
                if (this.mCarPlayExitCallbackList.size() == 0) {
                    ((ICarPlayProxy) this.mInterface).unregisterCPExitListener(this.mCarPlayExitListenerAIDL);
                    this.mCarPlayExitListenerAIDL = null;
                    return Integer.MIN_VALUE;
                }
                return Integer.MIN_VALUE;
            }
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int unregisterDeviceInfoCallback(ICPDeviceInfoCallback iCPDeviceInfoCallback) {
        Log.i(TAG, "unregisterDeviceInfoCallback in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unregisterDeviceInfoCallback~~mInterface is null");
                return -2147483646;
            } else if (iCPDeviceInfoCallback == null || !this.mDeviceInfoCallbackList.contains(iCPDeviceInfoCallback)) {
                return -2147418113;
            } else {
                this.mDeviceInfoCallbackList.remove(iCPDeviceInfoCallback);
                if (this.mDeviceInfoCallbackList.size() == 0) {
                    ((ICarPlayProxy) this.mInterface).unregisterCPDeviceInfoListener(this.mDeviceInfoListenerAIDL);
                    this.mDeviceInfoListenerAIDL = null;
                    return Integer.MIN_VALUE;
                }
                return Integer.MIN_VALUE;
            }
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int unregisterMediaCallback(IMediaCallback iMediaCallback) {
        Log.i(TAG, "unregisterMediaCallback in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unregisterMediaCallback~~mInterface is null");
                return -2147483646;
            } else if (iMediaCallback == null || !this.mMediaCallbackList.contains(iMediaCallback)) {
                return -2147418113;
            } else {
                this.mMediaCallbackList.remove(iMediaCallback);
                if (this.mMediaCallbackList.size() == 0) {
                    ((ICarPlayProxy) this.mInterface).unregisterMediaListener(this.mMediaListenerAIDL);
                    this.mMediaListenerAIDL = null;
                    return Integer.MIN_VALUE;
                }
                return Integer.MIN_VALUE;
            }
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int unregisterPhoneInfoCallback(ICPPhoneInfoCallback iCPPhoneInfoCallback) {
        Log.i(TAG, "unregisterPhoneInfoCallback in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unregisterPhoneInfoCallback~~mInterface is null");
                return -2147483646;
            } else if (iCPPhoneInfoCallback == null || !this.mPhoneInfoCallbackList.contains(iCPPhoneInfoCallback)) {
                return -2147418113;
            } else {
                this.mPhoneInfoCallbackList.remove(iCPPhoneInfoCallback);
                if (this.mPhoneInfoCallbackList.size() == 0) {
                    ((ICarPlayProxy) this.mInterface).unregisterPhoneInfoListener(this.mPhoneInfoListenerAIDL);
                    this.mPhoneInfoListenerAIDL = null;
                    return Integer.MIN_VALUE;
                }
                return Integer.MIN_VALUE;
            }
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int unregisterSiriStatusCallback(ISiriStatusCallback iSiriStatusCallback) {
        Log.i(TAG, "unregisterSiriStatusCallback in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unregisterSiriStatusCallback~~mInterface is null");
                return -2147483646;
            } else if (iSiriStatusCallback == null || !this.mSiriStatusCallbackList.contains(iSiriStatusCallback)) {
                return -2147418113;
            } else {
                this.mSiriStatusCallbackList.remove(iSiriStatusCallback);
                if (this.mSiriStatusCallbackList.size() == 0) {
                    ((ICarPlayProxy) this.mInterface).unregisterSiriStatusListener(this.mSiriStatusListenerAIDL);
                    this.mSiriStatusListenerAIDL = null;
                    return Integer.MIN_VALUE;
                }
                return Integer.MIN_VALUE;
            }
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int unregisterUICtlCallback(IUICtlCallback iUICtlCallback) {
        Log.i(TAG, "unregisterUICtlCallback in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unregisterUICtlCallback~~mInterface is null");
                return -2147483646;
            } else if (iUICtlCallback == null || !this.mUICtlCallbackList.contains(iUICtlCallback)) {
                return -2147418113;
            } else {
                this.mUICtlCallbackList.remove(iUICtlCallback);
                if (this.mUICtlCallbackList.size() == 0) {
                    ((ICarPlayProxy) this.mInterface).unregisterUICtlListener(this.mUICtlListenerAIDL);
                    this.mUICtlListenerAIDL = null;
                    return Integer.MIN_VALUE;
                }
                return Integer.MIN_VALUE;
            }
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int unregisterWirelessCPBTServerConnectCallback(IWirelessCPBTServerConnectCallback iWirelessCPBTServerConnectCallback) {
        Log.i(TAG, "unregisterWirelessCPBTServerConnectCallback in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unregisterWirelessCPBTServerConnectCallback~~mInterface is null");
                return -2147483646;
            } else if (iWirelessCPBTServerConnectCallback == null || !this.mWirelessCPBTServerConnectCallbackList.contains(iWirelessCPBTServerConnectCallback)) {
                return -2147418113;
            } else {
                this.mWirelessCPBTServerConnectCallbackList.remove(iWirelessCPBTServerConnectCallback);
                if (this.mWirelessCPBTServerConnectCallbackList.size() == 0) {
                    ((ICarPlayProxy) this.mInterface).unregisterWirelessCPBTServerConnectListener(this.mWirelessCPBTServerConnectListenerAIDL);
                    this.mWirelessCPBTServerConnectListenerAIDL = null;
                    return Integer.MIN_VALUE;
                }
                return Integer.MIN_VALUE;
            }
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return -2147418113;
        }
    }

    public boolean connect(BluetoothDevice bluetoothDevice, boolean z) {
        Log.i(TAG, "connect wireless carplay in. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "connect~~mInterface is null");
                return false;
            }
            return ((ICarPlayProxy) this.mInterface).connectWirelessCP(bluetoothDevice, z);
        } catch (RemoteException e) {
            Log.e(TAG, e.toString());
            return false;
        } catch (Exception e2) {
            Log.e(TAG, e2.toString());
            return false;
        }
    }
}
