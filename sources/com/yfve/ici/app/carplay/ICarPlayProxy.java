package com.yfve.ici.app.carplay;

import android.bluetooth.BluetoothDevice;
import android.hardware.usb.UsbDevice;
import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import com.yfve.ici.app.carplay.ICPConnectStatusListenerAIDL;
import com.yfve.ici.app.carplay.ICPCtlCmdListenerAIDL;
import com.yfve.ici.app.carplay.ICPDeviceInfoListenerAIDL;
import com.yfve.ici.app.carplay.ICPExitListenerAIDL;
import com.yfve.ici.app.carplay.ICPOOBPairingInfoListenerAIDL;
import com.yfve.ici.app.carplay.ICPPhoneInfoListenerAIDL;
import com.yfve.ici.app.carplay.IMediaListenerAIDL;
import com.yfve.ici.app.carplay.ISiriStatusListenerAIDL;
import com.yfve.ici.app.carplay.IUICtlListenerAIDL;
import com.yfve.ici.app.carplay.IWirelessCPBTServerConnectListenerAIDL;

/* loaded from: classes.dex */
public interface ICarPlayProxy extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements ICarPlayProxy {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public boolean cancelConnectWirelessCPServer() throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public boolean confirmConnectWirelessCPServer(boolean z) throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public boolean connectWiredCP(UsbDevice usbDevice, int i, boolean z) throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public boolean connectWirelessCP(BluetoothDevice bluetoothDevice, boolean z) throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void disconnectCP() throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public String getAblum() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public String getArtist() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public String getArtwork() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public int getCPConnectStatus() throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public long getCurrentPosition() throws RemoteException {
            return 0L;
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public CPDeviceInfo getDeviceInfo() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public long getDuration() throws RemoteException {
            return 0L;
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public String getID3Info() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public CPPhoneInfo getPhoneInfo() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public int getPlayState() throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public int getRepeatMode() throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public int getShuffleMode() throws RemoteException {
            return 0;
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public String getTitle() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public boolean isCarPlaySessionActived() throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public boolean isSiriActived() throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void mediaPlayControl(int i) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void notifyUIHided(String str, int i) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void notifyUIShowForVR(String str, int i) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void notifyUIShowed(String str, int i) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void registerCPConnectStatusListener(ICPConnectStatusListenerAIDL iCPConnectStatusListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void registerCPCtlCmdListenerListener(ICPCtlCmdListenerAIDL iCPCtlCmdListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void registerCPDeviceInfoListener(ICPDeviceInfoListenerAIDL iCPDeviceInfoListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void registerCPExitListener(ICPExitListenerAIDL iCPExitListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void registerCPOOBPairingInfoListener(ICPOOBPairingInfoListenerAIDL iCPOOBPairingInfoListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void registerMediaListener(IMediaListenerAIDL iMediaListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void registerPhoneInfoListener(ICPPhoneInfoListenerAIDL iCPPhoneInfoListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void registerSiriStatusListener(ISiriStatusListenerAIDL iSiriStatusListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void registerUICtlListener(IUICtlListenerAIDL iUICtlListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void registerWirelessCPBTServerConnectListener(IWirelessCPBTServerConnectListenerAIDL iWirelessCPBTServerConnectListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void requestHideUI(String str, int i) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void requestShowUI(String str, int i) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void setPlayingTime(int i) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void startCarPlayByUrl(String str) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void startCarPlayUI() throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void unregisterCPConnectStatusListener(ICPConnectStatusListenerAIDL iCPConnectStatusListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void unregisterCPCtlCmdListenerListener(ICPCtlCmdListenerAIDL iCPCtlCmdListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void unregisterCPDeviceInfoListener(ICPDeviceInfoListenerAIDL iCPDeviceInfoListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void unregisterCPExitListener(ICPExitListenerAIDL iCPExitListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void unregisterCPOOBPairingInfoListener(ICPOOBPairingInfoListenerAIDL iCPOOBPairingInfoListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void unregisterMediaListener(IMediaListenerAIDL iMediaListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void unregisterPhoneInfoListener(ICPPhoneInfoListenerAIDL iCPPhoneInfoListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void unregisterSiriStatusListener(ISiriStatusListenerAIDL iSiriStatusListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void unregisterUICtlListener(IUICtlListenerAIDL iUICtlListenerAIDL) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.ICarPlayProxy
        public void unregisterWirelessCPBTServerConnectListener(IWirelessCPBTServerConnectListenerAIDL iWirelessCPBTServerConnectListenerAIDL) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements ICarPlayProxy {
        public static final String DESCRIPTOR = "com.yfve.ici.app.carplay.ICarPlayProxy";
        public static final int TRANSACTION_cancelConnectWirelessCPServer = 5;
        public static final int TRANSACTION_confirmConnectWirelessCPServer = 4;
        public static final int TRANSACTION_connectWiredCP = 2;
        public static final int TRANSACTION_connectWirelessCP = 3;
        public static final int TRANSACTION_disconnectCP = 6;
        public static final int TRANSACTION_getAblum = 30;
        public static final int TRANSACTION_getArtist = 28;
        public static final int TRANSACTION_getArtwork = 29;
        public static final int TRANSACTION_getCPConnectStatus = 13;
        public static final int TRANSACTION_getCurrentPosition = 25;
        public static final int TRANSACTION_getDeviceInfo = 16;
        public static final int TRANSACTION_getDuration = 26;
        public static final int TRANSACTION_getID3Info = 41;
        public static final int TRANSACTION_getPhoneInfo = 8;
        public static final int TRANSACTION_getPlayState = 22;
        public static final int TRANSACTION_getRepeatMode = 24;
        public static final int TRANSACTION_getShuffleMode = 23;
        public static final int TRANSACTION_getTitle = 27;
        public static final int TRANSACTION_isCarPlaySessionActived = 1;
        public static final int TRANSACTION_isSiriActived = 19;
        public static final int TRANSACTION_mediaPlayControl = 31;
        public static final int TRANSACTION_notifyUIHided = 46;
        public static final int TRANSACTION_notifyUIShowForVR = 42;
        public static final int TRANSACTION_notifyUIShowed = 44;
        public static final int TRANSACTION_registerCPConnectStatusListener = 14;
        public static final int TRANSACTION_registerCPCtlCmdListenerListener = 34;
        public static final int TRANSACTION_registerCPDeviceInfoListener = 17;
        public static final int TRANSACTION_registerCPExitListener = 11;
        public static final int TRANSACTION_registerCPOOBPairingInfoListener = 37;
        public static final int TRANSACTION_registerMediaListener = 32;
        public static final int TRANSACTION_registerPhoneInfoListener = 9;
        public static final int TRANSACTION_registerSiriStatusListener = 20;
        public static final int TRANSACTION_registerUICtlListener = 47;
        public static final int TRANSACTION_registerWirelessCPBTServerConnectListener = 39;
        public static final int TRANSACTION_requestHideUI = 45;
        public static final int TRANSACTION_requestShowUI = 43;
        public static final int TRANSACTION_setPlayingTime = 36;
        public static final int TRANSACTION_startCarPlayByUrl = 49;
        public static final int TRANSACTION_startCarPlayUI = 7;
        public static final int TRANSACTION_unregisterCPConnectStatusListener = 15;
        public static final int TRANSACTION_unregisterCPCtlCmdListenerListener = 35;
        public static final int TRANSACTION_unregisterCPDeviceInfoListener = 18;
        public static final int TRANSACTION_unregisterCPExitListener = 12;
        public static final int TRANSACTION_unregisterCPOOBPairingInfoListener = 38;
        public static final int TRANSACTION_unregisterMediaListener = 33;
        public static final int TRANSACTION_unregisterPhoneInfoListener = 10;
        public static final int TRANSACTION_unregisterSiriStatusListener = 21;
        public static final int TRANSACTION_unregisterUICtlListener = 48;
        public static final int TRANSACTION_unregisterWirelessCPBTServerConnectListener = 40;

        /* loaded from: classes.dex */
        public static class Proxy implements ICarPlayProxy {
            public static ICarPlayProxy sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public boolean cancelConnectWirelessCPServer() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    if (!this.mRemote.transact(5, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().cancelConnectWirelessCPServer();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public boolean confirmConnectWirelessCPServer(boolean z) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeInt(z ? 1 : 0);
                    if (!this.mRemote.transact(4, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().confirmConnectWirelessCPServer(z);
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public boolean connectWiredCP(UsbDevice usbDevice, int i, boolean z) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    if (usbDevice != null) {
                        obtain.writeInt(1);
                        usbDevice.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    obtain.writeInt(i);
                    obtain.writeInt(z ? 1 : 0);
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().connectWiredCP(usbDevice, i, z);
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public boolean connectWirelessCP(BluetoothDevice bluetoothDevice, boolean z) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    if (bluetoothDevice != null) {
                        obtain.writeInt(1);
                        bluetoothDevice.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    obtain.writeInt(z ? 1 : 0);
                    if (!this.mRemote.transact(3, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().connectWirelessCP(bluetoothDevice, z);
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void disconnectCP() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    if (!this.mRemote.transact(6, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().disconnectCP();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public String getAblum() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    if (!this.mRemote.transact(30, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getAblum();
                    }
                    obtain2.readException();
                    return obtain2.readString();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public String getArtist() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    if (!this.mRemote.transact(28, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getArtist();
                    }
                    obtain2.readException();
                    return obtain2.readString();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public String getArtwork() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    if (!this.mRemote.transact(29, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getArtwork();
                    }
                    obtain2.readException();
                    return obtain2.readString();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public int getCPConnectStatus() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    if (!this.mRemote.transact(13, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getCPConnectStatus();
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public long getCurrentPosition() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    if (!this.mRemote.transact(25, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getCurrentPosition();
                    }
                    obtain2.readException();
                    return obtain2.readLong();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public CPDeviceInfo getDeviceInfo() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    if (!this.mRemote.transact(16, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getDeviceInfo();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0 ? CPDeviceInfo.CREATOR.createFromParcel(obtain2) : null;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public long getDuration() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    if (!this.mRemote.transact(26, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getDuration();
                    }
                    obtain2.readException();
                    return obtain2.readLong();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public String getID3Info() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    if (!this.mRemote.transact(41, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getID3Info();
                    }
                    obtain2.readException();
                    return obtain2.readString();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            public String getInterfaceDescriptor() {
                return "com.yfve.ici.app.carplay.ICarPlayProxy";
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public CPPhoneInfo getPhoneInfo() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    if (!this.mRemote.transact(8, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getPhoneInfo();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0 ? CPPhoneInfo.CREATOR.createFromParcel(obtain2) : null;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public int getPlayState() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    if (!this.mRemote.transact(22, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getPlayState();
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public int getRepeatMode() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    if (!this.mRemote.transact(24, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getRepeatMode();
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public int getShuffleMode() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    if (!this.mRemote.transact(23, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getShuffleMode();
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public String getTitle() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    if (!this.mRemote.transact(27, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getTitle();
                    }
                    obtain2.readException();
                    return obtain2.readString();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public boolean isCarPlaySessionActived() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().isCarPlaySessionActived();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public boolean isSiriActived() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    if (!this.mRemote.transact(19, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().isSiriActived();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void mediaPlayControl(int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(31, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().mediaPlayControl(i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void notifyUIHided(String str, int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeString(str);
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(46, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().notifyUIHided(str, i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void notifyUIShowForVR(String str, int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeString(str);
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(42, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().notifyUIShowForVR(str, i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void notifyUIShowed(String str, int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeString(str);
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(44, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().notifyUIShowed(str, i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void registerCPConnectStatusListener(ICPConnectStatusListenerAIDL iCPConnectStatusListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeStrongBinder(iCPConnectStatusListenerAIDL != null ? iCPConnectStatusListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(14, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerCPConnectStatusListener(iCPConnectStatusListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void registerCPCtlCmdListenerListener(ICPCtlCmdListenerAIDL iCPCtlCmdListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeStrongBinder(iCPCtlCmdListenerAIDL != null ? iCPCtlCmdListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(34, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerCPCtlCmdListenerListener(iCPCtlCmdListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void registerCPDeviceInfoListener(ICPDeviceInfoListenerAIDL iCPDeviceInfoListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeStrongBinder(iCPDeviceInfoListenerAIDL != null ? iCPDeviceInfoListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(17, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerCPDeviceInfoListener(iCPDeviceInfoListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void registerCPExitListener(ICPExitListenerAIDL iCPExitListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeStrongBinder(iCPExitListenerAIDL != null ? iCPExitListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(11, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerCPExitListener(iCPExitListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void registerCPOOBPairingInfoListener(ICPOOBPairingInfoListenerAIDL iCPOOBPairingInfoListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeStrongBinder(iCPOOBPairingInfoListenerAIDL != null ? iCPOOBPairingInfoListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(37, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerCPOOBPairingInfoListener(iCPOOBPairingInfoListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void registerMediaListener(IMediaListenerAIDL iMediaListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeStrongBinder(iMediaListenerAIDL != null ? iMediaListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(32, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerMediaListener(iMediaListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void registerPhoneInfoListener(ICPPhoneInfoListenerAIDL iCPPhoneInfoListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeStrongBinder(iCPPhoneInfoListenerAIDL != null ? iCPPhoneInfoListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(9, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerPhoneInfoListener(iCPPhoneInfoListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void registerSiriStatusListener(ISiriStatusListenerAIDL iSiriStatusListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeStrongBinder(iSiriStatusListenerAIDL != null ? iSiriStatusListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(20, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerSiriStatusListener(iSiriStatusListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void registerUICtlListener(IUICtlListenerAIDL iUICtlListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeStrongBinder(iUICtlListenerAIDL != null ? iUICtlListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(47, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerUICtlListener(iUICtlListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void registerWirelessCPBTServerConnectListener(IWirelessCPBTServerConnectListenerAIDL iWirelessCPBTServerConnectListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeStrongBinder(iWirelessCPBTServerConnectListenerAIDL != null ? iWirelessCPBTServerConnectListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(39, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerWirelessCPBTServerConnectListener(iWirelessCPBTServerConnectListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void requestHideUI(String str, int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeString(str);
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(45, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().requestHideUI(str, i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void requestShowUI(String str, int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeString(str);
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(43, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().requestShowUI(str, i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void setPlayingTime(int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(36, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().setPlayingTime(i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void startCarPlayByUrl(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeString(str);
                    if (!this.mRemote.transact(49, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().startCarPlayByUrl(str);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void startCarPlayUI() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    if (!this.mRemote.transact(7, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().startCarPlayUI();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void unregisterCPConnectStatusListener(ICPConnectStatusListenerAIDL iCPConnectStatusListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeStrongBinder(iCPConnectStatusListenerAIDL != null ? iCPConnectStatusListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(15, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterCPConnectStatusListener(iCPConnectStatusListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void unregisterCPCtlCmdListenerListener(ICPCtlCmdListenerAIDL iCPCtlCmdListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeStrongBinder(iCPCtlCmdListenerAIDL != null ? iCPCtlCmdListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(35, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterCPCtlCmdListenerListener(iCPCtlCmdListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void unregisterCPDeviceInfoListener(ICPDeviceInfoListenerAIDL iCPDeviceInfoListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeStrongBinder(iCPDeviceInfoListenerAIDL != null ? iCPDeviceInfoListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(18, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterCPDeviceInfoListener(iCPDeviceInfoListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void unregisterCPExitListener(ICPExitListenerAIDL iCPExitListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeStrongBinder(iCPExitListenerAIDL != null ? iCPExitListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(12, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterCPExitListener(iCPExitListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void unregisterCPOOBPairingInfoListener(ICPOOBPairingInfoListenerAIDL iCPOOBPairingInfoListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeStrongBinder(iCPOOBPairingInfoListenerAIDL != null ? iCPOOBPairingInfoListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(38, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterCPOOBPairingInfoListener(iCPOOBPairingInfoListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void unregisterMediaListener(IMediaListenerAIDL iMediaListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeStrongBinder(iMediaListenerAIDL != null ? iMediaListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(33, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterMediaListener(iMediaListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void unregisterPhoneInfoListener(ICPPhoneInfoListenerAIDL iCPPhoneInfoListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeStrongBinder(iCPPhoneInfoListenerAIDL != null ? iCPPhoneInfoListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(10, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterPhoneInfoListener(iCPPhoneInfoListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void unregisterSiriStatusListener(ISiriStatusListenerAIDL iSiriStatusListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeStrongBinder(iSiriStatusListenerAIDL != null ? iSiriStatusListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(21, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterSiriStatusListener(iSiriStatusListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void unregisterUICtlListener(IUICtlListenerAIDL iUICtlListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeStrongBinder(iUICtlListenerAIDL != null ? iUICtlListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(48, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterUICtlListener(iUICtlListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.ICarPlayProxy
            public void unregisterWirelessCPBTServerConnectListener(IWirelessCPBTServerConnectListenerAIDL iWirelessCPBTServerConnectListenerAIDL) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.carplay.ICarPlayProxy");
                    obtain.writeStrongBinder(iWirelessCPBTServerConnectListenerAIDL != null ? iWirelessCPBTServerConnectListenerAIDL.asBinder() : null);
                    if (!this.mRemote.transact(40, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterWirelessCPBTServerConnectListener(iWirelessCPBTServerConnectListenerAIDL);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, "com.yfve.ici.app.carplay.ICarPlayProxy");
        }

        public static ICarPlayProxy asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
            if (queryLocalInterface != null && (queryLocalInterface instanceof ICarPlayProxy)) {
                return (ICarPlayProxy) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static ICarPlayProxy getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(ICarPlayProxy iCarPlayProxy) {
            if (Proxy.sDefaultImpl != null || iCarPlayProxy == null) {
                return false;
            }
            Proxy.sDefaultImpl = iCarPlayProxy;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1598968902) {
                switch (i) {
                    case 1:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        boolean isCarPlaySessionActived = isCarPlaySessionActived();
                        parcel2.writeNoException();
                        parcel2.writeInt(isCarPlaySessionActived ? 1 : 0);
                        return true;
                    case 2:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        boolean connectWiredCP = connectWiredCP(parcel.readInt() != 0 ? (UsbDevice) UsbDevice.CREATOR.createFromParcel(parcel) : null, parcel.readInt(), parcel.readInt() != 0);
                        parcel2.writeNoException();
                        parcel2.writeInt(connectWiredCP ? 1 : 0);
                        return true;
                    case 3:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        boolean connectWirelessCP = connectWirelessCP(parcel.readInt() != 0 ? (BluetoothDevice) BluetoothDevice.CREATOR.createFromParcel(parcel) : null, parcel.readInt() != 0);
                        parcel2.writeNoException();
                        parcel2.writeInt(connectWirelessCP ? 1 : 0);
                        return true;
                    case 4:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        boolean confirmConnectWirelessCPServer = confirmConnectWirelessCPServer(parcel.readInt() != 0);
                        parcel2.writeNoException();
                        parcel2.writeInt(confirmConnectWirelessCPServer ? 1 : 0);
                        return true;
                    case 5:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        boolean cancelConnectWirelessCPServer = cancelConnectWirelessCPServer();
                        parcel2.writeNoException();
                        parcel2.writeInt(cancelConnectWirelessCPServer ? 1 : 0);
                        return true;
                    case 6:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        disconnectCP();
                        parcel2.writeNoException();
                        return true;
                    case 7:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        startCarPlayUI();
                        parcel2.writeNoException();
                        return true;
                    case 8:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        CPPhoneInfo phoneInfo = getPhoneInfo();
                        parcel2.writeNoException();
                        if (phoneInfo != null) {
                            parcel2.writeInt(1);
                            phoneInfo.writeToParcel(parcel2, 1);
                        } else {
                            parcel2.writeInt(0);
                        }
                        return true;
                    case 9:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        registerPhoneInfoListener(ICPPhoneInfoListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 10:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        unregisterPhoneInfoListener(ICPPhoneInfoListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 11:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        registerCPExitListener(ICPExitListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 12:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        unregisterCPExitListener(ICPExitListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 13:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        int cPConnectStatus = getCPConnectStatus();
                        parcel2.writeNoException();
                        parcel2.writeInt(cPConnectStatus);
                        return true;
                    case 14:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        registerCPConnectStatusListener(ICPConnectStatusListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 15:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        unregisterCPConnectStatusListener(ICPConnectStatusListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 16:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        CPDeviceInfo deviceInfo = getDeviceInfo();
                        parcel2.writeNoException();
                        if (deviceInfo != null) {
                            parcel2.writeInt(1);
                            deviceInfo.writeToParcel(parcel2, 1);
                        } else {
                            parcel2.writeInt(0);
                        }
                        return true;
                    case 17:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        registerCPDeviceInfoListener(ICPDeviceInfoListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 18:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        unregisterCPDeviceInfoListener(ICPDeviceInfoListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 19:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        boolean isSiriActived = isSiriActived();
                        parcel2.writeNoException();
                        parcel2.writeInt(isSiriActived ? 1 : 0);
                        return true;
                    case 20:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        registerSiriStatusListener(ISiriStatusListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 21:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        unregisterSiriStatusListener(ISiriStatusListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 22:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        int playState = getPlayState();
                        parcel2.writeNoException();
                        parcel2.writeInt(playState);
                        return true;
                    case 23:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        int shuffleMode = getShuffleMode();
                        parcel2.writeNoException();
                        parcel2.writeInt(shuffleMode);
                        return true;
                    case 24:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        int repeatMode = getRepeatMode();
                        parcel2.writeNoException();
                        parcel2.writeInt(repeatMode);
                        return true;
                    case 25:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        long currentPosition = getCurrentPosition();
                        parcel2.writeNoException();
                        parcel2.writeLong(currentPosition);
                        return true;
                    case 26:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        long duration = getDuration();
                        parcel2.writeNoException();
                        parcel2.writeLong(duration);
                        return true;
                    case 27:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        String title = getTitle();
                        parcel2.writeNoException();
                        parcel2.writeString(title);
                        return true;
                    case 28:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        String artist = getArtist();
                        parcel2.writeNoException();
                        parcel2.writeString(artist);
                        return true;
                    case 29:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        String artwork = getArtwork();
                        parcel2.writeNoException();
                        parcel2.writeString(artwork);
                        return true;
                    case 30:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        String ablum = getAblum();
                        parcel2.writeNoException();
                        parcel2.writeString(ablum);
                        return true;
                    case 31:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        mediaPlayControl(parcel.readInt());
                        parcel2.writeNoException();
                        return true;
                    case 32:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        registerMediaListener(IMediaListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 33:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        unregisterMediaListener(IMediaListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 34:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        registerCPCtlCmdListenerListener(ICPCtlCmdListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 35:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        unregisterCPCtlCmdListenerListener(ICPCtlCmdListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 36:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        setPlayingTime(parcel.readInt());
                        parcel2.writeNoException();
                        return true;
                    case 37:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        registerCPOOBPairingInfoListener(ICPOOBPairingInfoListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 38:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        unregisterCPOOBPairingInfoListener(ICPOOBPairingInfoListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 39:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        registerWirelessCPBTServerConnectListener(IWirelessCPBTServerConnectListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 40:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        unregisterWirelessCPBTServerConnectListener(IWirelessCPBTServerConnectListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 41:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        String iD3Info = getID3Info();
                        parcel2.writeNoException();
                        parcel2.writeString(iD3Info);
                        return true;
                    case 42:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        notifyUIShowForVR(parcel.readString(), parcel.readInt());
                        parcel2.writeNoException();
                        return true;
                    case 43:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        requestShowUI(parcel.readString(), parcel.readInt());
                        parcel2.writeNoException();
                        return true;
                    case 44:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        notifyUIShowed(parcel.readString(), parcel.readInt());
                        parcel2.writeNoException();
                        return true;
                    case 45:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        requestHideUI(parcel.readString(), parcel.readInt());
                        parcel2.writeNoException();
                        return true;
                    case 46:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        notifyUIHided(parcel.readString(), parcel.readInt());
                        parcel2.writeNoException();
                        return true;
                    case 47:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        registerUICtlListener(IUICtlListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 48:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        unregisterUICtlListener(IUICtlListenerAIDL.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 49:
                        parcel.enforceInterface("com.yfve.ici.app.carplay.ICarPlayProxy");
                        startCarPlayByUrl(parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    default:
                        return super.onTransact(i, parcel, parcel2, i2);
                }
            }
            parcel2.writeString("com.yfve.ici.app.carplay.ICarPlayProxy");
            return true;
        }
    }

    boolean cancelConnectWirelessCPServer() throws RemoteException;

    boolean confirmConnectWirelessCPServer(boolean z) throws RemoteException;

    boolean connectWiredCP(UsbDevice usbDevice, int i, boolean z) throws RemoteException;

    boolean connectWirelessCP(BluetoothDevice bluetoothDevice, boolean z) throws RemoteException;

    void disconnectCP() throws RemoteException;

    String getAblum() throws RemoteException;

    String getArtist() throws RemoteException;

    String getArtwork() throws RemoteException;

    int getCPConnectStatus() throws RemoteException;

    long getCurrentPosition() throws RemoteException;

    CPDeviceInfo getDeviceInfo() throws RemoteException;

    long getDuration() throws RemoteException;

    String getID3Info() throws RemoteException;

    CPPhoneInfo getPhoneInfo() throws RemoteException;

    int getPlayState() throws RemoteException;

    int getRepeatMode() throws RemoteException;

    int getShuffleMode() throws RemoteException;

    String getTitle() throws RemoteException;

    boolean isCarPlaySessionActived() throws RemoteException;

    boolean isSiriActived() throws RemoteException;

    void mediaPlayControl(int i) throws RemoteException;

    void notifyUIHided(String str, int i) throws RemoteException;

    void notifyUIShowForVR(String str, int i) throws RemoteException;

    void notifyUIShowed(String str, int i) throws RemoteException;

    void registerCPConnectStatusListener(ICPConnectStatusListenerAIDL iCPConnectStatusListenerAIDL) throws RemoteException;

    void registerCPCtlCmdListenerListener(ICPCtlCmdListenerAIDL iCPCtlCmdListenerAIDL) throws RemoteException;

    void registerCPDeviceInfoListener(ICPDeviceInfoListenerAIDL iCPDeviceInfoListenerAIDL) throws RemoteException;

    void registerCPExitListener(ICPExitListenerAIDL iCPExitListenerAIDL) throws RemoteException;

    void registerCPOOBPairingInfoListener(ICPOOBPairingInfoListenerAIDL iCPOOBPairingInfoListenerAIDL) throws RemoteException;

    void registerMediaListener(IMediaListenerAIDL iMediaListenerAIDL) throws RemoteException;

    void registerPhoneInfoListener(ICPPhoneInfoListenerAIDL iCPPhoneInfoListenerAIDL) throws RemoteException;

    void registerSiriStatusListener(ISiriStatusListenerAIDL iSiriStatusListenerAIDL) throws RemoteException;

    void registerUICtlListener(IUICtlListenerAIDL iUICtlListenerAIDL) throws RemoteException;

    void registerWirelessCPBTServerConnectListener(IWirelessCPBTServerConnectListenerAIDL iWirelessCPBTServerConnectListenerAIDL) throws RemoteException;

    void requestHideUI(String str, int i) throws RemoteException;

    void requestShowUI(String str, int i) throws RemoteException;

    void setPlayingTime(int i) throws RemoteException;

    void startCarPlayByUrl(String str) throws RemoteException;

    void startCarPlayUI() throws RemoteException;

    void unregisterCPConnectStatusListener(ICPConnectStatusListenerAIDL iCPConnectStatusListenerAIDL) throws RemoteException;

    void unregisterCPCtlCmdListenerListener(ICPCtlCmdListenerAIDL iCPCtlCmdListenerAIDL) throws RemoteException;

    void unregisterCPDeviceInfoListener(ICPDeviceInfoListenerAIDL iCPDeviceInfoListenerAIDL) throws RemoteException;

    void unregisterCPExitListener(ICPExitListenerAIDL iCPExitListenerAIDL) throws RemoteException;

    void unregisterCPOOBPairingInfoListener(ICPOOBPairingInfoListenerAIDL iCPOOBPairingInfoListenerAIDL) throws RemoteException;

    void unregisterMediaListener(IMediaListenerAIDL iMediaListenerAIDL) throws RemoteException;

    void unregisterPhoneInfoListener(ICPPhoneInfoListenerAIDL iCPPhoneInfoListenerAIDL) throws RemoteException;

    void unregisterSiriStatusListener(ISiriStatusListenerAIDL iSiriStatusListenerAIDL) throws RemoteException;

    void unregisterUICtlListener(IUICtlListenerAIDL iUICtlListenerAIDL) throws RemoteException;

    void unregisterWirelessCPBTServerConnectListener(IWirelessCPBTServerConnectListenerAIDL iWirelessCPBTServerConnectListenerAIDL) throws RemoteException;
}
