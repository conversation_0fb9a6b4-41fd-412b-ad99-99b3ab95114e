package com.yfve.ici.app.carplay;

import android.os.Parcel;
import android.os.Parcelable;
import b.a.b.a.a;

/* loaded from: classes.dex */
public class CPPhoneInfo implements Parcelable {
    public static final Parcelable.Creator<CPPhoneInfo> CREATOR = new Parcelable.Creator<CPPhoneInfo>() { // from class: com.yfve.ici.app.carplay.CPPhoneInfo.1
        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public CPPhoneInfo createFromParcel(Parcel parcel) {
            return new CPPhoneInfo(parcel);
        }

        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public CPPhoneInfo[] newArray(int i) {
            return new CPPhoneInfo[i];
        }
    };
    public static final String DEFAULT_INFO = "UNKNOWN";
    public static final int DEFAULT_VALUE = 0;
    public static final int IAP2_CALL_DIRECTION_INCOMING = 1;
    public static final int IAP2_CALL_DIRECTION_OUTGOING = 2;
    public static final int IAP2_CALL_DIRECTION_UNKNOWN = 0;
    public static final int IAP2_CALL_STATE_ACTIVE = 4;
    public static final int IAP2_CALL_STATE_CONNECTING = 3;
    public static final int IAP2_CALL_STATE_DISCONNECTED = 0;
    public static final int IAP2_CALL_STATE_DISCONNECTING = 6;
    public static final int IAP2_CALL_STATE_HELD = 5;
    public static final int IAP2_CALL_STATE_RINGING = 2;
    public static final int IAP2_CALL_STATE_SENDING = 1;
    public int mCallDirection;
    public int mCallState;
    public String mDisplayName;
    public String mDisplayNumber;

    public CPPhoneInfo() {
        this.mCallState = 0;
        this.mCallDirection = 0;
        this.mDisplayName = "UNKNOWN";
        this.mDisplayNumber = "UNKNOWN";
    }

    @Override // android.os.Parcelable
    public int describeContents() {
        return 0;
    }

    public int getCallDirection() {
        return this.mCallDirection;
    }

    public int getCallState() {
        return this.mCallState;
    }

    public String getDisplayName() {
        return this.mDisplayName;
    }

    public String getDisplayNumber() {
        return this.mDisplayNumber;
    }

    public void setCallDirection(int i) {
        this.mCallDirection = i;
    }

    public void setCallState(int i) {
        this.mCallState = i;
    }

    public void setDisplayName(String str) {
        this.mDisplayName = str;
    }

    public void setDisplayNumber(String str) {
        this.mDisplayNumber = str;
    }

    public String toString() {
        StringBuilder e = a.e("IPODCALLSTATE{mCallState:");
        e.append(this.mCallState);
        e.append("mCallDirection:");
        e.append(this.mCallDirection);
        e.append("mDisplayName:");
        e.append(this.mDisplayName);
        e.append("mDisplayNumber:");
        return a.c(e, this.mDisplayNumber, "}");
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeInt(this.mCallState);
        parcel.writeInt(this.mCallDirection);
        parcel.writeString(this.mDisplayName);
        parcel.writeString(this.mDisplayNumber);
    }

    public CPPhoneInfo(Parcel parcel) {
        this.mCallState = parcel.readInt();
        this.mCallDirection = parcel.readInt();
        this.mDisplayName = parcel.readString();
        this.mDisplayNumber = parcel.readString();
    }
}
