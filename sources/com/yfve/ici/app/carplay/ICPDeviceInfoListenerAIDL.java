package com.yfve.ici.app.carplay;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface ICPDeviceInfoListenerAIDL extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements ICPDeviceInfoListenerAIDL {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.carplay.ICPDeviceInfoListenerAIDL
        public void onDeviceInfoChanged(CPDeviceInfo cPDeviceInfo) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements ICPDeviceInfoListenerAIDL {
        public static final String DESCRIPTOR = "com.yfve.ici.app.carplay.ICPDeviceInfoListenerAIDL";
        public static final int TRANSACTION_onDeviceInfoChanged = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements ICPDeviceInfoListenerAIDL {
            public static ICPDeviceInfoListenerAIDL sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            @Override // com.yfve.ici.app.carplay.ICPDeviceInfoListenerAIDL
            public void onDeviceInfoChanged(CPDeviceInfo cPDeviceInfo) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (cPDeviceInfo != null) {
                        obtain.writeInt(1);
                        cPDeviceInfo.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onDeviceInfoChanged(cPDeviceInfo);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static ICPDeviceInfoListenerAIDL asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof ICPDeviceInfoListenerAIDL)) {
                return (ICPDeviceInfoListenerAIDL) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static ICPDeviceInfoListenerAIDL getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(ICPDeviceInfoListenerAIDL iCPDeviceInfoListenerAIDL) {
            if (Proxy.sDefaultImpl != null || iCPDeviceInfoListenerAIDL == null) {
                return false;
            }
            Proxy.sDefaultImpl = iCPDeviceInfoListenerAIDL;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString(DESCRIPTOR);
                return true;
            }
            parcel.enforceInterface(DESCRIPTOR);
            onDeviceInfoChanged(parcel.readInt() != 0 ? CPDeviceInfo.CREATOR.createFromParcel(parcel) : null);
            parcel2.writeNoException();
            return true;
        }
    }

    void onDeviceInfoChanged(CPDeviceInfo cPDeviceInfo) throws RemoteException;
}
