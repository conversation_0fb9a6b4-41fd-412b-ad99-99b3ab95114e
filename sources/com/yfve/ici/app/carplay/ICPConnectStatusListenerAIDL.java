package com.yfve.ici.app.carplay;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface ICPConnectStatusListenerAIDL extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements ICPConnectStatusListenerAIDL {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.carplay.ICPConnectStatusListenerAIDL
        public void onConnectStatusChanged(int i) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements ICPConnectStatusListenerAIDL {
        public static final String DESCRIPTOR = "com.yfve.ici.app.carplay.ICPConnectStatusListenerAIDL";
        public static final int TRANSACTION_onConnectStatusChanged = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements ICPConnectStatusListenerAIDL {
            public static ICPConnectStatusListenerAIDL sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            @Override // com.yfve.ici.app.carplay.ICPConnectStatusListenerAIDL
            public void onConnectStatusChanged(int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeInt(i);
                    if (this.mRemote.transact(1, obtain, null, 1) || Stub.getDefaultImpl() == null) {
                        return;
                    }
                    Stub.getDefaultImpl().onConnectStatusChanged(i);
                } finally {
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static ICPConnectStatusListenerAIDL asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof ICPConnectStatusListenerAIDL)) {
                return (ICPConnectStatusListenerAIDL) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static ICPConnectStatusListenerAIDL getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(ICPConnectStatusListenerAIDL iCPConnectStatusListenerAIDL) {
            if (Proxy.sDefaultImpl != null || iCPConnectStatusListenerAIDL == null) {
                return false;
            }
            Proxy.sDefaultImpl = iCPConnectStatusListenerAIDL;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i == 1) {
                parcel.enforceInterface(DESCRIPTOR);
                onConnectStatusChanged(parcel.readInt());
                return true;
            } else if (i != 1598968902) {
                return super.onTransact(i, parcel, parcel2, i2);
            } else {
                parcel2.writeString(DESCRIPTOR);
                return true;
            }
        }
    }

    void onConnectStatusChanged(int i) throws RemoteException;
}
