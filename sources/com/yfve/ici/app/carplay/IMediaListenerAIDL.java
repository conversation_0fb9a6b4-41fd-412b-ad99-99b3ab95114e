package com.yfve.ici.app.carplay;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface IMediaListenerAIDL extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IMediaListenerAIDL {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.carplay.IMediaListenerAIDL
        public void onCurPositionChanged(long j) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.IMediaListenerAIDL
        public void onMetadataChanged(String str) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.IMediaListenerAIDL
        public void onPlayStateChanged(int i) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.IMediaListenerAIDL
        public void onRepeatModeChanged(int i) throws RemoteException {
        }

        @Override // com.yfve.ici.app.carplay.IMediaListenerAIDL
        public void onShuffleModeChanged(int i) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IMediaListenerAIDL {
        public static final String DESCRIPTOR = "com.yfve.ici.app.carplay.IMediaListenerAIDL";
        public static final int TRANSACTION_onCurPositionChanged = 1;
        public static final int TRANSACTION_onMetadataChanged = 5;
        public static final int TRANSACTION_onPlayStateChanged = 2;
        public static final int TRANSACTION_onRepeatModeChanged = 4;
        public static final int TRANSACTION_onShuffleModeChanged = 3;

        /* loaded from: classes.dex */
        public static class Proxy implements IMediaListenerAIDL {
            public static IMediaListenerAIDL sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            @Override // com.yfve.ici.app.carplay.IMediaListenerAIDL
            public void onCurPositionChanged(long j) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeLong(j);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onCurPositionChanged(j);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.IMediaListenerAIDL
            public void onMetadataChanged(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    if (!this.mRemote.transact(5, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onMetadataChanged(str);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.IMediaListenerAIDL
            public void onPlayStateChanged(int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onPlayStateChanged(i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.IMediaListenerAIDL
            public void onRepeatModeChanged(int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(4, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onRepeatModeChanged(i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.carplay.IMediaListenerAIDL
            public void onShuffleModeChanged(int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(3, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onShuffleModeChanged(i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static IMediaListenerAIDL asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof IMediaListenerAIDL)) {
                return (IMediaListenerAIDL) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IMediaListenerAIDL getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IMediaListenerAIDL iMediaListenerAIDL) {
            if (Proxy.sDefaultImpl != null || iMediaListenerAIDL == null) {
                return false;
            }
            Proxy.sDefaultImpl = iMediaListenerAIDL;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i == 1) {
                parcel.enforceInterface(DESCRIPTOR);
                onCurPositionChanged(parcel.readLong());
                parcel2.writeNoException();
                return true;
            } else if (i == 2) {
                parcel.enforceInterface(DESCRIPTOR);
                onPlayStateChanged(parcel.readInt());
                parcel2.writeNoException();
                return true;
            } else if (i == 3) {
                parcel.enforceInterface(DESCRIPTOR);
                onShuffleModeChanged(parcel.readInt());
                parcel2.writeNoException();
                return true;
            } else if (i == 4) {
                parcel.enforceInterface(DESCRIPTOR);
                onRepeatModeChanged(parcel.readInt());
                parcel2.writeNoException();
                return true;
            } else if (i != 5) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString(DESCRIPTOR);
                return true;
            } else {
                parcel.enforceInterface(DESCRIPTOR);
                onMetadataChanged(parcel.readString());
                parcel2.writeNoException();
                return true;
            }
        }
    }

    void onCurPositionChanged(long j) throws RemoteException;

    void onMetadataChanged(String str) throws RemoteException;

    void onPlayStateChanged(int i) throws RemoteException;

    void onRepeatModeChanged(int i) throws RemoteException;

    void onShuffleModeChanged(int i) throws RemoteException;
}
