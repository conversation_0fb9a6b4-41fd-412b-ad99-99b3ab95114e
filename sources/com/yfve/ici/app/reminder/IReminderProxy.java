package com.yfve.ici.app.reminder;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import com.yfve.ici.app.reminder.IReminderEditListener;
import com.yfve.ici.app.reminder.IReminderListener;

/* loaded from: classes.dex */
public interface IReminderProxy extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IReminderProxy {
        @Override // com.yfve.ici.app.reminder.IReminderProxy
        public void addReminder(ReminderInfo reminderInfo) throws RemoteException {
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.reminder.IReminderProxy
        public void deleteReminder(ReminderInfo reminderInfo) throws RemoteException {
        }

        @Override // com.yfve.ici.app.reminder.IReminderProxy
        public void editReminder(ReminderInfo reminderInfo) throws RemoteException {
        }

        @Override // com.yfve.ici.app.reminder.IReminderProxy
        public void getReminderList(long j) throws RemoteException {
        }

        @Override // com.yfve.ici.app.reminder.IReminderProxy
        public void openReminder(long j) throws RemoteException {
        }

        @Override // com.yfve.ici.app.reminder.IReminderProxy
        public void registerReminderEditListener(IReminderEditListener iReminderEditListener) throws RemoteException {
        }

        @Override // com.yfve.ici.app.reminder.IReminderProxy
        public void registerReminderListener(IReminderListener iReminderListener) throws RemoteException {
        }

        @Override // com.yfve.ici.app.reminder.IReminderProxy
        public void setDate(String str) throws RemoteException {
        }

        @Override // com.yfve.ici.app.reminder.IReminderProxy
        public void syncCalendar() throws RemoteException {
        }

        @Override // com.yfve.ici.app.reminder.IReminderProxy
        public void unregisterReminderEditListener(IReminderEditListener iReminderEditListener) throws RemoteException {
        }

        @Override // com.yfve.ici.app.reminder.IReminderProxy
        public void unregisterReminderListener(IReminderListener iReminderListener) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IReminderProxy {
        public static final String DESCRIPTOR = "com.yfve.ici.app.reminder.IReminderProxy";
        public static final int TRANSACTION_addReminder = 5;
        public static final int TRANSACTION_deleteReminder = 6;
        public static final int TRANSACTION_editReminder = 9;
        public static final int TRANSACTION_getReminderList = 7;
        public static final int TRANSACTION_openReminder = 11;
        public static final int TRANSACTION_registerReminderEditListener = 3;
        public static final int TRANSACTION_registerReminderListener = 1;
        public static final int TRANSACTION_setDate = 10;
        public static final int TRANSACTION_syncCalendar = 8;
        public static final int TRANSACTION_unregisterReminderEditListener = 4;
        public static final int TRANSACTION_unregisterReminderListener = 2;

        /* loaded from: classes.dex */
        public static class Proxy implements IReminderProxy {
            public static IReminderProxy sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // com.yfve.ici.app.reminder.IReminderProxy
            public void addReminder(ReminderInfo reminderInfo) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.reminder.IReminderProxy");
                    if (reminderInfo != null) {
                        obtain.writeInt(1);
                        reminderInfo.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    if (!this.mRemote.transact(5, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().addReminder(reminderInfo);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            @Override // com.yfve.ici.app.reminder.IReminderProxy
            public void deleteReminder(ReminderInfo reminderInfo) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.reminder.IReminderProxy");
                    if (reminderInfo != null) {
                        obtain.writeInt(1);
                        reminderInfo.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    if (!this.mRemote.transact(6, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().deleteReminder(reminderInfo);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.reminder.IReminderProxy
            public void editReminder(ReminderInfo reminderInfo) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.reminder.IReminderProxy");
                    if (reminderInfo != null) {
                        obtain.writeInt(1);
                        reminderInfo.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    if (!this.mRemote.transact(9, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().editReminder(reminderInfo);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            public String getInterfaceDescriptor() {
                return "com.yfve.ici.app.reminder.IReminderProxy";
            }

            @Override // com.yfve.ici.app.reminder.IReminderProxy
            public void getReminderList(long j) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.reminder.IReminderProxy");
                    obtain.writeLong(j);
                    if (!this.mRemote.transact(7, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().getReminderList(j);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.reminder.IReminderProxy
            public void openReminder(long j) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.reminder.IReminderProxy");
                    obtain.writeLong(j);
                    if (!this.mRemote.transact(11, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().openReminder(j);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.reminder.IReminderProxy
            public void registerReminderEditListener(IReminderEditListener iReminderEditListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.reminder.IReminderProxy");
                    obtain.writeStrongBinder(iReminderEditListener != null ? iReminderEditListener.asBinder() : null);
                    if (!this.mRemote.transact(3, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerReminderEditListener(iReminderEditListener);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.reminder.IReminderProxy
            public void registerReminderListener(IReminderListener iReminderListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.reminder.IReminderProxy");
                    obtain.writeStrongBinder(iReminderListener != null ? iReminderListener.asBinder() : null);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerReminderListener(iReminderListener);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.reminder.IReminderProxy
            public void setDate(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.reminder.IReminderProxy");
                    obtain.writeString(str);
                    if (!this.mRemote.transact(10, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().setDate(str);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.reminder.IReminderProxy
            public void syncCalendar() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.reminder.IReminderProxy");
                    if (!this.mRemote.transact(8, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().syncCalendar();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.reminder.IReminderProxy
            public void unregisterReminderEditListener(IReminderEditListener iReminderEditListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.reminder.IReminderProxy");
                    obtain.writeStrongBinder(iReminderEditListener != null ? iReminderEditListener.asBinder() : null);
                    if (!this.mRemote.transact(4, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterReminderEditListener(iReminderEditListener);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.reminder.IReminderProxy
            public void unregisterReminderListener(IReminderListener iReminderListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.app.reminder.IReminderProxy");
                    obtain.writeStrongBinder(iReminderListener != null ? iReminderListener.asBinder() : null);
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterReminderListener(iReminderListener);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, "com.yfve.ici.app.reminder.IReminderProxy");
        }

        public static IReminderProxy asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface("com.yfve.ici.app.reminder.IReminderProxy");
            if (queryLocalInterface != null && (queryLocalInterface instanceof IReminderProxy)) {
                return (IReminderProxy) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IReminderProxy getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IReminderProxy iReminderProxy) {
            if (Proxy.sDefaultImpl != null || iReminderProxy == null) {
                return false;
            }
            Proxy.sDefaultImpl = iReminderProxy;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1598968902) {
                switch (i) {
                    case 1:
                        parcel.enforceInterface("com.yfve.ici.app.reminder.IReminderProxy");
                        registerReminderListener(IReminderListener.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 2:
                        parcel.enforceInterface("com.yfve.ici.app.reminder.IReminderProxy");
                        unregisterReminderListener(IReminderListener.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 3:
                        parcel.enforceInterface("com.yfve.ici.app.reminder.IReminderProxy");
                        registerReminderEditListener(IReminderEditListener.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 4:
                        parcel.enforceInterface("com.yfve.ici.app.reminder.IReminderProxy");
                        unregisterReminderEditListener(IReminderEditListener.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 5:
                        parcel.enforceInterface("com.yfve.ici.app.reminder.IReminderProxy");
                        addReminder(parcel.readInt() != 0 ? ReminderInfo.CREATOR.createFromParcel(parcel) : null);
                        parcel2.writeNoException();
                        return true;
                    case 6:
                        parcel.enforceInterface("com.yfve.ici.app.reminder.IReminderProxy");
                        deleteReminder(parcel.readInt() != 0 ? ReminderInfo.CREATOR.createFromParcel(parcel) : null);
                        parcel2.writeNoException();
                        return true;
                    case 7:
                        parcel.enforceInterface("com.yfve.ici.app.reminder.IReminderProxy");
                        getReminderList(parcel.readLong());
                        parcel2.writeNoException();
                        return true;
                    case 8:
                        parcel.enforceInterface("com.yfve.ici.app.reminder.IReminderProxy");
                        syncCalendar();
                        parcel2.writeNoException();
                        return true;
                    case 9:
                        parcel.enforceInterface("com.yfve.ici.app.reminder.IReminderProxy");
                        editReminder(parcel.readInt() != 0 ? ReminderInfo.CREATOR.createFromParcel(parcel) : null);
                        parcel2.writeNoException();
                        return true;
                    case 10:
                        parcel.enforceInterface("com.yfve.ici.app.reminder.IReminderProxy");
                        setDate(parcel.readString());
                        parcel2.writeNoException();
                        return true;
                    case 11:
                        parcel.enforceInterface("com.yfve.ici.app.reminder.IReminderProxy");
                        openReminder(parcel.readLong());
                        parcel2.writeNoException();
                        return true;
                    default:
                        return super.onTransact(i, parcel, parcel2, i2);
                }
            }
            parcel2.writeString("com.yfve.ici.app.reminder.IReminderProxy");
            return true;
        }
    }

    void addReminder(ReminderInfo reminderInfo) throws RemoteException;

    void deleteReminder(ReminderInfo reminderInfo) throws RemoteException;

    void editReminder(ReminderInfo reminderInfo) throws RemoteException;

    void getReminderList(long j) throws RemoteException;

    void openReminder(long j) throws RemoteException;

    void registerReminderEditListener(IReminderEditListener iReminderEditListener) throws RemoteException;

    void registerReminderListener(IReminderListener iReminderListener) throws RemoteException;

    void setDate(String str) throws RemoteException;

    void syncCalendar() throws RemoteException;

    void unregisterReminderEditListener(IReminderEditListener iReminderEditListener) throws RemoteException;

    void unregisterReminderListener(IReminderListener iReminderListener) throws RemoteException;
}
