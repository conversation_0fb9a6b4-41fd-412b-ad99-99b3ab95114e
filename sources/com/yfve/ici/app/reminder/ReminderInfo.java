package com.yfve.ici.app.reminder;

import android.os.Parcel;
import android.os.Parcelable;
import b.a.b.a.a;
import java.util.Objects;

/* loaded from: classes.dex */
public class ReminderInfo implements Parcelable {
    public static final Parcelable.Creator<ReminderInfo> CREATOR = new Parcelable.Creator<ReminderInfo>() { // from class: com.yfve.ici.app.reminder.ReminderInfo.1
        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public ReminderInfo createFromParcel(Parcel parcel) {
            return new ReminderInfo(parcel);
        }

        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public ReminderInfo[] newArray(int i) {
            return new ReminderInfo[i];
        }
    };
    public String alertDay;
    public long alertTime;
    public String audioUrl;
    public String batchId;
    public String contacts;
    public String createBy;
    public int endTimeType;
    public String exceptionalReminder;
    public boolean expired;
    public String file;
    public long hasTriggeredTime;
    public boolean isTrigger;
    public String isTrriger;
    public String lastModifiedTime;
    public String lat;
    public String lng;
    public int mid;
    public String phone;
    public String poi;
    public String reminderId;
    public String reminderType;
    public String source;
    public String text;
    public long triggerAlertTime;
    public String triggerCycle;
    public String triggerEndDate;
    public long triggerEndTime;
    public String triggerIgnition;
    public String triggerStartDate;
    public long triggerStartTime;
    public String triggerTime;
    public String triggerTimeType;
    public String triggerType;
    public String updateSingle;

    public ReminderInfo() {
        this.reminderId = "";
        this.triggerTimeType = "0";
    }

    @Override // android.os.Parcelable
    public int describeContents() {
        return 0;
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || ReminderInfo.class != obj.getClass()) {
            return false;
        }
        ReminderInfo reminderInfo = (ReminderInfo) obj;
        return this.mid == reminderInfo.mid && Objects.equals(this.reminderId, reminderInfo.reminderId);
    }

    public String getAlertDay() {
        return this.alertDay;
    }

    public long getAlertTime() {
        return this.alertTime;
    }

    public String getAudioUrl() {
        return this.audioUrl;
    }

    public String getBatchId() {
        return this.batchId;
    }

    public String getContacts() {
        return this.contacts;
    }

    public String getCreateBy() {
        return this.createBy;
    }

    public int getEndTimeType() {
        return this.endTimeType;
    }

    public String getExceptionalReminder() {
        return this.exceptionalReminder;
    }

    public String getFile() {
        return this.file;
    }

    public long getHasTriggeredTime() {
        return this.hasTriggeredTime;
    }

    public String getIsTrriger() {
        return this.isTrriger;
    }

    public String getLastModifiedTime() {
        return this.lastModifiedTime;
    }

    public String getLat() {
        return this.lat;
    }

    public String getLng() {
        return this.lng;
    }

    public int getMid() {
        return this.mid;
    }

    public String getPhone() {
        return this.phone;
    }

    public String getPoi() {
        return this.poi;
    }

    public String getReminderId() {
        return this.reminderId;
    }

    public String getReminderType() {
        return this.reminderType;
    }

    public String getSource() {
        return this.source;
    }

    public String getText() {
        return this.text;
    }

    public long getTriggerAlertTime() {
        return this.triggerAlertTime;
    }

    public String getTriggerCycle() {
        return this.triggerCycle;
    }

    public String getTriggerEndDate() {
        return this.triggerEndDate;
    }

    public long getTriggerEndTime() {
        return this.triggerEndTime;
    }

    public String getTriggerIgnition() {
        return this.triggerIgnition;
    }

    public String getTriggerStartDate() {
        return this.triggerStartDate;
    }

    public long getTriggerStartTime() {
        return this.triggerStartTime;
    }

    public String getTriggerTime() {
        return this.triggerTime;
    }

    public String getTriggerTimeType() {
        return this.triggerTimeType;
    }

    public String getTriggerType() {
        return this.triggerType;
    }

    public String getUpdateSingle() {
        return this.updateSingle;
    }

    public int hashCode() {
        return Objects.hash(Integer.valueOf(this.mid), this.reminderId);
    }

    public boolean isExpired() {
        return this.expired;
    }

    public boolean isTrigger() {
        return this.isTrigger;
    }

    public void setAlertDay(String str) {
        this.alertDay = str;
    }

    public void setAlertTime(long j) {
        this.alertTime = j;
    }

    public void setAudioUrl(String str) {
        this.audioUrl = str;
    }

    public void setBatchId(String str) {
        this.batchId = str;
    }

    public void setContacts(String str) {
        this.contacts = str;
    }

    public void setCreateBy(String str) {
        this.createBy = str;
    }

    public void setEndTimeType(int i) {
        this.endTimeType = i;
    }

    public void setExceptionalReminder(String str) {
        this.exceptionalReminder = str;
    }

    public void setExpired(boolean z) {
        this.expired = z;
    }

    public void setFile(String str) {
        this.file = str;
    }

    public void setHasTriggeredTime(long j) {
        this.hasTriggeredTime = j;
    }

    public void setIsTrriger(String str) {
        this.isTrriger = str;
    }

    public void setLastModifiedTime(String str) {
        this.lastModifiedTime = str;
    }

    public void setLat(String str) {
        this.lat = str;
    }

    public void setLng(String str) {
        this.lng = str;
    }

    public void setMid(int i) {
        this.mid = i;
    }

    public void setPhone(String str) {
        this.phone = str;
    }

    public void setPoi(String str) {
        this.poi = str;
    }

    public void setReminderId(String str) {
        this.reminderId = str;
    }

    public void setReminderType(String str) {
        this.reminderType = str;
    }

    public void setSource(String str) {
        this.source = str;
    }

    public void setText(String str) {
        this.text = str;
    }

    public void setTrigger(boolean z) {
        this.isTrigger = z;
    }

    public void setTriggerAlertTime(long j) {
        this.triggerAlertTime = j;
    }

    public void setTriggerCycle(String str) {
        this.triggerCycle = str;
    }

    public void setTriggerEndDate(String str) {
        this.triggerEndDate = str;
    }

    public void setTriggerEndTime(long j) {
        this.triggerEndTime = j;
    }

    public void setTriggerIgnition(String str) {
        this.triggerIgnition = str;
    }

    public void setTriggerStartDate(String str) {
        this.triggerStartDate = str;
    }

    public void setTriggerStartTime(long j) {
        this.triggerStartTime = j;
    }

    public void setTriggerTime(String str) {
        this.triggerTime = str;
    }

    public void setTriggerTimeType(String str) {
        this.triggerTimeType = str;
    }

    public void setTriggerType(String str) {
        this.triggerType = str;
    }

    public void setUpdateSingle(String str) {
        this.updateSingle = str;
    }

    public String toString() {
        StringBuilder e = a.e("ReminderInfo{mid=");
        e.append(this.mid);
        e.append(", reminderId='");
        a.o(e, this.reminderId, '\'', ", triggerType='");
        a.o(e, this.triggerType, '\'', ", triggerStartDate='");
        a.o(e, this.triggerStartDate, '\'', ", triggerEndDate='");
        a.o(e, this.triggerEndDate, '\'', ", triggerIgnition='");
        a.o(e, this.triggerIgnition, '\'', ", triggerTime='");
        a.o(e, this.triggerTime, '\'', ", triggerCycle='");
        a.o(e, this.triggerCycle, '\'', ", text='");
        a.o(e, this.text, '\'', ", reminderType='");
        a.o(e, this.reminderType, '\'', ", poi='");
        a.o(e, this.poi, '\'', ", lng='");
        a.o(e, this.lng, '\'', ", lat='");
        a.o(e, this.lat, '\'', ", file='");
        a.o(e, this.file, '\'', ", audioUrl='");
        a.o(e, this.audioUrl, '\'', ", triggerTimeType='");
        a.o(e, this.triggerTimeType, '\'', ", batchId='");
        a.o(e, this.batchId, '\'', ", exceptionalReminder='");
        a.o(e, this.exceptionalReminder, '\'', ", isTrigger=");
        e.append(this.isTrigger);
        e.append(", phone='");
        a.o(e, this.phone, '\'', ", source='");
        a.o(e, this.source, '\'', ", isTrriger='");
        a.o(e, this.isTrriger, '\'', ", contacts='");
        a.o(e, this.contacts, '\'', ", lastModifiedTime='");
        a.o(e, this.lastModifiedTime, '\'', ", createBy='");
        a.o(e, this.createBy, '\'', ", updateSingle='");
        a.o(e, this.updateSingle, '\'', ", triggerStartTime=");
        e.append(this.triggerStartTime);
        e.append(", triggerEndTime=");
        e.append(this.triggerEndTime);
        e.append(", triggerAlertTime=");
        e.append(this.triggerAlertTime);
        e.append(", alertDay='");
        a.o(e, this.alertDay, '\'', ", alertTime=");
        e.append(this.alertTime);
        e.append(", hasTriggeredTime=");
        e.append(this.hasTriggeredTime);
        e.append(", endTimeType=");
        e.append(this.endTimeType);
        e.append(", expired=");
        e.append(this.expired);
        e.append('}');
        return e.toString();
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeInt(this.mid);
        parcel.writeString(this.reminderId);
        parcel.writeString(this.triggerType);
        parcel.writeString(this.triggerStartDate);
        parcel.writeString(this.triggerEndDate);
        parcel.writeString(this.triggerIgnition);
        parcel.writeString(this.triggerTime);
        parcel.writeString(this.triggerCycle);
        parcel.writeString(this.text);
        parcel.writeString(this.reminderType);
        parcel.writeString(this.poi);
        parcel.writeString(this.lng);
        parcel.writeString(this.lat);
        parcel.writeString(this.file);
        parcel.writeString(this.audioUrl);
        parcel.writeString(this.triggerTimeType);
        parcel.writeString(this.batchId);
        parcel.writeString(this.exceptionalReminder);
        parcel.writeByte(this.isTrigger ? (byte) 1 : (byte) 0);
        parcel.writeString(this.phone);
        parcel.writeString(this.source);
        parcel.writeString(this.isTrriger);
        parcel.writeString(this.contacts);
        parcel.writeString(this.lastModifiedTime);
        parcel.writeString(this.createBy);
        parcel.writeString(this.updateSingle);
        parcel.writeLong(this.triggerStartTime);
        parcel.writeLong(this.triggerEndTime);
        parcel.writeString(this.alertDay);
        parcel.writeLong(this.alertTime);
        parcel.writeLong(this.hasTriggeredTime);
        parcel.writeInt(this.endTimeType);
        parcel.writeByte(this.expired ? (byte) 1 : (byte) 0);
        parcel.writeLong(this.triggerAlertTime);
    }

    public ReminderInfo(Parcel parcel) {
        this.reminderId = "";
        this.triggerTimeType = "0";
        this.mid = parcel.readInt();
        this.reminderId = parcel.readString();
        this.triggerType = parcel.readString();
        this.triggerStartDate = parcel.readString();
        this.triggerEndDate = parcel.readString();
        this.triggerIgnition = parcel.readString();
        this.triggerTime = parcel.readString();
        this.triggerCycle = parcel.readString();
        this.text = parcel.readString();
        this.reminderType = parcel.readString();
        this.poi = parcel.readString();
        this.lng = parcel.readString();
        this.lat = parcel.readString();
        this.file = parcel.readString();
        this.audioUrl = parcel.readString();
        this.triggerTimeType = parcel.readString();
        this.batchId = parcel.readString();
        this.exceptionalReminder = parcel.readString();
        this.isTrigger = parcel.readByte() != 0;
        this.phone = parcel.readString();
        this.source = parcel.readString();
        this.isTrriger = parcel.readString();
        this.contacts = parcel.readString();
        this.lastModifiedTime = parcel.readString();
        this.createBy = parcel.readString();
        this.updateSingle = parcel.readString();
        this.triggerStartTime = parcel.readLong();
        this.triggerEndTime = parcel.readLong();
        this.alertDay = parcel.readString();
        this.alertTime = parcel.readLong();
        this.hasTriggeredTime = parcel.readLong();
        this.endTimeType = parcel.readInt();
        this.expired = parcel.readByte() != 0;
        this.triggerAlertTime = parcel.readLong();
    }
}
