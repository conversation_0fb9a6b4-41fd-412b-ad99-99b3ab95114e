package com.yfve.ici.app.reminder;

import android.os.RemoteException;
import android.util.Log;
import b.a.b.a.a;
import com.yfve.ici.app.reminder.IReminderEditListener;
import com.yfve.ici.app.reminder.IReminderListener;
import com.yfve.ici.service.base.BaseProxy;
import com.yfve.ici.service.contanst.ServiceConstant;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/* loaded from: classes.dex */
public class ReminderProxy extends BaseProxy<IReminderProxy> {
    public static final String TAG = "ReminderProxy";
    public static ReminderProxy mInstance;
    public IReminderEditListener.Stub mIReminderEditListenerAIDL;
    public IReminderListener.Stub mIReminderListenerAIDL;
    public List<IReminderListener> mReminderCallbackList = new CopyOnWriteArrayList();
    public List<IReminderEditListener> mReminderEditCallbackList = new CopyOnWriteArrayList();

    public static ReminderProxy getInstance() {
        if (mInstance == null) {
            synchronized (ReminderProxy.class) {
                if (mInstance == null) {
                    mInstance = new ReminderProxy();
                }
            }
        }
        return mInstance;
    }

    public void addReminder(ReminderInfo reminderInfo) {
        Log.i(TAG, "addReminder. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "addReminder~~mInterface is null");
            } else {
                ((IReminderProxy) this.mInterface).addReminder(reminderInfo);
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public void deleteReminder(ReminderInfo reminderInfo) {
        Log.i(TAG, "deleteReminder. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "deleteReminder~~mInterface is null");
            } else {
                ((IReminderProxy) this.mInterface).deleteReminder(reminderInfo);
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public void getReminderList(long j) {
        Log.i(TAG, "getReminderList. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "getReminderList~~mInterface is null");
            } else {
                ((IReminderProxy) this.mInterface).getReminderList(j);
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public String getServiceName() {
        return ServiceConstant.REMINDER_BINDER_NAME;
    }

    public void openReminder(long j) {
        Log.i(TAG, "openReminder: ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "openReminder~~mInterface is null");
            } else {
                ((IReminderProxy) this.mInterface).openReminder(j);
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public int registerReminderEditListener(IReminderEditListener iReminderEditListener) {
        Log.i(TAG, "registerReminderEditListener in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerReminderEditListener~~mInterface is null");
                return -2147483646;
            }
            if (iReminderEditListener != null && !this.mReminderEditCallbackList.contains(iReminderEditListener)) {
                this.mReminderEditCallbackList.add(iReminderEditListener);
                Log.i(TAG, "registerReminderEditListener add.");
                if (this.mReminderCallbackList.size() == 1) {
                    IReminderEditListener.Stub stub = new IReminderEditListener.Stub() { // from class: com.yfve.ici.app.reminder.ReminderProxy.2
                        @Override // com.yfve.ici.app.reminder.IReminderEditListener
                        public void onReminderAutoDelete(List<ReminderInfo> list) throws RemoteException {
                            String str = ReminderProxy.TAG;
                            StringBuilder e = a.e("onReminderAutoDelete is called. Reminder:");
                            e.append(list.size());
                            Log.d(str, e.toString());
                            for (IReminderEditListener iReminderEditListener2 : ReminderProxy.this.mReminderEditCallbackList) {
                                iReminderEditListener2.onReminderAutoDelete(list);
                            }
                        }

                        @Override // com.yfve.ici.app.reminder.IReminderEditListener
                        public void onReminderDelete(ReminderInfo reminderInfo) throws RemoteException {
                            String str = ReminderProxy.TAG;
                            Log.d(str, "onReminderDelete is called. Reminder:" + reminderInfo);
                            for (IReminderEditListener iReminderEditListener2 : ReminderProxy.this.mReminderEditCallbackList) {
                                iReminderEditListener2.onReminderDelete(reminderInfo);
                            }
                        }

                        @Override // com.yfve.ici.app.reminder.IReminderEditListener
                        public void onReminderEdit(ReminderInfo reminderInfo) throws RemoteException {
                            String str = ReminderProxy.TAG;
                            Log.d(str, "onReminderEdit is called. Reminder:" + reminderInfo);
                            for (IReminderEditListener iReminderEditListener2 : ReminderProxy.this.mReminderEditCallbackList) {
                                iReminderEditListener2.onReminderEdit(reminderInfo);
                            }
                        }
                    };
                    this.mIReminderEditListenerAIDL = stub;
                    ((IReminderProxy) this.mInterface).registerReminderEditListener(stub);
                    Log.i(TAG, "registerReminderEditListener AIDL.");
                    return Integer.MIN_VALUE;
                }
            }
            return -2147418113;
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int registerReminderListener(IReminderListener iReminderListener) {
        Log.i(TAG, "registerReminderListener in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "registerReminderListener~~mInterface is null");
                return -2147483646;
            }
            if (iReminderListener != null && !this.mReminderCallbackList.contains(iReminderListener)) {
                this.mReminderCallbackList.add(iReminderListener);
                Log.i(TAG, "registerReminderListener add.");
                if (this.mReminderCallbackList.size() == 1) {
                    IReminderListener.Stub stub = new IReminderListener.Stub() { // from class: com.yfve.ici.app.reminder.ReminderProxy.1
                        @Override // com.yfve.ici.app.reminder.IReminderListener
                        public void onReminderAdd(ReminderInfo reminderInfo) throws RemoteException {
                            String str = ReminderProxy.TAG;
                            Log.d(str, "onReminderAdd is called. Reminder:" + reminderInfo);
                            for (IReminderListener iReminderListener2 : ReminderProxy.this.mReminderCallbackList) {
                                iReminderListener2.onReminderAdd(reminderInfo);
                            }
                        }

                        @Override // com.yfve.ici.app.reminder.IReminderListener
                        public void onReminderDelete(ReminderInfo reminderInfo) throws RemoteException {
                            String str = ReminderProxy.TAG;
                            Log.d(str, "onReminderDelete is called. Reminder:" + reminderInfo);
                            for (IReminderListener iReminderListener2 : ReminderProxy.this.mReminderCallbackList) {
                                iReminderListener2.onReminderDelete(reminderInfo);
                            }
                        }

                        @Override // com.yfve.ici.app.reminder.IReminderListener
                        public void onSyncCalendar() throws RemoteException {
                            Log.d(ReminderProxy.TAG, "onSyncCalendar is called. ");
                            for (IReminderListener iReminderListener2 : ReminderProxy.this.mReminderCallbackList) {
                                iReminderListener2.onSyncCalendar();
                            }
                        }

                        @Override // com.yfve.ici.app.reminder.IReminderListener
                        public void onSyncReminder(List<ReminderInfo> list) throws RemoteException {
                            String str = ReminderProxy.TAG;
                            Log.d(str, "onSyncReminder is called. Reminder:" + list);
                            for (IReminderListener iReminderListener2 : ReminderProxy.this.mReminderCallbackList) {
                                iReminderListener2.onSyncReminder(list);
                            }
                        }
                    };
                    this.mIReminderListenerAIDL = stub;
                    ((IReminderProxy) this.mInterface).registerReminderListener(stub);
                    Log.i(TAG, "registerReminderListener AIDL.");
                    return Integer.MIN_VALUE;
                }
            }
            return -2147418113;
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }

    public void setDate(String str) {
        Log.i(TAG, "setDate. ");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "setDate~~mInterface is null");
            } else {
                ((IReminderProxy) this.mInterface).setDate(str);
            }
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
        }
    }

    public int unregisterReminderEditListener(IReminderEditListener iReminderEditListener) {
        Log.i(TAG, "unregisterReminderEditListener in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unregisterReminderEditListener~~mInterface is null");
                return -2147483646;
            }
            if (iReminderEditListener != null && this.mReminderEditCallbackList.contains(iReminderEditListener)) {
                this.mReminderEditCallbackList.remove(iReminderEditListener);
                if (this.mReminderEditCallbackList.size() == 0) {
                    ((IReminderProxy) this.mInterface).unregisterReminderEditListener(this.mIReminderEditListenerAIDL);
                    this.mIReminderEditListenerAIDL = null;
                    return Integer.MIN_VALUE;
                }
            }
            return -2147418113;
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }

    public int unregisterReminderListener(IReminderListener iReminderListener) {
        Log.i(TAG, "unregisterReminderListener in.");
        try {
            if (!isAvailable()) {
                Log.e(TAG, "unregisterReminderListener~~mInterface is null");
                return -2147483646;
            }
            if (iReminderListener != null && this.mReminderCallbackList.contains(iReminderListener)) {
                this.mReminderCallbackList.remove(iReminderListener);
                if (this.mReminderCallbackList.size() == 0) {
                    ((IReminderProxy) this.mInterface).unregisterReminderListener(this.mIReminderListenerAIDL);
                    this.mIReminderListenerAIDL = null;
                    return Integer.MIN_VALUE;
                }
            }
            return -2147418113;
        } catch (RemoteException e) {
            Log.i(TAG, e.toString());
            return -2147483646;
        } catch (Exception e2) {
            Log.i(TAG, e2.toString());
            return -2147418113;
        }
    }
}
