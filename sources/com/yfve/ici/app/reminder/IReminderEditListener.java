package com.yfve.ici.app.reminder;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import java.util.List;

/* loaded from: classes.dex */
public interface IReminderEditListener extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IReminderEditListener {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.reminder.IReminderEditListener
        public void onReminderAutoDelete(List<ReminderInfo> list) throws RemoteException {
        }

        @Override // com.yfve.ici.app.reminder.IReminderEditListener
        public void onReminderDelete(ReminderInfo reminderInfo) throws RemoteException {
        }

        @Override // com.yfve.ici.app.reminder.IReminderEditListener
        public void onReminderEdit(ReminderInfo reminderInfo) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IReminderEditListener {
        public static final String DESCRIPTOR = "com.yfve.ici.app.reminder.IReminderEditListener";
        public static final int TRANSACTION_onReminderAutoDelete = 3;
        public static final int TRANSACTION_onReminderDelete = 2;
        public static final int TRANSACTION_onReminderEdit = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements IReminderEditListener {
            public static IReminderEditListener sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            @Override // com.yfve.ici.app.reminder.IReminderEditListener
            public void onReminderAutoDelete(List<ReminderInfo> list) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeTypedList(list);
                    if (!this.mRemote.transact(3, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onReminderAutoDelete(list);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.reminder.IReminderEditListener
            public void onReminderDelete(ReminderInfo reminderInfo) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (reminderInfo != null) {
                        obtain.writeInt(1);
                        reminderInfo.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onReminderDelete(reminderInfo);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.reminder.IReminderEditListener
            public void onReminderEdit(ReminderInfo reminderInfo) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (reminderInfo != null) {
                        obtain.writeInt(1);
                        reminderInfo.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onReminderEdit(reminderInfo);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static IReminderEditListener asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof IReminderEditListener)) {
                return (IReminderEditListener) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IReminderEditListener getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IReminderEditListener iReminderEditListener) {
            if (Proxy.sDefaultImpl != null || iReminderEditListener == null) {
                return false;
            }
            Proxy.sDefaultImpl = iReminderEditListener;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i == 1) {
                parcel.enforceInterface(DESCRIPTOR);
                onReminderEdit(parcel.readInt() != 0 ? ReminderInfo.CREATOR.createFromParcel(parcel) : null);
                parcel2.writeNoException();
                return true;
            } else if (i == 2) {
                parcel.enforceInterface(DESCRIPTOR);
                onReminderDelete(parcel.readInt() != 0 ? ReminderInfo.CREATOR.createFromParcel(parcel) : null);
                parcel2.writeNoException();
                return true;
            } else if (i != 3) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString(DESCRIPTOR);
                return true;
            } else {
                parcel.enforceInterface(DESCRIPTOR);
                onReminderAutoDelete(parcel.createTypedArrayList(ReminderInfo.CREATOR));
                parcel2.writeNoException();
                return true;
            }
        }
    }

    void onReminderAutoDelete(List<ReminderInfo> list) throws RemoteException;

    void onReminderDelete(ReminderInfo reminderInfo) throws RemoteException;

    void onReminderEdit(ReminderInfo reminderInfo) throws RemoteException;
}
