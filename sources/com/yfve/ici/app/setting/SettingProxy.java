package com.yfve.ici.app.setting;

import android.os.RemoteException;
import android.util.Log;
import b.a.b.a.a;
import com.yfve.ici.service.base.BaseProxy;
import com.yfve.ici.service.contanst.ServiceConstant;

/* loaded from: classes.dex */
public class SettingProxy extends BaseProxy<ISettingProxy> {
    public static final String TAG = "SettingProxy";
    public static SettingProxy mSettingProxy;

    public static synchronized SettingProxy getInstance() {
        SettingProxy settingProxy;
        synchronized (SettingProxy.class) {
            if (mSettingProxy == null) {
                mSettingProxy = new SettingProxy();
            }
            settingProxy = mSettingProxy;
        }
        return settingProxy;
    }

    public String getCurrentView() {
        try {
            if (!isAvailable()) {
                Log.e(TAG, "setting~~mInterface is null");
                return "";
            }
            return ((ISettingProxy) this.mInterface).getCurrentView();
        } catch (RemoteException e) {
            e.printStackTrace();
            return "";
        }
    }

    public boolean getHostIsHide() {
        try {
            if (!isAvailable()) {
                Log.e(TAG, "setting~~mInterface is null");
                return false;
            }
            return ((ISettingProxy) this.mInterface).getHostIsHide();
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean getHostState() {
        try {
            if (!isAvailable()) {
                Log.e(TAG, "setting~~mInterface is null");
                return false;
            }
            return ((ISettingProxy) this.mInterface).getHostState();
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public String getServiceName() {
        return ServiceConstant.SETTING_BINDER_NAME;
    }

    public boolean getWifiState() {
        try {
            if (!isAvailable()) {
                Log.e(TAG, "setting~~mInterface is null");
                return false;
            }
            return ((ISettingProxy) this.mInterface).getWifiState();
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    public void openCarFragment() {
        try {
            if (!isAvailable()) {
                Log.e(TAG, "setting~~mInterface is null");
            } else {
                ((ISettingProxy) this.mInterface).openCarFragment();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void openDisplay() {
        try {
            if (!isAvailable()) {
                Log.e(TAG, "setting~~mInterface is null");
            } else {
                ((ISettingProxy) this.mInterface).openDisplay();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void openSetting() {
        try {
            if (!isAvailable()) {
                Log.e(TAG, "setting~~mInterface is null");
            } else {
                ((ISettingProxy) this.mInterface).openSetting();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void openVoice() {
        try {
            if (!isAvailable()) {
                Log.e(TAG, "setting~~mInterface is null");
            } else {
                ((ISettingProxy) this.mInterface).openVoice();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void openVoiceAssistant() {
        try {
            if (!isAvailable()) {
                Log.e(TAG, "setting~~mInterface is null");
            } else {
                ((ISettingProxy) this.mInterface).openVoiceAssistant();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void openWifiList() {
        try {
            if (!isAvailable()) {
                Log.e(TAG, "setting~~mInterface is null");
            } else {
                ((ISettingProxy) this.mInterface).openWifiList();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void opentLanguage() {
        try {
            if (!isAvailable()) {
                Log.e(TAG, "setting~~mInterface is null");
            } else {
                ((ISettingProxy) this.mInterface).opentLanguage();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void setWifiApEnabled(boolean z) {
        a.l(" setWifiApEnabled  enabled:", z, TAG);
        try {
            if (!isAvailable()) {
                Log.e(TAG, "setting~~mInterface is null");
            } else {
                ((ISettingProxy) this.mInterface).setWifiApEnabled(z);
            }
        } catch (Exception e) {
            Log.i(TAG, e.toString());
        }
    }

    public void setWifiEnabled(boolean z) {
        a.l(" setWifiEnabled  enabled:", z, TAG);
        try {
            if (!isAvailable()) {
                Log.e(TAG, "setting~~mInterface is null");
            } else {
                ((ISettingProxy) this.mInterface).setWifiEnabled(z);
            }
        } catch (Exception e) {
            Log.i(TAG, e.toString());
        }
    }
}
