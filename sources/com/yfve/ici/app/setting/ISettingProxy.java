package com.yfve.ici.app.setting;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface ISettingProxy extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements ISettingProxy {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.app.setting.ISettingProxy
        public String getCurrentView() throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.app.setting.ISettingProxy
        public boolean getHostIsHide() throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.setting.ISettingProxy
        public boolean getHostState() throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.setting.ISettingProxy
        public boolean getWifiState() throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.app.setting.ISettingProxy
        public void openCarFragment() throws RemoteException {
        }

        @Override // com.yfve.ici.app.setting.ISettingProxy
        public void openDisplay() throws RemoteException {
        }

        @Override // com.yfve.ici.app.setting.ISettingProxy
        public void openSetting() throws RemoteException {
        }

        @Override // com.yfve.ici.app.setting.ISettingProxy
        public void openVoice() throws RemoteException {
        }

        @Override // com.yfve.ici.app.setting.ISettingProxy
        public void openVoiceAssistant() throws RemoteException {
        }

        @Override // com.yfve.ici.app.setting.ISettingProxy
        public void openWifiList() throws RemoteException {
        }

        @Override // com.yfve.ici.app.setting.ISettingProxy
        public void opentLanguage() throws RemoteException {
        }

        @Override // com.yfve.ici.app.setting.ISettingProxy
        public void setWifiApEnabled(boolean z) throws RemoteException {
        }

        @Override // com.yfve.ici.app.setting.ISettingProxy
        public void setWifiEnabled(boolean z) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements ISettingProxy {
        public static final String DESCRIPTOR = "com.yfve.ici.app.setting.ISettingProxy";
        public static final int TRANSACTION_getCurrentView = 13;
        public static final int TRANSACTION_getHostIsHide = 12;
        public static final int TRANSACTION_getHostState = 11;
        public static final int TRANSACTION_getWifiState = 10;
        public static final int TRANSACTION_openCarFragment = 9;
        public static final int TRANSACTION_openDisplay = 5;
        public static final int TRANSACTION_openSetting = 3;
        public static final int TRANSACTION_openVoice = 6;
        public static final int TRANSACTION_openVoiceAssistant = 8;
        public static final int TRANSACTION_openWifiList = 4;
        public static final int TRANSACTION_opentLanguage = 7;
        public static final int TRANSACTION_setWifiApEnabled = 2;
        public static final int TRANSACTION_setWifiEnabled = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements ISettingProxy {
            public static ISettingProxy sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            @Override // com.yfve.ici.app.setting.ISettingProxy
            public String getCurrentView() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(13, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getCurrentView();
                    }
                    obtain2.readException();
                    return obtain2.readString();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.setting.ISettingProxy
            public boolean getHostIsHide() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(12, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getHostIsHide();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.setting.ISettingProxy
            public boolean getHostState() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(11, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getHostState();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            @Override // com.yfve.ici.app.setting.ISettingProxy
            public boolean getWifiState() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(10, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getWifiState();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.setting.ISettingProxy
            public void openCarFragment() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(9, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().openCarFragment();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.setting.ISettingProxy
            public void openDisplay() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(5, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().openDisplay();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.setting.ISettingProxy
            public void openSetting() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(3, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().openSetting();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.setting.ISettingProxy
            public void openVoice() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(6, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().openVoice();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.setting.ISettingProxy
            public void openVoiceAssistant() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(8, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().openVoiceAssistant();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.setting.ISettingProxy
            public void openWifiList() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(4, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().openWifiList();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.setting.ISettingProxy
            public void opentLanguage() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(7, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().opentLanguage();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.setting.ISettingProxy
            public void setWifiApEnabled(boolean z) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeInt(z ? 1 : 0);
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().setWifiApEnabled(z);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.app.setting.ISettingProxy
            public void setWifiEnabled(boolean z) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeInt(z ? 1 : 0);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().setWifiEnabled(z);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static ISettingProxy asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof ISettingProxy)) {
                return (ISettingProxy) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static ISettingProxy getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(ISettingProxy iSettingProxy) {
            if (Proxy.sDefaultImpl != null || iSettingProxy == null) {
                return false;
            }
            Proxy.sDefaultImpl = iSettingProxy;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1598968902) {
                switch (i) {
                    case 1:
                        parcel.enforceInterface(DESCRIPTOR);
                        setWifiEnabled(parcel.readInt() != 0);
                        parcel2.writeNoException();
                        return true;
                    case 2:
                        parcel.enforceInterface(DESCRIPTOR);
                        setWifiApEnabled(parcel.readInt() != 0);
                        parcel2.writeNoException();
                        return true;
                    case 3:
                        parcel.enforceInterface(DESCRIPTOR);
                        openSetting();
                        parcel2.writeNoException();
                        return true;
                    case 4:
                        parcel.enforceInterface(DESCRIPTOR);
                        openWifiList();
                        parcel2.writeNoException();
                        return true;
                    case 5:
                        parcel.enforceInterface(DESCRIPTOR);
                        openDisplay();
                        parcel2.writeNoException();
                        return true;
                    case 6:
                        parcel.enforceInterface(DESCRIPTOR);
                        openVoice();
                        parcel2.writeNoException();
                        return true;
                    case 7:
                        parcel.enforceInterface(DESCRIPTOR);
                        opentLanguage();
                        parcel2.writeNoException();
                        return true;
                    case 8:
                        parcel.enforceInterface(DESCRIPTOR);
                        openVoiceAssistant();
                        parcel2.writeNoException();
                        return true;
                    case 9:
                        parcel.enforceInterface(DESCRIPTOR);
                        openCarFragment();
                        parcel2.writeNoException();
                        return true;
                    case 10:
                        parcel.enforceInterface(DESCRIPTOR);
                        boolean wifiState = getWifiState();
                        parcel2.writeNoException();
                        parcel2.writeInt(wifiState ? 1 : 0);
                        return true;
                    case 11:
                        parcel.enforceInterface(DESCRIPTOR);
                        boolean hostState = getHostState();
                        parcel2.writeNoException();
                        parcel2.writeInt(hostState ? 1 : 0);
                        return true;
                    case 12:
                        parcel.enforceInterface(DESCRIPTOR);
                        boolean hostIsHide = getHostIsHide();
                        parcel2.writeNoException();
                        parcel2.writeInt(hostIsHide ? 1 : 0);
                        return true;
                    case 13:
                        parcel.enforceInterface(DESCRIPTOR);
                        String currentView = getCurrentView();
                        parcel2.writeNoException();
                        parcel2.writeString(currentView);
                        return true;
                    default:
                        return super.onTransact(i, parcel, parcel2, i2);
                }
            }
            parcel2.writeString(DESCRIPTOR);
            return true;
        }
    }

    String getCurrentView() throws RemoteException;

    boolean getHostIsHide() throws RemoteException;

    boolean getHostState() throws RemoteException;

    boolean getWifiState() throws RemoteException;

    void openCarFragment() throws RemoteException;

    void openDisplay() throws RemoteException;

    void openSetting() throws RemoteException;

    void openVoice() throws RemoteException;

    void openVoiceAssistant() throws RemoteException;

    void openWifiList() throws RemoteException;

    void opentLanguage() throws RemoteException;

    void setWifiApEnabled(boolean z) throws RemoteException;

    void setWifiEnabled(boolean z) throws RemoteException;
}
