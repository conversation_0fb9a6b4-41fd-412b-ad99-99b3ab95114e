package com.yfve.ici.service.base;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.Message;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;
import b.a.b.a.a;
import com.yfve.ici.service.servicemgr.IServiceConnChangeListener;
import com.yfve.ici.service.servicemgr.IServiceConnListener;
import com.yfve.ici.service.servicemgr.ServiceMgrProxy;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/* loaded from: classes.dex */
public abstract class BaseProxy<T> {
    public static final int NOTIFY_SERVICE_CONNECT_STATUS = 1;
    public static final int RET_ERR_COMMAND_AMBIGUOUS = -2147483637;
    public static final int RET_ERR_DEVICE_UNCONNECTED = -2147483641;
    public static final int RET_ERR_FILE_EXCEPTION = -2147483634;
    public static final int RET_ERR_FUNCTION_UNINIT = -2147483640;
    public static final int RET_ERR_INTERNET_UNAVAILABLE = -2147483645;
    public static final int RET_ERR_INVALID_COMMAND = -2147483638;
    public static final int RET_ERR_KEY_EXPIRED = -2147483643;
    public static final int RET_ERR_KEY_INVALID = -2147483644;
    public static final int RET_ERR_LOCAL_SERVICE_UNCONNECTED = -2147483646;
    public static final int RET_ERR_MEDIA_TYPE_UINIT = -2147483639;
    public static final int RET_ERR_NO_DEVICE = -2147483636;
    public static final int RET_ERR_NO_FILE = -2147483635;
    public static final int RET_ERR_PARAMETER_EXCEPTION = -2147483647;
    public static final int RET_ERR_REMOTE_SERVICE_UNCONNECTED = -2147483642;
    public static final int RET_ERR_UNKNOWN = -2147418113;
    public static final int RET_OK = Integer.MIN_VALUE;
    public IBinder mClientBinder;
    public T mInterface;
    public BaseProxy<T>.ServiceConnListener mServiceConnListener;
    public final ServiceMgrProxy mServiceMngProxy;
    public String TAG = "CoreService BaseProxy";
    public HandlerThread mHandlerThread = null;
    public Handler mWorkHandler = null;
    public Object mBinderLock = new Object();
    public List<IServiceConnChangeListener> mServiceConnChangeListener = new CopyOnWriteArrayList();

    /* loaded from: classes.dex */
    public class ServiceConnListener extends IServiceConnListener.Stub {
        public ServiceConnListener() {
        }

        @Override // com.yfve.ici.service.servicemgr.IServiceConnListener
        public void onServiceConnStatusChanged(boolean z) throws RemoteException {
            String str = BaseProxy.this.TAG;
            StringBuilder e = a.e("ServiceConnListener.onServiceConnStatusChanged.svrName:");
            e.append(BaseProxy.this.getServiceName());
            e.append(" isConn:");
            e.append(z);
            Log.i(str, e.toString());
            if (BaseProxy.this.mWorkHandler == null) {
                Log.e(BaseProxy.this.TAG, "mWorkHandler != null");
                return;
            }
            Message obtain = Message.obtain();
            obtain.what = 1;
            obtain.arg1 = z ? 1 : 0;
            BaseProxy.this.mWorkHandler.sendMessage(obtain);
        }
    }

    public BaseProxy() {
        Log.i(this.TAG, "BaseProxy~~~~");
        initHandler();
        ServiceMgrProxy serviceMgrProxy = ServiceMgrProxy.getInstance();
        this.mServiceMngProxy = serviceMgrProxy;
        serviceMgrProxy.getService();
        getInterface();
        registerServiceConnListener(getServiceName());
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void handleServiceConnStatusChanged(boolean z) {
        String str = this.TAG;
        StringBuilder e = a.e("handleServiceConnStatusChanged. ServerName:");
        e.append(getServiceName());
        e.append(" isConnected:");
        e.append(z);
        Log.i(str, e.toString());
        if (z) {
            getInterface();
        } else {
            synchronized (this.mBinderLock) {
                this.mInterface = null;
                this.mClientBinder = null;
            }
        }
        onServiceConnectStatusChanged(z);
        notifyClientServiceConnChanged(z);
    }

    private void initHandler() {
        if (this.mHandlerThread == null) {
            HandlerThread handlerThread = new HandlerThread("svr_conn_sts_nty");
            this.mHandlerThread = handlerThread;
            handlerThread.start();
            if (this.mWorkHandler == null) {
                this.mWorkHandler = new Handler(this.mHandlerThread.getLooper()) { // from class: com.yfve.ici.service.base.BaseProxy.1
                    @Override // android.os.Handler
                    public void handleMessage(Message message) {
                        if (message.what != 1) {
                            Log.i(BaseProxy.this.TAG, "handleMessage default called......");
                        } else {
                            BaseProxy.this.handleServiceConnStatusChanged(message.arg1 == 1);
                        }
                    }
                };
            }
        }
    }

    private void notifyClientServiceConnChanged(boolean z) {
        String str = this.TAG;
        Log.d(str, "notifyClientServiceConnChanged~~~~isConn:" + z);
        for (int i = 0; i < this.mServiceConnChangeListener.size(); i++) {
            this.mServiceConnChangeListener.get(i).onServiceConnChange(z);
        }
    }

    public T getInterface() {
        ServiceMgrProxy serviceMgrProxy;
        if (TextUtils.isEmpty(getServiceName()) || (serviceMgrProxy = this.mServiceMngProxy) == null) {
            return null;
        }
        IBinder clientBinder = serviceMgrProxy.getClientBinder(getServiceName());
        if (clientBinder == null) {
            String str = this.TAG;
            StringBuilder e = a.e("getInterface (null == svrBinder : true). ServiceName:");
            e.append(getServiceName());
            Log.e(str, e.toString());
            return null;
        }
        synchronized (this.mBinderLock) {
            this.mClientBinder = clientBinder;
            String str2 = this.TAG;
            Log.d(str2, "getInterface~~~mClientBinder.isBinderAlive():" + this.mClientBinder.isBinderAlive() + ",mClientBinder.pingBinder():" + this.mClientBinder.pingBinder());
            Type[] actualTypeArguments = ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments();
            if (actualTypeArguments != null) {
                try {
                    try {
                        try {
                            Class<?> cls = Class.forName(actualTypeArguments[0].getTypeName() + "$Stub");
                            String str3 = this.TAG;
                            Log.d(str3, "getInterface~~~userClass:" + cls);
                            Method method = cls.getMethod("asInterface", IBinder.class);
                            String str4 = this.TAG;
                            Log.d(str4, "getInterface~~~method:" + method);
                            this.mInterface = (T) method.invoke(null, this.mClientBinder);
                            String str5 = this.TAG;
                            Log.d(str5, "getClientService~~~Success~~~mInterface:" + this.mInterface);
                        } catch (NoSuchMethodException e2) {
                            Log.e(this.TAG, e2.toString());
                        } catch (Exception e3) {
                            Log.e(this.TAG, e3.toString());
                        }
                    } catch (ClassNotFoundException e4) {
                        Log.e(this.TAG, e4.toString());
                    } catch (IllegalAccessException e5) {
                        Log.e(this.TAG, e5.toString());
                    }
                } catch (InvocationTargetException e6) {
                    Log.e(this.TAG, e6.toString());
                }
            }
        }
        return this.mInterface;
    }

    public abstract String getServiceName();

    public boolean isAvailable() {
        boolean z;
        boolean z2;
        synchronized (this.mBinderLock) {
            z = true;
            z2 = this.mInterface != null && this.mClientBinder != null && this.mClientBinder.isBinderAlive() && this.mClientBinder.pingBinder();
        }
        if (z2) {
            return z2;
        }
        getInterface();
        synchronized (this.mBinderLock) {
            if (this.mInterface == null || this.mClientBinder == null || !this.mClientBinder.isBinderAlive() || !this.mClientBinder.pingBinder()) {
                z = false;
            }
        }
        return z;
    }

    public void onServiceConnectStatusChanged(boolean z) {
        a.l("notifyServiceConnStatus status: ", z, this.TAG);
    }

    public void registerConnChangeListener(IServiceConnChangeListener iServiceConnChangeListener) {
        String str = this.TAG;
        Log.d(str, "registerConnChangeListener~~~listener:" + iServiceConnChangeListener);
        if (iServiceConnChangeListener == null || this.mServiceConnChangeListener.contains(iServiceConnChangeListener)) {
            return;
        }
        this.mServiceConnChangeListener.add(iServiceConnChangeListener);
    }

    public void registerService(String str, IBinder iBinder) {
        ServiceMgrProxy serviceMgrProxy;
        String str2 = this.TAG;
        Log.d(str2, "registerService~~~binderName:" + str + ",binder:" + iBinder);
        if (TextUtils.isEmpty(str) || iBinder == null || (serviceMgrProxy = this.mServiceMngProxy) == null) {
            return;
        }
        serviceMgrProxy.registerService(str, iBinder);
    }

    public void registerServiceConnListener(String str) {
        String str2 = this.TAG;
        Log.d(str2, "registerServiceConnListener~~~svrName:" + str);
        if (TextUtils.isEmpty(str) || this.mServiceMngProxy == null) {
            return;
        }
        BaseProxy<T>.ServiceConnListener serviceConnListener = new ServiceConnListener();
        this.mServiceConnListener = serviceConnListener;
        this.mServiceMngProxy.registerServiceConnListener(str, serviceConnListener);
    }

    public void unregisterConnChangeListener(IServiceConnChangeListener iServiceConnChangeListener) {
        String str = this.TAG;
        Log.d(str, "unregisterConnChangeListener~~~listener:" + iServiceConnChangeListener);
        if (iServiceConnChangeListener == null || !this.mServiceConnChangeListener.contains(iServiceConnChangeListener)) {
            return;
        }
        this.mServiceConnChangeListener.remove(iServiceConnChangeListener);
    }

    public void unregisterService(String str) {
        ServiceMgrProxy serviceMgrProxy;
        String str2 = this.TAG;
        Log.d(str2, "unregisterService~~~svrName:" + str);
        if (TextUtils.isEmpty(str) || (serviceMgrProxy = this.mServiceMngProxy) == null) {
            return;
        }
        serviceMgrProxy.unregisterService(str);
    }

    public void unregisterServiceConnListener(String str) {
        ServiceMgrProxy serviceMgrProxy;
        String str2 = this.TAG;
        Log.d(str2, "unregisterServiceConnListener~~~svrName:" + str);
        if (TextUtils.isEmpty(str) || (serviceMgrProxy = this.mServiceMngProxy) == null) {
            return;
        }
        serviceMgrProxy.unregisterServiceConnListener(str, this.mServiceConnListener);
    }
}
