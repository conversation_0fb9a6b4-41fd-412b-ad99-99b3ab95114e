package com.yfve.ici.service.popupwindow;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface IPopupWindowCallBack extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IPopupWindowCallBack {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.service.popupwindow.IPopupWindowCallBack
        public void onHide() throws RemoteException {
        }

        @Override // com.yfve.ici.service.popupwindow.IPopupWindowCallBack
        public void onShow() throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IPopupWindowCallBack {
        public static final String DESCRIPTOR = "com.yfve.ici.service.popupwindow.IPopupWindowCallBack";
        public static final int TRANSACTION_onHide = 2;
        public static final int TRANSACTION_onShow = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements IPopupWindowCallBack {
            public static IPopupWindowCallBack sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            @Override // com.yfve.ici.service.popupwindow.IPopupWindowCallBack
            public void onHide() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onHide();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.service.popupwindow.IPopupWindowCallBack
            public void onShow() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onShow();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static IPopupWindowCallBack asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof IPopupWindowCallBack)) {
                return (IPopupWindowCallBack) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IPopupWindowCallBack getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IPopupWindowCallBack iPopupWindowCallBack) {
            if (Proxy.sDefaultImpl != null || iPopupWindowCallBack == null) {
                return false;
            }
            Proxy.sDefaultImpl = iPopupWindowCallBack;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i == 1) {
                parcel.enforceInterface(DESCRIPTOR);
                onShow();
                parcel2.writeNoException();
                return true;
            } else if (i != 2) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString(DESCRIPTOR);
                return true;
            } else {
                parcel.enforceInterface(DESCRIPTOR);
                onHide();
                parcel2.writeNoException();
                return true;
            }
        }
    }

    void onHide() throws RemoteException;

    void onShow() throws RemoteException;
}
