package com.yfve.ici.service.popupwindow;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import com.yfve.ici.service.popupwindow.IPopupWindowCallBack;
import com.yfve.ici.service.popupwindow.IPopupWindowListener;

/* loaded from: classes.dex */
public interface IPopupWindow extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IPopupWindow {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.service.popupwindow.IPopupWindow
        public void cancelWindow(String str, IPopupWindowCallBack iPopupWindowCallBack) throws RemoteException {
        }

        @Override // com.yfve.ici.service.popupwindow.IPopupWindow
        public void enqueueWindow(String str, IPopupWindowCallBack iPopupWindowCallBack, int i) throws RemoteException {
        }

        @Override // com.yfve.ici.service.popupwindow.IPopupWindow
        public boolean isWindowShow(int i) throws RemoteException {
            return false;
        }

        @Override // com.yfve.ici.service.popupwindow.IPopupWindow
        public void registerPopupwindowListener(IPopupWindowListener iPopupWindowListener) throws RemoteException {
        }

        @Override // com.yfve.ici.service.popupwindow.IPopupWindow
        public void unRegisterPopupwindowListener(IPopupWindowListener iPopupWindowListener) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IPopupWindow {
        public static final String DESCRIPTOR = "com.yfve.ici.service.popupwindow.IPopupWindow";
        public static final int TRANSACTION_cancelWindow = 2;
        public static final int TRANSACTION_enqueueWindow = 1;
        public static final int TRANSACTION_isWindowShow = 5;
        public static final int TRANSACTION_registerPopupwindowListener = 3;
        public static final int TRANSACTION_unRegisterPopupwindowListener = 4;

        /* loaded from: classes.dex */
        public static class Proxy implements IPopupWindow {
            public static IPopupWindow sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            @Override // com.yfve.ici.service.popupwindow.IPopupWindow
            public void cancelWindow(String str, IPopupWindowCallBack iPopupWindowCallBack) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.service.popupwindow.IPopupWindow");
                    obtain.writeString(str);
                    obtain.writeStrongBinder(iPopupWindowCallBack != null ? iPopupWindowCallBack.asBinder() : null);
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().cancelWindow(str, iPopupWindowCallBack);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.service.popupwindow.IPopupWindow
            public void enqueueWindow(String str, IPopupWindowCallBack iPopupWindowCallBack, int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.service.popupwindow.IPopupWindow");
                    obtain.writeString(str);
                    obtain.writeStrongBinder(iPopupWindowCallBack != null ? iPopupWindowCallBack.asBinder() : null);
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().enqueueWindow(str, iPopupWindowCallBack, i);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            public String getInterfaceDescriptor() {
                return "com.yfve.ici.service.popupwindow.IPopupWindow";
            }

            @Override // com.yfve.ici.service.popupwindow.IPopupWindow
            public boolean isWindowShow(int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.service.popupwindow.IPopupWindow");
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(5, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().isWindowShow(i);
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.service.popupwindow.IPopupWindow
            public void registerPopupwindowListener(IPopupWindowListener iPopupWindowListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.service.popupwindow.IPopupWindow");
                    obtain.writeStrongBinder(iPopupWindowListener != null ? iPopupWindowListener.asBinder() : null);
                    if (!this.mRemote.transact(3, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerPopupwindowListener(iPopupWindowListener);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.service.popupwindow.IPopupWindow
            public void unRegisterPopupwindowListener(IPopupWindowListener iPopupWindowListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.service.popupwindow.IPopupWindow");
                    obtain.writeStrongBinder(iPopupWindowListener != null ? iPopupWindowListener.asBinder() : null);
                    if (!this.mRemote.transact(4, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unRegisterPopupwindowListener(iPopupWindowListener);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, "com.yfve.ici.service.popupwindow.IPopupWindow");
        }

        public static IPopupWindow asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface("com.yfve.ici.service.popupwindow.IPopupWindow");
            if (queryLocalInterface != null && (queryLocalInterface instanceof IPopupWindow)) {
                return (IPopupWindow) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IPopupWindow getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IPopupWindow iPopupWindow) {
            if (Proxy.sDefaultImpl != null || iPopupWindow == null) {
                return false;
            }
            Proxy.sDefaultImpl = iPopupWindow;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i == 1) {
                parcel.enforceInterface("com.yfve.ici.service.popupwindow.IPopupWindow");
                enqueueWindow(parcel.readString(), IPopupWindowCallBack.Stub.asInterface(parcel.readStrongBinder()), parcel.readInt());
                parcel2.writeNoException();
                return true;
            } else if (i == 2) {
                parcel.enforceInterface("com.yfve.ici.service.popupwindow.IPopupWindow");
                cancelWindow(parcel.readString(), IPopupWindowCallBack.Stub.asInterface(parcel.readStrongBinder()));
                parcel2.writeNoException();
                return true;
            } else if (i == 3) {
                parcel.enforceInterface("com.yfve.ici.service.popupwindow.IPopupWindow");
                registerPopupwindowListener(IPopupWindowListener.Stub.asInterface(parcel.readStrongBinder()));
                parcel2.writeNoException();
                return true;
            } else if (i == 4) {
                parcel.enforceInterface("com.yfve.ici.service.popupwindow.IPopupWindow");
                unRegisterPopupwindowListener(IPopupWindowListener.Stub.asInterface(parcel.readStrongBinder()));
                parcel2.writeNoException();
                return true;
            } else if (i != 5) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString("com.yfve.ici.service.popupwindow.IPopupWindow");
                return true;
            } else {
                parcel.enforceInterface("com.yfve.ici.service.popupwindow.IPopupWindow");
                boolean isWindowShow = isWindowShow(parcel.readInt());
                parcel2.writeNoException();
                parcel2.writeInt(isWindowShow ? 1 : 0);
                return true;
            }
        }
    }

    void cancelWindow(String str, IPopupWindowCallBack iPopupWindowCallBack) throws RemoteException;

    void enqueueWindow(String str, IPopupWindowCallBack iPopupWindowCallBack, int i) throws RemoteException;

    boolean isWindowShow(int i) throws RemoteException;

    void registerPopupwindowListener(IPopupWindowListener iPopupWindowListener) throws RemoteException;

    void unRegisterPopupwindowListener(IPopupWindowListener iPopupWindowListener) throws RemoteException;
}
