package com.yfve.ici.service.popupwindow;

import android.content.Context;
import android.os.Handler;
import android.os.Message;
import android.os.RemoteException;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import b.a.b.a.a;
import com.yfve.ici.service.base.BaseProxy;
import com.yfve.ici.service.contanst.ServiceConstant;
import com.yfve.ici.service.popupwindow.IPopupWindowCallBack;
import com.yfve.ici.service.popupwindow.IPopupWindowListener;
import com.yfve.ici.service.popupwindow.PopupConst;

/* loaded from: classes.dex */
public class PopupWnd extends BaseProxy<IPopupWindow> {
    public static String TAG = "CoreService_POPUP_MANAGER_PopupWnd";
    public static PopupWnd mPopupWindowProxy;
    public final Context mContext;
    public int mDuration;
    public PopupConst.Level mLevel;
    public PopupListener mListener;
    public View mNextView;
    public WindowManager.LayoutParams mParams;
    public PopupWindowCallBack mPopupCallBack;
    public PopupWindowListener mPopupWindowListenr;

    /* loaded from: classes.dex */
    public static class PopupWindowCallBack extends IPopupWindowCallBack.Stub {
        public final Handler mHandler;
        public final Runnable mHide;
        public PopupConst.Level mLevel;
        public PopupListener mListener;
        public boolean mNeedShow;
        public View mNextView;
        public WindowManager.LayoutParams mParams;
        public final Runnable mRunnable;
        public final Runnable mShow;
        public View mView;
        public WindowManager mWM;

        public PopupWindowCallBack() {
            this.mHandler = new Handler();
            this.mHide = new Runnable() { // from class: com.yfve.ici.service.popupwindow.PopupWnd.PopupWindowCallBack.1
                @Override // java.lang.Runnable
                public void run() {
                    PopupWindowCallBack.this.handleHide();
                    PopupWindowCallBack.this.mNextView = null;
                }
            };
            this.mShow = new Runnable() { // from class: com.yfve.ici.service.popupwindow.PopupWnd.PopupWindowCallBack.2
                @Override // java.lang.Runnable
                public void run() {
                    PopupWindowCallBack.this.handleShow();
                }
            };
            this.mRunnable = new Runnable() { // from class: com.yfve.ici.service.popupwindow.PopupWnd.PopupWindowCallBack.3
                @Override // java.lang.Runnable
                public void run() {
                }
            };
        }

        private void removeUnclosedScreen() {
            Log.i(PopupWnd.TAG, "removeUnclosedScreen~~~");
            this.mHandler.removeCallbacks(this.mRunnable);
        }

        private void sendUnclosedScreen() {
            Log.i(PopupWnd.TAG, "sendUnclosedScreen~~~");
            removeUnclosedScreen();
            this.mHandler.postDelayed(this.mRunnable, 500L);
        }

        public void handleHide() {
            String str = PopupWnd.TAG;
            Log.v(str, "HANDLE HIDE: " + this + " mView=" + this.mView);
            View view = this.mView;
            if (view != null) {
                if (view.getParent() != null) {
                    String str2 = PopupWnd.TAG;
                    StringBuilder e = a.e("REMOVE! ");
                    e.append(this.mView);
                    e.append(" in ");
                    e.append(this);
                    Log.v(str2, e.toString());
                    this.mWM.removeView(this.mView);
                }
                this.mView = null;
            }
        }

        public void handleShow() {
            String str = PopupWnd.TAG;
            Log.i(str, "HANDLE SHOW: " + this + " mView=" + this.mView + " mNextView=" + this.mNextView + " mParams=" + this.mParams);
            if (this.mView != this.mNextView || this.mNeedShow) {
                handleHide();
                View view = this.mNextView;
                this.mView = view;
                this.mNeedShow = false;
                Context applicationContext = view.getContext().getApplicationContext();
                if (applicationContext == null) {
                    applicationContext = this.mView.getContext();
                }
                this.mWM = (WindowManager) applicationContext.getSystemService("window");
                if (this.mView.getParent() != null) {
                    String str2 = PopupWnd.TAG;
                    StringBuilder e = a.e("REMOVE! ");
                    e.append(this.mView);
                    e.append(" in ");
                    e.append(this);
                    Log.v(str2, e.toString());
                    this.mWM.removeView(this.mView);
                }
                String str3 = PopupWnd.TAG;
                StringBuilder e2 = a.e("ADD! ");
                e2.append(this.mView);
                e2.append(" in ");
                e2.append(this);
                Log.v(str3, e2.toString());
                this.mWM.addView(this.mView, this.mParams);
            }
        }

        @Override // com.yfve.ici.service.popupwindow.IPopupWindowCallBack
        public void onHide() throws RemoteException {
            Log.i(PopupWnd.TAG, "onHide~~~");
            this.mHandler.post(this.mHide);
        }

        @Override // com.yfve.ici.service.popupwindow.IPopupWindowCallBack
        public void onShow() throws RemoteException {
            Log.i(PopupWnd.TAG, "onShow~~~");
            this.mHandler.post(this.mShow);
        }
    }

    /* loaded from: classes.dex */
    public static class PopupWindowListener extends IPopupWindowListener.Stub {
        public static final int HIDE = 1;
        public static final int SHOW = 0;
        public final Handler mHandler = new Handler() { // from class: com.yfve.ici.service.popupwindow.PopupWnd.PopupWindowListener.1
            @Override // android.os.Handler
            public void handleMessage(Message message) {
                a.n(a.e("handleMessage~~~msg.what:"), message.what, PopupWnd.TAG);
                PopupListener popupListener = PopupWindowListener.this.mPopupListener;
                if (popupListener != null) {
                    int i = message.what;
                    if (i == 0) {
                        popupListener.onShow((PopupConst.Level) message.obj);
                    } else if (i != 1) {
                    } else {
                        popupListener.onHide((PopupConst.Level) message.obj);
                    }
                }
            }
        };
        public PopupListener mPopupListener;

        @Override // com.yfve.ici.service.popupwindow.IPopupWindowListener
        public void onHide(int i) throws RemoteException {
            a.i("PopupWindowListener~~~onHide~~~level:", i, PopupWnd.TAG);
            Message.obtain(this.mHandler, 1, PopupConst.Level.values()[i]).sendToTarget();
        }

        @Override // com.yfve.ici.service.popupwindow.IPopupWindowListener
        public void onShow(int i) throws RemoteException {
            a.i("PopupWindowListener~~~onShow~~~level:", i, PopupWnd.TAG);
            Message.obtain(this.mHandler, 0, PopupConst.Level.values()[i]).sendToTarget();
        }
    }

    public PopupWnd(Context context) {
        Log.i(TAG, "PopupWnd～～～");
        this.mContext = context;
        this.mPopupCallBack = new PopupWindowCallBack();
        this.mPopupWindowListenr = new PopupWindowListener();
    }

    @Deprecated
    public static PopupWnd getInstance(Context context) {
        if (mPopupWindowProxy == null) {
            synchronized (PopupWnd.class) {
                if (mPopupWindowProxy == null) {
                    mPopupWindowProxy = new PopupWnd(context);
                }
            }
        }
        return mPopupWindowProxy;
    }

    public static PopupWnd makeWindow(Context context, View view, WindowManager.LayoutParams layoutParams, PopupConst.Level level) {
        layoutParams.type = level.type();
        PopupWnd popupWnd = new PopupWnd(context);
        popupWnd.mDuration = level.duration();
        popupWnd.mParams = layoutParams;
        popupWnd.mNextView = view;
        popupWnd.mLevel = level;
        String str = TAG;
        StringBuilder e = a.e("makeWindow~~~params.type");
        e.append(layoutParams.type);
        e.append(",result.mDuration:");
        e.append(popupWnd.mDuration);
        e.append(",result.mLevel:");
        e.append(popupWnd.mLevel);
        Log.i(str, e.toString());
        return popupWnd;
    }

    public void cancel() {
        Log.i(TAG, "cancel~~~");
        try {
            if (isAvailable()) {
                ((IPopupWindow) this.mInterface).cancelWindow(this.mContext.getPackageName(), this.mPopupCallBack);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            String str = TAG;
            Log.e(str, "cancel error e:" + e);
        }
    }

    @Override // com.yfve.ici.service.base.BaseProxy
    public String getServiceName() {
        return ServiceConstant.POPUP_WINDOW_BINDER_NAME;
    }

    public View getView() {
        Log.i(TAG, "getView~~~~");
        return this.mNextView;
    }

    public boolean isWindowShow(int i) {
        a.i("isWindowShow~~~level:", i, TAG);
        try {
            if (isAvailable()) {
                return ((IPopupWindow) this.mInterface).isWindowShow(i);
            }
            return false;
        } catch (RemoteException e) {
            String str = TAG;
            Log.e(str, "isWindowShow error e:" + e);
            return true;
        }
    }

    public void registerPopupwindowListener(PopupListener popupListener) {
        String str = TAG;
        Log.i(str, "registerListeter~~~popupListener:" + popupListener);
        this.mPopupWindowListenr.mPopupListener = popupListener;
        try {
            if (isAvailable()) {
                ((IPopupWindow) this.mInterface).registerPopupwindowListener(this.mPopupWindowListenr);
            }
        } catch (RemoteException e) {
            String str2 = TAG;
            Log.e(str2, "registerListeter error e:" + e);
        }
    }

    public void resetType() {
        Log.i(TAG, "resetType～～～");
        this.mParams.type = this.mLevel.type();
        this.mPopupCallBack.mNeedShow = true;
    }

    public void setType(int i) {
        a.i("setType~~~~type:", i, TAG);
        this.mParams.type = i;
        this.mPopupCallBack.mNeedShow = true;
    }

    public void setView(View view) {
        Log.i(TAG, "setView~~~~");
        this.mNextView = view;
    }

    @Deprecated
    public void setWinListener(PopupListener popupListener) {
        String str = TAG;
        Log.i(str, "setWinListener~~~listener:" + popupListener);
        this.mListener = popupListener;
    }

    public void show() {
        Log.i(TAG, "show~~~");
        if (this.mNextView != null) {
            String packageName = this.mContext.getPackageName();
            PopupWindowCallBack popupWindowCallBack = this.mPopupCallBack;
            popupWindowCallBack.mParams = this.mParams;
            popupWindowCallBack.mNextView = this.mNextView;
            popupWindowCallBack.mListener = this.mListener;
            popupWindowCallBack.mLevel = this.mLevel;
            try {
                if (isAvailable()) {
                    ((IPopupWindow) this.mInterface).enqueueWindow(packageName, popupWindowCallBack, this.mLevel.ordinal());
                    return;
                }
                return;
            } catch (RemoteException e) {
                e.printStackTrace();
                String str = TAG;
                Log.e(str, "show error e:" + e);
                return;
            }
        }
        throw new RuntimeException("setView must have been called");
    }

    public void unRegisterPopupwindowListener() {
        Log.i(TAG, "unRegisterPopupwindowListener~~~");
        this.mPopupWindowListenr.mPopupListener = null;
        try {
            if (isAvailable()) {
                ((IPopupWindow) this.mInterface).unRegisterPopupwindowListener(this.mPopupWindowListenr);
            }
        } catch (RemoteException e) {
            String str = TAG;
            Log.e(str, "unregisterListeter error e:" + e);
        }
    }
}
