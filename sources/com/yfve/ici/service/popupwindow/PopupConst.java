package com.yfve.ici.service.popupwindow;

/* loaded from: classes.dex */
public class PopupConst {
    public static final int FIRST_SYSTEM_WINDOW = 2000;
    public static final int LENGTH_ALWAYS = 2;
    public static final int LENGTH_LONG = 1;
    public static final int LENGTH_SHORT = 0;

    /* loaded from: classes.dex */
    public enum Level {
        POWER_OFF(2021, 2),
        SYSTEM_UPGRADE(2021, 2),
        CALL(2026, 2),
        REVERSE(2036, 2),
        VOLUME(2027, 0),
        CALL_MAIN(2024, 2),
        SCREEN_OFF(2024, 2),
        VR(2024, 2),
        TOAST(2014, 0),
        UNIVERSAL(2038, 2);
        
        public int duration;
        public int type;

        Level(int i, int i2) {
            this.type = i;
            this.duration = i2;
        }

        public static final Level findByIndex(int i) {
            try {
                return values()[i];
            } catch (ArrayIndexOutOfBoundsException unused) {
                return null;
            }
        }

        public int duration() {
            return this.duration;
        }

        public int type() {
            return this.type;
        }
    }
}
