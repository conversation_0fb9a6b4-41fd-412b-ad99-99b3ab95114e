package com.yfve.ici.service.servicemgr;

import com.yfve.ici.service.contanst.ServiceConstant;

/* loaded from: classes.dex */
public class ServiceMgrConstant {
    public static int PRIORITY_LEVEL_1 = 0;
    public static int PRIORITY_LEVEL_2 = 1;
    public static int PRIORITY_LEVEL_3 = 2;
    public static int PRIORITY_LEVEL_4 = 3;
    public static int PRIORITY_LEVEL_5 = 4;
    public static int PRIORITY_LEVEL_6 = 5;
    public static int PRIORITY_LEVEL_MAX = 5;

    /* loaded from: classes.dex */
    public enum ServiceGathering {
        LAUNCHER(ServiceConstant.LAUNCHER_PACKAGE_NAME, ServiceConstant.LAUNCHER_SERVICE_ACTION, ServiceConstant.LAUNCHER_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_1),
        DND(ServiceConstant.DND_PACKAGE_NAME, ServiceConstant.DND_SERVICE_ACTION, ServiceConstant.DND_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_1),
        CONNECTIVITY(ServiceConstant.CONNECTIVITY_PACKAGE_NAME, ServiceConstant.CONNECTIVITY_SERVICE_ACTION, ServiceConstant.CONNECTIVITY_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_1),
        BTPHONE(ServiceConstant.BT_PHONE_PACKAGE_NAME, ServiceConstant.BT_PHONE_SERVICE_ACTION, ServiceConstant.BT_PHONE_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_1),
        CARPLAY("com.ici.carplay", ServiceConstant.CARPLAY_SERVICE_ACTION, ServiceConstant.CARPLAY_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_1),
        ACCOUNT(ServiceConstant.ACCOUNT_PACKAGE_NAME, ServiceConstant.ACCOUNT_SERVICE_ACTION, ServiceConstant.ACCOUNT_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_1),
        SETTINGSYNC(ServiceConstant.ACCOUNT_PACKAGE_NAME, ServiceConstant.SETTING_SYNC_SERVICE_ACTION, ServiceConstant.SETTING_SYNC_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_1),
        WALLPAPERTHEME(ServiceConstant.WALLPAPERTHEME_PACKAGE_NAME, ServiceConstant.WALLPAPERTHEME_SERVICE_ACTION, ServiceConstant.WALLPAPERTHEME_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_1),
        SOURCEVR("com.ici.audio.source", ServiceConstant.SOURCE_VR_SERVICE_ACTION, ServiceConstant.SOURCE_VR_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_1),
        SOURCE("com.ici.audio.source", ServiceConstant.SOURCE_SERVICE_ACTION, ServiceConstant.SOURCE_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_1),
        BTMUSIC("com.ici.btaudio", ServiceConstant.BT_MUSIC_SERVICE_ACTION, "", ServiceMgrConstant.PRIORITY_LEVEL_1),
        USBMEDIA("com.ici.media", ServiceConstant.USBMEDIA_SERVICE_ACTION, "", ServiceMgrConstant.PRIORITY_LEVEL_1),
        RADIO("com.ici.radio", ServiceConstant.RADIO_SERVICE_ACTION, ServiceConstant.RADIO_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_1),
        CARLIFE("com.ici.carlife", ServiceConstant.CARLIFE_SERVICE_ACTION, ServiceConstant.CARLIFE_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_1),
        VEHICLEINFO(ServiceConstant.VEHICLEINFO_PACKAGE_NAME, ServiceConstant.VEHICLEINFO_SERVICE_ACTION, ServiceConstant.VEHICLEINFO_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_1),
        ADASSetting(ServiceConstant.ADAS_PACKAGE_NAME, ServiceConstant.ADAS_SERVICE_ACTION, ServiceConstant.ADAS_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_1),
        Reminder(ServiceConstant.REMINDER_PACKAGE_NAME, ServiceConstant.REMINDER_SERVICE_ACTION, ServiceConstant.REMINDER_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_1),
        SETTING(ServiceConstant.SETTING_PACKAGE_NAME, ServiceConstant.SETTING_SERVICE_ACTION, ServiceConstant.SETTING_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_1),
        ONSTAR(ServiceConstant.ONSTAR_PACKAGE_NAME, "ici.intent.action.ONSTAR", ServiceConstant.ONSTAR_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_1),
        DATATRAFFIC(ServiceConstant.ONSTAR_PACKAGE_NAME, "ici.intent.action.DATATRAFFIC", ServiceConstant.DATA_TRAFFIC_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_1),
        OTA(ServiceConstant.OTA_PACKAGE_NAME, ServiceConstant.OTA_SERVICE_ACTION, ServiceConstant.OTA_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_1),
        HVAC(ServiceConstant.HVAC_PACKAGE_NAME, ServiceConstant.HVAC_SERVICE_ACTION, ServiceConstant.HVAC_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_1),
        VEHICLECONTROL(ServiceConstant.VEHICLECONTROL_PACKAGE_NAME, ServiceConstant.VEHICLECONTROL_SERVICE_ACTION, "", ServiceMgrConstant.PRIORITY_LEVEL_1),
        FUNDRIVING(ServiceConstant.FUNDRIVING_PACKAGE_NAME, ServiceConstant.FUNDRIVING_SERVICE_ACTION, ServiceConstant.FUNDRIVING_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_4),
        WCS(ServiceConstant.WCS_PACKAGE_NAME, ServiceConstant.WCS_SERVICE_ACTION, "", ServiceMgrConstant.PRIORITY_LEVEL_4),
        DBA(ServiceConstant.DBA_PACKAGE_NAME, ServiceConstant.DBA_SERVICE_ACTION, "", ServiceMgrConstant.PRIORITY_LEVEL_4),
        BOOTANIMATION(ServiceConstant.BOOTANIMATION_PACKAGE_NAME, ServiceConstant.BOOTANIMATION_SERVICE_ACTION, "", ServiceMgrConstant.PRIORITY_LEVEL_4),
        AMBIENTLIGHTING(ServiceConstant.AMBIENTLIGHTING_PACKAGE_NAME, ServiceConstant.AMBIENTLIGHTING_SERVICE_ACTION, ServiceConstant.AMBIENTLIGHTING_BINDER_NAME, ServiceMgrConstant.PRIORITY_LEVEL_4);
        
        public String binderName;
        public String intentAction;
        public String packageName;
        public int priorityLevel;

        ServiceGathering(String str, String str2, String str3, int i) {
            this.packageName = str;
            this.intentAction = str2;
            this.binderName = str3;
            this.priorityLevel = i;
        }

        public static ServiceGathering findById(int i) {
            try {
                return values()[i];
            } catch (Exception unused) {
                return null;
            }
        }

        public static String getActionByBinderName(String str) {
            ServiceGathering[] values;
            for (ServiceGathering serviceGathering : values()) {
                if (str.equals(serviceGathering.getBinderName())) {
                    return serviceGathering.getIntentAction();
                }
            }
            return "";
        }

        public static String getPackageNameByBinderName(String str) {
            ServiceGathering[] values;
            for (ServiceGathering serviceGathering : values()) {
                if (str.equals(serviceGathering.getBinderName())) {
                    return serviceGathering.getPackageName();
                }
            }
            return "";
        }

        public String getBinderName() {
            return this.binderName;
        }

        public String getIntentAction() {
            return this.intentAction;
        }

        public String getPackageName() {
            return this.packageName;
        }

        public int getPriorityLevel() {
            return this.priorityLevel;
        }
    }
}
