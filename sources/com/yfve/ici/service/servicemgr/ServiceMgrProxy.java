package com.yfve.ici.service.servicemgr;

import android.os.IBinder;
import android.text.TextUtils;
import android.util.Log;
import b.a.b.a.a;
import com.yfve.ici.service.contanst.ServiceConstant;
import com.yfve.ici.service.servicemgr.IServiceMgr;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

/* loaded from: classes.dex */
public class ServiceMgrProxy {
    public static ServiceMgrProxy mServiceMgrProxy;
    public String TAG = "CoreService ServiceMgrProxy";
    public HashMap<String, IServiceConnListener> mConnListeners = new HashMap<>();
    public IServiceConnListener mServiceConnListener;
    public IServiceMgr mServiceManger;
    public Timer mTimer;

    private void checkServiceStatus() {
        synchronized (ServiceMgrProxy.class) {
            if (this.mTimer != null) {
                Timer timer = new Timer();
                this.mTimer = timer;
                timer.scheduleAtFixedRate(new TimerTask() { // from class: com.yfve.ici.service.servicemgr.ServiceMgrProxy.1
                    @Override // java.util.TimerTask, java.lang.Runnable
                    public void run() {
                        Log.i(ServiceMgrProxy.this.TAG, "checkServiceStatus TimerTask run is in~~~");
                        if (ServiceMgrProxy.this.getService() != null) {
                            synchronized (ServiceMgrProxy.class) {
                                ServiceMgrProxy.this.mTimer.cancel();
                                ServiceMgrProxy.this.mTimer = null;
                                Iterator<Map.Entry<String, IServiceConnListener>> it = ServiceMgrProxy.this.mConnListeners.entrySet().iterator();
                                while (it.hasNext()) {
                                    Map.Entry<String, IServiceConnListener> next = it.next();
                                    ServiceMgrProxy.this.registerServiceConnListener(next.getKey(), next.getValue());
                                    it.remove();
                                }
                            }
                        }
                        Log.i(ServiceMgrProxy.this.TAG, "checkServiceStatus TimerTask run is out~~~");
                    }
                }, 1000L, 1000L);
            }
        }
    }

    public static ServiceMgrProxy getInstance() {
        if (mServiceMgrProxy == null) {
            synchronized (ServiceMgrProxy.class) {
                if (mServiceMgrProxy == null) {
                    mServiceMgrProxy = new ServiceMgrProxy();
                }
            }
        }
        return mServiceMgrProxy;
    }

    private IServiceMgr getNewStub() {
        try {
            Log.d(this.TAG, "getServiceMangerService~~~start");
            Method method = Class.forName("android.os.ServiceManager").getMethod("getService", String.class);
            Object[] objArr = {getServiceName()};
            Log.d(this.TAG, "getServiceMangerService~~~end");
            this.mServiceManger = IServiceMgr.Stub.asInterface((IBinder) method.invoke(null, objArr));
        } catch (Exception e) {
            this.mServiceManger = null;
            Log.e(this.TAG, e.toString());
        }
        return this.mServiceManger;
    }

    public IBinder getClientBinder(String str) {
        String str2 = this.TAG;
        Log.d(str2, "getClientBinder~~~serverName:" + str);
        try {
            getService();
            if (this.mServiceManger != null) {
                return this.mServiceManger.getService(str);
            }
            return null;
        } catch (Exception e) {
            String str3 = this.TAG;
            StringBuilder e2 = a.e("getClientBinder~~~fail.e:");
            e2.append(e.toString());
            Log.e(str3, e2.toString());
            return null;
        }
    }

    public IServiceMgr getService() {
        synchronized (ServiceMgrProxy.class) {
            if (this.mServiceManger == null) {
                Log.e(this.TAG, "mServiceManger==null~~~");
                getNewStub();
            }
            if (this.mServiceManger == null) {
                Log.e(this.TAG, "coreservice doesn't start");
                return null;
            } else if (this.mServiceManger.asBinder().isBinderAlive() && this.mServiceManger.asBinder().pingBinder()) {
                return this.mServiceManger;
            } else {
                Log.e(this.TAG, "mServiceManger.asBinder().isBinderAlive() && mServiceManger.asBinder().pingBinder() : false");
                this.mServiceManger = null;
                return getNewStub();
            }
        }
    }

    public String getServiceName() {
        return ServiceConstant.SVR_MNG_BINDER_NAME;
    }

    public void registerService(String str, IBinder iBinder) {
        String str2 = this.TAG;
        Log.d(str2, "registerService~~~binderName" + str + ",binder" + iBinder);
        if (TextUtils.isEmpty(str) || iBinder == null) {
            return;
        }
        try {
            getService();
            if (this.mServiceManger != null) {
                this.mServiceManger.registerService(str, iBinder);
                Log.d(this.TAG, "registerService~~~~Success");
            }
        } catch (Exception e) {
            String str3 = this.TAG;
            StringBuilder e2 = a.e("registerService~~~~fail. e:");
            e2.append(e.toString());
            Log.e(str3, e2.toString());
        }
    }

    public void registerServiceConnListener(String str, IServiceConnListener iServiceConnListener) {
        String str2 = this.TAG;
        Log.d(str2, "registerServiceConnListener~~~binderName:" + str + ",listener:" + iServiceConnListener);
        if (TextUtils.isEmpty(str) || iServiceConnListener == null) {
            return;
        }
        try {
            getService();
            if (this.mServiceManger != null) {
                this.mServiceManger.registerServiceConnListener(str, iServiceConnListener);
                Log.d(this.TAG, "registerServiceConnListener~~~Success");
                return;
            }
            Log.e(this.TAG, "mServiceManger != null : false");
            synchronized (ServiceMgrProxy.class) {
                if (this.mConnListeners.containsKey(str)) {
                    this.mConnListeners.remove(str);
                }
                this.mConnListeners.put(str, iServiceConnListener);
            }
            checkServiceStatus();
        } catch (Exception e) {
            String str3 = this.TAG;
            StringBuilder e2 = a.e("registerServiceConnListener~~~failed.e:");
            e2.append(e.toString());
            Log.e(str3, e2.toString());
        }
    }

    public void unregisterService(String str) {
        String str2 = this.TAG;
        Log.d(str2, "unregisterService~~~binderName:" + str);
        if (TextUtils.isEmpty(str)) {
            return;
        }
        try {
            getService();
            if (this.mServiceManger != null) {
                this.mServiceManger.unregisterService(str);
                Log.d(this.TAG, "unregisterService~~~Success");
            }
        } catch (Exception e) {
            String str3 = this.TAG;
            StringBuilder e2 = a.e("unregisterService~~~fail. e:");
            e2.append(e.toString());
            Log.e(str3, e2.toString());
        }
    }

    public void unregisterServiceConnListener(String str, IServiceConnListener iServiceConnListener) {
        String str2 = this.TAG;
        Log.d(str2, "unregisterServiceConnListener~~~binderName:" + str + ",listener:" + iServiceConnListener);
        if (TextUtils.isEmpty(str) || iServiceConnListener == null) {
            return;
        }
        try {
            getService();
            if (this.mServiceManger != null) {
                synchronized (ServiceMgrProxy.class) {
                    if (this.mConnListeners.containsKey(str)) {
                        this.mConnListeners.remove(str);
                    }
                }
                this.mServiceManger.unregisterServiceConnListener(str, iServiceConnListener);
                Log.d(this.TAG, "unregisterServiceConnListener~~Success");
            }
        } catch (Exception e) {
            String str3 = this.TAG;
            StringBuilder e2 = a.e("registServConnListener~~~fail.e:");
            e2.append(e.toString());
            Log.e(str3, e2.toString());
        }
    }
}
