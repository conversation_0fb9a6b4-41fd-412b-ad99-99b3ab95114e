package com.yfve.ici.service.servicemgr;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface IServiceConnListener extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IServiceConnListener {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.service.servicemgr.IServiceConnListener
        public void onServiceConnStatusChanged(boolean z) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IServiceConnListener {
        public static final String DESCRIPTOR = "com.yfve.ici.service.servicemgr.IServiceConnListener";
        public static final int TRANSACTION_onServiceConnStatusChanged = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements IServiceConnListener {
            public static IServiceConnListener sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            @Override // com.yfve.ici.service.servicemgr.IServiceConnListener
            public void onServiceConnStatusChanged(boolean z) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeInt(z ? 1 : 0);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().onServiceConnStatusChanged(z);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static IServiceConnListener asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof IServiceConnListener)) {
                return (IServiceConnListener) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IServiceConnListener getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IServiceConnListener iServiceConnListener) {
            if (Proxy.sDefaultImpl != null || iServiceConnListener == null) {
                return false;
            }
            Proxy.sDefaultImpl = iServiceConnListener;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString(DESCRIPTOR);
                return true;
            }
            parcel.enforceInterface(DESCRIPTOR);
            onServiceConnStatusChanged(parcel.readInt() != 0);
            parcel2.writeNoException();
            return true;
        }
    }

    void onServiceConnStatusChanged(boolean z) throws RemoteException;
}
