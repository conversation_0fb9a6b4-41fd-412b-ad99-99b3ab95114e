package com.yfve.ici.service.servicemgr;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import com.yfve.ici.service.servicemgr.IServiceConnListener;

/* loaded from: classes.dex */
public interface IServiceMgr extends IInterface {

    /* loaded from: classes.dex */
    public static class Default implements IServiceMgr {
        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.yfve.ici.service.servicemgr.IServiceMgr
        public IBinder getService(String str) throws RemoteException {
            return null;
        }

        @Override // com.yfve.ici.service.servicemgr.IServiceMgr
        public void registerService(String str, IBinder iBinder) throws RemoteException {
        }

        @Override // com.yfve.ici.service.servicemgr.IServiceMgr
        public void registerServiceConnListener(String str, IServiceConnListener iServiceConnListener) throws RemoteException {
        }

        @Override // com.yfve.ici.service.servicemgr.IServiceMgr
        public void unregisterService(String str) throws RemoteException {
        }

        @Override // com.yfve.ici.service.servicemgr.IServiceMgr
        public void unregisterServiceConnListener(String str, IServiceConnListener iServiceConnListener) throws RemoteException {
        }
    }

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements IServiceMgr {
        public static final String DESCRIPTOR = "com.yfve.ici.service.servicemgr.IServiceMgr";
        public static final int TRANSACTION_getService = 5;
        public static final int TRANSACTION_registerService = 1;
        public static final int TRANSACTION_registerServiceConnListener = 3;
        public static final int TRANSACTION_unregisterService = 2;
        public static final int TRANSACTION_unregisterServiceConnListener = 4;

        /* loaded from: classes.dex */
        public static class Proxy implements IServiceMgr {
            public static IServiceMgr sDefaultImpl;
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return "com.yfve.ici.service.servicemgr.IServiceMgr";
            }

            @Override // com.yfve.ici.service.servicemgr.IServiceMgr
            public IBinder getService(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.service.servicemgr.IServiceMgr");
                    obtain.writeString(str);
                    if (!this.mRemote.transact(5, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getService(str);
                    }
                    obtain2.readException();
                    return obtain2.readStrongBinder();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.service.servicemgr.IServiceMgr
            public void registerService(String str, IBinder iBinder) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.service.servicemgr.IServiceMgr");
                    obtain.writeString(str);
                    obtain.writeStrongBinder(iBinder);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerService(str, iBinder);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.service.servicemgr.IServiceMgr
            public void registerServiceConnListener(String str, IServiceConnListener iServiceConnListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.service.servicemgr.IServiceMgr");
                    obtain.writeString(str);
                    obtain.writeStrongBinder(iServiceConnListener != null ? iServiceConnListener.asBinder() : null);
                    if (!this.mRemote.transact(3, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().registerServiceConnListener(str, iServiceConnListener);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.service.servicemgr.IServiceMgr
            public void unregisterService(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.service.servicemgr.IServiceMgr");
                    obtain.writeString(str);
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterService(str);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.yfve.ici.service.servicemgr.IServiceMgr
            public void unregisterServiceConnListener(String str, IServiceConnListener iServiceConnListener) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.yfve.ici.service.servicemgr.IServiceMgr");
                    obtain.writeString(str);
                    obtain.writeStrongBinder(iServiceConnListener != null ? iServiceConnListener.asBinder() : null);
                    if (!this.mRemote.transact(4, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().unregisterServiceConnListener(str, iServiceConnListener);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, "com.yfve.ici.service.servicemgr.IServiceMgr");
        }

        public static IServiceMgr asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface("com.yfve.ici.service.servicemgr.IServiceMgr");
            if (queryLocalInterface != null && (queryLocalInterface instanceof IServiceMgr)) {
                return (IServiceMgr) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        public static IServiceMgr getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }

        public static boolean setDefaultImpl(IServiceMgr iServiceMgr) {
            if (Proxy.sDefaultImpl != null || iServiceMgr == null) {
                return false;
            }
            Proxy.sDefaultImpl = iServiceMgr;
            return true;
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i == 1) {
                parcel.enforceInterface("com.yfve.ici.service.servicemgr.IServiceMgr");
                registerService(parcel.readString(), parcel.readStrongBinder());
                parcel2.writeNoException();
                return true;
            } else if (i == 2) {
                parcel.enforceInterface("com.yfve.ici.service.servicemgr.IServiceMgr");
                unregisterService(parcel.readString());
                parcel2.writeNoException();
                return true;
            } else if (i == 3) {
                parcel.enforceInterface("com.yfve.ici.service.servicemgr.IServiceMgr");
                registerServiceConnListener(parcel.readString(), IServiceConnListener.Stub.asInterface(parcel.readStrongBinder()));
                parcel2.writeNoException();
                return true;
            } else if (i == 4) {
                parcel.enforceInterface("com.yfve.ici.service.servicemgr.IServiceMgr");
                unregisterServiceConnListener(parcel.readString(), IServiceConnListener.Stub.asInterface(parcel.readStrongBinder()));
                parcel2.writeNoException();
                return true;
            } else if (i != 5) {
                if (i != 1598968902) {
                    return super.onTransact(i, parcel, parcel2, i2);
                }
                parcel2.writeString("com.yfve.ici.service.servicemgr.IServiceMgr");
                return true;
            } else {
                parcel.enforceInterface("com.yfve.ici.service.servicemgr.IServiceMgr");
                IBinder service = getService(parcel.readString());
                parcel2.writeNoException();
                parcel2.writeStrongBinder(service);
                return true;
            }
        }
    }

    IBinder getService(String str) throws RemoteException;

    void registerService(String str, IBinder iBinder) throws RemoteException;

    void registerServiceConnListener(String str, IServiceConnListener iServiceConnListener) throws RemoteException;

    void unregisterService(String str) throws RemoteException;

    void unregisterServiceConnListener(String str, IServiceConnListener iServiceConnListener) throws RemoteException;
}
