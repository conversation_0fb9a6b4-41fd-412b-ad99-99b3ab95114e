package com.yfve.ici.service.contanst;

/* loaded from: classes.dex */
public class ServiceConstant {
    public static final String ACCOUNT_BINDER_NAME = "com.ici.account/com.yfve.ici.app.account.IAccountProxy";
    public static final String ACCOUNT_IF_NAME = "com.yfve.ici.app.account.IAccountProxy";
    public static final String ACCOUNT_PACKAGE_NAME = "com.ici.account";
    public static final String ACCOUNT_SERVICE_ACTION = "ici.intent.action.ACCOUNT";
    public static final String ADAS_BINDER_NAME = "com.ici.adas/com.yfve.ici.app.ADASSetting.IADASSettingManager";
    public static final String ADAS_IF_NAME = "com.yfve.ici.app.ADASSetting.IADASSettingManager";
    public static final String ADAS_PACKAGE_NAME = "com.ici.adas";
    public static final String ADAS_SERVICE_ACTION = "ici.intent.action.ADAS";
    public static final String AMBIENTLIGHTING_BINDER_NAME = "com.ici.ambientlighting/com.yfve.ici.app.ambientlighting.IAmbientLightingManager";
    public static final String AMBIENTLIGHTING_IF_NAME = "com.yfve.ici.app.ambientlighting.IAmbientLightingManager";
    public static final String AMBIENTLIGHTING_PACKAGE_NAME = "com.ici.ambientlighting";
    public static final String AMBIENTLIGHTING_SERVICE_ACTION = "com.ici.ambientlighting.action.AmbientLightingService";
    public static final String BOOTANIMATION_PACKAGE_NAME = "com.ici.bootanimation";
    public static final String BOOTANIMATION_SERVICE_ACTION = "ici.intent.action.BOOTANIMATION";
    public static final String BT_MUSIC_BINDER_NAME = "com.ici.btaudio/";
    public static final String BT_MUSIC_IF_NAME = "";
    public static final String BT_MUSIC_PACKAGE_NAME = "com.ici.btaudio";
    public static final String BT_MUSIC_SERVICE_ACTION = "ici.intent.action.BT_MUSIC";
    public static final String BT_PHONE_BINDER_NAME = "com.ici.btphone/com.yfve.ici.app.btphone.IBtPhoneProxy";
    public static final String BT_PHONE_IF_NAME = "com.yfve.ici.app.btphone.IBtPhoneProxy";
    public static final String BT_PHONE_PACKAGE_NAME = "com.ici.btphone";
    public static final String BT_PHONE_SERVICE_ACTION = "ici.intent.action.ICI_BT_PHONE";
    public static final String BT_SETTING_BINDER_NAME = "com.ici.bluetoothsetting/com.yfve.ici.app.btsetting.IBtSettingProxy";
    public static final String BT_SETTING_IF_NAME = "com.yfve.ici.app.btsetting.IBtSettingProxy";
    public static final String BT_SETTING_PACKAGE_NAME = "com.ici.bluetoothsetting";
    public static final String BT_SETTING_SERVICE_ACTION = "ici.intent.action.BT_SETTING";
    public static final String CARLIFE_BINDER_NAME = "com.ici.carlife/com.yfve.ici.app.carlife.ICarlife";
    public static final String CARLIFE_IF_NAME = "com.yfve.ici.app.carlife.ICarlife";
    public static final String CARLIFE_PACKAGE_NAME = "com.ici.carlife";
    public static final String CARLIFE_SERVICE_ACTION = "ici.intent.action.CARLIFE";
    public static final String CARPLAY_BINDER_NAME = "com.ici.carplay/com.yfve.ici.app.carplay.ICarPlayProxy";
    public static final String CARPLAY_IF_NAME = "com.yfve.ici.app.carplay.ICarPlayProxy";
    public static final String CARPLAY_PACKAGE_NAME = "com.ici.carplay";
    public static final String CARPLAY_SERVICE_ACTION = "ici.intent.action.CARPLAY";
    public static final String CONNECTIVITY_BINDER_NAME = "com.ici.connectivity/com.yfve.ici.app.btsetting.IBtSettingProxy";
    public static final String CONNECTIVITY_IF_NAME = "com.yfve.ici.app.btsetting.IBtSettingProxy";
    public static final String CONNECTIVITY_PACKAGE_NAME = "com.ici.connectivity";
    public static final String CONNECTIVITY_SERVICE_ACTION = "ici.intent.action.CONNECTIVITY";
    public static final String CORESERVICE_PACKAGE_NAME = "com.ici.coreservice";
    public static final String DATA_TRAFFIC_BINDER_NAME = "com.ici.onstar/com.yfve.ici.app.onstar.IDataManager";
    public static final String DATA_TRAFFIC_IF_NAME = "com.yfve.ici.app.onstar.IDataManager";
    public static final String DATA_TRAFFIC_SERVICE_ACTION = "ici.intent.action.DATATRAFFIC";
    public static final String DBA_PACKAGE_NAME = "com.ici.dba";
    public static final String DBA_SERVICE_ACTION = "ici.intent.action.dba";
    public static final String DND_BINDER_NAME = "com.ici.dnd/com.yfve.ici.app.dnd.IDNDProxy";
    public static final String DND_IF_NAME = "com.yfve.ici.app.dnd.IDNDProxy";
    public static final String DND_PACKAGE_NAME = "com.ici.dnd";
    public static final String DND_SERVICE_ACTION = "ici.intent.action.DND";
    public static final String FUNDRIVING_BINDER_NAME = "com.ici.fundriving/com.yfve.ici.app.setting.IFundriving";
    public static final String FUNDRIVING_IF_NAME = "com.yfve.ici.app.setting.IFundriving";
    public static final String FUNDRIVING_PACKAGE_NAME = "com.ici.fundriving";
    public static final String FUNDRIVING_SERVICE_ACTION = "ici.intent.action.fundriving";
    public static final String HVAC_BINDER_NAME = "com.ici.hvac/com.yfve.ici.app.hvac.IHvacManager";
    public static final String HVAC_IF_NAME = "com.yfve.ici.app.hvac.IHvacManager";
    public static final String HVAC_PACKAGE_NAME = "com.ici.hvac";
    public static final String HVAC_SERVICE_ACTION = "ici.intent.action.HVAC";
    public static final String LAUNCHER_BINDER_NAME = "com.ici.launcher/com.yfve.ici.app.launcher.ILauncherManager";
    public static final String LAUNCHER_IF_NAME = "com.yfve.ici.app.launcher.ILauncherManager";
    public static final String LAUNCHER_PACKAGE_NAME = "com.ici.launcher";
    public static final String LAUNCHER_SERVICE_ACTION = "ici.intent.action.launcher";
    public static final String ONSTAR_BINDER_NAME = "com.ici.onstar/com.yfve.ici.app.onstar.ITboxManager";
    public static final String ONSTAR_IF_NAME = "com.yfve.ici.app.onstar.ITboxManager";
    public static final String ONSTAR_PACKAGE_NAME = "com.ici.onstar";
    public static final String ONSTAR_SERVICE_ACTION = "ici.intent.action.ONSTAR";
    public static final String OTA_BINDER_NAME = "com.ici.ota/com.yfve.ici.app.ota.IOTAManager";
    public static final String OTA_IF_NAME = "com.yfve.ici.app.ota.IOTAManager";
    public static final String OTA_PACKAGE_NAME = "com.ici.ota";
    public static final String OTA_SERVICE_ACTION = "ici.intent.action.OTA";
    public static final String POPUP_WINDOW_BINDER_NAME = "com.ici.coreservice/com.yfve.ici.service.popupwindow.IPopupWindow";
    public static final String POPUP_WINDOW_IF_NAME = "com.yfve.ici.service.popupwindow.IPopupWindow";
    public static final String RADIO_BINDER_NAME = "com.ici.radio/com.yfve.ici.app.radio.IRadio";
    public static final String RADIO_IF_NAME = "com.yfve.ici.app.radio.IRadio";
    public static final String RADIO_PACKAGE_NAME = "com.ici.radio";
    public static final String RADIO_SERVICE_ACTION = "ici.intent.action.radio";
    public static final String REMINDER_BINDER_NAME = "com.ici.reminder/com.yfve.ici.app.reminder.IReminderProxy";
    public static final String REMINDER_IF_NAME = "com.yfve.ici.app.reminder.IReminderProxy";
    public static final String REMINDER_PACKAGE_NAME = "com.ici.reminder";
    public static final String REMINDER_SERVICE_ACTION = "ici.intent.action.REMINDER";
    public static final String SEPARATOR = "/";
    public static final String SETTING_BINDER_NAME = "com.ici.systemsetting/com.yfve.ici.app.setting.ISetting";
    public static final String SETTING_IF_NAME = "com.yfve.ici.app.setting.ISetting";
    public static final String SETTING_PACKAGE_NAME = "com.ici.systemsetting";
    public static final String SETTING_SERVICE_ACTION = "ici.intent.action.setting";
    public static final String SETTING_SYNC_BINDER_NAME = "com.ici.account/com.yfve.ici.app.account.ISettingSyncProxy";
    public static final String SETTING_SYNC_IF_NAME = "com.yfve.ici.app.account.ISettingSyncProxy";
    public static final String SETTING_SYNC_SERVICE_ACTION = "ici.intent.action.SETTINGSYNC";
    public static final String SOURCE_BINDER_NAME = "com.ici.audio.source/com.yfve.ici.app.source.IAudioSourceInterface";
    public static final String SOURCE_IF_NAME = "com.yfve.ici.app.source.IAudioSourceInterface";
    public static final String SOURCE_PACKAGE_NAME = "com.ici.audio.source";
    public static final String SOURCE_SERVICE_ACTION = "ici.intent.action.sourcemanager";
    public static final String SOURCE_VR_BINDER_NAME = "com.ici.audio.source/com.yfve.ici.app.source.ISourceManagerInterface";
    public static final String SOURCE_VR_IF_NAME = "com.yfve.ici.app.source.ISourceManagerInterface";
    public static final String SOURCE_VR_PACKAGE_NAME = "com.ici.audio.source";
    public static final String SOURCE_VR_SERVICE_ACTION = "ici.intent.action.sourcemanager.vr";
    public static final String SVR_MNG_BINDER_NAME = "com.yfve.ici.service.servicemgr";
    public static final String SVR_MNG_IF_NAME = "com.yfve.ici.service.servicemgr.IServiceMgr";
    public static final String TAG_LOG = "CoreService";
    public static final String USBMEDIA_PACKAGE_NAME = "com.ici.media";
    public static final String USBMEDIA_SERVICE_ACTION = "ici.intent.action.SCAN";
    public static final String VEHICLECONTROL_BINDER_NAME = "com.ici.vehiclecontrol/com.yfve.ici.app.vehiclecontrol.IVehicleControlManager";
    public static final String VEHICLECONTROL_IF_NAME = "com.yfve.ici.app.vehiclecontrol.IVehicleControlManager";
    public static final String VEHICLECONTROL_PACKAGE_NAME = "com.ici.vehiclecontrol";
    public static final String VEHICLECONTROL_SERVICE_ACTION = "com.ici.vehiclecontrol.action.VehicleControlService";
    public static final String VEHICLEINFO_BINDER_NAME = "com.ici.vehiclestatusremind/com.yfve.ici.app.vehicleinfo.IVehicleInfoManager";
    public static final String VEHICLEINFO_IF_NAME = "com.yfve.ici.app.vehicleinfo.IVehicleInfoManager";
    public static final String VEHICLEINFO_PACKAGE_NAME = "com.ici.vehiclestatusremind";
    public static final String VEHICLEINFO_SERVICE_ACTION = "com.ici.vehiclestatusremind.action.VehicleInfoService";
    public static final String WALLPAPERTHEME_BINDER_NAME = "com.ici.wallpapertheme/com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager";
    public static final String WALLPAPERTHEME_IF_NAME = "com.yfve.ici.app.wallpapertheme.IWallpaperThemeManager";
    public static final String WALLPAPERTHEME_PACKAGE_NAME = "com.ici.wallpapertheme";
    public static final String WALLPAPERTHEME_SERVICE_ACTION = "ici.intent.action.WallpaperTheme";
    public static final String WCS_PACKAGE_NAME = "com.ici.wcs";
    public static final String WCS_SERVICE_ACTION = "ici.intent.action.wcs";
}
