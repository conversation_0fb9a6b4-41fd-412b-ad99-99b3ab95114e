package com.baidu.che.voice.control.vts.parser;

import com.baidu.che.voice.control.vts.ParserInterface;
import com.baidu.che.voice.control.vts.VtsControlType;

/* loaded from: classes.dex */
public class IovFavoriteParser implements ParserInterface {
    @Override // com.baidu.che.voice.control.vts.ParserInterface
    public String getType() {
        return VtsControlType.IOV_FAVORITE;
    }

    /* JADX WARN: Removed duplicated region for block: B:13:0x003d A[LOOP:0: B:11:0x0037->B:13:0x003d, LOOP_END] */
    @Override // com.baidu.che.voice.control.vts.ParserInterface
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public boolean parse(com.alibaba.fastjson.JSONObject r5, java.util.List<com.baidu.che.voice.control.vts.VtsDirectiveListener> r6) {
        /*
            r4 = this;
            java.lang.String r0 = "params"
            com.alibaba.fastjson.JSONObject r0 = r5.getJSONObject(r0)
            java.lang.String r1 = "url"
            java.lang.String r5 = r5.getString(r1)
            java.lang.String r1 = "&"
            java.lang.String[] r2 = r5.split(r1)
            int r2 = r2.length
            r3 = 3
            if (r2 < r3) goto L26
            java.lang.String[] r5 = r5.split(r1)
            r1 = 2
            r5 = r5[r1]
            int r5 = java.lang.Integer.parseInt(r5)     // Catch: java.lang.Exception -> L22
            goto L27
        L22:
            r5 = move-exception
            r5.printStackTrace()
        L26:
            r5 = -1
        L27:
            java.lang.String r1 = "action"
            java.lang.String r1 = r0.getString(r1)
            java.lang.String r2 = "l_name"
            java.lang.String r0 = r0.getString(r2)
            java.util.Iterator r6 = r6.iterator()
        L37:
            boolean r2 = r6.hasNext()
            if (r2 == 0) goto L47
            java.lang.Object r2 = r6.next()
            com.baidu.che.voice.control.vts.VtsDirectiveListener r2 = (com.baidu.che.voice.control.vts.VtsDirectiveListener) r2
            r2.onFavorite(r1, r5, r0)
            goto L37
        L47:
            r5 = 1
            return r5
        */
        throw new UnsupportedOperationException("Method not decompiled: com.baidu.che.voice.control.vts.parser.IovFavoriteParser.parse(com.alibaba.fastjson.JSONObject, java.util.List):boolean");
    }
}
