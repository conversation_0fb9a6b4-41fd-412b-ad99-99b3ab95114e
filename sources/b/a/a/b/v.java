package b.a.a.b;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothPan;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.util.Log;
import java.util.HashMap;
import java.util.List;

/* compiled from: PanProfile.java */
/* loaded from: classes.dex */
public class v implements q {

    /* renamed from: a  reason: collision with root package name */
    public BluetoothPan f65a;

    /* renamed from: b  reason: collision with root package name */
    public boolean f66b;
    public final o c;
    public final HashMap<BluetoothDevice, Integer> d = new HashMap<>();

    /* compiled from: PanProfile.java */
    /* loaded from: classes.dex */
    public final class b implements BluetoothProfile.ServiceListener {
        public b(a aVar) {
        }

        @Override // android.bluetooth.BluetoothProfile.ServiceListener
        public void onServiceConnected(int i, BluetoothProfile bluetoothProfile) {
            Log.i("PanProfile", "Bluetooth service connected");
            v.this.f65a = (BluetoothPan) bluetoothProfile;
        }

        @Override // android.bluetooth.BluetoothProfile.ServiceListener
        public void onServiceDisconnected(int i) {
            Log.i("PanProfile", "Bluetooth service disconnected");
            v.this.f66b = false;
        }
    }

    public v(Context context, o oVar) {
        this.c = oVar;
        oVar.f51a.getProfileProxy(context, new b(null), 5);
    }

    @Override // b.a.a.b.q
    public int a() {
        return 5;
    }

    @Override // b.a.a.b.q
    public boolean b(BluetoothDevice bluetoothDevice) {
        BluetoothPan bluetoothPan = this.f65a;
        if (bluetoothPan == null) {
            return false;
        }
        return bluetoothPan.disconnect(bluetoothDevice);
    }

    @Override // b.a.a.b.q
    public boolean c() {
        return false;
    }

    @Override // b.a.a.b.q
    public int d(BluetoothDevice bluetoothDevice) {
        BluetoothPan bluetoothPan = this.f65a;
        if (bluetoothPan == null) {
            return 0;
        }
        return bluetoothPan.getConnectionState(bluetoothDevice);
    }

    @Override // b.a.a.b.q
    public void e(BluetoothDevice bluetoothDevice, boolean z) {
    }

    @Override // b.a.a.b.q
    public boolean f(BluetoothDevice bluetoothDevice) {
        BluetoothPan bluetoothPan = this.f65a;
        if (bluetoothPan == null) {
            return false;
        }
        List<BluetoothDevice> connectedDevices = bluetoothPan.getConnectedDevices();
        if (connectedDevices != null) {
            for (BluetoothDevice bluetoothDevice2 : connectedDevices) {
                this.f65a.disconnect(bluetoothDevice2);
            }
        }
        return this.f65a.connect(bluetoothDevice);
    }

    public void finalize() {
        Log.i("PanProfile", "finalize()");
        if (this.f65a != null) {
            try {
                BluetoothAdapter.getDefaultAdapter().closeProfileProxy(5, this.f65a);
                this.f65a = null;
            } catch (Throwable th) {
                Log.w("PanProfile", "Error cleaning up PAN proxy", th);
            }
        }
    }

    @Override // b.a.a.b.q
    public boolean g(BluetoothDevice bluetoothDevice) {
        return true;
    }

    @Override // b.a.a.b.q
    public boolean h() {
        return true;
    }

    public boolean i(BluetoothDevice bluetoothDevice) {
        return this.d.containsKey(bluetoothDevice) && this.d.get(bluetoothDevice).intValue() == 1;
    }

    public String toString() {
        return "PAN";
    }
}
