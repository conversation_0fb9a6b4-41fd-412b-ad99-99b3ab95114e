package b.a.a.b;

import android.bluetooth.BluetoothA2dp;
import android.bluetooth.BluetoothClass;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothHeadset;
import android.bluetooth.BluetoothHearingAid;
import android.bluetooth.BluetoothPbap;
import android.bluetooth.BluetoothUuid;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.media.AudioManager;
import android.os.ParcelUuid;
import android.os.SystemClock;
import android.os.UserHandle;
import android.text.TextUtils;
import android.util.Log;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

/* compiled from: CachedBluetoothDevice.java */
/* loaded from: classes.dex */
public class h implements Comparable<h> {

    /* renamed from: b  reason: collision with root package name */
    public final Context f33b;
    public final o c;
    public final r d;
    public final BluetoothDevice e;
    public String f;
    public long g;
    public short h;
    public int i;
    public int j;
    public int k;
    public BluetoothClass l;
    public HashMap<q, Integer> m;
    public boolean p;
    public boolean q;
    public int r;
    public long u;
    public final List<q> n = new ArrayList();
    public final List<q> o = new ArrayList();
    public final Collection<a> s = new ArrayList();
    public final Collection<b> t = new ArrayList();
    public boolean v = false;
    public boolean w = false;
    public boolean x = false;

    /* compiled from: CachedBluetoothDevice.java */
    /* loaded from: classes.dex */
    public interface a {
        void a();
    }

    /* compiled from: CachedBluetoothDevice.java */
    /* loaded from: classes.dex */
    public interface b {
        void a();
    }

    public h(Context context, o oVar, r rVar, BluetoothDevice bluetoothDevice) {
        this.f33b = context;
        this.c = oVar;
        this.d = rVar;
        AudioManager audioManager = (AudioManager) context.getSystemService(AudioManager.class);
        this.e = bluetoothDevice;
        this.m = new HashMap<>();
        i();
        this.l = this.e.getBluetoothClass();
        w();
        h();
        SharedPreferences sharedPreferences = this.f33b.getSharedPreferences("bluetooth_phonebook_permission", 0);
        if (sharedPreferences.contains(this.e.getAddress())) {
            if (this.e.getPhonebookAccessPermission() == 0) {
                int i = sharedPreferences.getInt(this.e.getAddress(), 0);
                if (i == 1) {
                    this.e.setPhonebookAccessPermission(1);
                } else if (i == 2) {
                    this.e.setPhonebookAccessPermission(2);
                }
            }
            SharedPreferences.Editor edit = sharedPreferences.edit();
            edit.remove(this.e.getAddress());
            edit.commit();
        }
        SharedPreferences sharedPreferences2 = this.f33b.getSharedPreferences("bluetooth_message_permission", 0);
        if (sharedPreferences2.contains(this.e.getAddress())) {
            if (this.e.getMessageAccessPermission() == 0) {
                int i2 = sharedPreferences2.getInt(this.e.getAddress(), 0);
                if (i2 == 1) {
                    this.e.setMessageAccessPermission(1);
                } else if (i2 == 2) {
                    this.e.setMessageAccessPermission(2);
                }
            }
            SharedPreferences.Editor edit2 = sharedPreferences2.edit();
            edit2.remove(this.e.getAddress());
            edit2.commit();
        }
        this.r = this.f33b.getSharedPreferences("bluetooth_message_reject", 0).getInt(this.e.getAddress(), 0);
        f();
        this.g = 0L;
    }

    public void a() {
        StringBuilder e = b.a.b.a.a.e(" Clearing all connection state for dev:");
        e.append(this.e.getName());
        Log.i("CachedBluetoothDevice", e.toString());
        for (q qVar : l()) {
            this.m.put(qVar, 0);
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:39:0x00aa A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:44:0x0039 A[SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void b(boolean r7) {
        /*
            r6 = this;
            boolean r0 = r6.g()
            if (r0 != 0) goto L7
            return
        L7:
            long r0 = android.os.SystemClock.elapsedRealtime()
            r6.u = r0
            java.util.List<b.a.a.b.q> r0 = r6.n
            boolean r0 = r0.isEmpty()
            java.lang.String r1 = "CachedBluetoothDevice"
            if (r0 == 0) goto L1e
            java.lang.String r7 = "No profiles. Maybe we will connect later"
            android.util.Log.i(r1, r7)
            goto Le0
        L1e:
            android.bluetooth.BluetoothDevice r0 = r6.e
            r6.n(r0)
            b.a.a.b.o r0 = r6.c
            boolean r0 = r0.b()
            if (r0 == 0) goto L32
            b.a.a.b.o r0 = r6.c
            android.bluetooth.BluetoothAdapter r0 = r0.f51a
            r0.cancelDiscovery()
        L32:
            r0 = 0
            java.util.List<b.a.a.b.q> r2 = r6.n
            java.util.Iterator r2 = r2.iterator()
        L39:
            boolean r3 = r2.hasNext()
            if (r3 == 0) goto Lb0
            java.lang.Object r3 = r2.next()
            b.a.a.b.q r3 = (b.a.a.b.q) r3
            java.lang.String r4 = "try to connectInt "
            java.lang.StringBuilder r4 = b.a.b.a.a.e(r4)
            java.lang.String r5 = r6.d(r3)
            r4.append(r5)
            java.lang.String r5 = ", connectAllProfiles = "
            r4.append(r5)
            r4.append(r7)
            java.lang.String r4 = r4.toString()
            android.util.Log.i(r1, r4)
            java.lang.StringBuilder r4 = new java.lang.StringBuilder
            r4.<init>()
            java.lang.String r5 = "isConnectable = "
            r4.append(r5)
            boolean r5 = r3.h()
            r4.append(r5)
            java.lang.String r5 = ", isAutoConnectable = "
            r4.append(r5)
            boolean r5 = r3.c()
            r4.append(r5)
            java.lang.String r5 = ",isPreferred = "
            r4.append(r5)
            android.bluetooth.BluetoothDevice r5 = r6.e
            boolean r5 = r3.g(r5)
            r4.append(r5)
            java.lang.String r4 = r4.toString()
            android.util.Log.i(r1, r4)
            if (r7 == 0) goto L9c
            boolean r4 = r3.h()
            if (r4 == 0) goto L39
            goto La2
        L9c:
            boolean r4 = r3.c()
            if (r4 == 0) goto L39
        La2:
            android.bluetooth.BluetoothDevice r4 = r6.e
            boolean r4 = r3.g(r4)
            if (r4 == 0) goto L39
            int r0 = r0 + 1
            r6.c(r3)
            goto L39
        Lb0:
            java.lang.String r7 = "Preferred profiles = "
            b.a.b.a.a.i(r7, r0, r1)
            if (r0 != 0) goto Le0
            boolean r7 = r6.g()
            if (r7 != 0) goto Lbe
            goto Le0
        Lbe:
            java.util.List<b.a.a.b.q> r7 = r6.n
            java.util.Iterator r7 = r7.iterator()
        Lc4:
            boolean r0 = r7.hasNext()
            if (r0 == 0) goto Le0
            java.lang.Object r0 = r7.next()
            b.a.a.b.q r0 = (b.a.a.b.q) r0
            boolean r1 = r0.c()
            if (r1 == 0) goto Lc4
            android.bluetooth.BluetoothDevice r1 = r6.e
            r2 = 1
            r0.e(r1, r2)
            r6.c(r0)
            goto Lc4
        Le0:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: b.a.a.b.h.b(boolean):void");
    }

    public synchronized void c(q qVar) {
        if (g()) {
            if (qVar.f(this.e)) {
                Log.i("CachedBluetoothDevice", "Command sent successfully:CONNECT " + d(qVar));
                return;
            }
            Log.i("CachedBluetoothDevice", "Failed to connect " + qVar.toString() + " to " + this.f);
        }
    }

    @Override // java.lang.Comparable
    public int compareTo(h hVar) {
        h hVar2 = hVar;
        int i = (hVar2.m() ? 1 : 0) - (m() ? 1 : 0);
        if (i != 0) {
            return i;
        }
        int i2 = (hVar2.k() == 12 ? 1 : 0) - (k() != 12 ? 0 : 1);
        if (i2 != 0) {
            return i2;
        }
        int i3 = (hVar2.q ? 1 : 0) - (this.q ? 1 : 0);
        if (i3 != 0) {
            return i3;
        }
        int i4 = hVar2.h - this.h;
        return i4 != 0 ? i4 : this.f.compareTo(hVar2.f);
    }

    public final String d(q qVar) {
        StringBuilder e = b.a.b.a.a.e("Address:");
        e.append(this.e);
        if (qVar != null) {
            e.append(" Profile:");
            e.append(qVar);
        }
        return e.toString();
    }

    public void e() {
        n(this.e);
        for (q qVar : this.n) {
            if (qVar.b(this.e)) {
                StringBuilder e = b.a.b.a.a.e("Command sent successfully:DISCONNECT ");
                e.append(d(qVar));
                Log.i("CachedBluetoothDevice", e.toString());
            }
        }
        x xVar = this.d.p;
        if (xVar.d(this.e) == 2) {
            BluetoothDevice bluetoothDevice = this.e;
            BluetoothPbap bluetoothPbap = xVar.f71a;
            if (bluetoothPbap == null) {
                return;
            }
            bluetoothPbap.disconnect(bluetoothDevice);
        }
    }

    public boolean equals(Object obj) {
        if (obj == null || !(obj instanceof h)) {
            return false;
        }
        return this.e.equals(((h) obj).e);
    }

    public final void f() {
        synchronized (this.s) {
            for (a aVar : this.s) {
                aVar.a();
            }
        }
    }

    public final boolean g() {
        if (k() == 10) {
            u();
            return false;
        }
        return true;
    }

    public final void h() {
        d dVar = this.d.e;
        if (dVar != null) {
            BluetoothDevice bluetoothDevice = this.e;
            BluetoothA2dp bluetoothA2dp = dVar.f9a;
            this.v = bluetoothDevice.equals(bluetoothA2dp == null ? null : bluetoothA2dp.getActiveDevice());
        }
        j jVar = this.d.g;
        if (jVar != null) {
            BluetoothDevice bluetoothDevice2 = this.e;
            BluetoothHeadset bluetoothHeadset = jVar.f36a;
            this.w = bluetoothDevice2.equals(bluetoothHeadset != null ? bluetoothHeadset.getActiveDevice() : null);
        }
        k kVar = this.d.s;
        if (kVar != null) {
            BluetoothHearingAid bluetoothHearingAid = kVar.f39a;
            this.x = (bluetoothHearingAid == null ? new ArrayList() : bluetoothHearingAid.getActiveDevices()).contains(this.e);
        }
    }

    public int hashCode() {
        return this.e.getAddress().hashCode();
    }

    public final void i() {
        String aliasName = this.e.getAliasName();
        this.f = aliasName;
        if (TextUtils.isEmpty(aliasName)) {
            this.f = this.e.getAddress();
            b.a.b.a.a.p(b.a.b.a.a.e("Device has no name (yet), use address: "), this.f, "CachedBluetoothDevice");
        }
    }

    public String j() {
        return this.e.getAddress();
    }

    public int k() {
        return this.e.getBondState();
    }

    public List<q> l() {
        return Collections.unmodifiableList(this.n);
    }

    public boolean m() {
        int i;
        Iterator<q> it = this.n.iterator();
        do {
            i = 0;
            if (!it.hasNext()) {
                return false;
            }
            q next = it.next();
            if (this.m.get(next) == null) {
                if (next != null) {
                    this.m.put(next, Integer.valueOf(next.d(this.e)));
                }
            }
            i = this.m.get(next).intValue();
        } while (i != 2);
        return true;
    }

    public final void n(BluetoothDevice bluetoothDevice) {
        Intent intent = new Intent("android.btsettings.action.START_MANUAL_CON");
        intent.putExtra("android.bluetooth.device.extra.DEVICE", bluetoothDevice);
        this.f33b.sendBroadcastAsUser(intent, UserHandle.ALL);
        Log.i("CachedBluetoothDevice", "notifyManualConnect, start manual connect");
    }

    /* JADX WARN: Removed duplicated region for block: B:26:0x0047  */
    /* JADX WARN: Removed duplicated region for block: B:28:? A[RETURN, SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void o(boolean r4, int r5) {
        /*
            r3 = this;
            r0 = 1
            r1 = 0
            if (r5 == r0) goto L3c
            r2 = 2
            if (r5 == r2) goto L33
            r2 = 21
            if (r5 == r2) goto L2a
            java.lang.StringBuilder r0 = new java.lang.StringBuilder
            r0.<init>()
            java.lang.String r2 = "onActiveDeviceChanged: unknown profile "
            r0.append(r2)
            r0.append(r5)
            java.lang.String r5 = " isActive "
            r0.append(r5)
            r0.append(r4)
            java.lang.String r4 = r0.toString()
            java.lang.String r5 = "CachedBluetoothDevice"
            android.util.Log.w(r5, r4)
            goto L45
        L2a:
            boolean r5 = r3.x
            if (r5 == r4) goto L2f
            goto L30
        L2f:
            r0 = r1
        L30:
            r3.x = r4
            goto L44
        L33:
            boolean r5 = r3.v
            if (r5 == r4) goto L38
            goto L39
        L38:
            r0 = r1
        L39:
            r3.v = r4
            goto L44
        L3c:
            boolean r5 = r3.w
            if (r5 == r4) goto L41
            goto L42
        L41:
            r0 = r1
        L42:
            r3.w = r4
        L44:
            r1 = r0
        L45:
            if (r1 == 0) goto L4a
            r3.f()
        L4a:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: b.a.a.b.h.o(boolean, int):void");
    }

    public void p(q qVar, int i) {
        int i2;
        Log.i("CachedBluetoothDevice", "onProfileStateChanged: profile " + qVar + " newProfileState " + i);
        o oVar = this.c;
        synchronized (oVar) {
            if (oVar.f51a.getState() != oVar.c) {
                oVar.c(oVar.f51a.getState());
            }
            i2 = oVar.c;
        }
        if (i2 == 13) {
            Log.i("CachedBluetoothDevice", " BT Turninig Off...Profile conn state change ignored...");
            return;
        }
        this.m.put(qVar, Integer.valueOf(i));
        if (i == 2) {
            if (qVar instanceof t) {
                qVar.e(this.e, true);
            }
            if (!this.n.contains(qVar)) {
                this.o.remove(qVar);
                Log.i("CachedBluetoothDevice", " add profile " + qVar.toString());
                this.n.add(qVar);
                if ((qVar instanceof v) && ((v) qVar).i(this.e)) {
                    this.p = true;
                }
            }
        } else if ((qVar instanceof t) && i == 0) {
            qVar.e(this.e, false);
        } else if (this.p && (qVar instanceof v) && ((v) qVar).i(this.e) && i == 0) {
            Log.i("CachedBluetoothDevice", "Removing PanProfile from device after NAP disconnect");
            this.n.remove(qVar);
            this.o.add(qVar);
            this.p = false;
        }
        h();
    }

    public void q() {
        w();
        BluetoothUuid.isUuidPresent(this.e.getUuids(), BluetoothUuid.Hogp);
        StringBuilder e = b.a.b.a.a.e("onUuidChanged: Time since last connect");
        e.append(SystemClock.elapsedRealtime() - this.u);
        Log.i("CachedBluetoothDevice", e.toString());
        if (!this.n.isEmpty()) {
            SystemClock.elapsedRealtime();
        }
        f();
        synchronized (this.t) {
            for (b bVar : this.t) {
                bVar.a();
            }
        }
    }

    public void r(long j) {
        StringBuilder e = b.a.b.a.a.e("setHiSyncId: mDevice ");
        e.append(this.e);
        e.append(", id ");
        e.append(j);
        Log.i("CachedBluetoothDevice", e.toString());
        this.g = j;
    }

    public void s(boolean z) {
        if (this.q != z) {
            this.q = z;
            f();
        }
    }

    public void t(int i) {
        int i2 = 2;
        if (i == 1) {
            i2 = 1;
        } else if (i != 2) {
            i2 = 0;
        }
        this.e.setPhonebookAccessPermission(i2);
    }

    public String toString() {
        return this.e.toString();
    }

    public boolean u() {
        if (this.c.b()) {
            this.c.f51a.cancelDiscovery();
        }
        return this.e.createBond();
    }

    public void v() {
        BluetoothDevice bluetoothDevice;
        int k = k();
        if (k == 11) {
            this.e.cancelBondProcess();
        }
        if (k == 10 || (bluetoothDevice = this.e) == null) {
            return;
        }
        if (bluetoothDevice.removeBond()) {
            StringBuilder e = b.a.b.a.a.e("Command sent successfully:REMOVE_BOND ");
            e.append(d(null));
            Log.i("CachedBluetoothDevice", e.toString());
            return;
        }
        StringBuilder e2 = b.a.b.a.a.e("Framework rejected command immediately:REMOVE_BOND ");
        e2.append(d(null));
        Log.v("CachedBluetoothDevice", e2.toString());
    }

    public final boolean w() {
        ParcelUuid[] a2;
        ParcelUuid[] uuids = this.e.getUuids();
        if (uuids == null || (a2 = this.c.a()) == null) {
            return false;
        }
        if (this.e.getBondState() == 12 && BluetoothUuid.containsAnyUuid(this.e.getUuids(), x.c)) {
            int phonebookAccessPermission = this.e.getPhonebookAccessPermission();
            if (!(phonebookAccessPermission == 1 ? true : phonebookAccessPermission == 2 ? true : false)) {
                if (this.e.getBluetoothClass().getDeviceClass() != 1032 && this.e.getBluetoothClass().getDeviceClass() != 1028) {
                    t(2);
                } else {
                    t(1);
                }
            }
        }
        r rVar = this.d;
        List<q> list = this.n;
        List<q> list2 = this.o;
        boolean z = this.p;
        BluetoothDevice bluetoothDevice = this.e;
        synchronized (rVar) {
            list2.clear();
            list2.addAll(list);
            Log.i("LocalBluetoothProfileManager", "Current Profiles" + list.toString());
            list.clear();
            if (rVar.g != null && ((BluetoothUuid.isUuidPresent(a2, BluetoothUuid.HSP_AG) && BluetoothUuid.isUuidPresent(uuids, BluetoothUuid.HSP)) || (BluetoothUuid.isUuidPresent(a2, BluetoothUuid.Handsfree_AG) && BluetoothUuid.isUuidPresent(uuids, BluetoothUuid.Handsfree)))) {
                list.add(rVar.g);
                list2.remove(rVar.g);
            }
            if (rVar.h != null && BluetoothUuid.isUuidPresent(uuids, BluetoothUuid.Handsfree_AG)) {
                Log.i("LocalBluetoothProfileManager", "add Profile" + list.toString());
                list.add(rVar.h);
                list2.remove(rVar.h);
            }
            if (BluetoothUuid.containsAnyUuid(uuids, d.f) && rVar.e != null) {
                list.add(rVar.e);
                list2.remove(rVar.e);
            }
            if (BluetoothUuid.containsAnyUuid(uuids, e.f) && rVar.f != null) {
                Log.i("LocalBluetoothProfileManager", "add Profile" + list.toString());
                list.add(rVar.f);
                list2.remove(rVar.f);
            }
            if (BluetoothUuid.isUuidPresent(uuids, BluetoothUuid.ObexObjectPush) && rVar.m != null) {
                list.add(rVar.m);
                list2.remove(rVar.m);
            }
            if ((BluetoothUuid.isUuidPresent(uuids, BluetoothUuid.Hid) || BluetoothUuid.isUuidPresent(uuids, BluetoothUuid.Hogp)) && rVar.k != null) {
                list.add(rVar.k);
                list2.remove(rVar.k);
            }
            if (rVar.l != null && rVar.l.d(bluetoothDevice) != 0) {
                list.add(rVar.l);
                list2.remove(rVar.l);
            }
            if (z) {
                Log.i("LocalBluetoothProfileManager", "Valid PAN-NAP connection exists.");
            }
            if ((BluetoothUuid.isUuidPresent(uuids, BluetoothUuid.NAP) && rVar.n != null) || z) {
                list.add(rVar.n);
                list2.remove(rVar.n);
            }
            if (rVar.i != null && rVar.i.d(bluetoothDevice) == 2) {
                list.add(rVar.i);
                list2.remove(rVar.i);
                rVar.i.e(bluetoothDevice, true);
            }
            if (rVar.p != null && rVar.p.d(bluetoothDevice) == 2) {
                list.add(rVar.p);
                list2.remove(rVar.p);
                if (rVar.p == null) {
                    throw null;
                }
            }
            if (rVar.j != null) {
                list.add(rVar.j);
                list2.remove(rVar.j);
            }
            if (rVar.q && rVar.o != null) {
                list.add(rVar.o);
                list2.remove(rVar.o);
            }
            if (BluetoothUuid.isUuidPresent(uuids, BluetoothUuid.HearingAid) && rVar.s != null) {
                list.add(rVar.s);
                list2.remove(rVar.s);
            }
            Log.i("LocalBluetoothProfileManager", "New Profiles" + list.toString());
        }
        StringBuilder e = b.a.b.a.a.e("updating profiles for ");
        e.append(this.e.getAliasName());
        Log.e("CachedBluetoothDevice", e.toString());
        BluetoothClass bluetoothClass = this.e.getBluetoothClass();
        if (bluetoothClass != null) {
            StringBuilder e2 = b.a.b.a.a.e("Class: ");
            e2.append(bluetoothClass.toString());
            Log.v("CachedBluetoothDevice", e2.toString());
        }
        Log.v("CachedBluetoothDevice", "UUID:");
        for (ParcelUuid parcelUuid : uuids) {
            Log.v("CachedBluetoothDevice", "  " + parcelUuid);
        }
        return true;
    }
}
