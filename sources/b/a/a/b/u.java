package b.a.a.b;

import android.bluetooth.BluetoothDevice;

/* compiled from: OppProfile.java */
/* loaded from: classes.dex */
public final class u implements q {
    @Override // b.a.a.b.q
    public int a() {
        return 20;
    }

    @Override // b.a.a.b.q
    public boolean b(BluetoothDevice bluetoothDevice) {
        return false;
    }

    @Override // b.a.a.b.q
    public boolean c() {
        return false;
    }

    @Override // b.a.a.b.q
    public int d(BluetoothDevice bluetoothDevice) {
        return 0;
    }

    @Override // b.a.a.b.q
    public void e(BluetoothDevice bluetoothDevice, boolean z) {
    }

    @Override // b.a.a.b.q
    public boolean f(BluetoothDevice bluetoothDevice) {
        return false;
    }

    @Override // b.a.a.b.q
    public boolean g(BluetoothDevice bluetoothDevice) {
        return false;
    }

    @Override // b.a.a.b.q
    public boolean h() {
        return false;
    }

    public String toString() {
        return "OPP";
    }
}
