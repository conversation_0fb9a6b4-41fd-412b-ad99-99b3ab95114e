package b.a.a.b;

import android.bluetooth.BluetoothAdapter;
import android.os.ParcelUuid;

/* compiled from: LocalBluetoothAdapter.java */
/* loaded from: classes.dex */
public class o {
    public static o e;

    /* renamed from: a  reason: collision with root package name */
    public final BluetoothAdapter f51a;

    /* renamed from: b  reason: collision with root package name */
    public r f52b;
    public int c = Integer.MIN_VALUE;
    public long d;

    public o(BluetoothAdapter bluetoothAdapter) {
        this.f51a = bluetoothAdapter;
    }

    public ParcelUuid[] a() {
        return this.f51a.getUuids();
    }

    public boolean b() {
        return this.f51a.isDiscovering();
    }

    public void c(int i) {
        r rVar;
        synchronized (this) {
            if (this.c == i) {
                return;
            }
            this.c = i;
            if (i != 12 || (rVar = this.f52b) == null) {
                return;
            }
            ParcelUuid[] a2 = rVar.f56b.a();
            if (a2 != null) {
                rVar.e(a2);
            }
            rVar.d.c();
        }
    }
}
