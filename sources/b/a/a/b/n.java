package b.a.a.b;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothHidHost;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.util.Log;
import java.util.List;

/* compiled from: HidProfile.java */
/* loaded from: classes.dex */
public class n implements q {

    /* renamed from: a  reason: collision with root package name */
    public BluetoothHidHost f48a;

    /* renamed from: b  reason: collision with root package name */
    public boolean f49b;
    public final o c;
    public final i d;
    public final r e;

    /* compiled from: HidProfile.java */
    /* loaded from: classes.dex */
    public final class b implements BluetoothProfile.ServiceListener {
        public b(a aVar) {
        }

        @Override // android.bluetooth.BluetoothProfile.ServiceListener
        public void onServiceConnected(int i, BluetoothProfile bluetoothProfile) {
            Log.i("HidProfile", "Bluetooth service connected");
            BluetoothHidHost bluetoothHidHost = (BluetoothHidHost) bluetoothProfile;
            n.this.f48a = bluetoothHidHost;
            List connectedDevices = bluetoothHidHost.getConnectedDevices();
            while (!connectedDevices.isEmpty()) {
                BluetoothDevice bluetoothDevice = (BluetoothDevice) connectedDevices.remove(0);
                h b2 = n.this.d.b(bluetoothDevice);
                if (b2 == null) {
                    Log.w("HidProfile", "HidProfile found new device: " + bluetoothDevice);
                    n nVar = n.this;
                    b2 = nVar.d.a(nVar.c, nVar.e, bluetoothDevice);
                }
                b2.p(n.this, 2);
                b2.f();
            }
            n.this.f49b = true;
        }

        @Override // android.bluetooth.BluetoothProfile.ServiceListener
        public void onServiceDisconnected(int i) {
            Log.i("HidProfile", "Bluetooth service disconnected");
            n.this.f49b = false;
        }
    }

    public n(Context context, o oVar, i iVar, r rVar) {
        this.c = oVar;
        this.d = iVar;
        this.e = rVar;
        oVar.f51a.getProfileProxy(context, new b(null), 4);
    }

    @Override // b.a.a.b.q
    public int a() {
        return 4;
    }

    @Override // b.a.a.b.q
    public boolean b(BluetoothDevice bluetoothDevice) {
        BluetoothHidHost bluetoothHidHost = this.f48a;
        if (bluetoothHidHost == null) {
            return false;
        }
        return bluetoothHidHost.disconnect(bluetoothDevice);
    }

    @Override // b.a.a.b.q
    public boolean c() {
        return true;
    }

    @Override // b.a.a.b.q
    public int d(BluetoothDevice bluetoothDevice) {
        BluetoothHidHost bluetoothHidHost = this.f48a;
        if (bluetoothHidHost == null) {
            return 0;
        }
        List connectedDevices = bluetoothHidHost.getConnectedDevices();
        if (connectedDevices.isEmpty() || !((BluetoothDevice) connectedDevices.get(0)).equals(bluetoothDevice)) {
            return 0;
        }
        return this.f48a.getConnectionState(bluetoothDevice);
    }

    @Override // b.a.a.b.q
    public void e(BluetoothDevice bluetoothDevice, boolean z) {
        BluetoothHidHost bluetoothHidHost = this.f48a;
        if (bluetoothHidHost == null) {
            return;
        }
        if (z) {
            if (bluetoothHidHost.getPriority(bluetoothDevice) < 100) {
                this.f48a.setPriority(bluetoothDevice, 100);
                return;
            }
            return;
        }
        bluetoothHidHost.setPriority(bluetoothDevice, 0);
    }

    @Override // b.a.a.b.q
    public boolean f(BluetoothDevice bluetoothDevice) {
        BluetoothHidHost bluetoothHidHost = this.f48a;
        if (bluetoothHidHost == null) {
            return false;
        }
        return bluetoothHidHost.connect(bluetoothDevice);
    }

    public void finalize() {
        Log.i("HidProfile", "finalize()");
        if (this.f48a != null) {
            try {
                BluetoothAdapter.getDefaultAdapter().closeProfileProxy(4, this.f48a);
                this.f48a = null;
            } catch (Throwable th) {
                Log.w("HidProfile", "Error cleaning up HID proxy", th);
            }
        }
    }

    @Override // b.a.a.b.q
    public boolean g(BluetoothDevice bluetoothDevice) {
        BluetoothHidHost bluetoothHidHost = this.f48a;
        return bluetoothHidHost != null && bluetoothHidHost.getPriority(bluetoothDevice) > 0;
    }

    @Override // b.a.a.b.q
    public boolean h() {
        return true;
    }

    public String toString() {
        return "HID";
    }
}
