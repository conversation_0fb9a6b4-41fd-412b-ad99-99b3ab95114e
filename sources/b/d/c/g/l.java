package b.d.c.g;

import android.util.Log;
import b.d.c.h.e.e0;
import com.ici.connectivitylib.bean.DevicesPairedStatusBean;
import org.greenrobot.eventbus.EventBus;

/* compiled from: BluetoothDeviceModel.java */
/* loaded from: classes.dex */
public class l implements e0.c {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ b.a.a.b.h f404a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ h f405b;

    public l(h hVar, b.a.a.b.h hVar2) {
        this.f405b = hVar;
        this.f404a = hVar2;
    }

    @Override // b.d.c.h.e.e0.c
    public void h() {
        Log.i("ICI_BS_BluetoothDeviceModel", "配对超时，点击重试按钮");
        e0.l(this.f405b.f384a).R();
    }

    @Override // b.d.c.h.e.e0.c
    public void i() {
        EventBus.getDefault().post(new DevicesPairedStatusBean(0, this.f404a));
    }
}
