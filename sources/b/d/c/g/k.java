package b.d.c.g;

import android.util.Log;
import b.d.c.h.e.e0;

/* compiled from: BluetoothDeviceModel.java */
/* loaded from: classes.dex */
public class k implements e0.c {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ b.a.a.b.h f402a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ h f403b;

    public k(h hVar, b.a.a.b.h hVar2) {
        this.f403b = hVar;
        this.f402a = hVar2;
    }

    @Override // b.d.c.h.e.e0.c
    public void g() {
        e0.l(this.f403b.f384a).c();
        Log.i("ICI_BS_BluetoothDeviceModel", "未成功断开，点击重试按钮");
        this.f403b.u(this.f402a);
    }
}
