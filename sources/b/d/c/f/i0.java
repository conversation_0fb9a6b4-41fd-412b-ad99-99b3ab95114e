package b.d.c.f;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.function.IntConsumer;

/* compiled from: SystemKeyManager.java */
/* loaded from: classes.dex */
public class i0 extends BroadcastReceiver {
    public static i0 c;

    /* renamed from: a  reason: collision with root package name */
    public Context f341a;

    /* renamed from: b  reason: collision with root package name */
    public final List<IntConsumer> f342b = Collections.synchronizedList(new ArrayList());

    public i0(Context context) {
        if (context != null) {
            this.f341a = context;
            return;
        }
        throw new IllegalArgumentException("Context is null");
    }

    public static i0 a(Context context) {
        if (c == null) {
            synchronized (i0.class) {
                if (c == null) {
                    c = new i0(context);
                }
            }
        }
        return c;
    }

    @Override // android.content.BroadcastReceiver
    public void onReceive(Context context, Intent intent) {
        String str;
        String action = intent.getAction();
        if (TextUtils.equals(action, "android.intent.action.CLOSE_SYSTEM_DIALOGS")) {
            str = "HOME KEY";
        } else if (!TextUtils.equals(action, "com.ici.launcher.CLOSE_SYSTEM_DIALOGS")) {
            return;
        } else {
            str = "ICI HOME BUTTON";
        }
        if (TextUtils.equals(intent.getStringExtra("reason"), "homekey")) {
            b.a.b.a.a.k("HOME key event from -> ", str, "Connectivity:SystemKeyManager");
            if (this.f342b.isEmpty()) {
                return;
            }
            Iterator it = new ArrayList(this.f342b).iterator();
            while (it.hasNext()) {
                try {
                    ((IntConsumer) it.next()).accept(3);
                } catch (Exception unused) {
                }
            }
        }
    }
}
