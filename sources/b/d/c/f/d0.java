package b.d.c.f;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.car.Car;
import android.car.hardware.eol.CarYFEolManager;
import android.car.hardware.power.CarPowerManager;
import android.content.ComponentName;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.hardware.usb.UsbDevice;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import androidx.annotation.NonNull;
import androidx.appcompat.widget.TooltipCompatHandler;
import com.ici.connectivitylib.bean.DeviceInfoBean;
import com.ici.connectivitylib.manager.CarlifeManager;
import com.yfve.ici.app.btphone.IBtPhoneCurrentCallStateChangedCallback;
import com.yfve.ici.app.carlife.CarlifeProxy;
import com.yfve.ici.app.carplay.CPDeviceInfo;
import com.yfve.ici.app.carplay.CarPlayProxy;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;

/* compiled from: DeviceManager.java */
/* loaded from: classes.dex */
public class d0 {
    @SuppressLint({"StaticFieldLeak"})
    public static volatile d0 l;

    /* renamed from: a  reason: collision with root package name */
    public Context f326a;

    /* renamed from: b  reason: collision with root package name */
    public b.d.c.g.w f327b;
    public Car c;
    public CarPowerManager d;
    public CarYFEolManager e;
    public final HashMap<String, DeviceInfoBean> f = new HashMap<>();
    public final Handler g = new a(Looper.getMainLooper());
    public final ServiceConnection h = new b();
    public final CarPowerManager.CarPowerStateListener i = new c(this);
    public final CarYFEolManager.CarEolEventCallback j = new d();
    public IBtPhoneCurrentCallStateChangedCallback k = new e();

    /* compiled from: DeviceManager.java */
    /* loaded from: classes.dex */
    public class a extends Handler {
        public a(Looper looper) {
            super(looper);
        }

        @Override // android.os.Handler
        public void dispatchMessage(@NonNull Message message) {
            super.dispatchMessage(message);
            if (message.what == 1) {
                int i = message.arg1;
                if (i == 4 || i == 1) {
                    e0.d(d0.this.f326a).h(e0.d(d0.this.f326a).b());
                }
            }
        }
    }

    /* compiled from: DeviceManager.java */
    /* loaded from: classes.dex */
    public class b implements ServiceConnection {
        public b() {
        }

        @Override // android.content.ServiceConnection
        public void onServiceConnected(ComponentName componentName, IBinder iBinder) {
            try {
                Log.i("Connectivity:DeviceManager", "onServiceConnected: 111");
                d0.a(d0.this);
                d0.b(d0.this);
            } catch (Exception e) {
                e.printStackTrace();
                b.a.a.b.y.f("Connectivity:DeviceManager", "mPowerConnection onServiceConnected exception : " + e.getMessage());
            }
        }

        @Override // android.content.ServiceConnection
        public void onServiceDisconnected(ComponentName componentName) {
            Log.i("Connectivity:DeviceManager", "onServiceDisconnected: 111");
        }
    }

    /* compiled from: DeviceManager.java */
    /* loaded from: classes.dex */
    public class c implements CarPowerManager.CarPowerStateListener {
        public c(d0 d0Var) {
        }

        @Override // android.car.hardware.power.CarPowerManager.CarPowerStateListener
        public void onStateChanged(int i) {
            b.a.b.a.a.i("onStateChanged, state: ", i, "Connectivity:DeviceManager");
        }

        @Override // android.car.hardware.power.CarPowerManager.CarPowerStateListener
        public void onSystemStateBroadCast(int i, int i2) {
            Log.i("Connectivity:DeviceManager", "onSystemStateBroadCast, powerstate: " + i + " systemState " + i2);
        }

        @Override // android.car.hardware.power.CarPowerManager.CarPowerStateListener
        public void onSystemStateNotify(int i, int i2) {
            Log.i("Connectivity:DeviceManager", "onSystemStateNotify, powerstate: " + i + " systemState " + i2);
        }
    }

    /* compiled from: DeviceManager.java */
    /* loaded from: classes.dex */
    public class d implements CarYFEolManager.CarEolEventCallback {
        public d() {
        }

        /* JADX WARN: Removed duplicated region for block: B:26:0x007e  */
        @Override // android.car.hardware.eol.CarYFEolManager.CarEolEventCallback
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct code enable 'Show inconsistent code' option in preferences
        */
        public void onChangeEvent(android.car.hardware.CarPropertyValue r9) {
            /*
                r8 = this;
                java.lang.String r0 = "Connectivity:DeviceManager"
                java.lang.StringBuilder r1 = new java.lang.StringBuilder
                r1.<init>()
                java.lang.String r2 = "onChangeEvent, value: "
                r1.append(r2)
                r1.append(r9)
                java.lang.String r1 = r1.toString()
                android.util.Log.i(r0, r1)
                int r0 = r9.getPropertyId()
                r1 = 561045514(0x2170e00a, float:8.1611676E-19)
                if (r0 != r1) goto Ldb
                b.d.c.f.d0 r0 = b.d.c.f.d0.this
                r1 = 0
                if (r0 == 0) goto Lda
                java.lang.String r2 = "Connectivity:DeviceManager"
                java.lang.String r3 = "handleClearCacheDevices property Id:"
                java.lang.StringBuilder r3 = b.a.b.a.a.e(r3)
                int r4 = r9.getPropertyId()
                r3.append(r4)
                java.lang.String r3 = r3.toString()
                android.util.Log.i(r2, r3)
                r2 = 1
                byte[] r3 = new byte[r2]
                r4 = 34
                r5 = 0
                r3[r5] = r4
                b.d.c.g.w r4 = r0.f327b
                if (r4 == 0) goto L74
                b.d.c.d.c r4 = r4.i
                if (r4 == 0) goto L6c
                monitor-enter(r4)
                java.lang.String r6 = "Connectivity:DeviceInfoDBManager"
                java.lang.String r7 = "deleteAllCache is called"
                android.util.Log.i(r6, r7)     // Catch: java.lang.Throwable -> L69
                android.database.sqlite.SQLiteDatabase r6 = r4.f312b     // Catch: java.lang.Throwable -> L69
                java.lang.String r7 = "device_info"
                int r1 = r6.delete(r7, r1, r1)     // Catch: java.lang.Throwable -> L69
                if (r1 <= 0) goto L63
                java.lang.String r6 = "Connectivity:DeviceInfoDBManager"
                java.lang.String r7 = "deleteAllCache Success"
                android.util.Log.i(r6, r7)     // Catch: java.lang.Throwable -> L69
            L63:
                if (r1 <= 0) goto L66
                goto L67
            L66:
                r2 = r5
            L67:
                monitor-exit(r4)
                goto L7c
            L69:
                r9 = move-exception
                monitor-exit(r4)
                throw r9
            L6c:
                java.lang.String r1 = "Connectivity:DeviceManagerModel"
                java.lang.String r2 = "clearAllCacheDevices mDbManager is null"
                android.util.Log.i(r1, r2)
                goto L7b
            L74:
                java.lang.String r1 = "Connectivity:DeviceManager"
                java.lang.String r2 = "mDeviceManagerModel == null"
                android.util.Log.e(r1, r2)
            L7b:
                r2 = r5
            L7c:
                if (r2 == 0) goto Lab
                b.d.c.f.x r1 = b.d.c.f.x.a()
                java.util.concurrent.CopyOnWriteArrayList<b.d.c.f.f0> r2 = r1.f363a
                if (r2 == 0) goto La2
                int r2 = r2.size()
                if (r2 <= 0) goto La2
                java.util.concurrent.CopyOnWriteArrayList<b.d.c.f.f0> r1 = r1.f363a
                java.util.Iterator r1 = r1.iterator()
            L92:
                boolean r2 = r1.hasNext()
                if (r2 == 0) goto La2
                java.lang.Object r2 = r1.next()
                b.d.c.f.f0 r2 = (b.d.c.f.f0) r2
                r2.f()
                goto L92
            La2:
                r3[r5] = r5
                java.lang.String r1 = "Connectivity:DeviceManager"
                java.lang.String r2 = "handleClearCacheDevices clearResult : true"
                android.util.Log.d(r1, r2)
            Lab:
                android.car.hardware.eol.CarYFEolManager r0 = r0.e     // Catch: java.lang.Exception -> Lbb
                java.lang.Class<byte[]> r1 = byte[].class
                int r2 = r9.getPropertyId()     // Catch: java.lang.Exception -> Lbb
                int r9 = r9.getAreaId()     // Catch: java.lang.Exception -> Lbb
                r0.setProperty(r1, r2, r9, r3)     // Catch: java.lang.Exception -> Lbb
                goto Ldb
            Lbb:
                r9 = move-exception
                r9.printStackTrace()
                java.lang.String r0 = "Connectivity:DeviceManager"
                java.lang.StringBuilder r1 = new java.lang.StringBuilder
                r1.<init>()
                java.lang.String r2 = "handleClearCacheDevices exception : "
                r1.append(r2)
                java.lang.String r9 = r9.getMessage()
                r1.append(r9)
                java.lang.String r9 = r1.toString()
                b.a.a.b.y.f(r0, r9)
                goto Ldb
            Lda:
                throw r1
            Ldb:
                return
            */
            throw new UnsupportedOperationException("Method not decompiled: b.d.c.f.d0.d.onChangeEvent(android.car.hardware.CarPropertyValue):void");
        }

        @Override // android.car.hardware.eol.CarYFEolManager.CarEolEventCallback
        public void onErrorEvent(int i, int i2) {
            Log.i("Connectivity:DeviceManager", "onErrorEvent, propertyId: " + i + " zone " + i2);
        }
    }

    /* compiled from: DeviceManager.java */
    /* loaded from: classes.dex */
    public class e implements IBtPhoneCurrentCallStateChangedCallback {
        public e() {
        }

        @Override // com.yfve.ici.app.btphone.IBtPhoneCurrentCallStateChangedCallback
        public void onCurrentCallStateChanged(int i) {
            b.a.b.a.a.i("onCurrentCallStateChanged i: ", i, "Connectivity:DeviceManager");
            Message obtainMessage = d0.this.g.obtainMessage();
            obtainMessage.what = 1;
            obtainMessage.arg1 = i;
            d0.this.g.sendMessage(obtainMessage);
        }
    }

    public static void a(d0 d0Var) {
        if (d0Var != null) {
            Log.i("Connectivity:DeviceManager", "initPowerManager in");
            Car car = d0Var.c;
            if (car != null) {
                try {
                    CarPowerManager carPowerManager = (CarPowerManager) car.getCarManager(Car.POWER_SERVICE);
                    d0Var.d = carPowerManager;
                    if (carPowerManager != null) {
                        carPowerManager.setListener(d0Var.i, null);
                    } else {
                        Log.i("Connectivity:DeviceManager", "onClick: carPowerExtensionManager==null");
                    }
                    return;
                } catch (Exception e2) {
                    e2.printStackTrace();
                    b.a.a.b.y.f("Connectivity:DeviceManager", "initPowerManager exception : " + e2.getMessage());
                    return;
                }
            }
            return;
        }
        throw null;
    }

    public static void b(d0 d0Var) {
        if (d0Var != null) {
            Log.i("Connectivity:DeviceManager", "initEolManager in");
            Car car = d0Var.c;
            if (car != null) {
                try {
                    CarYFEolManager carYFEolManager = (CarYFEolManager) car.getCarManager(Car.EOL_SERVICE);
                    d0Var.e = carYFEolManager;
                    if (carYFEolManager != null) {
                        Log.i("Connectivity:DeviceManager", "register EOl Callback ");
                        d0Var.e.registerCallback(d0Var.j);
                    } else {
                        Log.i("Connectivity:DeviceManager", "mCarYFEolManager==null");
                    }
                    return;
                } catch (Exception e2) {
                    e2.printStackTrace();
                    b.a.a.b.y.f("Connectivity:DeviceManager", "initEolManager exception : " + e2.getMessage());
                    return;
                }
            }
            Log.i("Connectivity:DeviceManager", "initEolManager mCar is null");
            return;
        }
        throw null;
    }

    public static d0 m() {
        d0 d0Var;
        if (l == null) {
            synchronized (d0.class) {
                if (l == null) {
                    l = new d0();
                }
                d0Var = l;
            }
            return d0Var;
        }
        return l;
    }

    public void c() {
        b.d.c.g.w wVar = this.f327b;
        if (wVar == null) {
            Log.e("Connectivity:DeviceManager", "connectCarlife: mDeviceManagerModel == null");
        } else if (wVar != null) {
            String e2 = b.d.c.h.f.c.e();
            StringBuilder e3 = b.a.b.a.a.e("connectCarlife:");
            e3.append(wVar.m);
            Log.i(e2, e3.toString());
            DeviceInfoBean deviceInfoBean = wVar.m;
            if (deviceInfoBean != null && !TextUtils.isEmpty(deviceInfoBean.getSerialNum())) {
                try {
                    Log.i("Connectivity:DeviceManagerModel", "connectCarlife -> " + wVar.m.getSerialNum() + ", " + wVar.m.getPortNum());
                    wVar.p.setCurDeviceInfoBean(wVar.m);
                    CarlifeManager.getInstance().connect(wVar.m.getSerialNum(), wVar.m.getPortNum());
                    return;
                } catch (Exception e4) {
                    StringBuilder e5 = b.a.b.a.a.e("connectCarlife fail. ");
                    e5.append(e4.getMessage());
                    Log.e("Connectivity:DeviceManagerModel", e5.toString());
                    Log.e("Connectivity:DeviceManagerModel", Log.getStackTraceString(e4));
                    return;
                }
            }
            Log.e(b.d.c.h.f.c.e(), "connectCarlife fail, invalid state or info");
        } else {
            throw null;
        }
    }

    public void d(int i, int i2) {
        Log.i("Connectivity:DeviceManager", "connectDevice listType: " + i + ", index = " + i2);
        b.d.c.g.w wVar = this.f327b;
        if (wVar != null) {
            wVar.q(i, i2);
        } else {
            Log.e("Connectivity:DeviceManager", "mDeviceManagerModel == null");
        }
    }

    public void e(String str, String str2) {
        int carLifeSwitch;
        Log.i("Connectivity:DeviceManager", "connectDevice macAddress: " + str + ", serial = " + str2);
        b.d.c.g.w wVar = this.f327b;
        if (wVar != null) {
            List<DeviceInfoBean> list = wVar.k.get(3);
            if (list == null) {
                Log.e("Connectivity:DeviceManagerModel", "connectDevice knownList is null");
                return;
            }
            int i = -1;
            if (!TextUtils.isEmpty(str2)) {
                Iterator<DeviceInfoBean> it = list.iterator();
                while (true) {
                    if (!it.hasNext()) {
                        break;
                    }
                    DeviceInfoBean next = it.next();
                    if (str2.equals(next.getSerialNum())) {
                        wVar.m = next;
                        break;
                    }
                }
                UsbDevice x = wVar.x(str2);
                if (x != null) {
                    Log.i("Connectivity:DeviceManagerModel", "Find Device, usbDevice is not null");
                    wVar.m.setUsbDevice(x);
                } else {
                    Log.i("Connectivity:DeviceManagerModel", "Find  Device, usbDevice is null");
                    wVar.m.setUsbDevice(null);
                }
                DeviceInfoBean l2 = wVar.i.l(str2);
                if (l2 != null) {
                    i = l2.getCarPlaySwitch();
                    carLifeSwitch = l2.getCarLifeSwitch();
                }
                carLifeSwitch = -1;
            } else {
                if (!TextUtils.isEmpty(str)) {
                    Iterator<DeviceInfoBean> it2 = list.iterator();
                    while (true) {
                        if (!it2.hasNext()) {
                            break;
                        }
                        DeviceInfoBean next2 = it2.next();
                        if (str.equals(next2.getMacAddress())) {
                            wVar.m = next2;
                            break;
                        }
                    }
                    DeviceInfoBean k = wVar.i.k(str);
                    if (k != null) {
                        i = k.getCarPlaySwitch();
                        carLifeSwitch = k.getCarLifeSwitch();
                    }
                }
                carLifeSwitch = -1;
            }
            Log.i("Connectivity:DeviceManagerModel", "CarPlaySwitch ========== " + i + " CarLifeSwitch ========== " + carLifeSwitch);
            if (i == 1) {
                wVar.p.handleDeviceConnection(wVar.m, 3);
                return;
            } else if (carLifeSwitch == 1) {
                wVar.p.handleDeviceConnection(wVar.m, 4);
                return;
            } else {
                wVar.p.handleDeviceConnection(wVar.m, 1);
                return;
            }
        }
        Log.e("Connectivity:DeviceManager", "mDeviceManagerModel == null");
    }

    public void f() {
        b.d.c.g.w wVar = this.f327b;
        if (wVar != null) {
            wVar.s();
        } else {
            Log.e("Connectivity:DeviceManager", "mDeviceManagerModel == null");
        }
    }

    public void g() {
        b.d.c.g.w wVar = this.f327b;
        if (wVar != null) {
            wVar.t();
        } else {
            Log.e("Connectivity:DeviceManager", "mDeviceManagerModel == null");
        }
    }

    public void h() {
        b.d.c.g.w wVar = this.f327b;
        if (wVar != null) {
            int curConnectType = wVar.p.getCurConnectType();
            if (curConnectType != 1) {
                if (curConnectType == 3) {
                    wVar.t();
                    return;
                } else if (curConnectType == 4) {
                    wVar.s();
                    return;
                } else if (curConnectType != 5) {
                    return;
                }
            }
            y.l(wVar.c).x(y.l(wVar.c).j());
            return;
        }
        Log.e("Connectivity:DeviceManager", "mDeviceManagerModel == null");
    }

    public int i() {
        b.d.c.g.w wVar = this.f327b;
        if (wVar == null) {
            Log.e("Connectivity:DeviceManager", "mDeviceManagerModel == null");
            return -1;
        } else if (wVar != null) {
            Log.i("Connectivity:DeviceManagerModel", "getCarPlayBatteryLevel IN");
            if (CarPlayProxy.getInstance().isAvailable()) {
                CPDeviceInfo deviceInfo = CarPlayProxy.getInstance().getDeviceInfo();
                if (deviceInfo != null) {
                    return deviceInfo.getBatteryLevel();
                }
                Log.e("Connectivity:DeviceManagerModel", "getDeviceInfo is null");
                return -1;
            }
            Log.e("Connectivity:DeviceManagerModel", "getCarPlayBatteryLevel interface not isAvailable");
            return -1;
        } else {
            throw null;
        }
    }

    public int j() {
        b.d.c.g.w wVar = this.f327b;
        if (wVar == null) {
            Log.e("Connectivity:DeviceManager", "mDeviceManagerModel == null");
            return -1;
        } else if (wVar != null) {
            Log.i("Connectivity:DeviceManagerModel", "getCarPlaySignalLevel IN");
            if (CarPlayProxy.getInstance().isAvailable()) {
                CPDeviceInfo deviceInfo = CarPlayProxy.getInstance().getDeviceInfo();
                if (deviceInfo != null) {
                    return deviceInfo.getSignalStrength();
                }
                Log.e("Connectivity:DeviceManagerModel", "getDeviceInfo is null");
                return -1;
            }
            Log.e("Connectivity:DeviceManagerModel", "getCarPlaySignalLevel interface not isAvailable");
            return -1;
        } else {
            throw null;
        }
    }

    public DeviceInfoBean k() {
        b.d.c.g.w wVar = this.f327b;
        if (wVar != null) {
            return wVar.m;
        }
        Log.e("Connectivity:DeviceManager", "getCurDeviceInfoBean mDeviceManagerModel is null");
        return null;
    }

    public List<DeviceInfoBean> l(int i) {
        DeviceInfoBean deviceInfoBean;
        DeviceInfoBean deviceInfoBean2;
        boolean z;
        boolean z2;
        b.d.c.g.w wVar = this.f327b;
        List<b.a.a.b.h> list = null;
        if (wVar == null) {
            Log.e("Connectivity:DeviceManager", "mDeviceManagerModel == null");
            return null;
        } else if (wVar != null) {
            Log.i("Connectivity:DeviceManagerModel", "getDeviceList listType: " + i);
            ArrayList arrayList = new ArrayList();
            int i2 = 1;
            if (i == 1) {
                ArrayList arrayList2 = new ArrayList();
                arrayList2.addAll(wVar.M(wVar.e));
                ArrayList arrayList3 = new ArrayList();
                arrayList3.addAll(wVar.m(wVar.A()));
                Log.i("Connectivity:DeviceManagerModel", "获取已配对电话列表" + wVar.m(wVar.A()).toString());
                arrayList.addAll(arrayList2);
                if (arrayList2.size() > 0) {
                    StringBuilder e2 = b.a.b.a.a.e("wiredCarPlayDevices size: ");
                    e2.append(arrayList2.size());
                    Log.i("Connectivity:DeviceManagerModel", e2.toString());
                    if (arrayList3.size() > 0) {
                        Iterator it = arrayList3.iterator();
                        while (it.hasNext()) {
                            DeviceInfoBean deviceInfoBean3 = (DeviceInfoBean) it.next();
                            int v = wVar.v(arrayList2, deviceInfoBean3);
                            if (v != -1) {
                                ((DeviceInfoBean) arrayList.get(v)).setCachedBluetoothDevice(deviceInfoBean3.getCachedBluetoothDevice());
                                ((DeviceInfoBean) arrayList.get(v)).setMacAddress(deviceInfoBean3.getMacAddress());
                            } else {
                                arrayList.add(deviceInfoBean3);
                                Log.i("Connectivity:DeviceManagerModel", "缓存电话列表===" + arrayList.size());
                            }
                        }
                    }
                } else {
                    arrayList.addAll(wVar.m(wVar.A()));
                }
                StringBuilder e3 = b.a.b.a.a.e("缓存电话列表===");
                e3.append(arrayList.size());
                Log.i("Connectivity:DeviceManagerModel", e3.toString());
            } else if (i == 2) {
                arrayList.addAll(wVar.M(wVar.f));
            } else if (i == 3) {
                Log.i("Connectivity:DeviceManagerModel", "getKnownDevicesList IN");
                ArrayList arrayList4 = new ArrayList();
                List<b.a.a.b.h> z3 = wVar.z();
                if (z3.size() > 0) {
                    for (b.a.a.b.h hVar : z3) {
                        StringBuilder e4 = b.a.b.a.a.e("CachedBluetoothDevice MAC: ");
                        e4.append(hVar.j());
                        Log.i("Connectivity:DeviceManagerModel", e4.toString());
                        DeviceInfoBean k = wVar.i.k(hVar.j());
                        if (k == null) {
                            Log.e("Connectivity:DeviceManagerModel", "device db bt state not save .......");
                            wVar.i.e(hVar);
                        } else if (k.getBtPairedState() == 0) {
                            Log.e("Connectivity:DeviceManagerModel", "device db bt state save is 0");
                            wVar.i.p(hVar.j(), 1);
                        }
                    }
                }
                Iterator it2 = ((ArrayList) wVar.i.j()).iterator();
                while (it2.hasNext()) {
                    DeviceInfoBean deviceInfoBean4 = (DeviceInfoBean) it2.next();
                    if (deviceInfoBean4.getBtPairedState() == 1) {
                        StringBuilder e5 = b.a.b.a.a.e("PAIR STATE IS 1, MAC: ");
                        e5.append(deviceInfoBean4.getMacAddress());
                        Log.i("Connectivity:DeviceManagerModel", e5.toString());
                        if (wVar.u(z3, deviceInfoBean4.getMacAddress()) == null) {
                            wVar.i.p(deviceInfoBean4.getMacAddress(), 0);
                        }
                    }
                }
                Iterator it3 = ((ArrayList) wVar.i.j()).iterator();
                while (it3.hasNext()) {
                    DeviceInfoBean deviceInfoBean5 = (DeviceInfoBean) it3.next();
                    if (deviceInfoBean5 != null) {
                        StringBuilder e6 = b.a.b.a.a.e("bean : ");
                        e6.append(deviceInfoBean5.toString());
                        Log.i("Connectivity:DeviceManagerModel", e6.toString());
                        int v2 = wVar.v(arrayList4, deviceInfoBean5);
                        if (deviceInfoBean5.getBtPairedState() == i2) {
                            StringBuilder e7 = b.a.b.a.a.e("PAIR STATE IS 1, MAC: ");
                            e7.append(deviceInfoBean5.getMacAddress());
                            e7.append("j====");
                            e7.append(v2);
                            Log.i("Connectivity:DeviceManagerModel", e7.toString());
                            b.a.a.b.h u = wVar.u(z3, deviceInfoBean5.getMacAddress());
                            deviceInfoBean5.setCachedBluetoothDevice(u);
                            b.d.c.g.h hVar2 = y.l(wVar.c).f366b;
                            if (hVar2 != null) {
                                z = hVar2.l(u);
                            } else {
                                b.a.a.b.y.f("ICI_BS_BtSettingManager", "mBtDeviceModel == null");
                                z = false;
                            }
                            if (z) {
                                Log.i("Connectivity:DeviceManagerModel", "setBtConnState CONNECTED");
                                deviceInfoBean5.setConnectState(2);
                            } else {
                                b.d.c.g.h hVar3 = y.l(wVar.c).f366b;
                                if (hVar3 != null) {
                                    z2 = hVar3.m(u);
                                } else {
                                    b.a.a.b.y.f("ICI_BS_BtSettingManager", "isDeviceConnecting device == null");
                                    z2 = false;
                                }
                                if (z2) {
                                    Log.i("Connectivity:DeviceManagerModel", "setBtConnState CONNECTING");
                                    deviceInfoBean5.setConnectState(1);
                                }
                            }
                            if (v2 == -1) {
                                arrayList4.add(deviceInfoBean5);
                                Log.i("Connectivity:DeviceManagerModel", "getKnownDevicesList: " + arrayList4.size());
                            } else {
                                Log.i("Connectivity:DeviceManagerModel", "getKnownDevicesList: ====有重复设备出现");
                                Log.i("Connectivity:DeviceManagerModel", "getKnownDevicesList: " + arrayList4.toString());
                            }
                        } else if (deviceInfoBean5.getCarLifeDisclaimer() != 0 || deviceInfoBean5.getCarPlayDisclaimer() != 0) {
                            if (v2 == -1) {
                                arrayList4.add(deviceInfoBean5);
                                Log.i("Connectivity:DeviceManagerModel", "getKnownDevicesList: " + arrayList4.size());
                            } else {
                                Log.i("Connectivity:DeviceManagerModel", "getKnownDevicesList: ====有重复设备出现");
                                Log.i("Connectivity:DeviceManagerModel", "getKnownDevicesList: " + arrayList4.toString());
                            }
                        }
                    }
                    i2 = 1;
                }
                if (CarlifeProxy.getInstance().isCarLifeSessionActived()) {
                    String string = CarlifeProxy.getInstance().getDeviceInfo().getString(CarlifeProxy.EXTRA_DEV_INFO_SERIAL);
                    int i3 = 0;
                    while (true) {
                        if (i3 >= arrayList4.size()) {
                            break;
                        }
                        if (((DeviceInfoBean) arrayList4.get(i3)).getSerialNum() != null && ((DeviceInfoBean) arrayList4.get(i3)).getSerialNum().contains(string)) {
                            Log.i("Connectivity:DeviceManagerModel", "setCarLifeConnState CONNECTED");
                            ((DeviceInfoBean) arrayList4.get(i3)).setConnectState(2);
                            break;
                        }
                        i3++;
                    }
                } else if (CarPlayProxy.getInstance().isCarPlaySessionActived()) {
                    if (CarPlayProxy.getInstance() != null && CarPlayProxy.getInstance().getDeviceInfo() != null) {
                        String usbSerialNumber = CarPlayProxy.getInstance().getDeviceInfo().getUsbSerialNumber();
                        b.a.b.a.a.k("serialNum ========= ", usbSerialNumber, "Connectivity:DeviceManagerModel");
                        int i4 = 0;
                        while (true) {
                            if (i4 >= arrayList4.size()) {
                                break;
                            }
                            if (((DeviceInfoBean) arrayList4.get(i4)).getSerialNum() != null && ((DeviceInfoBean) arrayList4.get(i4)).getSerialNum().contains(usbSerialNumber)) {
                                Log.i("Connectivity:DeviceManagerModel", "setCarPlayConnState CONNECTED");
                                ((DeviceInfoBean) arrayList4.get(i4)).setConnectState(2);
                                break;
                            }
                            i4++;
                        }
                    }
                } else if (CarPlayProxy.getInstance().getCPConnectStatus() == 1) {
                    int i5 = 0;
                    while (true) {
                        if (i5 >= arrayList4.size()) {
                            break;
                        } else if (!TextUtils.isEmpty(((DeviceInfoBean) arrayList4.get(i5)).getSerialNum()) && (deviceInfoBean2 = wVar.m) != null && !TextUtils.isEmpty(deviceInfoBean2.getSerialNum())) {
                            if (((DeviceInfoBean) arrayList4.get(i5)).getSerialNum().equals(wVar.m.getSerialNum())) {
                                Log.i("Connectivity:DeviceManagerModel", "setCarPlayConnState CONNECTING1");
                                ((DeviceInfoBean) arrayList4.get(i5)).setConnectState(1);
                                break;
                            }
                            i5++;
                        } else {
                            if (!TextUtils.isEmpty(((DeviceInfoBean) arrayList4.get(i5)).getMacAddress()) && (deviceInfoBean = wVar.m) != null && !TextUtils.isEmpty(deviceInfoBean.getMacAddress()) && ((DeviceInfoBean) arrayList4.get(i5)).getMacAddress().equals(wVar.m.getMacAddress())) {
                                Log.i("Connectivity:DeviceManagerModel", "setCarPlayConnState CONNECTING2");
                                ((DeviceInfoBean) arrayList4.get(i5)).setConnectState(1);
                                break;
                            }
                            i5++;
                        }
                    }
                }
                arrayList.addAll(arrayList4);
            } else if (i == 4) {
                b.d.c.g.h hVar4 = y.l(wVar.c).f366b;
                if (hVar4 != null) {
                    list = hVar4.i();
                } else {
                    b.a.a.b.y.f("ICI_BS_BtSettingManager", "mBtDeviceModel == null");
                }
                arrayList.addAll(wVar.m(list));
            }
            for (int i6 = 0; i6 < arrayList.size(); i6++) {
                if (((DeviceInfoBean) arrayList.get(i6)).getConnectState() == 2 && i6 != 0) {
                    arrayList.remove(i6);
                    arrayList.add(0, (DeviceInfoBean) arrayList.get(i6));
                }
            }
            wVar.k.put(Integer.valueOf(i), arrayList);
            Log.i("Connectivity:DeviceManagerModel", "getDeviceList SIZE = " + arrayList.size());
            Iterator it4 = arrayList.iterator();
            while (it4.hasNext()) {
                StringBuilder e8 = b.a.b.a.a.e("DeviceInfoBean: ");
                e8.append(((DeviceInfoBean) it4.next()).toString());
                Log.i("Connectivity:DeviceManagerModel", e8.toString());
            }
            return new ArrayList(arrayList);
        } else {
            throw null;
        }
    }

    public HashMap<String, Integer> n() {
        Log.i("Connectivity:DeviceManager", "getLastDiviceDisconnetReason IN");
        b.d.c.g.w wVar = this.f327b;
        if (wVar != null) {
            return wVar.l;
        }
        Log.e("Connectivity:DeviceManager", "mDeviceManagerModel == null");
        return null;
    }

    public boolean o(String str, String str2) {
        int delete;
        b.d.c.g.w wVar = this.f327b;
        int i = 0;
        if (wVar == null) {
            Log.e("Connectivity:DeviceManager", "mDeviceManagerModel == null");
            return false;
        } else if (wVar != null) {
            Log.i("Connectivity:DeviceManagerModel", "ignoreDevice mac: " + str + " ,serial:" + str2);
            if (CarPlayProxy.getInstance().isCarPlaySessionActived() && CarPlayProxy.getInstance() != null && CarPlayProxy.getInstance().getDeviceInfo() != null) {
                String usbSerialNumber = CarPlayProxy.getInstance().getDeviceInfo().getUsbSerialNumber();
                String btMacAddress = CarPlayProxy.getInstance().getDeviceInfo().getBtMacAddress();
                Log.i("Connectivity:DeviceManagerModel", "ignoreDevice cpMacAddr:" + btMacAddress + " ,cpSerialNum:" + usbSerialNumber);
                if ((str2 != null && usbSerialNumber != null && str2.equals(usbSerialNumber)) || (str != null && btMacAddress != null && str.equals(btMacAddress))) {
                    wVar.t();
                }
            } else if (CarlifeProxy.getInstance().isCarLifeSessionActived()) {
                String string = CarlifeProxy.getInstance().getDeviceInfo().getString(CarlifeProxy.EXTRA_DEV_INFO_SERIAL);
                Log.i("Connectivity:DeviceManagerModel", "ignoreDevice clSerialNum:" + string);
                if (str2 != null && string != null && str2.equals(string)) {
                    wVar.s();
                }
            }
            b.d.c.d.c cVar = wVar.i;
            synchronized (cVar) {
                String q = b.d.c.h.f.c.q(str2);
                Log.i("Connectivity:DeviceInfoDBManager", "deleteDeviceInfo macAddress:" + str + ",serialNum:" + q);
                if (!TextUtils.isEmpty(str)) {
                    delete = cVar.f312b.delete("device_info", "macAddress=?", new String[]{str});
                } else {
                    delete = cVar.f312b.delete("device_info", "serialNum=?", new String[]{q});
                }
                if (delete > 0) {
                    c0.a().b(3);
                    List<DeviceInfoBean> l2 = m().l(3);
                    x.a().d(l2);
                    x a2 = x.a();
                    if (l2 != null) {
                        i = l2.size();
                    }
                    a2.b(i);
                    Log.i("Connectivity:DeviceInfoDBManager", "deleteDeviceInfo Success");
                    return true;
                }
                Log.i("Connectivity:DeviceInfoDBManager", "deleteDeviceInfo fail");
                return false;
            }
        } else {
            throw null;
        }
    }

    public boolean p(int i, String str, String str2) {
        b.d.c.g.w wVar = this.f327b;
        if (wVar == null) {
            Log.e("Connectivity:DeviceManager", "mDeviceManagerModel == null");
            return false;
        } else if (wVar != null) {
            Log.i("Connectivity:DeviceManagerModel", "isAgreedTerms appType = " + i + "; macAddress = " + str + "; serialNum = " + str2);
            if (!TextUtils.isEmpty(str)) {
                DeviceInfoBean k = wVar.i.k(str);
                if (k == null) {
                    Log.i("Connectivity:DeviceManagerModel", "query data is not exist by macAddress");
                    return false;
                } else if (i == 4) {
                    if (k.getCarLifeDisclaimer() != 1) {
                        return false;
                    }
                } else if (k.getCarPlayDisclaimer() != 1) {
                    return false;
                }
            } else {
                DeviceInfoBean l2 = wVar.i.l(str2);
                if (l2 == null) {
                    Log.i("Connectivity:DeviceManagerModel", "query data is not exist by serialNum");
                    return false;
                } else if (i == 4) {
                    if (l2.getCarLifeDisclaimer() != 1) {
                        return false;
                    }
                } else if (l2.getCarPlayDisclaimer() != 1) {
                    return false;
                }
            }
            return true;
        } else {
            throw null;
        }
    }

    public boolean q(String str, String str2) {
        b.d.c.g.w wVar = this.f327b;
        if (wVar == null) {
            Log.e("Connectivity:DeviceManager", "isCarPlayConnected mDeviceManagerModel is null");
            return false;
        } else if (wVar != null) {
            if (!CarPlayProxy.getInstance().isCarPlaySessionActived() || CarPlayProxy.getInstance() == null || CarPlayProxy.getInstance().getDeviceInfo() == null) {
                return false;
            }
            return (str != null && str.equals(CarPlayProxy.getInstance().getDeviceInfo().getBtMacAddress().toUpperCase())) || (str2 != null && str2.equals(CarPlayProxy.getInstance().getDeviceInfo().getUsbSerialNumber()));
        } else {
            throw null;
        }
    }

    public boolean r(int i) {
        b.d.c.g.w wVar = this.f327b;
        if (wVar != null) {
            return wVar.E(i);
        }
        Log.e("Connectivity:DeviceManager", "mDeviceManagerModel == null");
        return false;
    }

    public void s() {
        Log.i("Connectivity:DeviceManager", "notifyRemoveReconnectMessage called");
        b.d.c.g.w wVar = this.f327b;
        if (wVar == null) {
            Log.e("Connectivity:DeviceManager", "mDeviceManagerModel == null");
        } else if (wVar != null) {
            Log.i("Connectivity:DeviceManagerModel", "notifyRemoveReconnectMessage");
            if (wVar.B.hasMessages(1001)) {
                wVar.B.removeMessages(1001);
            }
            if (wVar.B.hasMessages(1002)) {
                wVar.B.removeMessages(1002);
            }
        } else {
            throw null;
        }
    }

    public int t() {
        final b.d.c.g.w wVar = this.f327b;
        if (wVar == null) {
            Log.e("Connectivity:DeviceManager", "mDeviceManagerModel == null");
            return 0;
        } else if (wVar != null) {
            if (CarPlayProxy.getInstance().isCarPlaySessionActived()) {
                Intent intent = new Intent();
                intent.setClassName("com.ici.carplay", "com.ici.carplay.ui.activity.CPDisplayActivity");
                intent.addFlags(268435456);
                wVar.c.startActivity(intent);
                return 1;
            } else if (wVar.E(1)) {
                wVar.q(1, 0);
                return 2;
            } else {
                b.a.a.b.y.p(new Runnable() { // from class: b.d.c.g.g
                    @Override // java.lang.Runnable
                    public final void run() {
                        w.this.H();
                    }
                });
                return 3;
            }
        } else {
            throw null;
        }
    }

    public int u() {
        Log.e("Connectivity:DeviceManager", "openCarlife");
        b.d.c.g.w wVar = this.f327b;
        if (wVar == null) {
            Log.e("Connectivity:DeviceManager", "openCarlife mDeviceManagerModel is null");
            return 0;
        } else if (wVar != null) {
            int carlifeConnectState = CarlifeProxy.getInstance().getCarlifeConnectState();
            if (carlifeConnectState == 1) {
                if (wVar.E(2)) {
                    Log.i(b.d.c.h.f.c.e(), "local vr open carlife: current disconnected, try connect");
                    wVar.q(2, 0);
                    return 2;
                }
                Log.i(b.d.c.h.f.c.e(), "local vr open carlife: current disconnected, but not device");
                return 3;
            } else if (carlifeConnectState != 2 && carlifeConnectState != 3) {
                if (carlifeConnectState == 4) {
                    Log.i(b.d.c.h.f.c.e(), "local vr open carlife: goto casting screen");
                    b.d.c.h.f.c.h(wVar.c);
                    return 1;
                }
                return 3;
            } else {
                Log.i(b.d.c.h.f.c.e(), "local vr open carlife: current connecting");
                Context context = wVar.c;
                if (context == null) {
                    return 0;
                }
                e0 d2 = e0.d(context);
                if (!d2.i(2008) && d2.i(2009)) {
                    d2.p(2008, e0.c());
                }
                return 2;
            }
        } else {
            throw null;
        }
    }

    public void v(h0 h0Var) {
        c0 a2 = c0.a();
        if (a2 != null) {
            if (h0Var == null || a2.f324a.contains(h0Var)) {
                return;
            }
            a2.f324a.add(h0Var);
            return;
        }
        throw null;
    }

    public void w(BluetoothDevice bluetoothDevice, int i) {
        b.a.b.a.a.i("setLastDiviceDisconnetReason reason: ", i, "Connectivity:DeviceManager");
        b.d.c.g.w wVar = this.f327b;
        if (wVar == null) {
            Log.e("Connectivity:DeviceManager", "mDeviceManagerModel == null");
        } else if (wVar == null) {
            throw null;
        } else {
            if (bluetoothDevice == null) {
                return;
            }
            StringBuilder e2 = b.a.b.a.a.e("setLastDiviceDisconnetReason address: ");
            e2.append(bluetoothDevice.getAddress());
            e2.append(" ,lastAddress: ");
            b.a.b.a.a.p(e2, wVar.z, "Connectivity:DeviceManagerModel");
            wVar.l.put(bluetoothDevice.getAddress(), Integer.valueOf(i));
            String str = wVar.z;
            if (str != null && str.equals(bluetoothDevice.getAddress()) && i == 1) {
                wVar.B.sendEmptyMessageDelayed(1002, TooltipCompatHandler.HOVER_HIDE_TIMEOUT_SHORT_MS);
            }
        }
    }

    public void x(h0 h0Var) {
        c0 a2 = c0.a();
        synchronized (a2.f324a) {
            if (h0Var != null) {
                if (a2.f324a.contains(h0Var)) {
                    ListIterator<h0> listIterator = a2.f324a.listIterator();
                    while (listIterator.hasNext()) {
                        if (h0Var == listIterator.next()) {
                            listIterator.remove();
                        }
                    }
                }
            }
        }
    }

    public void y(int i, boolean z) {
        b.d.c.g.w wVar = this.f327b;
        if (wVar == null) {
            Log.e("Connectivity:DeviceManager", "mDeviceManagerModel == null");
            return;
        }
        int i2 = z ? 1 : 2;
        if (wVar != null) {
            Log.i("Connectivity:DeviceManagerModel", "updateUserTerms IN.");
            String macAddress = (wVar.m.getCachedBluetoothDevice() == null || TextUtils.isEmpty(wVar.m.getCachedBluetoothDevice().j())) ? wVar.m.getMacAddress() : wVar.m.getCachedBluetoothDevice().j();
            String serialNum = wVar.m.getSerialNum();
            String deviceName = wVar.m.getDeviceName();
            if (!TextUtils.isEmpty(serialNum)) {
                UsbDevice usbDevice = wVar.m.getUsbDevice();
                Boolean valueOf = Boolean.valueOf(usbDevice != null && 1452 == usbDevice.getVendorId());
                DeviceInfoBean l2 = wVar.i.l(serialNum);
                if (l2 != null) {
                    b.d.c.d.c cVar = wVar.i;
                    synchronized (cVar) {
                        Log.i("Connectivity:DeviceInfoDBManager", "updateDateBySerialNum appType = " + i + "; termsState = " + i2);
                        StringBuilder sb = new StringBuilder();
                        sb.append("***********11 DeviceName : ");
                        sb.append(l2.getDeviceName());
                        Log.i("Connectivity:DeviceInfoDBManager", sb.toString());
                        Log.i("Connectivity:DeviceInfoDBManager", "***********11 SerialNum : " + l2.getSerialNum());
                        ContentValues contentValues = new ContentValues();
                        if (i == 4) {
                            Log.i("Connectivity:DeviceInfoDBManager", "************* TYPE_CARLIFE");
                            contentValues.put("carlifeSwitch", (Integer) 1);
                            if (l2.getCarPlaySwitch() == 1) {
                                contentValues.put("carplaySwitch", (Integer) 0);
                            }
                            contentValues.put("connectType", (Integer) 4);
                            contentValues.put("connectTime", cVar.e.format(new Date(System.currentTimeMillis())));
                            contentValues.put("carlifeDisclaimer", Integer.valueOf(i2));
                        } else {
                            Log.i("Connectivity:DeviceInfoDBManager", "************* TYPE_CARPLAY");
                            contentValues.put("carplaySwitch", (Integer) 1);
                            contentValues.put("carlifeSwitch", (Integer) 0);
                            contentValues.put("connectType", (Integer) 3);
                            contentValues.put("connectTime", cVar.e.format(new Date(System.currentTimeMillis())));
                            contentValues.put("carplayDisclaimer", Integer.valueOf(i2));
                        }
                        cVar.n(contentValues);
                        cVar.m(contentValues);
                        cVar.f312b.update("device_info", contentValues, "serialNum=?", new String[]{l2.getSerialNum()});
                        Log.i("Connectivity:DeviceInfoDBManager", "updateDateBySerialNum Success");
                    }
                } else if (((ArrayList) wVar.i.j()).size() < 10) {
                    wVar.i.g(valueOf, deviceName, i, i2, serialNum, macAddress);
                } else {
                    wVar.i.a();
                    wVar.i.g(valueOf, deviceName, i, i2, serialNum, macAddress);
                }
            } else if (!TextUtils.isEmpty(macAddress)) {
                DeviceInfoBean k = wVar.i.k(macAddress);
                if (k != null) {
                    b.d.c.d.c cVar2 = wVar.i;
                    synchronized (cVar2) {
                        Log.i("Connectivity:DeviceInfoDBManager", "updateDataByAddress appType = " + i + "; termsState = " + i2);
                        StringBuilder sb2 = new StringBuilder();
                        sb2.append("updateDataByAddress macAddress = ");
                        sb2.append(k.getMacAddress());
                        Log.i("Connectivity:DeviceInfoDBManager", sb2.toString());
                        ContentValues contentValues2 = new ContentValues();
                        if (i2 == 1) {
                            contentValues2.put("carplaySwitch", (Integer) 1);
                        }
                        contentValues2.put("carlifeSwitch", (Integer) 0);
                        if (i == 4) {
                            Log.i("Connectivity:DeviceInfoDBManager", "************* TYPE_CARLIFE");
                            contentValues2.put("connectType", (Integer) 4);
                            contentValues2.put("connectTime", cVar2.e.format(new Date(System.currentTimeMillis())));
                            contentValues2.put("carlifeDisclaimer", Integer.valueOf(i2));
                        } else if (i == 3) {
                            Log.i("Connectivity:DeviceInfoDBManager", "************* TYPE_CARPLAY");
                            if (i2 == 1) {
                                contentValues2.put("connectType", (Integer) 3);
                            }
                            contentValues2.put("connectTime", cVar2.e.format(new Date(System.currentTimeMillis())));
                            contentValues2.put("carplayDisclaimer", Integer.valueOf(i2));
                        }
                        cVar2.n(contentValues2);
                        cVar2.m(contentValues2);
                        cVar2.f312b.update("device_info", contentValues2, "macAddress=?", new String[]{k.getMacAddress()});
                        Log.i("Connectivity:DeviceInfoDBManager", "updateDataByAddress Success");
                    }
                } else if (((ArrayList) wVar.i.j()).size() < 10) {
                    wVar.i.f(i, i2, macAddress);
                } else {
                    wVar.i.a();
                    wVar.i.f(i, i2, macAddress);
                }
            }
            if (i == 3 && z) {
                this.f327b.n(0);
                return;
            }
            return;
        }
        throw null;
    }

    public void z(String str, String str2, int i, int i2) {
        b.d.c.g.w wVar = this.f327b;
        if (wVar == null) {
            Log.e("Connectivity:DeviceManager", "mDeviceManagerModel == null");
        } else if (wVar != null) {
            Log.i("Connectivity:DeviceManagerModel", "updateWaittingConnectDevices macAddress: " + str + " ,serialNum: " + str2 + " ,cpSwitch: " + i + " ,clSwitch: " + i2);
            int i3 = 0;
            if (!TextUtils.isEmpty(str)) {
                while (i3 < wVar.w.size()) {
                    if (str.equals(wVar.w.get(i3).getMacAddress())) {
                        Log.i("Connectivity:DeviceManagerModel", "updateWaittingConnectDevices macAddress");
                        wVar.w.get(i3).setCarPlaySwitch(i);
                        wVar.w.get(i3).setCarLifeSwitch(i2);
                        return;
                    }
                    i3++;
                }
                return;
            }
            while (i3 < wVar.w.size()) {
                if (str2.equals(wVar.w.get(i3).getSerialNum())) {
                    Log.i("Connectivity:DeviceManagerModel", "updateWaittingConnectDevices serialNum");
                    wVar.w.get(i3).setCarPlaySwitch(i);
                    wVar.w.get(i3).setCarLifeSwitch(i2);
                    return;
                }
                i3++;
            }
        } else {
            throw null;
        }
    }
}
