package b.d.c.h;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;
import java.io.File;

/* compiled from: DataCacheUtils.java */
/* loaded from: classes.dex */
public class d {
    @SuppressLint({"StaticFieldLeak"})

    /* renamed from: b  reason: collision with root package name */
    public static volatile d f437b;

    /* renamed from: a  reason: collision with root package name */
    public final Context f438a;

    public d(Context context) {
        this.f438a = context;
    }

    public static void a(d dVar) {
        if (dVar != null) {
            Log.e("DataCacheUtils_", "clearCache: ");
            File[] listFiles = dVar.f438a.getCacheDir().listFiles();
            if (listFiles != null && listFiles.length != 0) {
                for (File file : listFiles) {
                    if (file != null) {
                        StringBuilder e = b.a.b.a.a.e("clearCache: ");
                        e.append(file.delete());
                        Log.d("DataCacheUtils_", e.toString());
                    }
                }
            }
            Log.i("DataCacheUtils_", "sync: ");
            try {
                Runtime.getRuntime().exec("sync").waitFor();
                return;
            } catch (Exception e2) {
                e2.printStackTrace();
                return;
            }
        }
        throw null;
    }
}
