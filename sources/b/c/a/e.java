package b.c.a;

import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

/* compiled from: Gson.java */
/* loaded from: classes.dex */
public class e extends u<Number> {
    public e(i iVar) {
    }

    @Override // b.c.a.u
    public Number a(JsonReader jsonReader) throws IOException {
        if (jsonReader.peek() == JsonToken.NULL) {
            jsonReader.nextNull();
            return null;
        }
        return Float.valueOf((float) jsonReader.nextDouble());
    }

    @Override // b.c.a.u
    public void b(JsonWriter jsonWriter, Number number) throws IOException {
        Number number2 = number;
        if (number2 == null) {
            jsonWriter.nullValue();
            return;
        }
        i.a(number2.floatValue());
        jsonWriter.value(number2);
    }
}
