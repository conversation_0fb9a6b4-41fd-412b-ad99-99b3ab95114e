package b.c.a;

import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicLongArray;

/* compiled from: Gson.java */
/* loaded from: classes.dex */
public class h extends u<AtomicLongArray> {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ u f98a;

    public h(u uVar) {
        this.f98a = uVar;
    }

    @Override // b.c.a.u
    public AtomicLongArray a(JsonReader jsonReader) throws IOException {
        ArrayList arrayList = new ArrayList();
        jsonReader.beginArray();
        while (jsonReader.hasNext()) {
            arrayList.add(Long.valueOf(((Number) this.f98a.a(jsonReader)).longValue()));
        }
        jsonReader.endArray();
        int size = arrayList.size();
        AtomicLongArray atomicLongArray = new AtomicLongArray(size);
        for (int i = 0; i < size; i++) {
            atomicLongArray.set(i, ((Long) arrayList.get(i)).longValue());
        }
        return atomicLongArray;
    }

    @Override // b.c.a.u
    public void b(JsonWriter jsonWriter, AtomicLongArray atomicLongArray) throws IOException {
        AtomicLongArray atomicLongArray2 = atomicLongArray;
        jsonWriter.beginArray();
        int length = atomicLongArray2.length();
        for (int i = 0; i < length; i++) {
            this.f98a.b(jsonWriter, Long.valueOf(atomicLongArray2.get(i)));
        }
        jsonWriter.endArray();
    }
}
