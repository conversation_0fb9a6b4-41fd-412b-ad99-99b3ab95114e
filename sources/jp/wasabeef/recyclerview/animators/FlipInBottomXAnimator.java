package jp.wasabeef.recyclerview.animators;

import android.view.animation.Interpolator;
import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.RecyclerView;
import jp.wasabeef.recyclerview.animators.BaseItemAnimator;

/* loaded from: classes.dex */
public class FlipInBottomXAnimator extends BaseItemAnimator {
    public FlipInBottomXAnimator() {
    }

    @Override // jp.wasabeef.recyclerview.animators.BaseItemAnimator
    public void animateAddImpl(RecyclerView.ViewHolder viewHolder) {
        ViewCompat.animate(viewHolder.itemView).rotationX(0.0f).setDuration(getAddDuration()).setInterpolator(this.mInterpolator).setListener(new BaseItemAnimator.DefaultAddVpaListener(viewHolder)).setStartDelay(getAddDelay(viewHolder)).start();
    }

    @Override // jp.wasabeef.recyclerview.animators.BaseItemAnimator
    public void animateRemoveImpl(RecyclerView.ViewHolder viewHolder) {
        ViewCompat.animate(viewHolder.itemView).rotationX(-90.0f).setDuration(getRemoveDuration()).setInterpolator(this.mInterpolator).setListener(new BaseItemAnimator.DefaultRemoveVpaListener(viewHolder)).setStartDelay(getRemoveDelay(viewHolder)).start();
    }

    @Override // jp.wasabeef.recyclerview.animators.BaseItemAnimator
    public void preAnimateAddImpl(RecyclerView.ViewHolder viewHolder) {
        ViewCompat.setRotationX(viewHolder.itemView, -90.0f);
    }

    public FlipInBottomXAnimator(Interpolator interpolator) {
        this.mInterpolator = interpolator;
    }
}
