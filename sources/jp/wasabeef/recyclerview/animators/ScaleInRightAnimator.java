package jp.wasabeef.recyclerview.animators;

import android.view.View;
import android.view.animation.Interpolator;
import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.RecyclerView;
import jp.wasabeef.recyclerview.animators.BaseItemAnimator;

/* loaded from: classes.dex */
public class ScaleInRightAnimator extends BaseItemAnimator {
    public ScaleInRightAnimator() {
    }

    @Override // jp.wasabeef.recyclerview.animators.BaseItemAnimator
    public void animateAddImpl(RecyclerView.ViewHolder viewHolder) {
        ViewCompat.animate(viewHolder.itemView).scaleX(1.0f).scaleY(1.0f).setDuration(getAddDuration()).setInterpolator(this.mInterpolator).setListener(new BaseItemAnimator.DefaultAddVpaListener(viewHolder)).setStartDelay(getAddDelay(viewHolder)).start();
    }

    @Override // jp.wasabeef.recyclerview.animators.BaseItemAnimator
    public void animateRemoveImpl(RecyclerView.ViewHolder viewHolder) {
        ViewCompat.animate(viewHolder.itemView).scaleX(0.0f).scaleY(0.0f).setDuration(getRemoveDuration()).setInterpolator(this.mInterpolator).setListener(new BaseItemAnimator.DefaultRemoveVpaListener(viewHolder)).setStartDelay(getRemoveDelay(viewHolder)).start();
    }

    @Override // jp.wasabeef.recyclerview.animators.BaseItemAnimator
    public void preAnimateAddImpl(RecyclerView.ViewHolder viewHolder) {
        View view = viewHolder.itemView;
        ViewCompat.setPivotX(view, view.getWidth());
        ViewCompat.setScaleX(viewHolder.itemView, 0.0f);
        ViewCompat.setScaleY(viewHolder.itemView, 0.0f);
    }

    @Override // jp.wasabeef.recyclerview.animators.BaseItemAnimator
    public void preAnimateRemoveImpl(RecyclerView.ViewHolder viewHolder) {
        View view = viewHolder.itemView;
        ViewCompat.setPivotX(view, view.getWidth());
    }

    public ScaleInRightAnimator(Interpolator interpolator) {
        this.mInterpolator = interpolator;
    }
}
