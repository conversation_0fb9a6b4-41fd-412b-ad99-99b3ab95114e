package jp.wasabeef.recyclerview;

/* loaded from: classes.dex */
public final class R {

    /* loaded from: classes.dex */
    public static final class anim {
        public static final int abc_fade_in = 0x7f010000;
        public static final int abc_fade_out = 0x7f010001;
        public static final int abc_grow_fade_in_from_bottom = 0x7f010002;
        public static final int abc_popup_enter = 0x7f010003;
        public static final int abc_popup_exit = 0x7f010004;
        public static final int abc_shrink_fade_out_from_bottom = 0x7f010005;
        public static final int abc_slide_in_bottom = 0x7f010006;
        public static final int abc_slide_in_top = 0x7f010007;
        public static final int abc_slide_out_bottom = 0x7f010008;
        public static final int abc_slide_out_top = 0x7f010009;
    }

    /* loaded from: classes.dex */
    public static final class attr {
        public static final int actionBarDivider = 0x7f030000;
        public static final int actionBarItemBackground = 0x7f030001;
        public static final int actionBarPopupTheme = 0x7f030002;
        public static final int actionBarSize = 0x7f030003;
        public static final int actionBarSplitStyle = 0x7f030004;
        public static final int actionBarStyle = 0x7f030005;
        public static final int actionBarTabBarStyle = 0x7f030006;
        public static final int actionBarTabStyle = 0x7f030007;
        public static final int actionBarTabTextStyle = 0x7f030008;
        public static final int actionBarTheme = 0x7f030009;
        public static final int actionBarWidgetTheme = 0x7f03000a;
        public static final int actionButtonStyle = 0x7f03000b;
        public static final int actionDropDownStyle = 0x7f03000c;
        public static final int actionLayout = 0x7f03000d;
        public static final int actionMenuTextAppearance = 0x7f03000e;
        public static final int actionMenuTextColor = 0x7f03000f;
        public static final int actionModeBackground = 0x7f030010;
        public static final int actionModeCloseButtonStyle = 0x7f030011;
        public static final int actionModeCloseDrawable = 0x7f030012;
        public static final int actionModeCopyDrawable = 0x7f030013;
        public static final int actionModeCutDrawable = 0x7f030014;
        public static final int actionModeFindDrawable = 0x7f030015;
        public static final int actionModePasteDrawable = 0x7f030016;
        public static final int actionModePopupWindowStyle = 0x7f030017;
        public static final int actionModeSelectAllDrawable = 0x7f030018;
        public static final int actionModeShareDrawable = 0x7f030019;
        public static final int actionModeSplitBackground = 0x7f03001a;
        public static final int actionModeStyle = 0x7f03001b;
        public static final int actionModeWebSearchDrawable = 0x7f03001c;
        public static final int actionOverflowButtonStyle = 0x7f03001d;
        public static final int actionOverflowMenuStyle = 0x7f03001e;
        public static final int actionProviderClass = 0x7f03001f;
        public static final int actionViewClass = 0x7f030020;
        public static final int activityChooserViewStyle = 0x7f030021;
        public static final int alertDialogButtonGroupStyle = 0x7f030022;
        public static final int alertDialogCenterButtons = 0x7f030023;
        public static final int alertDialogStyle = 0x7f030024;
        public static final int alertDialogTheme = 0x7f030025;
        public static final int allowStacking = 0x7f030026;
        public static final int alpha = 0x7f030027;
        public static final int arrowHeadLength = 0x7f030029;
        public static final int arrowShaftLength = 0x7f03002a;
        public static final int autoCompleteTextViewStyle = 0x7f03002b;
        public static final int background = 0x7f030031;
        public static final int backgroundSplit = 0x7f030032;
        public static final int backgroundStacked = 0x7f030033;
        public static final int backgroundTint = 0x7f030034;
        public static final int backgroundTintMode = 0x7f030035;
        public static final int barLength = 0x7f030036;
        public static final int borderlessButtonStyle = 0x7f030039;
        public static final int buttonBarButtonStyle = 0x7f03003a;
        public static final int buttonBarNegativeButtonStyle = 0x7f03003b;
        public static final int buttonBarNeutralButtonStyle = 0x7f03003c;
        public static final int buttonBarPositiveButtonStyle = 0x7f03003d;
        public static final int buttonBarStyle = 0x7f03003e;
        public static final int buttonGravity = 0x7f030040;
        public static final int buttonPanelSideLayout = 0x7f030042;
        public static final int buttonStyle = 0x7f030043;
        public static final int buttonStyleSmall = 0x7f030044;
        public static final int buttonTint = 0x7f030045;
        public static final int buttonTintMode = 0x7f030046;
        public static final int checkboxStyle = 0x7f030048;
        public static final int checkedTextViewStyle = 0x7f030049;
        public static final int closeIcon = 0x7f03004a;
        public static final int closeItemLayout = 0x7f03004b;
        public static final int collapseContentDescription = 0x7f03004c;
        public static final int collapseIcon = 0x7f03004d;
        public static final int color = 0x7f03004e;
        public static final int colorAccent = 0x7f03004f;
        public static final int colorBackgroundFloating = 0x7f030050;
        public static final int colorButtonNormal = 0x7f030051;
        public static final int colorControlActivated = 0x7f030052;
        public static final int colorControlHighlight = 0x7f030053;
        public static final int colorControlNormal = 0x7f030054;
        public static final int colorPrimary = 0x7f030056;
        public static final int colorPrimaryDark = 0x7f030057;
        public static final int colorSwitchThumbNormal = 0x7f030058;
        public static final int commitIcon = 0x7f030059;
        public static final int contentInsetEnd = 0x7f03005e;
        public static final int contentInsetEndWithActions = 0x7f03005f;
        public static final int contentInsetLeft = 0x7f030060;
        public static final int contentInsetRight = 0x7f030061;
        public static final int contentInsetStart = 0x7f030062;
        public static final int contentInsetStartWithNavigation = 0x7f030063;
        public static final int controlBackground = 0x7f030064;
        public static final int customNavigationLayout = 0x7f030065;
        public static final int defaultQueryHint = 0x7f030066;
        public static final int dialogPreferredPadding = 0x7f030068;
        public static final int dialogTheme = 0x7f030069;
        public static final int displayOptions = 0x7f03006a;
        public static final int divider = 0x7f03006b;
        public static final int dividerHorizontal = 0x7f03006c;
        public static final int dividerPadding = 0x7f03006d;
        public static final int dividerVertical = 0x7f03006e;
        public static final int drawableSize = 0x7f030073;
        public static final int drawerArrowStyle = 0x7f030078;
        public static final int dropDownListViewStyle = 0x7f030079;
        public static final int dropdownListPreferredItemHeight = 0x7f03007a;
        public static final int editTextBackground = 0x7f03007b;
        public static final int editTextColor = 0x7f03007c;
        public static final int editTextStyle = 0x7f03007d;
        public static final int elevation = 0x7f03007e;
        public static final int expandActivityOverflowButtonDrawable = 0x7f030080;
        public static final int gapBetweenBars = 0x7f030092;
        public static final int goIcon = 0x7f030093;
        public static final int height = 0x7f030094;
        public static final int hideOnContentScroll = 0x7f030095;
        public static final int homeAsUpIndicator = 0x7f030096;
        public static final int homeLayout = 0x7f030097;
        public static final int icon = 0x7f030098;
        public static final int iconifiedByDefault = 0x7f03009b;
        public static final int imageButtonStyle = 0x7f03009c;
        public static final int indeterminateProgressStyle = 0x7f03009d;
        public static final int initialActivityCount = 0x7f03009e;
        public static final int isLightTheme = 0x7f03009f;
        public static final int itemPadding = 0x7f0300a0;
        public static final int layout = 0x7f0300a2;
        public static final int layoutManager = 0x7f0300a3;
        public static final int listChoiceBackgroundIndicator = 0x7f0300d7;
        public static final int listDividerAlertDialog = 0x7f0300da;
        public static final int listItemLayout = 0x7f0300db;
        public static final int listLayout = 0x7f0300dc;
        public static final int listMenuViewStyle = 0x7f0300dd;
        public static final int listPopupWindowStyle = 0x7f0300de;
        public static final int listPreferredItemHeight = 0x7f0300df;
        public static final int listPreferredItemHeightLarge = 0x7f0300e0;
        public static final int listPreferredItemHeightSmall = 0x7f0300e1;
        public static final int listPreferredItemPaddingLeft = 0x7f0300e3;
        public static final int listPreferredItemPaddingRight = 0x7f0300e4;
        public static final int logo = 0x7f0300e6;
        public static final int logoDescription = 0x7f0300e7;
        public static final int maxButtonHeight = 0x7f0300e8;
        public static final int measureWithLargestChild = 0x7f0300e9;
        public static final int multiChoiceItemLayout = 0x7f0300eb;
        public static final int navigationContentDescription = 0x7f0300ec;
        public static final int navigationIcon = 0x7f0300ed;
        public static final int navigationMode = 0x7f0300ee;
        public static final int overlapAnchor = 0x7f0300f0;
        public static final int paddingBottomNoButtons = 0x7f0300f1;
        public static final int paddingEnd = 0x7f0300f2;
        public static final int paddingStart = 0x7f0300f3;
        public static final int paddingTopNoTitle = 0x7f0300f4;
        public static final int panelBackground = 0x7f0300f5;
        public static final int panelMenuListTheme = 0x7f0300f6;
        public static final int panelMenuListWidth = 0x7f0300f7;
        public static final int popupMenuStyle = 0x7f030100;
        public static final int popupTheme = 0x7f030101;
        public static final int popupWindowStyle = 0x7f030102;
        public static final int preserveIconSpacing = 0x7f030103;
        public static final int progressBarPadding = 0x7f030104;
        public static final int progressBarStyle = 0x7f030105;
        public static final int queryBackground = 0x7f030106;
        public static final int queryHint = 0x7f030107;
        public static final int radioButtonStyle = 0x7f030108;
        public static final int ratingBarStyle = 0x7f030109;
        public static final int ratingBarStyleIndicator = 0x7f03010a;
        public static final int ratingBarStyleSmall = 0x7f03010b;
        public static final int reverseLayout = 0x7f03010d;
        public static final int searchHintIcon = 0x7f03010e;
        public static final int searchIcon = 0x7f03010f;
        public static final int searchViewStyle = 0x7f030110;
        public static final int seekBarStyle = 0x7f030111;
        public static final int selectableItemBackground = 0x7f030112;
        public static final int selectableItemBackgroundBorderless = 0x7f030113;
        public static final int showAsAction = 0x7f030114;
        public static final int showDividers = 0x7f030115;
        public static final int showText = 0x7f030116;
        public static final int showTitle = 0x7f030117;
        public static final int singleChoiceItemLayout = 0x7f030118;
        public static final int spanCount = 0x7f030119;
        public static final int spinBars = 0x7f03012a;
        public static final int spinnerDropDownItemStyle = 0x7f03012b;
        public static final int spinnerStyle = 0x7f03012c;
        public static final int splitTrack = 0x7f03012d;
        public static final int srcCompat = 0x7f03012e;
        public static final int stackFromEnd = 0x7f03012f;
        public static final int state_above_anchor = 0x7f030130;
        public static final int subMenuArrow = 0x7f030131;
        public static final int submitBackground = 0x7f030132;
        public static final int subtitle = 0x7f030133;
        public static final int subtitleTextAppearance = 0x7f030134;
        public static final int subtitleTextColor = 0x7f030135;
        public static final int subtitleTextStyle = 0x7f030136;
        public static final int suggestionRowLayout = 0x7f030137;
        public static final int switchMinWidth = 0x7f030138;
        public static final int switchPadding = 0x7f030139;
        public static final int switchStyle = 0x7f03013a;
        public static final int switchTextAppearance = 0x7f03013b;
        public static final int textAllCaps = 0x7f03013c;
        public static final int textAppearanceLargePopupMenu = 0x7f03013d;
        public static final int textAppearanceListItem = 0x7f03013e;
        public static final int textAppearanceListItemSmall = 0x7f030140;
        public static final int textAppearancePopupMenuHeader = 0x7f030141;
        public static final int textAppearanceSearchResultSubtitle = 0x7f030142;
        public static final int textAppearanceSearchResultTitle = 0x7f030143;
        public static final int textAppearanceSmallPopupMenu = 0x7f030144;
        public static final int textColorAlertDialogListItem = 0x7f030145;
        public static final int textColorSearchUrl = 0x7f030146;
        public static final int theme = 0x7f030148;
        public static final int thickness = 0x7f030149;
        public static final int thumbTextPadding = 0x7f03014a;
        public static final int thumbTint = 0x7f03014b;
        public static final int thumbTintMode = 0x7f03014c;
        public static final int tickMark = 0x7f03014d;
        public static final int tickMarkTint = 0x7f03014e;
        public static final int tickMarkTintMode = 0x7f03014f;
        public static final int title = 0x7f030152;
        public static final int titleMargin = 0x7f030153;
        public static final int titleMarginBottom = 0x7f030154;
        public static final int titleMarginEnd = 0x7f030155;
        public static final int titleMarginStart = 0x7f030156;
        public static final int titleMarginTop = 0x7f030157;
        public static final int titleMargins = 0x7f030158;
        public static final int titleTextAppearance = 0x7f030159;
        public static final int titleTextColor = 0x7f03015a;
        public static final int titleTextStyle = 0x7f03015b;
        public static final int toolbarNavigationButtonStyle = 0x7f03015c;
        public static final int toolbarStyle = 0x7f03015d;
        public static final int track = 0x7f030161;
        public static final int trackTint = 0x7f030162;
        public static final int trackTintMode = 0x7f030163;
        public static final int voiceIcon = 0x7f030167;
        public static final int windowActionBar = 0x7f030168;
        public static final int windowActionBarOverlay = 0x7f030169;
        public static final int windowActionModeOverlay = 0x7f03016a;
        public static final int windowFixedHeightMajor = 0x7f03016b;
        public static final int windowFixedHeightMinor = 0x7f03016c;
        public static final int windowFixedWidthMajor = 0x7f03016d;
        public static final int windowFixedWidthMinor = 0x7f03016e;
        public static final int windowMinWidthMajor = 0x7f03016f;
        public static final int windowMinWidthMinor = 0x7f030170;
        public static final int windowNoTitle = 0x7f030171;
    }

    /* loaded from: classes.dex */
    public static final class bool {
        public static final int abc_action_bar_embed_tabs = 0x7f040000;
        public static final int abc_allow_stacked_button_bar = 0x7f040001;
        public static final int abc_config_actionMenuItemAllCaps = 0x7f040002;
    }

    /* loaded from: classes.dex */
    public static final class color {
        public static final int abc_background_cache_hint_selector_material_dark = 0x7f050001;
        public static final int abc_background_cache_hint_selector_material_light = 0x7f050002;
        public static final int abc_btn_colored_borderless_text_material = 0x7f050003;
        public static final int abc_btn_colored_text_material = 0x7f050004;
        public static final int abc_color_highlight_material = 0x7f050005;
        public static final int abc_hint_foreground_material_dark = 0x7f050006;
        public static final int abc_hint_foreground_material_light = 0x7f050007;
        public static final int abc_input_method_navigation_guard = 0x7f050008;
        public static final int abc_primary_text_disable_only_material_dark = 0x7f050009;
        public static final int abc_primary_text_disable_only_material_light = 0x7f05000a;
        public static final int abc_primary_text_material_dark = 0x7f05000b;
        public static final int abc_primary_text_material_light = 0x7f05000c;
        public static final int abc_search_url_text = 0x7f05000d;
        public static final int abc_search_url_text_normal = 0x7f05000e;
        public static final int abc_search_url_text_pressed = 0x7f05000f;
        public static final int abc_search_url_text_selected = 0x7f050010;
        public static final int abc_secondary_text_material_dark = 0x7f050011;
        public static final int abc_secondary_text_material_light = 0x7f050012;
        public static final int abc_tint_btn_checkable = 0x7f050013;
        public static final int abc_tint_default = 0x7f050014;
        public static final int abc_tint_edittext = 0x7f050015;
        public static final int abc_tint_seek_thumb = 0x7f050016;
        public static final int abc_tint_spinner = 0x7f050017;
        public static final int abc_tint_switch_track = 0x7f050018;
        public static final int accent_material_dark = 0x7f050019;
        public static final int accent_material_light = 0x7f05001a;
        public static final int background_floating_material_dark = 0x7f05001b;
        public static final int background_floating_material_light = 0x7f05001c;
        public static final int background_material_dark = 0x7f05001d;
        public static final int background_material_light = 0x7f05001e;
        public static final int bright_foreground_disabled_material_dark = 0x7f05001f;
        public static final int bright_foreground_disabled_material_light = 0x7f050020;
        public static final int bright_foreground_inverse_material_dark = 0x7f050021;
        public static final int bright_foreground_inverse_material_light = 0x7f050022;
        public static final int bright_foreground_material_dark = 0x7f050023;
        public static final int bright_foreground_material_light = 0x7f050024;
        public static final int button_material_dark = 0x7f050025;
        public static final int button_material_light = 0x7f050026;
        public static final int dim_foreground_disabled_material_dark = 0x7f050073;
        public static final int dim_foreground_disabled_material_light = 0x7f050074;
        public static final int dim_foreground_material_dark = 0x7f050075;
        public static final int dim_foreground_material_light = 0x7f050076;
        public static final int foreground_material_dark = 0x7f050079;
        public static final int foreground_material_light = 0x7f05007a;
        public static final int highlighted_text_material_dark = 0x7f05007b;
        public static final int highlighted_text_material_light = 0x7f05007c;
        public static final int material_blue_grey_800 = 0x7f05007f;
        public static final int material_blue_grey_900 = 0x7f050080;
        public static final int material_blue_grey_950 = 0x7f050081;
        public static final int material_deep_teal_200 = 0x7f050082;
        public static final int material_deep_teal_500 = 0x7f050083;
        public static final int material_grey_100 = 0x7f050084;
        public static final int material_grey_300 = 0x7f050085;
        public static final int material_grey_50 = 0x7f050086;
        public static final int material_grey_600 = 0x7f050087;
        public static final int material_grey_800 = 0x7f050088;
        public static final int material_grey_850 = 0x7f050089;
        public static final int material_grey_900 = 0x7f05008a;
        public static final int notification_action_color_filter = 0x7f05008b;
        public static final int notification_icon_bg_color = 0x7f05008c;
        public static final int primary_dark_material_dark = 0x7f05008e;
        public static final int primary_dark_material_light = 0x7f05008f;
        public static final int primary_material_dark = 0x7f050090;
        public static final int primary_material_light = 0x7f050091;
        public static final int primary_text_default_material_dark = 0x7f050092;
        public static final int primary_text_default_material_light = 0x7f050093;
        public static final int primary_text_disabled_material_dark = 0x7f050094;
        public static final int primary_text_disabled_material_light = 0x7f050095;
        public static final int ripple_material_dark = 0x7f050096;
        public static final int ripple_material_light = 0x7f050097;
        public static final int secondary_text_default_material_dark = 0x7f050098;
        public static final int secondary_text_default_material_light = 0x7f050099;
        public static final int secondary_text_disabled_material_dark = 0x7f05009a;
        public static final int secondary_text_disabled_material_light = 0x7f05009b;
        public static final int switch_thumb_disabled_material_dark = 0x7f05009d;
        public static final int switch_thumb_disabled_material_light = 0x7f05009e;
        public static final int switch_thumb_material_dark = 0x7f05009f;
        public static final int switch_thumb_material_light = 0x7f0500a0;
        public static final int switch_thumb_normal_material_dark = 0x7f0500a1;
        public static final int switch_thumb_normal_material_light = 0x7f0500a2;
    }

    /* loaded from: classes.dex */
    public static final class dimen {
        public static final int abc_action_bar_content_inset_material = 0x7f060000;
        public static final int abc_action_bar_content_inset_with_nav = 0x7f060001;
        public static final int abc_action_bar_default_height_material = 0x7f060002;
        public static final int abc_action_bar_default_padding_end_material = 0x7f060003;
        public static final int abc_action_bar_default_padding_start_material = 0x7f060004;
        public static final int abc_action_bar_elevation_material = 0x7f060005;
        public static final int abc_action_bar_icon_vertical_padding_material = 0x7f060006;
        public static final int abc_action_bar_overflow_padding_end_material = 0x7f060007;
        public static final int abc_action_bar_overflow_padding_start_material = 0x7f060008;
        public static final int abc_action_bar_stacked_max_height = 0x7f060009;
        public static final int abc_action_bar_stacked_tab_max_width = 0x7f06000a;
        public static final int abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b;
        public static final int abc_action_bar_subtitle_top_margin_material = 0x7f06000c;
        public static final int abc_action_button_min_height_material = 0x7f06000d;
        public static final int abc_action_button_min_width_material = 0x7f06000e;
        public static final int abc_action_button_min_width_overflow_material = 0x7f06000f;
        public static final int abc_alert_dialog_button_bar_height = 0x7f060010;
        public static final int abc_button_inset_horizontal_material = 0x7f060012;
        public static final int abc_button_inset_vertical_material = 0x7f060013;
        public static final int abc_button_padding_horizontal_material = 0x7f060014;
        public static final int abc_button_padding_vertical_material = 0x7f060015;
        public static final int abc_cascading_menus_min_smallest_width = 0x7f060016;
        public static final int abc_config_prefDialogWidth = 0x7f060017;
        public static final int abc_control_corner_material = 0x7f060018;
        public static final int abc_control_inset_material = 0x7f060019;
        public static final int abc_control_padding_material = 0x7f06001a;
        public static final int abc_dialog_fixed_height_major = 0x7f06001c;
        public static final int abc_dialog_fixed_height_minor = 0x7f06001d;
        public static final int abc_dialog_fixed_width_major = 0x7f06001e;
        public static final int abc_dialog_fixed_width_minor = 0x7f06001f;
        public static final int abc_dialog_list_padding_bottom_no_buttons = 0x7f060020;
        public static final int abc_dialog_list_padding_top_no_title = 0x7f060021;
        public static final int abc_dialog_min_width_major = 0x7f060022;
        public static final int abc_dialog_min_width_minor = 0x7f060023;
        public static final int abc_dialog_padding_material = 0x7f060024;
        public static final int abc_dialog_padding_top_material = 0x7f060025;
        public static final int abc_dialog_title_divider_material = 0x7f060026;
        public static final int abc_disabled_alpha_material_dark = 0x7f060027;
        public static final int abc_disabled_alpha_material_light = 0x7f060028;
        public static final int abc_dropdownitem_icon_width = 0x7f060029;
        public static final int abc_dropdownitem_text_padding_left = 0x7f06002a;
        public static final int abc_dropdownitem_text_padding_right = 0x7f06002b;
        public static final int abc_edit_text_inset_bottom_material = 0x7f06002c;
        public static final int abc_edit_text_inset_horizontal_material = 0x7f06002d;
        public static final int abc_edit_text_inset_top_material = 0x7f06002e;
        public static final int abc_floating_window_z = 0x7f06002f;
        public static final int abc_list_item_padding_horizontal_material = 0x7f060033;
        public static final int abc_panel_menu_list_width = 0x7f060034;
        public static final int abc_progress_bar_height_material = 0x7f060035;
        public static final int abc_search_view_preferred_height = 0x7f060036;
        public static final int abc_search_view_preferred_width = 0x7f060037;
        public static final int abc_seekbar_track_background_height_material = 0x7f060038;
        public static final int abc_seekbar_track_progress_height_material = 0x7f060039;
        public static final int abc_select_dialog_padding_start_material = 0x7f06003a;
        public static final int abc_switch_padding = 0x7f06003b;
        public static final int abc_text_size_body_1_material = 0x7f06003c;
        public static final int abc_text_size_body_2_material = 0x7f06003d;
        public static final int abc_text_size_button_material = 0x7f06003e;
        public static final int abc_text_size_caption_material = 0x7f06003f;
        public static final int abc_text_size_display_1_material = 0x7f060040;
        public static final int abc_text_size_display_2_material = 0x7f060041;
        public static final int abc_text_size_display_3_material = 0x7f060042;
        public static final int abc_text_size_display_4_material = 0x7f060043;
        public static final int abc_text_size_headline_material = 0x7f060044;
        public static final int abc_text_size_large_material = 0x7f060045;
        public static final int abc_text_size_medium_material = 0x7f060046;
        public static final int abc_text_size_menu_header_material = 0x7f060047;
        public static final int abc_text_size_menu_material = 0x7f060048;
        public static final int abc_text_size_small_material = 0x7f060049;
        public static final int abc_text_size_subhead_material = 0x7f06004a;
        public static final int abc_text_size_subtitle_material_toolbar = 0x7f06004b;
        public static final int abc_text_size_title_material = 0x7f06004c;
        public static final int abc_text_size_title_material_toolbar = 0x7f06004d;
        public static final int disabled_alpha_material_dark = 0x7f060062;
        public static final int disabled_alpha_material_light = 0x7f060063;
        public static final int highlight_alpha_material_colored = 0x7f060450;
        public static final int highlight_alpha_material_dark = 0x7f060451;
        public static final int highlight_alpha_material_light = 0x7f060452;
        public static final int hint_alpha_material_dark = 0x7f060453;
        public static final int hint_alpha_material_light = 0x7f060454;
        public static final int hint_pressed_alpha_material_dark = 0x7f060455;
        public static final int hint_pressed_alpha_material_light = 0x7f060456;
        public static final int item_touch_helper_max_drag_scroll_per_frame = 0x7f060457;
        public static final int item_touch_helper_swipe_escape_max_velocity = 0x7f060458;
        public static final int item_touch_helper_swipe_escape_velocity = 0x7f060459;
        public static final int notification_action_icon_size = 0x7f06045f;
        public static final int notification_action_text_size = 0x7f060460;
        public static final int notification_big_circle_margin = 0x7f060461;
        public static final int notification_content_margin_start = 0x7f060462;
        public static final int notification_large_icon_height = 0x7f060463;
        public static final int notification_large_icon_width = 0x7f060464;
        public static final int notification_main_column_padding_top = 0x7f060465;
        public static final int notification_media_narrow_margin = 0x7f060466;
        public static final int notification_right_icon_size = 0x7f060467;
        public static final int notification_right_side_padding_top = 0x7f060468;
        public static final int notification_small_icon_background_padding = 0x7f060469;
        public static final int notification_small_icon_size_as_large = 0x7f06046a;
        public static final int notification_subtext_size = 0x7f06046b;
        public static final int notification_top_pad = 0x7f06046c;
        public static final int notification_top_pad_large_text = 0x7f06046d;
    }

    /* loaded from: classes.dex */
    public static final class drawable {
        public static final int abc_ab_share_pack_mtrl_alpha = 0x7f070005;
        public static final int abc_action_bar_item_background_material = 0x7f070006;
        public static final int abc_btn_borderless_material = 0x7f070007;
        public static final int abc_btn_check_material = 0x7f070008;
        public static final int abc_btn_check_to_on_mtrl_000 = 0x7f07000a;
        public static final int abc_btn_check_to_on_mtrl_015 = 0x7f07000b;
        public static final int abc_btn_colored_material = 0x7f07000c;
        public static final int abc_btn_default_mtrl_shape = 0x7f07000d;
        public static final int abc_btn_radio_material = 0x7f07000e;
        public static final int abc_btn_radio_to_on_mtrl_000 = 0x7f070010;
        public static final int abc_btn_radio_to_on_mtrl_015 = 0x7f070011;
        public static final int abc_btn_switch_to_on_mtrl_00001 = 0x7f070012;
        public static final int abc_btn_switch_to_on_mtrl_00012 = 0x7f070013;
        public static final int abc_cab_background_internal_bg = 0x7f070014;
        public static final int abc_cab_background_top_material = 0x7f070015;
        public static final int abc_cab_background_top_mtrl_alpha = 0x7f070016;
        public static final int abc_control_background_material = 0x7f070017;
        public static final int abc_dialog_material_background = 0x7f070018;
        public static final int abc_edit_text_material = 0x7f070019;
        public static final int abc_ic_ab_back_material = 0x7f07001a;
        public static final int abc_ic_arrow_drop_right_black_24dp = 0x7f07001b;
        public static final int abc_ic_clear_material = 0x7f07001c;
        public static final int abc_ic_commit_search_api_mtrl_alpha = 0x7f07001d;
        public static final int abc_ic_go_search_api_material = 0x7f07001e;
        public static final int abc_ic_menu_copy_mtrl_am_alpha = 0x7f07001f;
        public static final int abc_ic_menu_cut_mtrl_alpha = 0x7f070020;
        public static final int abc_ic_menu_overflow_material = 0x7f070021;
        public static final int abc_ic_menu_paste_mtrl_am_alpha = 0x7f070022;
        public static final int abc_ic_menu_selectall_mtrl_alpha = 0x7f070023;
        public static final int abc_ic_menu_share_mtrl_alpha = 0x7f070024;
        public static final int abc_ic_search_api_material = 0x7f070025;
        public static final int abc_ic_star_black_16dp = 0x7f070026;
        public static final int abc_ic_star_black_36dp = 0x7f070027;
        public static final int abc_ic_star_black_48dp = 0x7f070028;
        public static final int abc_ic_star_half_black_16dp = 0x7f070029;
        public static final int abc_ic_star_half_black_36dp = 0x7f07002a;
        public static final int abc_ic_star_half_black_48dp = 0x7f07002b;
        public static final int abc_ic_voice_search_api_material = 0x7f07002c;
        public static final int abc_item_background_holo_dark = 0x7f07002d;
        public static final int abc_item_background_holo_light = 0x7f07002e;
        public static final int abc_list_divider_mtrl_alpha = 0x7f070030;
        public static final int abc_list_focused_holo = 0x7f070031;
        public static final int abc_list_longpressed_holo = 0x7f070032;
        public static final int abc_list_pressed_holo_dark = 0x7f070033;
        public static final int abc_list_pressed_holo_light = 0x7f070034;
        public static final int abc_list_selector_background_transition_holo_dark = 0x7f070035;
        public static final int abc_list_selector_background_transition_holo_light = 0x7f070036;
        public static final int abc_list_selector_disabled_holo_dark = 0x7f070037;
        public static final int abc_list_selector_disabled_holo_light = 0x7f070038;
        public static final int abc_list_selector_holo_dark = 0x7f070039;
        public static final int abc_list_selector_holo_light = 0x7f07003a;
        public static final int abc_menu_hardkey_panel_mtrl_mult = 0x7f07003b;
        public static final int abc_popup_background_mtrl_mult = 0x7f07003c;
        public static final int abc_ratingbar_indicator_material = 0x7f07003d;
        public static final int abc_ratingbar_material = 0x7f07003e;
        public static final int abc_ratingbar_small_material = 0x7f07003f;
        public static final int abc_scrubber_control_off_mtrl_alpha = 0x7f070040;
        public static final int abc_scrubber_control_to_pressed_mtrl_000 = 0x7f070041;
        public static final int abc_scrubber_control_to_pressed_mtrl_005 = 0x7f070042;
        public static final int abc_scrubber_primary_mtrl_alpha = 0x7f070043;
        public static final int abc_scrubber_track_mtrl_alpha = 0x7f070044;
        public static final int abc_seekbar_thumb_material = 0x7f070045;
        public static final int abc_seekbar_tick_mark_material = 0x7f070046;
        public static final int abc_seekbar_track_material = 0x7f070047;
        public static final int abc_spinner_mtrl_am_alpha = 0x7f070048;
        public static final int abc_spinner_textfield_background_material = 0x7f070049;
        public static final int abc_switch_thumb_material = 0x7f07004a;
        public static final int abc_switch_track_mtrl_alpha = 0x7f07004b;
        public static final int abc_tab_indicator_material = 0x7f07004c;
        public static final int abc_tab_indicator_mtrl_alpha = 0x7f07004d;
        public static final int abc_text_cursor_material = 0x7f07004e;
        public static final int abc_text_select_handle_left_mtrl_dark = 0x7f07004f;
        public static final int abc_text_select_handle_left_mtrl_light = 0x7f070050;
        public static final int abc_text_select_handle_middle_mtrl_dark = 0x7f070051;
        public static final int abc_text_select_handle_middle_mtrl_light = 0x7f070052;
        public static final int abc_text_select_handle_right_mtrl_dark = 0x7f070053;
        public static final int abc_text_select_handle_right_mtrl_light = 0x7f070054;
        public static final int abc_textfield_activated_mtrl_alpha = 0x7f070055;
        public static final int abc_textfield_default_mtrl_alpha = 0x7f070056;
        public static final int abc_textfield_search_activated_mtrl_alpha = 0x7f070057;
        public static final int abc_textfield_search_default_mtrl_alpha = 0x7f070058;
        public static final int abc_textfield_search_material = 0x7f070059;
        public static final int abc_vector_test = 0x7f07005a;
        public static final int notification_action_background = 0x7f07009a;
        public static final int notification_bg = 0x7f07009b;
        public static final int notification_bg_low = 0x7f07009c;
        public static final int notification_bg_low_normal = 0x7f07009d;
        public static final int notification_bg_low_pressed = 0x7f07009e;
        public static final int notification_bg_normal = 0x7f07009f;
        public static final int notification_bg_normal_pressed = 0x7f0700a0;
        public static final int notification_icon_background = 0x7f0700a1;
        public static final int notification_template_icon_bg = 0x7f0700a2;
        public static final int notification_template_icon_low_bg = 0x7f0700a3;
        public static final int notification_tile_bg = 0x7f0700a4;
        public static final int notify_panel_notification_icon_bg = 0x7f0700a5;
    }

    /* loaded from: classes.dex */
    public static final class id {
        public static final int action_bar = 0x7f090027;
        public static final int action_bar_activity_content = 0x7f090028;
        public static final int action_bar_container = 0x7f090029;
        public static final int action_bar_root = 0x7f09002a;
        public static final int action_bar_spinner = 0x7f09002b;
        public static final int action_bar_subtitle = 0x7f09002c;
        public static final int action_bar_title = 0x7f09002d;
        public static final int action_container = 0x7f09002e;
        public static final int action_context_bar = 0x7f09002f;
        public static final int action_divider = 0x7f090030;
        public static final int action_image = 0x7f090031;
        public static final int action_menu_divider = 0x7f090032;
        public static final int action_menu_presenter = 0x7f090033;
        public static final int action_mode_bar = 0x7f090034;
        public static final int action_mode_bar_stub = 0x7f090035;
        public static final int action_mode_close_button = 0x7f090036;
        public static final int action_text = 0x7f090037;
        public static final int actions = 0x7f090038;
        public static final int activity_chooser_view_content = 0x7f090039;
        public static final int add = 0x7f09003a;
        public static final int alertTitle = 0x7f09003b;
        public static final int always = 0x7f09003c;
        public static final int beginning = 0x7f090040;
        public static final int bottom = 0x7f090042;
        public static final int buttonPanel = 0x7f090045;
        public static final int checkbox = 0x7f09004b;
        public static final int chronometer = 0x7f09004d;
        public static final int collapseActionView = 0x7f09005a;
        public static final int contentPanel = 0x7f090061;
        public static final int custom = 0x7f090066;
        public static final int customPanel = 0x7f090067;
        public static final int decor_content_parent = 0x7f090068;
        public static final int default_activity_button = 0x7f090069;
        public static final int disableHome = 0x7f090072;
        public static final int edit_query = 0x7f090073;
        public static final int end = 0x7f090074;
        public static final int expand_activities_button = 0x7f090075;
        public static final int expanded_menu = 0x7f090076;
        public static final int home = 0x7f090087;
        public static final int homeAsUp = 0x7f090088;
        public static final int icon = 0x7f090089;
        public static final int icon_group = 0x7f09008a;
        public static final int ifRoom = 0x7f0900bd;
        public static final int image = 0x7f0900be;
        public static final int info = 0x7f0900c0;
        public static final int item_touch_helper_previous_elevation = 0x7f0900c4;
        public static final int line1 = 0x7f0900d9;
        public static final int line3 = 0x7f0900da;
        public static final int listMode = 0x7f0900db;
        public static final int list_item = 0x7f0900dc;
        public static final int middle = 0x7f0900e4;
        public static final int multiply = 0x7f0900e5;
        public static final int never = 0x7f0900e6;
        public static final int none = 0x7f0900e9;
        public static final int normal = 0x7f0900ea;
        public static final int notification_background = 0x7f0900eb;
        public static final int notification_main_column = 0x7f0900ec;
        public static final int notification_main_column_container = 0x7f0900ed;
        public static final int parentPanel = 0x7f0900f5;
        public static final int progress_circular = 0x7f0900fb;
        public static final int progress_horizontal = 0x7f0900fc;
        public static final int radio = 0x7f0900fd;
        public static final int right_icon = 0x7f090100;
        public static final int right_side = 0x7f090101;
        public static final int screen = 0x7f090108;
        public static final int scrollIndicatorDown = 0x7f090109;
        public static final int scrollIndicatorUp = 0x7f09010a;
        public static final int scrollView = 0x7f09010b;
        public static final int search_badge = 0x7f09010d;
        public static final int search_bar = 0x7f09010e;
        public static final int search_button = 0x7f09010f;
        public static final int search_close_btn = 0x7f090110;
        public static final int search_edit_frame = 0x7f090111;
        public static final int search_go_btn = 0x7f090112;
        public static final int search_mag_icon = 0x7f090113;
        public static final int search_plate = 0x7f090114;
        public static final int search_src_text = 0x7f090115;
        public static final int search_voice_btn = 0x7f090116;
        public static final int select_dialog_listview = 0x7f090117;
        public static final int shortcut = 0x7f090118;
        public static final int showCustom = 0x7f090119;
        public static final int showHome = 0x7f09011a;
        public static final int showTitle = 0x7f09011b;
        public static final int spacer = 0x7f09011c;
        public static final int split_action_bar = 0x7f090121;
        public static final int src_atop = 0x7f090124;
        public static final int src_in = 0x7f090125;
        public static final int src_over = 0x7f090126;
        public static final int submenuarrow = 0x7f090129;
        public static final int submit_area = 0x7f09012a;
        public static final int tabMode = 0x7f09012d;
        public static final int text = 0x7f090138;
        public static final int text2 = 0x7f090139;
        public static final int textSpacerNoButtons = 0x7f09013a;
        public static final int textSpacerNoTitle = 0x7f09013b;
        public static final int time = 0x7f09013c;
        public static final int title = 0x7f09013d;
        public static final int titleDividerNoCustom = 0x7f09013e;
        public static final int title_template = 0x7f090140;
        public static final int top = 0x7f090141;
        public static final int topPanel = 0x7f090142;
        public static final int up = 0x7f09017a;
        public static final int useLogo = 0x7f09017b;
        public static final int withText = 0x7f09017f;
        public static final int wrap_content = 0x7f090181;
    }

    /* loaded from: classes.dex */
    public static final class integer {
        public static final int abc_config_activityDefaultDur = 0x7f0a0000;
        public static final int abc_config_activityShortDur = 0x7f0a0001;
        public static final int cancel_button_image_alpha = 0x7f0a0002;
        public static final int status_bar_notification_info_maxnum = 0x7f0a0006;
    }

    /* loaded from: classes.dex */
    public static final class layout {
        public static final int abc_action_bar_title_item = 0x7f0c0000;
        public static final int abc_action_bar_up_container = 0x7f0c0001;
        public static final int abc_action_menu_item_layout = 0x7f0c0002;
        public static final int abc_action_menu_layout = 0x7f0c0003;
        public static final int abc_action_mode_bar = 0x7f0c0004;
        public static final int abc_action_mode_close_item_material = 0x7f0c0005;
        public static final int abc_activity_chooser_view = 0x7f0c0006;
        public static final int abc_activity_chooser_view_list_item = 0x7f0c0007;
        public static final int abc_alert_dialog_button_bar_material = 0x7f0c0008;
        public static final int abc_alert_dialog_material = 0x7f0c0009;
        public static final int abc_alert_dialog_title_material = 0x7f0c000a;
        public static final int abc_dialog_title_material = 0x7f0c000c;
        public static final int abc_expanded_menu_layout = 0x7f0c000d;
        public static final int abc_list_menu_item_checkbox = 0x7f0c000e;
        public static final int abc_list_menu_item_icon = 0x7f0c000f;
        public static final int abc_list_menu_item_layout = 0x7f0c0010;
        public static final int abc_list_menu_item_radio = 0x7f0c0011;
        public static final int abc_popup_menu_header_item_layout = 0x7f0c0012;
        public static final int abc_popup_menu_item_layout = 0x7f0c0013;
        public static final int abc_screen_content_include = 0x7f0c0014;
        public static final int abc_screen_simple = 0x7f0c0015;
        public static final int abc_screen_simple_overlay_action_mode = 0x7f0c0016;
        public static final int abc_screen_toolbar = 0x7f0c0017;
        public static final int abc_search_dropdown_item_icons_2line = 0x7f0c0018;
        public static final int abc_search_view = 0x7f0c0019;
        public static final int abc_select_dialog_material = 0x7f0c001a;
        public static final int notification_action = 0x7f0c006f;
        public static final int notification_action_tombstone = 0x7f0c0070;
        public static final int notification_template_custom_big = 0x7f0c0071;
        public static final int notification_template_icon_group = 0x7f0c0072;
        public static final int notification_template_part_chronometer = 0x7f0c0073;
        public static final int notification_template_part_time = 0x7f0c0074;
        public static final int select_dialog_item_material = 0x7f0c0077;
        public static final int select_dialog_multichoice_material = 0x7f0c0078;
        public static final int select_dialog_singlechoice_material = 0x7f0c0079;
        public static final int support_simple_spinner_dropdown_item = 0x7f0c007a;
    }

    /* loaded from: classes.dex */
    public static final class string {
        public static final int abc_action_bar_home_description = 0x7f0e0000;
        public static final int abc_action_bar_up_description = 0x7f0e0001;
        public static final int abc_action_menu_overflow_description = 0x7f0e0002;
        public static final int abc_action_mode_done = 0x7f0e0003;
        public static final int abc_activity_chooser_view_see_all = 0x7f0e0004;
        public static final int abc_activitychooserview_choose_application = 0x7f0e0005;
        public static final int abc_capital_off = 0x7f0e0006;
        public static final int abc_capital_on = 0x7f0e0007;
        public static final int abc_search_hint = 0x7f0e0012;
        public static final int abc_searchview_description_clear = 0x7f0e0013;
        public static final int abc_searchview_description_query = 0x7f0e0014;
        public static final int abc_searchview_description_search = 0x7f0e0015;
        public static final int abc_searchview_description_submit = 0x7f0e0016;
        public static final int abc_searchview_description_voice = 0x7f0e0017;
        public static final int abc_shareactionprovider_share_with = 0x7f0e0018;
        public static final int abc_shareactionprovider_share_with_application = 0x7f0e0019;
        public static final int abc_toolbar_collapse_description = 0x7f0e001a;
        public static final int search_menu_title = 0x7f0e006a;
        public static final int status_bar_notification_info_overflow = 0x7f0e006f;
    }

    /* loaded from: classes.dex */
    public static final class style {
        public static final int AlertDialog_AppCompat = 0x7f0f0000;
        public static final int AlertDialog_AppCompat_Light = 0x7f0f0001;
        public static final int Animation_AppCompat_Dialog = 0x7f0f0003;
        public static final int Animation_AppCompat_DropDownUp = 0x7f0f0004;
        public static final int Base_AlertDialog_AppCompat = 0x7f0f0007;
        public static final int Base_AlertDialog_AppCompat_Light = 0x7f0f0008;
        public static final int Base_Animation_AppCompat_Dialog = 0x7f0f0009;
        public static final int Base_Animation_AppCompat_DropDownUp = 0x7f0f000a;
        public static final int Base_DialogWindowTitleBackground_AppCompat = 0x7f0f000d;
        public static final int Base_DialogWindowTitle_AppCompat = 0x7f0f000c;
        public static final int Base_TextAppearance_AppCompat = 0x7f0f000e;
        public static final int Base_TextAppearance_AppCompat_Body1 = 0x7f0f000f;
        public static final int Base_TextAppearance_AppCompat_Body2 = 0x7f0f0010;
        public static final int Base_TextAppearance_AppCompat_Button = 0x7f0f0011;
        public static final int Base_TextAppearance_AppCompat_Caption = 0x7f0f0012;
        public static final int Base_TextAppearance_AppCompat_Display1 = 0x7f0f0013;
        public static final int Base_TextAppearance_AppCompat_Display2 = 0x7f0f0014;
        public static final int Base_TextAppearance_AppCompat_Display3 = 0x7f0f0015;
        public static final int Base_TextAppearance_AppCompat_Display4 = 0x7f0f0016;
        public static final int Base_TextAppearance_AppCompat_Headline = 0x7f0f0017;
        public static final int Base_TextAppearance_AppCompat_Inverse = 0x7f0f0018;
        public static final int Base_TextAppearance_AppCompat_Large = 0x7f0f0019;
        public static final int Base_TextAppearance_AppCompat_Large_Inverse = 0x7f0f001a;
        public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f0f001b;
        public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f0f001c;
        public static final int Base_TextAppearance_AppCompat_Medium = 0x7f0f001d;
        public static final int Base_TextAppearance_AppCompat_Medium_Inverse = 0x7f0f001e;
        public static final int Base_TextAppearance_AppCompat_Menu = 0x7f0f001f;
        public static final int Base_TextAppearance_AppCompat_SearchResult = 0x7f0f0020;
        public static final int Base_TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f0f0021;
        public static final int Base_TextAppearance_AppCompat_SearchResult_Title = 0x7f0f0022;
        public static final int Base_TextAppearance_AppCompat_Small = 0x7f0f0023;
        public static final int Base_TextAppearance_AppCompat_Small_Inverse = 0x7f0f0024;
        public static final int Base_TextAppearance_AppCompat_Subhead = 0x7f0f0025;
        public static final int Base_TextAppearance_AppCompat_Subhead_Inverse = 0x7f0f0026;
        public static final int Base_TextAppearance_AppCompat_Title = 0x7f0f0027;
        public static final int Base_TextAppearance_AppCompat_Title_Inverse = 0x7f0f0028;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f0f002a;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f0f002b;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f0f002c;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f0f002d;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f0f002e;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f0f002f;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f0f0030;
        public static final int Base_TextAppearance_AppCompat_Widget_Button = 0x7f0f0031;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f0f0032;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Colored = 0x7f0f0033;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f0f0034;
        public static final int Base_TextAppearance_AppCompat_Widget_DropDownItem = 0x7f0f0035;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f0f0036;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f0f0037;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f0f0038;
        public static final int Base_TextAppearance_AppCompat_Widget_Switch = 0x7f0f0039;
        public static final int Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f0f003a;
        public static final int Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f0f003b;
        public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f0f003c;
        public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f0f003d;
        public static final int Base_ThemeOverlay_AppCompat = 0x7f0f004c;
        public static final int Base_ThemeOverlay_AppCompat_ActionBar = 0x7f0f004d;
        public static final int Base_ThemeOverlay_AppCompat_Dark = 0x7f0f004e;
        public static final int Base_ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f0f004f;
        public static final int Base_ThemeOverlay_AppCompat_Dialog = 0x7f0f0050;
        public static final int Base_ThemeOverlay_AppCompat_Dialog_Alert = 0x7f0f0051;
        public static final int Base_ThemeOverlay_AppCompat_Light = 0x7f0f0052;
        public static final int Base_Theme_AppCompat = 0x7f0f003e;
        public static final int Base_Theme_AppCompat_CompactMenu = 0x7f0f003f;
        public static final int Base_Theme_AppCompat_Dialog = 0x7f0f0040;
        public static final int Base_Theme_AppCompat_DialogWhenLarge = 0x7f0f0044;
        public static final int Base_Theme_AppCompat_Dialog_Alert = 0x7f0f0041;
        public static final int Base_Theme_AppCompat_Dialog_FixedSize = 0x7f0f0042;
        public static final int Base_Theme_AppCompat_Dialog_MinWidth = 0x7f0f0043;
        public static final int Base_Theme_AppCompat_Light = 0x7f0f0045;
        public static final int Base_Theme_AppCompat_Light_DarkActionBar = 0x7f0f0046;
        public static final int Base_Theme_AppCompat_Light_Dialog = 0x7f0f0047;
        public static final int Base_Theme_AppCompat_Light_DialogWhenLarge = 0x7f0f004b;
        public static final int Base_Theme_AppCompat_Light_Dialog_Alert = 0x7f0f0048;
        public static final int Base_Theme_AppCompat_Light_Dialog_FixedSize = 0x7f0f0049;
        public static final int Base_Theme_AppCompat_Light_Dialog_MinWidth = 0x7f0f004a;
        public static final int Base_V21_ThemeOverlay_AppCompat_Dialog = 0x7f0f0057;
        public static final int Base_V21_Theme_AppCompat = 0x7f0f0053;
        public static final int Base_V21_Theme_AppCompat_Dialog = 0x7f0f0054;
        public static final int Base_V21_Theme_AppCompat_Light = 0x7f0f0055;
        public static final int Base_V21_Theme_AppCompat_Light_Dialog = 0x7f0f0056;
        public static final int Base_V22_Theme_AppCompat = 0x7f0f0058;
        public static final int Base_V22_Theme_AppCompat_Light = 0x7f0f0059;
        public static final int Base_V23_Theme_AppCompat = 0x7f0f005a;
        public static final int Base_V23_Theme_AppCompat_Light = 0x7f0f005b;
        public static final int Base_V7_ThemeOverlay_AppCompat_Dialog = 0x7f0f0065;
        public static final int Base_V7_Theme_AppCompat = 0x7f0f0061;
        public static final int Base_V7_Theme_AppCompat_Dialog = 0x7f0f0062;
        public static final int Base_V7_Theme_AppCompat_Light = 0x7f0f0063;
        public static final int Base_V7_Theme_AppCompat_Light_Dialog = 0x7f0f0064;
        public static final int Base_V7_Widget_AppCompat_AutoCompleteTextView = 0x7f0f0066;
        public static final int Base_V7_Widget_AppCompat_EditText = 0x7f0f0067;
        public static final int Base_Widget_AppCompat_ActionBar = 0x7f0f0069;
        public static final int Base_Widget_AppCompat_ActionBar_Solid = 0x7f0f006a;
        public static final int Base_Widget_AppCompat_ActionBar_TabBar = 0x7f0f006b;
        public static final int Base_Widget_AppCompat_ActionBar_TabText = 0x7f0f006c;
        public static final int Base_Widget_AppCompat_ActionBar_TabView = 0x7f0f006d;
        public static final int Base_Widget_AppCompat_ActionButton = 0x7f0f006e;
        public static final int Base_Widget_AppCompat_ActionButton_CloseMode = 0x7f0f006f;
        public static final int Base_Widget_AppCompat_ActionButton_Overflow = 0x7f0f0070;
        public static final int Base_Widget_AppCompat_ActionMode = 0x7f0f0071;
        public static final int Base_Widget_AppCompat_ActivityChooserView = 0x7f0f0072;
        public static final int Base_Widget_AppCompat_AutoCompleteTextView = 0x7f0f0073;
        public static final int Base_Widget_AppCompat_Button = 0x7f0f0074;
        public static final int Base_Widget_AppCompat_ButtonBar = 0x7f0f007a;
        public static final int Base_Widget_AppCompat_ButtonBar_AlertDialog = 0x7f0f007b;
        public static final int Base_Widget_AppCompat_Button_Borderless = 0x7f0f0075;
        public static final int Base_Widget_AppCompat_Button_Borderless_Colored = 0x7f0f0076;
        public static final int Base_Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f0f0077;
        public static final int Base_Widget_AppCompat_Button_Colored = 0x7f0f0078;
        public static final int Base_Widget_AppCompat_Button_Small = 0x7f0f0079;
        public static final int Base_Widget_AppCompat_CompoundButton_CheckBox = 0x7f0f007c;
        public static final int Base_Widget_AppCompat_CompoundButton_RadioButton = 0x7f0f007d;
        public static final int Base_Widget_AppCompat_CompoundButton_Switch = 0x7f0f007e;
        public static final int Base_Widget_AppCompat_DrawerArrowToggle = 0x7f0f007f;
        public static final int Base_Widget_AppCompat_DrawerArrowToggle_Common = 0x7f0f0080;
        public static final int Base_Widget_AppCompat_DropDownItem_Spinner = 0x7f0f0081;
        public static final int Base_Widget_AppCompat_EditText = 0x7f0f0082;
        public static final int Base_Widget_AppCompat_ImageButton = 0x7f0f0083;
        public static final int Base_Widget_AppCompat_Light_ActionBar = 0x7f0f0084;
        public static final int Base_Widget_AppCompat_Light_ActionBar_Solid = 0x7f0f0085;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabBar = 0x7f0f0086;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabText = 0x7f0f0087;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f0f0088;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabView = 0x7f0f0089;
        public static final int Base_Widget_AppCompat_Light_PopupMenu = 0x7f0f008a;
        public static final int Base_Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f0f008b;
        public static final int Base_Widget_AppCompat_ListMenuView = 0x7f0f008c;
        public static final int Base_Widget_AppCompat_ListPopupWindow = 0x7f0f008d;
        public static final int Base_Widget_AppCompat_ListView = 0x7f0f008e;
        public static final int Base_Widget_AppCompat_ListView_DropDown = 0x7f0f008f;
        public static final int Base_Widget_AppCompat_ListView_Menu = 0x7f0f0090;
        public static final int Base_Widget_AppCompat_PopupMenu = 0x7f0f0091;
        public static final int Base_Widget_AppCompat_PopupMenu_Overflow = 0x7f0f0092;
        public static final int Base_Widget_AppCompat_PopupWindow = 0x7f0f0093;
        public static final int Base_Widget_AppCompat_ProgressBar = 0x7f0f0094;
        public static final int Base_Widget_AppCompat_ProgressBar_Horizontal = 0x7f0f0095;
        public static final int Base_Widget_AppCompat_RatingBar = 0x7f0f0096;
        public static final int Base_Widget_AppCompat_RatingBar_Indicator = 0x7f0f0097;
        public static final int Base_Widget_AppCompat_RatingBar_Small = 0x7f0f0098;
        public static final int Base_Widget_AppCompat_SearchView = 0x7f0f0099;
        public static final int Base_Widget_AppCompat_SearchView_ActionBar = 0x7f0f009a;
        public static final int Base_Widget_AppCompat_SeekBar = 0x7f0f009b;
        public static final int Base_Widget_AppCompat_SeekBar_Discrete = 0x7f0f009c;
        public static final int Base_Widget_AppCompat_Spinner = 0x7f0f009d;
        public static final int Base_Widget_AppCompat_Spinner_Underlined = 0x7f0f009e;
        public static final int Base_Widget_AppCompat_TextView_SpinnerItem = 0x7f0f00a0;
        public static final int Base_Widget_AppCompat_Toolbar = 0x7f0f00a1;
        public static final int Base_Widget_AppCompat_Toolbar_Button_Navigation = 0x7f0f00a2;
        public static final int Platform_AppCompat = 0x7f0f00a7;
        public static final int Platform_AppCompat_Light = 0x7f0f00a8;
        public static final int Platform_ThemeOverlay_AppCompat = 0x7f0f00a9;
        public static final int Platform_ThemeOverlay_AppCompat_Dark = 0x7f0f00aa;
        public static final int Platform_ThemeOverlay_AppCompat_Light = 0x7f0f00ab;
        public static final int Platform_V21_AppCompat = 0x7f0f00ac;
        public static final int Platform_V21_AppCompat_Light = 0x7f0f00ad;
        public static final int Platform_V25_AppCompat = 0x7f0f00ae;
        public static final int Platform_V25_AppCompat_Light = 0x7f0f00af;
        public static final int Platform_Widget_AppCompat_Spinner = 0x7f0f00b0;
        public static final int RtlOverlay_DialogWindowTitle_AppCompat = 0x7f0f00b1;
        public static final int RtlOverlay_Widget_AppCompat_ActionBar_TitleItem = 0x7f0f00b2;
        public static final int RtlOverlay_Widget_AppCompat_DialogTitle_Icon = 0x7f0f00b3;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem = 0x7f0f00b4;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup = 0x7f0f00b5;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Text = 0x7f0f00b8;
        public static final int RtlOverlay_Widget_AppCompat_SearchView_MagIcon = 0x7f0f00bf;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown = 0x7f0f00ba;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 = 0x7f0f00bb;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 = 0x7f0f00bc;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Query = 0x7f0f00bd;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Text = 0x7f0f00be;
        public static final int RtlUnderlay_Widget_AppCompat_ActionButton = 0x7f0f00c0;
        public static final int RtlUnderlay_Widget_AppCompat_ActionButton_Overflow = 0x7f0f00c1;
        public static final int TextAppearance_AppCompat = 0x7f0f00c4;
        public static final int TextAppearance_AppCompat_Body1 = 0x7f0f00c5;
        public static final int TextAppearance_AppCompat_Body2 = 0x7f0f00c6;
        public static final int TextAppearance_AppCompat_Button = 0x7f0f00c7;
        public static final int TextAppearance_AppCompat_Caption = 0x7f0f00c8;
        public static final int TextAppearance_AppCompat_Display1 = 0x7f0f00c9;
        public static final int TextAppearance_AppCompat_Display2 = 0x7f0f00ca;
        public static final int TextAppearance_AppCompat_Display3 = 0x7f0f00cb;
        public static final int TextAppearance_AppCompat_Display4 = 0x7f0f00cc;
        public static final int TextAppearance_AppCompat_Headline = 0x7f0f00cd;
        public static final int TextAppearance_AppCompat_Inverse = 0x7f0f00ce;
        public static final int TextAppearance_AppCompat_Large = 0x7f0f00cf;
        public static final int TextAppearance_AppCompat_Large_Inverse = 0x7f0f00d0;
        public static final int TextAppearance_AppCompat_Light_SearchResult_Subtitle = 0x7f0f00d1;
        public static final int TextAppearance_AppCompat_Light_SearchResult_Title = 0x7f0f00d2;
        public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f0f00d3;
        public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f0f00d4;
        public static final int TextAppearance_AppCompat_Medium = 0x7f0f00d5;
        public static final int TextAppearance_AppCompat_Medium_Inverse = 0x7f0f00d6;
        public static final int TextAppearance_AppCompat_Menu = 0x7f0f00d7;
        public static final int TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f0f00d8;
        public static final int TextAppearance_AppCompat_SearchResult_Title = 0x7f0f00d9;
        public static final int TextAppearance_AppCompat_Small = 0x7f0f00da;
        public static final int TextAppearance_AppCompat_Small_Inverse = 0x7f0f00db;
        public static final int TextAppearance_AppCompat_Subhead = 0x7f0f00dc;
        public static final int TextAppearance_AppCompat_Subhead_Inverse = 0x7f0f00dd;
        public static final int TextAppearance_AppCompat_Title = 0x7f0f00de;
        public static final int TextAppearance_AppCompat_Title_Inverse = 0x7f0f00df;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f0f00e1;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f0f00e2;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f0f00e3;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f0f00e4;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f0f00e5;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f0f00e6;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse = 0x7f0f00e7;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f0f00e8;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse = 0x7f0f00e9;
        public static final int TextAppearance_AppCompat_Widget_Button = 0x7f0f00ea;
        public static final int TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f0f00eb;
        public static final int TextAppearance_AppCompat_Widget_Button_Colored = 0x7f0f00ec;
        public static final int TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f0f00ed;
        public static final int TextAppearance_AppCompat_Widget_DropDownItem = 0x7f0f00ee;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f0f00ef;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f0f00f0;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f0f00f1;
        public static final int TextAppearance_AppCompat_Widget_Switch = 0x7f0f00f2;
        public static final int TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f0f00f3;
        public static final int TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f0f00f9;
        public static final int TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f0f00fa;
        public static final int TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f0f00fb;
        public static final int ThemeOverlay_AppCompat = 0x7f0f0112;
        public static final int ThemeOverlay_AppCompat_ActionBar = 0x7f0f0113;
        public static final int ThemeOverlay_AppCompat_Dark = 0x7f0f0114;
        public static final int ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f0f0115;
        public static final int ThemeOverlay_AppCompat_Dialog = 0x7f0f0118;
        public static final int ThemeOverlay_AppCompat_Dialog_Alert = 0x7f0f0119;
        public static final int ThemeOverlay_AppCompat_Light = 0x7f0f011a;
        public static final int Theme_AppCompat = 0x7f0f00fc;
        public static final int Theme_AppCompat_CompactMenu = 0x7f0f00fd;
        public static final int Theme_AppCompat_DayNight = 0x7f0f00fe;
        public static final int Theme_AppCompat_DayNight_DarkActionBar = 0x7f0f00ff;
        public static final int Theme_AppCompat_DayNight_Dialog = 0x7f0f0100;
        public static final int Theme_AppCompat_DayNight_DialogWhenLarge = 0x7f0f0103;
        public static final int Theme_AppCompat_DayNight_Dialog_Alert = 0x7f0f0101;
        public static final int Theme_AppCompat_DayNight_Dialog_MinWidth = 0x7f0f0102;
        public static final int Theme_AppCompat_DayNight_NoActionBar = 0x7f0f0104;
        public static final int Theme_AppCompat_Dialog = 0x7f0f0105;
        public static final int Theme_AppCompat_DialogWhenLarge = 0x7f0f0108;
        public static final int Theme_AppCompat_Dialog_Alert = 0x7f0f0106;
        public static final int Theme_AppCompat_Dialog_MinWidth = 0x7f0f0107;
        public static final int Theme_AppCompat_Light = 0x7f0f0109;
        public static final int Theme_AppCompat_Light_DarkActionBar = 0x7f0f010a;
        public static final int Theme_AppCompat_Light_Dialog = 0x7f0f010b;
        public static final int Theme_AppCompat_Light_DialogWhenLarge = 0x7f0f010e;
        public static final int Theme_AppCompat_Light_Dialog_Alert = 0x7f0f010c;
        public static final int Theme_AppCompat_Light_Dialog_MinWidth = 0x7f0f010d;
        public static final int Theme_AppCompat_Light_NoActionBar = 0x7f0f010f;
        public static final int Theme_AppCompat_NoActionBar = 0x7f0f0110;
        public static final int Widget_AppCompat_ActionBar = 0x7f0f011b;
        public static final int Widget_AppCompat_ActionBar_Solid = 0x7f0f011c;
        public static final int Widget_AppCompat_ActionBar_TabBar = 0x7f0f011d;
        public static final int Widget_AppCompat_ActionBar_TabText = 0x7f0f011e;
        public static final int Widget_AppCompat_ActionBar_TabView = 0x7f0f011f;
        public static final int Widget_AppCompat_ActionButton = 0x7f0f0120;
        public static final int Widget_AppCompat_ActionButton_CloseMode = 0x7f0f0121;
        public static final int Widget_AppCompat_ActionButton_Overflow = 0x7f0f0122;
        public static final int Widget_AppCompat_ActionMode = 0x7f0f0123;
        public static final int Widget_AppCompat_ActivityChooserView = 0x7f0f0124;
        public static final int Widget_AppCompat_AutoCompleteTextView = 0x7f0f0125;
        public static final int Widget_AppCompat_Button = 0x7f0f0126;
        public static final int Widget_AppCompat_ButtonBar = 0x7f0f012c;
        public static final int Widget_AppCompat_ButtonBar_AlertDialog = 0x7f0f012d;
        public static final int Widget_AppCompat_Button_Borderless = 0x7f0f0127;
        public static final int Widget_AppCompat_Button_Borderless_Colored = 0x7f0f0128;
        public static final int Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f0f0129;
        public static final int Widget_AppCompat_Button_Colored = 0x7f0f012a;
        public static final int Widget_AppCompat_Button_Small = 0x7f0f012b;
        public static final int Widget_AppCompat_CompoundButton_CheckBox = 0x7f0f012e;
        public static final int Widget_AppCompat_CompoundButton_RadioButton = 0x7f0f012f;
        public static final int Widget_AppCompat_CompoundButton_Switch = 0x7f0f0130;
        public static final int Widget_AppCompat_DrawerArrowToggle = 0x7f0f0131;
        public static final int Widget_AppCompat_DropDownItem_Spinner = 0x7f0f0132;
        public static final int Widget_AppCompat_EditText = 0x7f0f0133;
        public static final int Widget_AppCompat_ImageButton = 0x7f0f0134;
        public static final int Widget_AppCompat_Light_ActionBar = 0x7f0f0135;
        public static final int Widget_AppCompat_Light_ActionBar_Solid = 0x7f0f0136;
        public static final int Widget_AppCompat_Light_ActionBar_Solid_Inverse = 0x7f0f0137;
        public static final int Widget_AppCompat_Light_ActionBar_TabBar = 0x7f0f0138;
        public static final int Widget_AppCompat_Light_ActionBar_TabBar_Inverse = 0x7f0f0139;
        public static final int Widget_AppCompat_Light_ActionBar_TabText = 0x7f0f013a;
        public static final int Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f0f013b;
        public static final int Widget_AppCompat_Light_ActionBar_TabView = 0x7f0f013c;
        public static final int Widget_AppCompat_Light_ActionBar_TabView_Inverse = 0x7f0f013d;
        public static final int Widget_AppCompat_Light_ActionButton = 0x7f0f013e;
        public static final int Widget_AppCompat_Light_ActionButton_CloseMode = 0x7f0f013f;
        public static final int Widget_AppCompat_Light_ActionButton_Overflow = 0x7f0f0140;
        public static final int Widget_AppCompat_Light_ActionMode_Inverse = 0x7f0f0141;
        public static final int Widget_AppCompat_Light_ActivityChooserView = 0x7f0f0142;
        public static final int Widget_AppCompat_Light_AutoCompleteTextView = 0x7f0f0143;
        public static final int Widget_AppCompat_Light_DropDownItem_Spinner = 0x7f0f0144;
        public static final int Widget_AppCompat_Light_ListPopupWindow = 0x7f0f0145;
        public static final int Widget_AppCompat_Light_ListView_DropDown = 0x7f0f0146;
        public static final int Widget_AppCompat_Light_PopupMenu = 0x7f0f0147;
        public static final int Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f0f0148;
        public static final int Widget_AppCompat_Light_SearchView = 0x7f0f0149;
        public static final int Widget_AppCompat_Light_Spinner_DropDown_ActionBar = 0x7f0f014a;
        public static final int Widget_AppCompat_ListMenuView = 0x7f0f014b;
        public static final int Widget_AppCompat_ListPopupWindow = 0x7f0f014c;
        public static final int Widget_AppCompat_ListView = 0x7f0f014d;
        public static final int Widget_AppCompat_ListView_DropDown = 0x7f0f014e;
        public static final int Widget_AppCompat_ListView_Menu = 0x7f0f014f;
        public static final int Widget_AppCompat_PopupMenu = 0x7f0f0150;
        public static final int Widget_AppCompat_PopupMenu_Overflow = 0x7f0f0151;
        public static final int Widget_AppCompat_PopupWindow = 0x7f0f0152;
        public static final int Widget_AppCompat_ProgressBar = 0x7f0f0153;
        public static final int Widget_AppCompat_ProgressBar_Horizontal = 0x7f0f0154;
        public static final int Widget_AppCompat_RatingBar = 0x7f0f0155;
        public static final int Widget_AppCompat_RatingBar_Indicator = 0x7f0f0156;
        public static final int Widget_AppCompat_RatingBar_Small = 0x7f0f0157;
        public static final int Widget_AppCompat_SearchView = 0x7f0f0158;
        public static final int Widget_AppCompat_SearchView_ActionBar = 0x7f0f0159;
        public static final int Widget_AppCompat_SeekBar = 0x7f0f015a;
        public static final int Widget_AppCompat_SeekBar_Discrete = 0x7f0f015b;
        public static final int Widget_AppCompat_Spinner = 0x7f0f015c;
        public static final int Widget_AppCompat_Spinner_DropDown = 0x7f0f015d;
        public static final int Widget_AppCompat_Spinner_DropDown_ActionBar = 0x7f0f015e;
        public static final int Widget_AppCompat_Spinner_Underlined = 0x7f0f015f;
        public static final int Widget_AppCompat_TextView_SpinnerItem = 0x7f0f0161;
        public static final int Widget_AppCompat_Toolbar = 0x7f0f0162;
        public static final int Widget_AppCompat_Toolbar_Button_Navigation = 0x7f0f0163;
    }

    /* loaded from: classes.dex */
    public static final class styleable {
        public static final int ActionBarLayout_android_layout_gravity = 0x00000000;
        public static final int ActionBar_background = 0x00000000;
        public static final int ActionBar_backgroundSplit = 0x00000001;
        public static final int ActionBar_backgroundStacked = 0x00000002;
        public static final int ActionBar_contentInsetEnd = 0x00000003;
        public static final int ActionBar_contentInsetEndWithActions = 0x00000004;
        public static final int ActionBar_contentInsetLeft = 0x00000005;
        public static final int ActionBar_contentInsetRight = 0x00000006;
        public static final int ActionBar_contentInsetStart = 0x00000007;
        public static final int ActionBar_contentInsetStartWithNavigation = 0x00000008;
        public static final int ActionBar_customNavigationLayout = 0x00000009;
        public static final int ActionBar_displayOptions = 0x0000000a;
        public static final int ActionBar_divider = 0x0000000b;
        public static final int ActionBar_elevation = 0x0000000c;
        public static final int ActionBar_height = 0x0000000d;
        public static final int ActionBar_hideOnContentScroll = 0x0000000e;
        public static final int ActionBar_homeAsUpIndicator = 0x0000000f;
        public static final int ActionBar_homeLayout = 0x00000010;
        public static final int ActionBar_icon = 0x00000011;
        public static final int ActionBar_indeterminateProgressStyle = 0x00000012;
        public static final int ActionBar_itemPadding = 0x00000013;
        public static final int ActionBar_logo = 0x00000014;
        public static final int ActionBar_navigationMode = 0x00000015;
        public static final int ActionBar_popupTheme = 0x00000016;
        public static final int ActionBar_progressBarPadding = 0x00000017;
        public static final int ActionBar_progressBarStyle = 0x00000018;
        public static final int ActionBar_subtitle = 0x00000019;
        public static final int ActionBar_subtitleTextStyle = 0x0000001a;
        public static final int ActionBar_title = 0x0000001b;
        public static final int ActionBar_titleTextStyle = 0x0000001c;
        public static final int ActionMenuItemView_android_minWidth = 0x00000000;
        public static final int ActionMode_background = 0x00000000;
        public static final int ActionMode_backgroundSplit = 0x00000001;
        public static final int ActionMode_closeItemLayout = 0x00000002;
        public static final int ActionMode_height = 0x00000003;
        public static final int ActionMode_subtitleTextStyle = 0x00000004;
        public static final int ActionMode_titleTextStyle = 0x00000005;
        public static final int ActivityChooserView_expandActivityOverflowButtonDrawable = 0x00000000;
        public static final int ActivityChooserView_initialActivityCount = 0x00000001;
        public static final int AlertDialog_android_layout = 0x00000000;
        public static final int AlertDialog_buttonIconDimen = 0x00000001;
        public static final int AlertDialog_buttonPanelSideLayout = 0x00000002;
        public static final int AlertDialog_listItemLayout = 0x00000003;
        public static final int AlertDialog_listLayout = 0x00000004;
        public static final int AlertDialog_multiChoiceItemLayout = 0x00000005;
        public static final int AlertDialog_showTitle = 0x00000006;
        public static final int AlertDialog_singleChoiceItemLayout = 0x00000007;
        public static final int AppCompatImageView_android_src = 0x00000000;
        public static final int AppCompatImageView_srcCompat = 0x00000001;
        public static final int AppCompatImageView_tint = 0x00000002;
        public static final int AppCompatImageView_tintMode = 0x00000003;
        public static final int AppCompatSeekBar_android_thumb = 0x00000000;
        public static final int AppCompatSeekBar_tickMark = 0x00000001;
        public static final int AppCompatSeekBar_tickMarkTint = 0x00000002;
        public static final int AppCompatSeekBar_tickMarkTintMode = 0x00000003;
        public static final int AppCompatTextHelper_android_drawableBottom = 0x00000002;
        public static final int AppCompatTextHelper_android_drawableEnd = 0x00000006;
        public static final int AppCompatTextHelper_android_drawableLeft = 0x00000003;
        public static final int AppCompatTextHelper_android_drawableRight = 0x00000004;
        public static final int AppCompatTextHelper_android_drawableStart = 0x00000005;
        public static final int AppCompatTextHelper_android_drawableTop = 0x00000001;
        public static final int AppCompatTextHelper_android_textAppearance = 0x00000000;
        public static final int AppCompatTextView_android_textAppearance = 0x00000000;
        public static final int AppCompatTextView_autoSizeMaxTextSize = 0x00000001;
        public static final int AppCompatTextView_autoSizeMinTextSize = 0x00000002;
        public static final int AppCompatTextView_autoSizePresetSizes = 0x00000003;
        public static final int AppCompatTextView_autoSizeStepGranularity = 0x00000004;
        public static final int AppCompatTextView_autoSizeTextType = 0x00000005;
        public static final int AppCompatTextView_drawableBottomCompat = 0x00000006;
        public static final int AppCompatTextView_drawableEndCompat = 0x00000007;
        public static final int AppCompatTextView_drawableLeftCompat = 0x00000008;
        public static final int AppCompatTextView_drawableRightCompat = 0x00000009;
        public static final int AppCompatTextView_drawableStartCompat = 0x0000000a;
        public static final int AppCompatTextView_drawableTint = 0x0000000b;
        public static final int AppCompatTextView_drawableTintMode = 0x0000000c;
        public static final int AppCompatTextView_drawableTopCompat = 0x0000000d;
        public static final int AppCompatTextView_firstBaselineToTopHeight = 0x0000000e;
        public static final int AppCompatTextView_fontFamily = 0x0000000f;
        public static final int AppCompatTextView_fontVariationSettings = 0x00000010;
        public static final int AppCompatTextView_lastBaselineToBottomHeight = 0x00000011;
        public static final int AppCompatTextView_lineHeight = 0x00000012;
        public static final int AppCompatTextView_textAllCaps = 0x00000013;
        public static final int AppCompatTextView_textLocale = 0x00000014;
        public static final int AppCompatTheme_actionBarDivider = 0x00000002;
        public static final int AppCompatTheme_actionBarItemBackground = 0x00000003;
        public static final int AppCompatTheme_actionBarPopupTheme = 0x00000004;
        public static final int AppCompatTheme_actionBarSize = 0x00000005;
        public static final int AppCompatTheme_actionBarSplitStyle = 0x00000006;
        public static final int AppCompatTheme_actionBarStyle = 0x00000007;
        public static final int AppCompatTheme_actionBarTabBarStyle = 0x00000008;
        public static final int AppCompatTheme_actionBarTabStyle = 0x00000009;
        public static final int AppCompatTheme_actionBarTabTextStyle = 0x0000000a;
        public static final int AppCompatTheme_actionBarTheme = 0x0000000b;
        public static final int AppCompatTheme_actionBarWidgetTheme = 0x0000000c;
        public static final int AppCompatTheme_actionButtonStyle = 0x0000000d;
        public static final int AppCompatTheme_actionDropDownStyle = 0x0000000e;
        public static final int AppCompatTheme_actionMenuTextAppearance = 0x0000000f;
        public static final int AppCompatTheme_actionMenuTextColor = 0x00000010;
        public static final int AppCompatTheme_actionModeBackground = 0x00000011;
        public static final int AppCompatTheme_actionModeCloseButtonStyle = 0x00000012;
        public static final int AppCompatTheme_actionModeCloseDrawable = 0x00000013;
        public static final int AppCompatTheme_actionModeCopyDrawable = 0x00000014;
        public static final int AppCompatTheme_actionModeCutDrawable = 0x00000015;
        public static final int AppCompatTheme_actionModeFindDrawable = 0x00000016;
        public static final int AppCompatTheme_actionModePasteDrawable = 0x00000017;
        public static final int AppCompatTheme_actionModePopupWindowStyle = 0x00000018;
        public static final int AppCompatTheme_actionModeSelectAllDrawable = 0x00000019;
        public static final int AppCompatTheme_actionModeShareDrawable = 0x0000001a;
        public static final int AppCompatTheme_actionModeSplitBackground = 0x0000001b;
        public static final int AppCompatTheme_actionModeStyle = 0x0000001c;
        public static final int AppCompatTheme_actionModeWebSearchDrawable = 0x0000001d;
        public static final int AppCompatTheme_actionOverflowButtonStyle = 0x0000001e;
        public static final int AppCompatTheme_actionOverflowMenuStyle = 0x0000001f;
        public static final int AppCompatTheme_activityChooserViewStyle = 0x00000020;
        public static final int AppCompatTheme_alertDialogButtonGroupStyle = 0x00000021;
        public static final int AppCompatTheme_alertDialogCenterButtons = 0x00000022;
        public static final int AppCompatTheme_alertDialogStyle = 0x00000023;
        public static final int AppCompatTheme_alertDialogTheme = 0x00000024;
        public static final int AppCompatTheme_android_windowAnimationStyle = 0x00000001;
        public static final int AppCompatTheme_android_windowIsFloating = 0x00000000;
        public static final int AppCompatTheme_autoCompleteTextViewStyle = 0x00000025;
        public static final int AppCompatTheme_borderlessButtonStyle = 0x00000026;
        public static final int AppCompatTheme_buttonBarButtonStyle = 0x00000027;
        public static final int AppCompatTheme_buttonBarNegativeButtonStyle = 0x00000028;
        public static final int AppCompatTheme_buttonBarNeutralButtonStyle = 0x00000029;
        public static final int AppCompatTheme_buttonBarPositiveButtonStyle = 0x0000002a;
        public static final int AppCompatTheme_buttonBarStyle = 0x0000002b;
        public static final int AppCompatTheme_buttonStyle = 0x0000002c;
        public static final int AppCompatTheme_buttonStyleSmall = 0x0000002d;
        public static final int AppCompatTheme_checkboxStyle = 0x0000002e;
        public static final int AppCompatTheme_checkedTextViewStyle = 0x0000002f;
        public static final int AppCompatTheme_colorAccent = 0x00000030;
        public static final int AppCompatTheme_colorBackgroundFloating = 0x00000031;
        public static final int AppCompatTheme_colorButtonNormal = 0x00000032;
        public static final int AppCompatTheme_colorControlActivated = 0x00000033;
        public static final int AppCompatTheme_colorControlHighlight = 0x00000034;
        public static final int AppCompatTheme_colorControlNormal = 0x00000035;
        public static final int AppCompatTheme_colorError = 0x00000036;
        public static final int AppCompatTheme_colorPrimary = 0x00000037;
        public static final int AppCompatTheme_colorPrimaryDark = 0x00000038;
        public static final int AppCompatTheme_colorSwitchThumbNormal = 0x00000039;
        public static final int AppCompatTheme_controlBackground = 0x0000003a;
        public static final int AppCompatTheme_dialogCornerRadius = 0x0000003b;
        public static final int AppCompatTheme_dialogPreferredPadding = 0x0000003c;
        public static final int AppCompatTheme_dialogTheme = 0x0000003d;
        public static final int AppCompatTheme_dividerHorizontal = 0x0000003e;
        public static final int AppCompatTheme_dividerVertical = 0x0000003f;
        public static final int AppCompatTheme_dropDownListViewStyle = 0x00000040;
        public static final int AppCompatTheme_dropdownListPreferredItemHeight = 0x00000041;
        public static final int AppCompatTheme_editTextBackground = 0x00000042;
        public static final int AppCompatTheme_editTextColor = 0x00000043;
        public static final int AppCompatTheme_editTextStyle = 0x00000044;
        public static final int AppCompatTheme_homeAsUpIndicator = 0x00000045;
        public static final int AppCompatTheme_imageButtonStyle = 0x00000046;
        public static final int AppCompatTheme_listChoiceBackgroundIndicator = 0x00000047;
        public static final int AppCompatTheme_listChoiceIndicatorMultipleAnimated = 0x00000048;
        public static final int AppCompatTheme_listChoiceIndicatorSingleAnimated = 0x00000049;
        public static final int AppCompatTheme_listDividerAlertDialog = 0x0000004a;
        public static final int AppCompatTheme_listMenuViewStyle = 0x0000004b;
        public static final int AppCompatTheme_listPopupWindowStyle = 0x0000004c;
        public static final int AppCompatTheme_listPreferredItemHeight = 0x0000004d;
        public static final int AppCompatTheme_listPreferredItemHeightLarge = 0x0000004e;
        public static final int AppCompatTheme_listPreferredItemHeightSmall = 0x0000004f;
        public static final int AppCompatTheme_listPreferredItemPaddingEnd = 0x00000050;
        public static final int AppCompatTheme_listPreferredItemPaddingLeft = 0x00000051;
        public static final int AppCompatTheme_listPreferredItemPaddingRight = 0x00000052;
        public static final int AppCompatTheme_listPreferredItemPaddingStart = 0x00000053;
        public static final int AppCompatTheme_panelBackground = 0x00000054;
        public static final int AppCompatTheme_panelMenuListTheme = 0x00000055;
        public static final int AppCompatTheme_panelMenuListWidth = 0x00000056;
        public static final int AppCompatTheme_popupMenuStyle = 0x00000057;
        public static final int AppCompatTheme_popupWindowStyle = 0x00000058;
        public static final int AppCompatTheme_radioButtonStyle = 0x00000059;
        public static final int AppCompatTheme_ratingBarStyle = 0x0000005a;
        public static final int AppCompatTheme_ratingBarStyleIndicator = 0x0000005b;
        public static final int AppCompatTheme_ratingBarStyleSmall = 0x0000005c;
        public static final int AppCompatTheme_searchViewStyle = 0x0000005d;
        public static final int AppCompatTheme_seekBarStyle = 0x0000005e;
        public static final int AppCompatTheme_selectableItemBackground = 0x0000005f;
        public static final int AppCompatTheme_selectableItemBackgroundBorderless = 0x00000060;
        public static final int AppCompatTheme_spinnerDropDownItemStyle = 0x00000061;
        public static final int AppCompatTheme_spinnerStyle = 0x00000062;
        public static final int AppCompatTheme_switchStyle = 0x00000063;
        public static final int AppCompatTheme_textAppearanceLargePopupMenu = 0x00000064;
        public static final int AppCompatTheme_textAppearanceListItem = 0x00000065;
        public static final int AppCompatTheme_textAppearanceListItemSecondary = 0x00000066;
        public static final int AppCompatTheme_textAppearanceListItemSmall = 0x00000067;
        public static final int AppCompatTheme_textAppearancePopupMenuHeader = 0x00000068;
        public static final int AppCompatTheme_textAppearanceSearchResultSubtitle = 0x00000069;
        public static final int AppCompatTheme_textAppearanceSearchResultTitle = 0x0000006a;
        public static final int AppCompatTheme_textAppearanceSmallPopupMenu = 0x0000006b;
        public static final int AppCompatTheme_textColorAlertDialogListItem = 0x0000006c;
        public static final int AppCompatTheme_textColorSearchUrl = 0x0000006d;
        public static final int AppCompatTheme_toolbarNavigationButtonStyle = 0x0000006e;
        public static final int AppCompatTheme_toolbarStyle = 0x0000006f;
        public static final int AppCompatTheme_tooltipForegroundColor = 0x00000070;
        public static final int AppCompatTheme_tooltipFrameBackground = 0x00000071;
        public static final int AppCompatTheme_viewInflaterClass = 0x00000072;
        public static final int AppCompatTheme_windowActionBar = 0x00000073;
        public static final int AppCompatTheme_windowActionBarOverlay = 0x00000074;
        public static final int AppCompatTheme_windowActionModeOverlay = 0x00000075;
        public static final int AppCompatTheme_windowFixedHeightMajor = 0x00000076;
        public static final int AppCompatTheme_windowFixedHeightMinor = 0x00000077;
        public static final int AppCompatTheme_windowFixedWidthMajor = 0x00000078;
        public static final int AppCompatTheme_windowFixedWidthMinor = 0x00000079;
        public static final int AppCompatTheme_windowMinWidthMajor = 0x0000007a;
        public static final int AppCompatTheme_windowMinWidthMinor = 0x0000007b;
        public static final int AppCompatTheme_windowNoTitle = 0x0000007c;
        public static final int ButtonBarLayout_allowStacking = 0x00000000;
        public static final int ColorStateListItem_alpha = 0x00000002;
        public static final int ColorStateListItem_android_alpha = 0x00000001;
        public static final int ColorStateListItem_android_color = 0x00000000;
        public static final int CompoundButton_android_button = 0x00000000;
        public static final int CompoundButton_buttonCompat = 0x00000001;
        public static final int CompoundButton_buttonTint = 0x00000002;
        public static final int CompoundButton_buttonTintMode = 0x00000003;
        public static final int DrawerArrowToggle_arrowHeadLength = 0x00000000;
        public static final int DrawerArrowToggle_arrowShaftLength = 0x00000001;
        public static final int DrawerArrowToggle_barLength = 0x00000002;
        public static final int DrawerArrowToggle_color = 0x00000003;
        public static final int DrawerArrowToggle_drawableSize = 0x00000004;
        public static final int DrawerArrowToggle_gapBetweenBars = 0x00000005;
        public static final int DrawerArrowToggle_spinBars = 0x00000006;
        public static final int DrawerArrowToggle_thickness = 0x00000007;
        public static final int LinearLayoutCompat_Layout_android_layout_gravity = 0x00000000;
        public static final int LinearLayoutCompat_Layout_android_layout_height = 0x00000002;
        public static final int LinearLayoutCompat_Layout_android_layout_weight = 0x00000003;
        public static final int LinearLayoutCompat_Layout_android_layout_width = 0x00000001;
        public static final int LinearLayoutCompat_android_baselineAligned = 0x00000002;
        public static final int LinearLayoutCompat_android_baselineAlignedChildIndex = 0x00000003;
        public static final int LinearLayoutCompat_android_gravity = 0x00000000;
        public static final int LinearLayoutCompat_android_orientation = 0x00000001;
        public static final int LinearLayoutCompat_android_weightSum = 0x00000004;
        public static final int LinearLayoutCompat_divider = 0x00000005;
        public static final int LinearLayoutCompat_dividerPadding = 0x00000006;
        public static final int LinearLayoutCompat_measureWithLargestChild = 0x00000007;
        public static final int LinearLayoutCompat_showDividers = 0x00000008;
        public static final int ListPopupWindow_android_dropDownHorizontalOffset = 0x00000000;
        public static final int ListPopupWindow_android_dropDownVerticalOffset = 0x00000001;
        public static final int MenuGroup_android_checkableBehavior = 0x00000005;
        public static final int MenuGroup_android_enabled = 0x00000000;
        public static final int MenuGroup_android_id = 0x00000001;
        public static final int MenuGroup_android_menuCategory = 0x00000003;
        public static final int MenuGroup_android_orderInCategory = 0x00000004;
        public static final int MenuGroup_android_visible = 0x00000002;
        public static final int MenuItem_actionLayout = 0x0000000d;
        public static final int MenuItem_actionProviderClass = 0x0000000e;
        public static final int MenuItem_actionViewClass = 0x0000000f;
        public static final int MenuItem_alphabeticModifiers = 0x00000010;
        public static final int MenuItem_android_alphabeticShortcut = 0x00000009;
        public static final int MenuItem_android_checkable = 0x0000000b;
        public static final int MenuItem_android_checked = 0x00000003;
        public static final int MenuItem_android_enabled = 0x00000001;
        public static final int MenuItem_android_icon = 0x00000000;
        public static final int MenuItem_android_id = 0x00000002;
        public static final int MenuItem_android_menuCategory = 0x00000005;
        public static final int MenuItem_android_numericShortcut = 0x0000000a;
        public static final int MenuItem_android_onClick = 0x0000000c;
        public static final int MenuItem_android_orderInCategory = 0x00000006;
        public static final int MenuItem_android_title = 0x00000007;
        public static final int MenuItem_android_titleCondensed = 0x00000008;
        public static final int MenuItem_android_visible = 0x00000004;
        public static final int MenuItem_contentDescription = 0x00000011;
        public static final int MenuItem_iconTint = 0x00000012;
        public static final int MenuItem_iconTintMode = 0x00000013;
        public static final int MenuItem_numericModifiers = 0x00000014;
        public static final int MenuItem_showAsAction = 0x00000015;
        public static final int MenuItem_tooltipText = 0x00000016;
        public static final int MenuView_android_headerBackground = 0x00000004;
        public static final int MenuView_android_horizontalDivider = 0x00000002;
        public static final int MenuView_android_itemBackground = 0x00000005;
        public static final int MenuView_android_itemIconDisabledAlpha = 0x00000006;
        public static final int MenuView_android_itemTextAppearance = 0x00000001;
        public static final int MenuView_android_verticalDivider = 0x00000003;
        public static final int MenuView_android_windowAnimationStyle = 0x00000000;
        public static final int MenuView_preserveIconSpacing = 0x00000007;
        public static final int MenuView_subMenuArrow = 0x00000008;
        public static final int PopupWindowBackgroundState_state_above_anchor = 0x00000000;
        public static final int PopupWindow_android_popupAnimationStyle = 0x00000001;
        public static final int PopupWindow_android_popupBackground = 0x00000000;
        public static final int PopupWindow_overlapAnchor = 0x00000002;
        public static final int RecycleListView_paddingBottomNoButtons = 0x00000000;
        public static final int RecycleListView_paddingTopNoTitle = 0x00000001;
        public static final int RecyclerView_android_clipToPadding = 0x00000001;
        public static final int RecyclerView_android_descendantFocusability = 0x00000002;
        public static final int RecyclerView_android_orientation = 0x00000000;
        public static final int RecyclerView_fastScrollEnabled = 0x00000003;
        public static final int RecyclerView_fastScrollHorizontalThumbDrawable = 0x00000004;
        public static final int RecyclerView_fastScrollHorizontalTrackDrawable = 0x00000005;
        public static final int RecyclerView_fastScrollVerticalThumbDrawable = 0x00000006;
        public static final int RecyclerView_fastScrollVerticalTrackDrawable = 0x00000007;
        public static final int RecyclerView_layoutManager = 0x00000008;
        public static final int RecyclerView_reverseLayout = 0x00000009;
        public static final int RecyclerView_spanCount = 0x0000000a;
        public static final int RecyclerView_stackFromEnd = 0x0000000b;
        public static final int SearchView_android_focusable = 0x00000000;
        public static final int SearchView_android_imeOptions = 0x00000003;
        public static final int SearchView_android_inputType = 0x00000002;
        public static final int SearchView_android_maxWidth = 0x00000001;
        public static final int SearchView_closeIcon = 0x00000004;
        public static final int SearchView_commitIcon = 0x00000005;
        public static final int SearchView_defaultQueryHint = 0x00000006;
        public static final int SearchView_goIcon = 0x00000007;
        public static final int SearchView_iconifiedByDefault = 0x00000008;
        public static final int SearchView_layout = 0x00000009;
        public static final int SearchView_queryBackground = 0x0000000a;
        public static final int SearchView_queryHint = 0x0000000b;
        public static final int SearchView_searchHintIcon = 0x0000000c;
        public static final int SearchView_searchIcon = 0x0000000d;
        public static final int SearchView_submitBackground = 0x0000000e;
        public static final int SearchView_suggestionRowLayout = 0x0000000f;
        public static final int SearchView_voiceIcon = 0x00000010;
        public static final int Spinner_android_dropDownWidth = 0x00000003;
        public static final int Spinner_android_entries = 0x00000000;
        public static final int Spinner_android_popupBackground = 0x00000001;
        public static final int Spinner_android_prompt = 0x00000002;
        public static final int Spinner_popupTheme = 0x00000004;
        public static final int SwitchCompat_android_textOff = 0x00000001;
        public static final int SwitchCompat_android_textOn = 0x00000000;
        public static final int SwitchCompat_android_thumb = 0x00000002;
        public static final int SwitchCompat_showText = 0x00000003;
        public static final int SwitchCompat_splitTrack = 0x00000004;
        public static final int SwitchCompat_switchMinWidth = 0x00000005;
        public static final int SwitchCompat_switchPadding = 0x00000006;
        public static final int SwitchCompat_switchTextAppearance = 0x00000007;
        public static final int SwitchCompat_thumbTextPadding = 0x00000008;
        public static final int SwitchCompat_thumbTint = 0x00000009;
        public static final int SwitchCompat_thumbTintMode = 0x0000000a;
        public static final int SwitchCompat_track = 0x0000000b;
        public static final int SwitchCompat_trackTint = 0x0000000c;
        public static final int SwitchCompat_trackTintMode = 0x0000000d;
        public static final int TextAppearance_android_fontFamily = 0x0000000a;
        public static final int TextAppearance_android_shadowColor = 0x00000006;
        public static final int TextAppearance_android_shadowDx = 0x00000007;
        public static final int TextAppearance_android_shadowDy = 0x00000008;
        public static final int TextAppearance_android_shadowRadius = 0x00000009;
        public static final int TextAppearance_android_textColor = 0x00000003;
        public static final int TextAppearance_android_textColorHint = 0x00000004;
        public static final int TextAppearance_android_textColorLink = 0x00000005;
        public static final int TextAppearance_android_textFontWeight = 0x0000000b;
        public static final int TextAppearance_android_textSize = 0x00000000;
        public static final int TextAppearance_android_textStyle = 0x00000002;
        public static final int TextAppearance_android_typeface = 0x00000001;
        public static final int TextAppearance_fontFamily = 0x0000000c;
        public static final int TextAppearance_fontVariationSettings = 0x0000000d;
        public static final int TextAppearance_textAllCaps = 0x0000000e;
        public static final int TextAppearance_textLocale = 0x0000000f;
        public static final int Toolbar_android_gravity = 0x00000000;
        public static final int Toolbar_android_minHeight = 0x00000001;
        public static final int Toolbar_buttonGravity = 0x00000002;
        public static final int Toolbar_collapseContentDescription = 0x00000003;
        public static final int Toolbar_collapseIcon = 0x00000004;
        public static final int Toolbar_contentInsetEnd = 0x00000005;
        public static final int Toolbar_contentInsetEndWithActions = 0x00000006;
        public static final int Toolbar_contentInsetLeft = 0x00000007;
        public static final int Toolbar_contentInsetRight = 0x00000008;
        public static final int Toolbar_contentInsetStart = 0x00000009;
        public static final int Toolbar_contentInsetStartWithNavigation = 0x0000000a;
        public static final int Toolbar_logo = 0x0000000b;
        public static final int Toolbar_logoDescription = 0x0000000c;
        public static final int Toolbar_maxButtonHeight = 0x0000000d;
        public static final int Toolbar_menu = 0x0000000e;
        public static final int Toolbar_navigationContentDescription = 0x0000000f;
        public static final int Toolbar_navigationIcon = 0x00000010;
        public static final int Toolbar_popupTheme = 0x00000011;
        public static final int Toolbar_subtitle = 0x00000012;
        public static final int Toolbar_subtitleTextAppearance = 0x00000013;
        public static final int Toolbar_subtitleTextColor = 0x00000014;
        public static final int Toolbar_title = 0x00000015;
        public static final int Toolbar_titleMargin = 0x00000016;
        public static final int Toolbar_titleMarginBottom = 0x00000017;
        public static final int Toolbar_titleMarginEnd = 0x00000018;
        public static final int Toolbar_titleMarginStart = 0x00000019;
        public static final int Toolbar_titleMarginTop = 0x0000001a;
        public static final int Toolbar_titleMargins = 0x0000001b;
        public static final int Toolbar_titleTextAppearance = 0x0000001c;
        public static final int Toolbar_titleTextColor = 0x0000001d;
        public static final int ViewBackgroundHelper_android_background = 0x00000000;
        public static final int ViewBackgroundHelper_backgroundTint = 0x00000001;
        public static final int ViewBackgroundHelper_backgroundTintMode = 0x00000002;
        public static final int ViewStubCompat_android_id = 0x00000000;
        public static final int ViewStubCompat_android_inflatedId = 0x00000002;
        public static final int ViewStubCompat_android_layout = 0x00000001;
        public static final int View_android_focusable = 0x00000001;
        public static final int View_android_theme = 0x00000000;
        public static final int View_paddingEnd = 0x00000002;
        public static final int View_paddingStart = 0x00000003;
        public static final int View_theme = 0x00000004;
        public static final int[] ActionBar = {com.ici.connectivity.R.attr.background, com.ici.connectivity.R.attr.backgroundSplit, com.ici.connectivity.R.attr.backgroundStacked, com.ici.connectivity.R.attr.contentInsetEnd, com.ici.connectivity.R.attr.contentInsetEndWithActions, com.ici.connectivity.R.attr.contentInsetLeft, com.ici.connectivity.R.attr.contentInsetRight, com.ici.connectivity.R.attr.contentInsetStart, com.ici.connectivity.R.attr.contentInsetStartWithNavigation, com.ici.connectivity.R.attr.customNavigationLayout, com.ici.connectivity.R.attr.displayOptions, com.ici.connectivity.R.attr.divider, com.ici.connectivity.R.attr.elevation, com.ici.connectivity.R.attr.height, com.ici.connectivity.R.attr.hideOnContentScroll, com.ici.connectivity.R.attr.homeAsUpIndicator, com.ici.connectivity.R.attr.homeLayout, com.ici.connectivity.R.attr.icon, com.ici.connectivity.R.attr.indeterminateProgressStyle, com.ici.connectivity.R.attr.itemPadding, com.ici.connectivity.R.attr.logo, com.ici.connectivity.R.attr.navigationMode, com.ici.connectivity.R.attr.popupTheme, com.ici.connectivity.R.attr.progressBarPadding, com.ici.connectivity.R.attr.progressBarStyle, com.ici.connectivity.R.attr.subtitle, com.ici.connectivity.R.attr.subtitleTextStyle, com.ici.connectivity.R.attr.title, com.ici.connectivity.R.attr.titleTextStyle};
        public static final int[] ActionBarLayout = {16842931};
        public static final int[] ActionMenuItemView = {16843071};
        public static final int[] ActionMenuView = new int[0];
        public static final int[] ActionMode = {com.ici.connectivity.R.attr.background, com.ici.connectivity.R.attr.backgroundSplit, com.ici.connectivity.R.attr.closeItemLayout, com.ici.connectivity.R.attr.height, com.ici.connectivity.R.attr.subtitleTextStyle, com.ici.connectivity.R.attr.titleTextStyle};
        public static final int[] ActivityChooserView = {com.ici.connectivity.R.attr.expandActivityOverflowButtonDrawable, com.ici.connectivity.R.attr.initialActivityCount};
        public static final int[] AlertDialog = {16842994, com.ici.connectivity.R.attr.buttonIconDimen, com.ici.connectivity.R.attr.buttonPanelSideLayout, com.ici.connectivity.R.attr.listItemLayout, com.ici.connectivity.R.attr.listLayout, com.ici.connectivity.R.attr.multiChoiceItemLayout, com.ici.connectivity.R.attr.showTitle, com.ici.connectivity.R.attr.singleChoiceItemLayout};
        public static final int[] AppCompatImageView = {16843033, com.ici.connectivity.R.attr.srcCompat, com.ici.connectivity.R.attr.tint, com.ici.connectivity.R.attr.tintMode};
        public static final int[] AppCompatSeekBar = {16843074, com.ici.connectivity.R.attr.tickMark, com.ici.connectivity.R.attr.tickMarkTint, com.ici.connectivity.R.attr.tickMarkTintMode};
        public static final int[] AppCompatTextHelper = {16842804, 16843117, 16843118, 16843119, 16843120, 16843666, 16843667};
        public static final int[] AppCompatTextView = {16842804, com.ici.connectivity.R.attr.autoSizeMaxTextSize, com.ici.connectivity.R.attr.autoSizeMinTextSize, com.ici.connectivity.R.attr.autoSizePresetSizes, com.ici.connectivity.R.attr.autoSizeStepGranularity, com.ici.connectivity.R.attr.autoSizeTextType, com.ici.connectivity.R.attr.drawableBottomCompat, com.ici.connectivity.R.attr.drawableEndCompat, com.ici.connectivity.R.attr.drawableLeftCompat, com.ici.connectivity.R.attr.drawableRightCompat, com.ici.connectivity.R.attr.drawableStartCompat, com.ici.connectivity.R.attr.drawableTint, com.ici.connectivity.R.attr.drawableTintMode, com.ici.connectivity.R.attr.drawableTopCompat, com.ici.connectivity.R.attr.firstBaselineToTopHeight, com.ici.connectivity.R.attr.fontFamily, com.ici.connectivity.R.attr.fontVariationSettings, com.ici.connectivity.R.attr.lastBaselineToBottomHeight, com.ici.connectivity.R.attr.lineHeight, com.ici.connectivity.R.attr.textAllCaps, com.ici.connectivity.R.attr.textLocale};
        public static final int[] AppCompatTheme = {16842839, 16842926, com.ici.connectivity.R.attr.actionBarDivider, com.ici.connectivity.R.attr.actionBarItemBackground, com.ici.connectivity.R.attr.actionBarPopupTheme, com.ici.connectivity.R.attr.actionBarSize, com.ici.connectivity.R.attr.actionBarSplitStyle, com.ici.connectivity.R.attr.actionBarStyle, com.ici.connectivity.R.attr.actionBarTabBarStyle, com.ici.connectivity.R.attr.actionBarTabStyle, com.ici.connectivity.R.attr.actionBarTabTextStyle, com.ici.connectivity.R.attr.actionBarTheme, com.ici.connectivity.R.attr.actionBarWidgetTheme, com.ici.connectivity.R.attr.actionButtonStyle, com.ici.connectivity.R.attr.actionDropDownStyle, com.ici.connectivity.R.attr.actionMenuTextAppearance, com.ici.connectivity.R.attr.actionMenuTextColor, com.ici.connectivity.R.attr.actionModeBackground, com.ici.connectivity.R.attr.actionModeCloseButtonStyle, com.ici.connectivity.R.attr.actionModeCloseDrawable, com.ici.connectivity.R.attr.actionModeCopyDrawable, com.ici.connectivity.R.attr.actionModeCutDrawable, com.ici.connectivity.R.attr.actionModeFindDrawable, com.ici.connectivity.R.attr.actionModePasteDrawable, com.ici.connectivity.R.attr.actionModePopupWindowStyle, com.ici.connectivity.R.attr.actionModeSelectAllDrawable, com.ici.connectivity.R.attr.actionModeShareDrawable, com.ici.connectivity.R.attr.actionModeSplitBackground, com.ici.connectivity.R.attr.actionModeStyle, com.ici.connectivity.R.attr.actionModeWebSearchDrawable, com.ici.connectivity.R.attr.actionOverflowButtonStyle, com.ici.connectivity.R.attr.actionOverflowMenuStyle, com.ici.connectivity.R.attr.activityChooserViewStyle, com.ici.connectivity.R.attr.alertDialogButtonGroupStyle, com.ici.connectivity.R.attr.alertDialogCenterButtons, com.ici.connectivity.R.attr.alertDialogStyle, com.ici.connectivity.R.attr.alertDialogTheme, com.ici.connectivity.R.attr.autoCompleteTextViewStyle, com.ici.connectivity.R.attr.borderlessButtonStyle, com.ici.connectivity.R.attr.buttonBarButtonStyle, com.ici.connectivity.R.attr.buttonBarNegativeButtonStyle, com.ici.connectivity.R.attr.buttonBarNeutralButtonStyle, com.ici.connectivity.R.attr.buttonBarPositiveButtonStyle, com.ici.connectivity.R.attr.buttonBarStyle, com.ici.connectivity.R.attr.buttonStyle, com.ici.connectivity.R.attr.buttonStyleSmall, com.ici.connectivity.R.attr.checkboxStyle, com.ici.connectivity.R.attr.checkedTextViewStyle, com.ici.connectivity.R.attr.colorAccent, com.ici.connectivity.R.attr.colorBackgroundFloating, com.ici.connectivity.R.attr.colorButtonNormal, com.ici.connectivity.R.attr.colorControlActivated, com.ici.connectivity.R.attr.colorControlHighlight, com.ici.connectivity.R.attr.colorControlNormal, com.ici.connectivity.R.attr.colorError, com.ici.connectivity.R.attr.colorPrimary, com.ici.connectivity.R.attr.colorPrimaryDark, com.ici.connectivity.R.attr.colorSwitchThumbNormal, com.ici.connectivity.R.attr.controlBackground, com.ici.connectivity.R.attr.dialogCornerRadius, com.ici.connectivity.R.attr.dialogPreferredPadding, com.ici.connectivity.R.attr.dialogTheme, com.ici.connectivity.R.attr.dividerHorizontal, com.ici.connectivity.R.attr.dividerVertical, com.ici.connectivity.R.attr.dropDownListViewStyle, com.ici.connectivity.R.attr.dropdownListPreferredItemHeight, com.ici.connectivity.R.attr.editTextBackground, com.ici.connectivity.R.attr.editTextColor, com.ici.connectivity.R.attr.editTextStyle, com.ici.connectivity.R.attr.homeAsUpIndicator, com.ici.connectivity.R.attr.imageButtonStyle, com.ici.connectivity.R.attr.listChoiceBackgroundIndicator, com.ici.connectivity.R.attr.listChoiceIndicatorMultipleAnimated, com.ici.connectivity.R.attr.listChoiceIndicatorSingleAnimated, com.ici.connectivity.R.attr.listDividerAlertDialog, com.ici.connectivity.R.attr.listMenuViewStyle, com.ici.connectivity.R.attr.listPopupWindowStyle, com.ici.connectivity.R.attr.listPreferredItemHeight, com.ici.connectivity.R.attr.listPreferredItemHeightLarge, com.ici.connectivity.R.attr.listPreferredItemHeightSmall, com.ici.connectivity.R.attr.listPreferredItemPaddingEnd, com.ici.connectivity.R.attr.listPreferredItemPaddingLeft, com.ici.connectivity.R.attr.listPreferredItemPaddingRight, com.ici.connectivity.R.attr.listPreferredItemPaddingStart, com.ici.connectivity.R.attr.panelBackground, com.ici.connectivity.R.attr.panelMenuListTheme, com.ici.connectivity.R.attr.panelMenuListWidth, com.ici.connectivity.R.attr.popupMenuStyle, com.ici.connectivity.R.attr.popupWindowStyle, com.ici.connectivity.R.attr.radioButtonStyle, com.ici.connectivity.R.attr.ratingBarStyle, com.ici.connectivity.R.attr.ratingBarStyleIndicator, com.ici.connectivity.R.attr.ratingBarStyleSmall, com.ici.connectivity.R.attr.searchViewStyle, com.ici.connectivity.R.attr.seekBarStyle, com.ici.connectivity.R.attr.selectableItemBackground, com.ici.connectivity.R.attr.selectableItemBackgroundBorderless, com.ici.connectivity.R.attr.spinnerDropDownItemStyle, com.ici.connectivity.R.attr.spinnerStyle, com.ici.connectivity.R.attr.switchStyle, com.ici.connectivity.R.attr.textAppearanceLargePopupMenu, com.ici.connectivity.R.attr.textAppearanceListItem, com.ici.connectivity.R.attr.textAppearanceListItemSecondary, com.ici.connectivity.R.attr.textAppearanceListItemSmall, com.ici.connectivity.R.attr.textAppearancePopupMenuHeader, com.ici.connectivity.R.attr.textAppearanceSearchResultSubtitle, com.ici.connectivity.R.attr.textAppearanceSearchResultTitle, com.ici.connectivity.R.attr.textAppearanceSmallPopupMenu, com.ici.connectivity.R.attr.textColorAlertDialogListItem, com.ici.connectivity.R.attr.textColorSearchUrl, com.ici.connectivity.R.attr.toolbarNavigationButtonStyle, com.ici.connectivity.R.attr.toolbarStyle, com.ici.connectivity.R.attr.tooltipForegroundColor, com.ici.connectivity.R.attr.tooltipFrameBackground, com.ici.connectivity.R.attr.viewInflaterClass, com.ici.connectivity.R.attr.windowActionBar, com.ici.connectivity.R.attr.windowActionBarOverlay, com.ici.connectivity.R.attr.windowActionModeOverlay, com.ici.connectivity.R.attr.windowFixedHeightMajor, com.ici.connectivity.R.attr.windowFixedHeightMinor, com.ici.connectivity.R.attr.windowFixedWidthMajor, com.ici.connectivity.R.attr.windowFixedWidthMinor, com.ici.connectivity.R.attr.windowMinWidthMajor, com.ici.connectivity.R.attr.windowMinWidthMinor, com.ici.connectivity.R.attr.windowNoTitle};
        public static final int[] ButtonBarLayout = {com.ici.connectivity.R.attr.allowStacking};
        public static final int[] ColorStateListItem = {16843173, 16843551, com.ici.connectivity.R.attr.alpha};
        public static final int[] CompoundButton = {16843015, com.ici.connectivity.R.attr.buttonCompat, com.ici.connectivity.R.attr.buttonTint, com.ici.connectivity.R.attr.buttonTintMode};
        public static final int[] DrawerArrowToggle = {com.ici.connectivity.R.attr.arrowHeadLength, com.ici.connectivity.R.attr.arrowShaftLength, com.ici.connectivity.R.attr.barLength, com.ici.connectivity.R.attr.color, com.ici.connectivity.R.attr.drawableSize, com.ici.connectivity.R.attr.gapBetweenBars, com.ici.connectivity.R.attr.spinBars, com.ici.connectivity.R.attr.thickness};
        public static final int[] LinearLayoutCompat = {16842927, 16842948, 16843046, 16843047, 16843048, com.ici.connectivity.R.attr.divider, com.ici.connectivity.R.attr.dividerPadding, com.ici.connectivity.R.attr.measureWithLargestChild, com.ici.connectivity.R.attr.showDividers};
        public static final int[] LinearLayoutCompat_Layout = {16842931, 16842996, 16842997, 16843137};
        public static final int[] ListPopupWindow = {16843436, 16843437};
        public static final int[] MenuGroup = {16842766, 16842960, 16843156, 16843230, 16843231, 16843232};
        public static final int[] MenuItem = {16842754, 16842766, 16842960, 16843014, 16843156, 16843230, 16843231, 16843233, 16843234, 16843235, 16843236, 16843237, 16843375, com.ici.connectivity.R.attr.actionLayout, com.ici.connectivity.R.attr.actionProviderClass, com.ici.connectivity.R.attr.actionViewClass, com.ici.connectivity.R.attr.alphabeticModifiers, com.ici.connectivity.R.attr.contentDescription, com.ici.connectivity.R.attr.iconTint, com.ici.connectivity.R.attr.iconTintMode, com.ici.connectivity.R.attr.numericModifiers, com.ici.connectivity.R.attr.showAsAction, com.ici.connectivity.R.attr.tooltipText};
        public static final int[] MenuView = {16842926, 16843052, 16843053, 16843054, 16843055, 16843056, 16843057, com.ici.connectivity.R.attr.preserveIconSpacing, com.ici.connectivity.R.attr.subMenuArrow};
        public static final int[] PopupWindow = {16843126, 16843465, com.ici.connectivity.R.attr.overlapAnchor};
        public static final int[] PopupWindowBackgroundState = {com.ici.connectivity.R.attr.state_above_anchor};
        public static final int[] RecycleListView = {com.ici.connectivity.R.attr.paddingBottomNoButtons, com.ici.connectivity.R.attr.paddingTopNoTitle};
        public static final int[] RecyclerView = {16842948, 16842987, 16842993, com.ici.connectivity.R.attr.fastScrollEnabled, com.ici.connectivity.R.attr.fastScrollHorizontalThumbDrawable, com.ici.connectivity.R.attr.fastScrollHorizontalTrackDrawable, com.ici.connectivity.R.attr.fastScrollVerticalThumbDrawable, com.ici.connectivity.R.attr.fastScrollVerticalTrackDrawable, com.ici.connectivity.R.attr.layoutManager, com.ici.connectivity.R.attr.reverseLayout, com.ici.connectivity.R.attr.spanCount, com.ici.connectivity.R.attr.stackFromEnd};
        public static final int[] SearchView = {16842970, 16843039, 16843296, 16843364, com.ici.connectivity.R.attr.closeIcon, com.ici.connectivity.R.attr.commitIcon, com.ici.connectivity.R.attr.defaultQueryHint, com.ici.connectivity.R.attr.goIcon, com.ici.connectivity.R.attr.iconifiedByDefault, com.ici.connectivity.R.attr.layout, com.ici.connectivity.R.attr.queryBackground, com.ici.connectivity.R.attr.queryHint, com.ici.connectivity.R.attr.searchHintIcon, com.ici.connectivity.R.attr.searchIcon, com.ici.connectivity.R.attr.submitBackground, com.ici.connectivity.R.attr.suggestionRowLayout, com.ici.connectivity.R.attr.voiceIcon};
        public static final int[] Spinner = {16842930, 16843126, 16843131, 16843362, com.ici.connectivity.R.attr.popupTheme};
        public static final int[] SwitchCompat = {16843044, 16843045, 16843074, com.ici.connectivity.R.attr.showText, com.ici.connectivity.R.attr.splitTrack, com.ici.connectivity.R.attr.switchMinWidth, com.ici.connectivity.R.attr.switchPadding, com.ici.connectivity.R.attr.switchTextAppearance, com.ici.connectivity.R.attr.thumbTextPadding, com.ici.connectivity.R.attr.thumbTint, com.ici.connectivity.R.attr.thumbTintMode, com.ici.connectivity.R.attr.track, com.ici.connectivity.R.attr.trackTint, com.ici.connectivity.R.attr.trackTintMode};
        public static final int[] TextAppearance = {16842901, 16842902, 16842903, 16842904, 16842906, 16842907, 16843105, 16843106, 16843107, 16843108, 16843692, 16844165, com.ici.connectivity.R.attr.fontFamily, com.ici.connectivity.R.attr.fontVariationSettings, com.ici.connectivity.R.attr.textAllCaps, com.ici.connectivity.R.attr.textLocale};
        public static final int[] Toolbar = {16842927, 16843072, com.ici.connectivity.R.attr.buttonGravity, com.ici.connectivity.R.attr.collapseContentDescription, com.ici.connectivity.R.attr.collapseIcon, com.ici.connectivity.R.attr.contentInsetEnd, com.ici.connectivity.R.attr.contentInsetEndWithActions, com.ici.connectivity.R.attr.contentInsetLeft, com.ici.connectivity.R.attr.contentInsetRight, com.ici.connectivity.R.attr.contentInsetStart, com.ici.connectivity.R.attr.contentInsetStartWithNavigation, com.ici.connectivity.R.attr.logo, com.ici.connectivity.R.attr.logoDescription, com.ici.connectivity.R.attr.maxButtonHeight, com.ici.connectivity.R.attr.menu, com.ici.connectivity.R.attr.navigationContentDescription, com.ici.connectivity.R.attr.navigationIcon, com.ici.connectivity.R.attr.popupTheme, com.ici.connectivity.R.attr.subtitle, com.ici.connectivity.R.attr.subtitleTextAppearance, com.ici.connectivity.R.attr.subtitleTextColor, com.ici.connectivity.R.attr.title, com.ici.connectivity.R.attr.titleMargin, com.ici.connectivity.R.attr.titleMarginBottom, com.ici.connectivity.R.attr.titleMarginEnd, com.ici.connectivity.R.attr.titleMarginStart, com.ici.connectivity.R.attr.titleMarginTop, com.ici.connectivity.R.attr.titleMargins, com.ici.connectivity.R.attr.titleTextAppearance, com.ici.connectivity.R.attr.titleTextColor};
        public static final int[] View = {16842752, 16842970, com.ici.connectivity.R.attr.paddingEnd, com.ici.connectivity.R.attr.paddingStart, com.ici.connectivity.R.attr.theme};
        public static final int[] ViewBackgroundHelper = {16842964, com.ici.connectivity.R.attr.backgroundTint, com.ici.connectivity.R.attr.backgroundTintMode};
        public static final int[] ViewStubCompat = {16842960, 16842994, 16842995};
    }
}
