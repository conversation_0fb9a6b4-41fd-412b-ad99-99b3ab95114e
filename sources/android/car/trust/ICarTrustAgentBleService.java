package android.car.trust;

import android.bluetooth.BluetoothDevice;
import android.car.trust.ICarTrustAgentBleCallback;
import android.car.trust.ICarTrustAgentEnrolmentCallback;
import android.car.trust.ICarTrustAgentTokenRequestDelegate;
import android.car.trust.ICarTrustAgentTokenResponseCallback;
import android.car.trust.ICarTrustAgentUnlockCallback;
import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface ICarTrustAgentBleService extends IInterface {

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements ICarTrustAgentBleService {
        public static final String DESCRIPTOR = "android.car.trust.ICarTrustAgentBleService";
        public static final int TRANSACTION_addEscrowToken = 14;
        public static final int TRANSACTION_getUserIdByEscrowTokenHandle = 21;
        public static final int TRANSACTION_isEscrowTokenActive = 16;
        public static final int TRANSACTION_onEscrowTokenActiveStateChanged = 20;
        public static final int TRANSACTION_onEscrowTokenAdded = 18;
        public static final int TRANSACTION_onEscrowTokenRemoved = 19;
        public static final int TRANSACTION_registerBleCallback = 1;
        public static final int TRANSACTION_registerEnrolmentCallback = 6;
        public static final int TRANSACTION_registerUnlockCallback = 10;
        public static final int TRANSACTION_removeEscrowToken = 15;
        public static final int TRANSACTION_revokeTrust = 13;
        public static final int TRANSACTION_sendEnrolmentHandle = 5;
        public static final int TRANSACTION_setTokenRequestDelegate = 12;
        public static final int TRANSACTION_setTokenResponseCallback = 17;
        public static final int TRANSACTION_startEnrolmentAdvertising = 3;
        public static final int TRANSACTION_startUnlockAdvertising = 8;
        public static final int TRANSACTION_stopEnrolmentAdvertising = 4;
        public static final int TRANSACTION_stopUnlockAdvertising = 9;
        public static final int TRANSACTION_unregisterBleCallback = 2;
        public static final int TRANSACTION_unregisterEnrolmentCallback = 7;
        public static final int TRANSACTION_unregisterUnlockCallback = 11;

        /* loaded from: classes.dex */
        public static class Proxy implements ICarTrustAgentBleService {
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public void addEscrowToken(byte[] bArr, int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeByteArray(bArr);
                    obtain.writeInt(i);
                    this.mRemote.transact(14, obtain, obtain2, 0);
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public int getUserIdByEscrowTokenHandle(long j) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeLong(j);
                    this.mRemote.transact(21, obtain, obtain2, 0);
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public void isEscrowTokenActive(long j, int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeLong(j);
                    obtain.writeInt(i);
                    this.mRemote.transact(16, obtain, obtain2, 0);
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public void onEscrowTokenActiveStateChanged(long j, boolean z) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeLong(j);
                    obtain.writeInt(z ? 1 : 0);
                    this.mRemote.transact(20, obtain, obtain2, 0);
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public void onEscrowTokenAdded(byte[] bArr, long j, int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeByteArray(bArr);
                    obtain.writeLong(j);
                    obtain.writeInt(i);
                    this.mRemote.transact(18, obtain, obtain2, 0);
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public void onEscrowTokenRemoved(long j, boolean z) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeLong(j);
                    obtain.writeInt(z ? 1 : 0);
                    this.mRemote.transact(19, obtain, obtain2, 0);
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public void registerBleCallback(ICarTrustAgentBleCallback iCarTrustAgentBleCallback) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeStrongBinder(iCarTrustAgentBleCallback != null ? iCarTrustAgentBleCallback.asBinder() : null);
                    this.mRemote.transact(1, obtain, obtain2, 0);
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public void registerEnrolmentCallback(ICarTrustAgentEnrolmentCallback iCarTrustAgentEnrolmentCallback) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeStrongBinder(iCarTrustAgentEnrolmentCallback != null ? iCarTrustAgentEnrolmentCallback.asBinder() : null);
                    this.mRemote.transact(6, obtain, obtain2, 0);
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public void registerUnlockCallback(ICarTrustAgentUnlockCallback iCarTrustAgentUnlockCallback) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeStrongBinder(iCarTrustAgentUnlockCallback != null ? iCarTrustAgentUnlockCallback.asBinder() : null);
                    this.mRemote.transact(10, obtain, obtain2, 0);
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public void removeEscrowToken(long j, int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeLong(j);
                    obtain.writeInt(i);
                    this.mRemote.transact(15, obtain, obtain2, 0);
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public void revokeTrust() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    this.mRemote.transact(13, obtain, obtain2, 0);
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public void sendEnrolmentHandle(BluetoothDevice bluetoothDevice, long j) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (bluetoothDevice != null) {
                        obtain.writeInt(1);
                        bluetoothDevice.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    obtain.writeLong(j);
                    this.mRemote.transact(5, obtain, obtain2, 0);
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public void setTokenRequestDelegate(ICarTrustAgentTokenRequestDelegate iCarTrustAgentTokenRequestDelegate) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeStrongBinder(iCarTrustAgentTokenRequestDelegate != null ? iCarTrustAgentTokenRequestDelegate.asBinder() : null);
                    this.mRemote.transact(12, obtain, obtain2, 0);
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public void setTokenResponseCallback(ICarTrustAgentTokenResponseCallback iCarTrustAgentTokenResponseCallback) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeStrongBinder(iCarTrustAgentTokenResponseCallback != null ? iCarTrustAgentTokenResponseCallback.asBinder() : null);
                    this.mRemote.transact(17, obtain, obtain2, 0);
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public void startEnrolmentAdvertising() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    this.mRemote.transact(3, obtain, obtain2, 0);
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public void startUnlockAdvertising() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    this.mRemote.transact(8, obtain, obtain2, 0);
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public void stopEnrolmentAdvertising() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    this.mRemote.transact(4, obtain, obtain2, 0);
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public void stopUnlockAdvertising() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    this.mRemote.transact(9, obtain, obtain2, 0);
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public void unregisterBleCallback(ICarTrustAgentBleCallback iCarTrustAgentBleCallback) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeStrongBinder(iCarTrustAgentBleCallback != null ? iCarTrustAgentBleCallback.asBinder() : null);
                    this.mRemote.transact(2, obtain, obtain2, 0);
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public void unregisterEnrolmentCallback(ICarTrustAgentEnrolmentCallback iCarTrustAgentEnrolmentCallback) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeStrongBinder(iCarTrustAgentEnrolmentCallback != null ? iCarTrustAgentEnrolmentCallback.asBinder() : null);
                    this.mRemote.transact(7, obtain, obtain2, 0);
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // android.car.trust.ICarTrustAgentBleService
            public void unregisterUnlockCallback(ICarTrustAgentUnlockCallback iCarTrustAgentUnlockCallback) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeStrongBinder(iCarTrustAgentUnlockCallback != null ? iCarTrustAgentUnlockCallback.asBinder() : null);
                    this.mRemote.transact(11, obtain, obtain2, 0);
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static ICarTrustAgentBleService asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof ICarTrustAgentBleService)) {
                return (ICarTrustAgentBleService) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i != 1598968902) {
                switch (i) {
                    case 1:
                        parcel.enforceInterface(DESCRIPTOR);
                        registerBleCallback(ICarTrustAgentBleCallback.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 2:
                        parcel.enforceInterface(DESCRIPTOR);
                        unregisterBleCallback(ICarTrustAgentBleCallback.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 3:
                        parcel.enforceInterface(DESCRIPTOR);
                        startEnrolmentAdvertising();
                        parcel2.writeNoException();
                        return true;
                    case 4:
                        parcel.enforceInterface(DESCRIPTOR);
                        stopEnrolmentAdvertising();
                        parcel2.writeNoException();
                        return true;
                    case 5:
                        parcel.enforceInterface(DESCRIPTOR);
                        sendEnrolmentHandle(parcel.readInt() != 0 ? (BluetoothDevice) BluetoothDevice.CREATOR.createFromParcel(parcel) : null, parcel.readLong());
                        parcel2.writeNoException();
                        return true;
                    case 6:
                        parcel.enforceInterface(DESCRIPTOR);
                        registerEnrolmentCallback(ICarTrustAgentEnrolmentCallback.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 7:
                        parcel.enforceInterface(DESCRIPTOR);
                        unregisterEnrolmentCallback(ICarTrustAgentEnrolmentCallback.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 8:
                        parcel.enforceInterface(DESCRIPTOR);
                        startUnlockAdvertising();
                        parcel2.writeNoException();
                        return true;
                    case 9:
                        parcel.enforceInterface(DESCRIPTOR);
                        stopUnlockAdvertising();
                        parcel2.writeNoException();
                        return true;
                    case 10:
                        parcel.enforceInterface(DESCRIPTOR);
                        registerUnlockCallback(ICarTrustAgentUnlockCallback.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 11:
                        parcel.enforceInterface(DESCRIPTOR);
                        unregisterUnlockCallback(ICarTrustAgentUnlockCallback.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 12:
                        parcel.enforceInterface(DESCRIPTOR);
                        setTokenRequestDelegate(ICarTrustAgentTokenRequestDelegate.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 13:
                        parcel.enforceInterface(DESCRIPTOR);
                        revokeTrust();
                        parcel2.writeNoException();
                        return true;
                    case 14:
                        parcel.enforceInterface(DESCRIPTOR);
                        addEscrowToken(parcel.createByteArray(), parcel.readInt());
                        parcel2.writeNoException();
                        return true;
                    case 15:
                        parcel.enforceInterface(DESCRIPTOR);
                        removeEscrowToken(parcel.readLong(), parcel.readInt());
                        parcel2.writeNoException();
                        return true;
                    case 16:
                        parcel.enforceInterface(DESCRIPTOR);
                        isEscrowTokenActive(parcel.readLong(), parcel.readInt());
                        parcel2.writeNoException();
                        return true;
                    case 17:
                        parcel.enforceInterface(DESCRIPTOR);
                        setTokenResponseCallback(ICarTrustAgentTokenResponseCallback.Stub.asInterface(parcel.readStrongBinder()));
                        parcel2.writeNoException();
                        return true;
                    case 18:
                        parcel.enforceInterface(DESCRIPTOR);
                        onEscrowTokenAdded(parcel.createByteArray(), parcel.readLong(), parcel.readInt());
                        parcel2.writeNoException();
                        return true;
                    case 19:
                        parcel.enforceInterface(DESCRIPTOR);
                        onEscrowTokenRemoved(parcel.readLong(), parcel.readInt() != 0);
                        parcel2.writeNoException();
                        return true;
                    case 20:
                        parcel.enforceInterface(DESCRIPTOR);
                        onEscrowTokenActiveStateChanged(parcel.readLong(), parcel.readInt() != 0);
                        parcel2.writeNoException();
                        return true;
                    case 21:
                        parcel.enforceInterface(DESCRIPTOR);
                        int userIdByEscrowTokenHandle = getUserIdByEscrowTokenHandle(parcel.readLong());
                        parcel2.writeNoException();
                        parcel2.writeInt(userIdByEscrowTokenHandle);
                        return true;
                    default:
                        return super.onTransact(i, parcel, parcel2, i2);
                }
            }
            parcel2.writeString(DESCRIPTOR);
            return true;
        }
    }

    void addEscrowToken(byte[] bArr, int i) throws RemoteException;

    int getUserIdByEscrowTokenHandle(long j) throws RemoteException;

    void isEscrowTokenActive(long j, int i) throws RemoteException;

    void onEscrowTokenActiveStateChanged(long j, boolean z) throws RemoteException;

    void onEscrowTokenAdded(byte[] bArr, long j, int i) throws RemoteException;

    void onEscrowTokenRemoved(long j, boolean z) throws RemoteException;

    void registerBleCallback(ICarTrustAgentBleCallback iCarTrustAgentBleCallback) throws RemoteException;

    void registerEnrolmentCallback(ICarTrustAgentEnrolmentCallback iCarTrustAgentEnrolmentCallback) throws RemoteException;

    void registerUnlockCallback(ICarTrustAgentUnlockCallback iCarTrustAgentUnlockCallback) throws RemoteException;

    void removeEscrowToken(long j, int i) throws RemoteException;

    void revokeTrust() throws RemoteException;

    void sendEnrolmentHandle(BluetoothDevice bluetoothDevice, long j) throws RemoteException;

    void setTokenRequestDelegate(ICarTrustAgentTokenRequestDelegate iCarTrustAgentTokenRequestDelegate) throws RemoteException;

    void setTokenResponseCallback(ICarTrustAgentTokenResponseCallback iCarTrustAgentTokenResponseCallback) throws RemoteException;

    void startEnrolmentAdvertising() throws RemoteException;

    void startUnlockAdvertising() throws RemoteException;

    void stopEnrolmentAdvertising() throws RemoteException;

    void stopUnlockAdvertising() throws RemoteException;

    void unregisterBleCallback(ICarTrustAgentBleCallback iCarTrustAgentBleCallback) throws RemoteException;

    void unregisterEnrolmentCallback(ICarTrustAgentEnrolmentCallback iCarTrustAgentEnrolmentCallback) throws RemoteException;

    void unregisterUnlockCallback(ICarTrustAgentUnlockCallback iCarTrustAgentUnlockCallback) throws RemoteException;
}
