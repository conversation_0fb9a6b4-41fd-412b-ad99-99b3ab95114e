package android.car.storagemonitoring;

import android.annotation.SystemApi;
import android.car.CarApiUtil;
import android.car.CarManagerBase;
import android.car.CarNotConnectedException;
import android.car.storagemonitoring.ICarStorageMonitoring;
import android.car.storagemonitoring.IIoStatsListener;
import android.os.Handler;
import android.os.IBinder;
import android.os.RemoteException;
import com.android.car.internal.SingleMessageHandler;
import java.lang.ref.WeakReference;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@SystemApi
/* loaded from: classes.dex */
public final class CarStorageMonitoringManager implements CarManagerBase {
    public static final String INTENT_EXCESSIVE_IO = "android.car.storagemonitoring.EXCESSIVE_IO";
    public static final int MSG_IO_STATS_EVENT = 0;
    public static final int PRE_EOL_INFO_NORMAL = 1;
    public static final int PRE_EOL_INFO_UNKNOWN = 0;
    public static final int PRE_EOL_INFO_URGENT = 3;
    public static final int PRE_EOL_INFO_WARNING = 2;
    public static final long SHUTDOWN_COST_INFO_MISSING = -1;
    public static final String TAG = "CarStorageMonitoringManager";
    public ListenerToService mListenerToService;
    public final Set<IoStatsListener> mListeners = new HashSet();
    public final SingleMessageHandler<IoStats> mMessageHandler;
    public final ICarStorageMonitoring mService;

    /* loaded from: classes.dex */
    public interface IoStatsListener {
        void onSnapshot(IoStats ioStats);
    }

    /* loaded from: classes.dex */
    public static final class ListenerToService extends IIoStatsListener.Stub {
        public final WeakReference<CarStorageMonitoringManager> mManager;

        public ListenerToService(CarStorageMonitoringManager carStorageMonitoringManager) {
            this.mManager = new WeakReference<>(carStorageMonitoringManager);
        }

        @Override // android.car.storagemonitoring.IIoStatsListener
        public void onSnapshot(IoStats ioStats) {
            CarStorageMonitoringManager carStorageMonitoringManager = this.mManager.get();
            if (carStorageMonitoringManager != null) {
                carStorageMonitoringManager.mMessageHandler.sendEvents(Collections.singletonList(ioStats));
            }
        }
    }

    public CarStorageMonitoringManager(IBinder iBinder, Handler handler) {
        this.mService = ICarStorageMonitoring.Stub.asInterface(iBinder);
        this.mMessageHandler = new SingleMessageHandler<IoStats>(handler, 0) { // from class: android.car.storagemonitoring.CarStorageMonitoringManager.1
            @Override // com.android.car.internal.SingleMessageHandler
            public void handleEvent(IoStats ioStats) {
                for (IoStatsListener ioStatsListener : CarStorageMonitoringManager.this.mListeners) {
                    ioStatsListener.onSnapshot(ioStats);
                }
            }
        };
    }

    public List<IoStatsEntry> getAggregateIoStats() throws CarNotConnectedException {
        try {
            return this.mService.getAggregateIoStats();
        } catch (RemoteException unused) {
            throw new CarNotConnectedException();
        } catch (IllegalStateException e) {
            CarApiUtil.checkCarNotConnectedExceptionFromCarService(e);
            return Collections.emptyList();
        }
    }

    public List<IoStatsEntry> getBootIoStats() throws CarNotConnectedException {
        try {
            return this.mService.getBootIoStats();
        } catch (RemoteException unused) {
            throw new CarNotConnectedException();
        } catch (IllegalStateException e) {
            CarApiUtil.checkCarNotConnectedExceptionFromCarService(e);
            return Collections.emptyList();
        }
    }

    public List<IoStats> getIoStatsDeltas() throws CarNotConnectedException {
        try {
            return this.mService.getIoStatsDeltas();
        } catch (RemoteException unused) {
            throw new CarNotConnectedException();
        } catch (IllegalStateException e) {
            CarApiUtil.checkCarNotConnectedExceptionFromCarService(e);
            return Collections.emptyList();
        }
    }

    public int getPreEolIndicatorStatus() throws CarNotConnectedException {
        try {
            return this.mService.getPreEolIndicatorStatus();
        } catch (RemoteException unused) {
            throw new CarNotConnectedException();
        } catch (IllegalStateException e) {
            CarApiUtil.checkCarNotConnectedExceptionFromCarService(e);
            return 0;
        }
    }

    public long getShutdownDiskWriteAmount() throws CarNotConnectedException {
        try {
            return this.mService.getShutdownDiskWriteAmount();
        } catch (RemoteException unused) {
            throw new CarNotConnectedException();
        } catch (IllegalStateException e) {
            CarApiUtil.checkCarNotConnectedExceptionFromCarService(e);
            return -1L;
        }
    }

    public WearEstimate getWearEstimate() throws CarNotConnectedException {
        try {
            return this.mService.getWearEstimate();
        } catch (RemoteException unused) {
            throw new CarNotConnectedException();
        } catch (IllegalStateException e) {
            CarApiUtil.checkCarNotConnectedExceptionFromCarService(e);
            return WearEstimate.UNKNOWN_ESTIMATE;
        }
    }

    public List<WearEstimateChange> getWearEstimateHistory() throws CarNotConnectedException {
        try {
            return this.mService.getWearEstimateHistory();
        } catch (RemoteException unused) {
            throw new CarNotConnectedException();
        } catch (IllegalStateException e) {
            CarApiUtil.checkCarNotConnectedExceptionFromCarService(e);
            return Collections.emptyList();
        }
    }

    @Override // android.car.CarManagerBase
    public void onCarDisconnected() {
        this.mListeners.clear();
        this.mListenerToService = null;
    }

    public void registerListener(IoStatsListener ioStatsListener) throws CarNotConnectedException {
        try {
            if (this.mListeners.isEmpty()) {
                if (this.mListenerToService == null) {
                    this.mListenerToService = new ListenerToService(this);
                }
                this.mService.registerListener(this.mListenerToService);
            }
            this.mListeners.add(ioStatsListener);
        } catch (RemoteException unused) {
            throw new CarNotConnectedException();
        } catch (IllegalStateException e) {
            CarApiUtil.checkCarNotConnectedExceptionFromCarService(e);
        }
    }

    public void unregisterListener(IoStatsListener ioStatsListener) throws CarNotConnectedException {
        try {
            if (this.mListeners.remove(ioStatsListener) && this.mListeners.isEmpty()) {
                this.mService.unregisterListener(this.mListenerToService);
                this.mListenerToService = null;
            }
        } catch (RemoteException unused) {
            throw new CarNotConnectedException();
        } catch (IllegalStateException e) {
            CarApiUtil.checkCarNotConnectedExceptionFromCarService(e);
        }
    }
}
