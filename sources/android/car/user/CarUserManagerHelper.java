package android.car.user;

import android.app.ActivityManager;
import android.car.settings.CarSettings;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.UserInfo;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.SystemProperties;
import android.os.UserHandle;
import android.os.UserManager;
import android.provider.Settings;
import android.util.Log;
import androidx.appcompat.widget.ActivityChooserModel;
import androidx.appcompat.widget.ActivityChooserView;
import b.a.b.a.a;
import com.android.internal.util.UserIcons;
import com.google.android.collect.Sets;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

/* loaded from: classes.dex */
public class CarUserManagerHelper {
    public static final String HEADLESS_SYSTEM_USER = "android.car.systemuser.headless";
    public static final String TAG = "CarUserManagerHelper";
    public final ActivityManager mActivityManager;
    public final Context mContext;
    public Bitmap mDefaultGuestUserIcon;
    public final UserManager mUserManager;
    public static final Set<String> DEFAULT_NON_ADMIN_RESTRICTIONS = Sets.newArraySet(new String[]{"no_factory_reset"});
    public static final Set<String> DEFAULT_GUEST_RESTRICTIONS = Sets.newArraySet(new String[]{"no_factory_reset", "no_remove_user", "no_modify_accounts", "no_outgoing_calls", "no_sms", "no_install_apps", "no_uninstall_apps"});
    public int mLastActiveUser = 0;
    public final BroadcastReceiver mUserChangeReceiver = new BroadcastReceiver() { // from class: android.car.user.CarUserManagerHelper.1
        @Override // android.content.BroadcastReceiver
        public void onReceive(Context context, Intent intent) {
            ArrayList arrayList;
            synchronized (CarUserManagerHelper.this.mUpdateListeners) {
                arrayList = new ArrayList(CarUserManagerHelper.this.mUpdateListeners);
            }
            Iterator it = arrayList.iterator();
            while (it.hasNext()) {
                ((OnUsersUpdateListener) it.next()).onUsersUpdate();
            }
        }
    };
    public ArrayList<OnUsersUpdateListener> mUpdateListeners = new ArrayList<>();

    /* loaded from: classes.dex */
    public interface OnUsersUpdateListener {
        void onUsersUpdate();
    }

    public CarUserManagerHelper(Context context) {
        Context applicationContext = context.getApplicationContext();
        this.mContext = applicationContext;
        this.mUserManager = (UserManager) applicationContext.getSystemService("user");
        this.mActivityManager = (ActivityManager) this.mContext.getSystemService(ActivityChooserModel.ATTRIBUTE_ACTIVITY);
    }

    private Bitmap assignDefaultIcon(UserInfo userInfo) {
        Bitmap guestDefaultIcon = userInfo.isGuest() ? getGuestDefaultIcon() : getUserDefaultIcon(userInfo);
        this.mUserManager.setUserIcon(userInfo.id, guestDefaultIcon);
        return guestDefaultIcon;
    }

    private List<UserInfo> getAllUsersExceptSpecifiedUser(int i) {
        List<UserInfo> users = this.mUserManager.getUsers(true);
        Iterator<UserInfo> it = users.iterator();
        while (it.hasNext()) {
            if (it.next().id == i) {
                it.remove();
            }
        }
        return users;
    }

    private List<UserInfo> getAllUsersExceptSystemUserAndSpecifiedUser(int i) {
        List<UserInfo> users = this.mUserManager.getUsers(true);
        Iterator<UserInfo> it = users.iterator();
        while (it.hasNext()) {
            int i2 = it.next().id;
            if (i2 == i || i2 == 0) {
                it.remove();
            }
        }
        return users;
    }

    private int getManagedProfilesCount() {
        int i = 0;
        for (UserInfo userInfo : getAllUsers()) {
            if (userInfo.isManagedProfile()) {
                i++;
            }
        }
        return i;
    }

    private void registerReceiver() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction("android.intent.action.USER_REMOVED");
        intentFilter.addAction("android.intent.action.USER_ADDED");
        intentFilter.addAction("android.intent.action.USER_INFO_CHANGED");
        intentFilter.addAction("android.intent.action.USER_SWITCHED");
        intentFilter.addAction("android.intent.action.USER_STOPPED");
        intentFilter.addAction("android.intent.action.USER_UNLOCKED");
        this.mContext.registerReceiverAsUser(this.mUserChangeReceiver, UserHandle.ALL, intentFilter, null, null);
    }

    private void setDefaultNonAdminRestrictions(UserInfo userInfo, boolean z) {
        for (String str : DEFAULT_NON_ADMIN_RESTRICTIONS) {
            setUserRestriction(userInfo, str, z);
        }
    }

    private void unregisterReceiver() {
        this.mContext.unregisterReceiver(this.mUserChangeReceiver);
    }

    public void assignAdminPrivileges(UserInfo userInfo) {
        if (!isCurrentProcessAdminUser()) {
            Log.w(TAG, "Only admin users can assign admin privileges.");
            return;
        }
        this.mUserManager.setUserAdmin(userInfo.id);
        setDefaultNonAdminRestrictions(userInfo, false);
    }

    public boolean canCurrentProcessAddUsers() {
        return !isCurrentProcessUserHasRestriction("no_add_user");
    }

    public boolean canCurrentProcessModifyAccounts() {
        return (isCurrentProcessUserHasRestriction("no_modify_accounts") || isCurrentProcessDemoUser() || isCurrentProcessGuestUser()) ? false : true;
    }

    public boolean canCurrentProcessRemoveUsers() {
        return !isCurrentProcessUserHasRestriction("no_remove_user");
    }

    public boolean canCurrentProcessSwitchUsers() {
        return !isCurrentProcessUserHasRestriction("no_user_switch");
    }

    public boolean canForegroundUserAddUsers() {
        return !foregroundUserHasUserRestriction("no_add_user");
    }

    public boolean canUserBeRemoved(UserInfo userInfo) {
        return !isSystemUser(userInfo);
    }

    public UserInfo createNewAdminUser(String str) {
        if (!isCurrentProcessAdminUser() && !isCurrentProcessSystemUser()) {
            Log.e(TAG, "Only admin users and system user can create other admins.");
            return null;
        }
        UserInfo createUser = this.mUserManager.createUser(str, 2);
        if (createUser == null) {
            Log.w(TAG, "can't create admin user.");
            return null;
        }
        assignDefaultIcon(createUser);
        return createUser;
    }

    public UserInfo createNewNonAdminUser(String str) {
        UserInfo createUser = this.mUserManager.createUser(str, 0);
        if (createUser == null) {
            Log.w(TAG, "can't create non-admin user.");
            return null;
        }
        setDefaultNonAdminRestrictions(createUser, true);
        setUserRestriction(createUser, "no_sms", false);
        setUserRestriction(createUser, "no_outgoing_calls", false);
        assignDefaultIcon(createUser);
        return createUser;
    }

    public boolean foregroundUserHasUserRestriction(String str) {
        return this.mUserManager.hasUserRestriction(str, getCurrentForegroundUserInfo().getUserHandle());
    }

    public List<UserInfo> getAllAdminUsers() {
        List<UserInfo> allUsers = getAllUsers();
        Iterator<UserInfo> it = allUsers.iterator();
        while (it.hasNext()) {
            if (!it.next().isAdmin()) {
                it.remove();
            }
        }
        return allUsers;
    }

    public List<UserInfo> getAllPersistentUsers() {
        List<UserInfo> allUsers = getAllUsers();
        Iterator<UserInfo> it = allUsers.iterator();
        while (it.hasNext()) {
            if (it.next().isEphemeral()) {
                it.remove();
            }
        }
        return allUsers;
    }

    public List<UserInfo> getAllSwitchableUsers() {
        if (isHeadlessSystemUser()) {
            return getAllUsersExceptSystemUserAndSpecifiedUser(getCurrentForegroundUserId());
        }
        return getAllUsersExceptSpecifiedUser(getCurrentForegroundUserId());
    }

    public List<UserInfo> getAllUsers() {
        if (isHeadlessSystemUser()) {
            return getAllUsersExceptSystemUserAndSpecifiedUser(0);
        }
        return this.mUserManager.getUsers(true);
    }

    public List<UserInfo> getAllUsersExceptGuests() {
        List<UserInfo> allUsers = getAllUsers();
        Iterator<UserInfo> it = allUsers.iterator();
        while (it.hasNext()) {
            if (it.next().isGuest()) {
                it.remove();
            }
        }
        return allUsers;
    }

    public int getCurrentForegroundUserId() {
        return ActivityManager.getCurrentUser();
    }

    public UserInfo getCurrentForegroundUserInfo() {
        return this.mUserManager.getUserInfo(getCurrentForegroundUserId());
    }

    public int getCurrentProcessUserId() {
        return UserHandle.myUserId();
    }

    public UserInfo getCurrentProcessUserInfo() {
        return this.mUserManager.getUserInfo(getCurrentProcessUserId());
    }

    public int getDefaultBootUser() {
        return Settings.Global.getInt(this.mContext.getContentResolver(), CarSettings.Global.DEFAULT_USER_ID_TO_BOOT_INTO, 10);
    }

    public Bitmap getGuestDefaultIcon() {
        if (this.mDefaultGuestUserIcon == null) {
            this.mDefaultGuestUserIcon = UserIcons.convertToBitmap(UserIcons.getDefaultUserIcon(this.mContext.getResources(), -10000, false));
        }
        return this.mDefaultGuestUserIcon;
    }

    public int getInitialUser() {
        int lastActiveUser = getLastActiveUser();
        boolean z = false;
        int i = ActivityChooserView.ActivityChooserViewAdapter.MAX_ACTIVITY_COUNT_UNLIMITED;
        for (UserInfo userInfo : getAllPersistentUsers()) {
            if (userInfo.id == lastActiveUser) {
                z = true;
            }
            i = Math.min(userInfo.id, i);
        }
        if (lastActiveUser == 0 || !z) {
            Log.e(TAG, "Can't get last active user id or the user no longer exist, user id: ." + lastActiveUser);
            return i;
        }
        return lastActiveUser;
    }

    public int getLastActiveUser() {
        int i = this.mLastActiveUser;
        return i != 0 ? i : Settings.Global.getInt(this.mContext.getContentResolver(), CarSettings.Global.LAST_ACTIVE_USER_ID, 0);
    }

    public int getMaxSupportedRealUsers() {
        return getMaxSupportedUsers() - getManagedProfilesCount();
    }

    public int getMaxSupportedUsers() {
        if (isHeadlessSystemUser()) {
            return UserManager.getMaxSupportedUsers() - 1;
        }
        return UserManager.getMaxSupportedUsers();
    }

    public UserInfo getSystemUserInfo() {
        return this.mUserManager.getUserInfo(0);
    }

    public Bitmap getUserDefaultIcon(UserInfo userInfo) {
        return UserIcons.convertToBitmap(UserIcons.getDefaultUserIcon(this.mContext.getResources(), userInfo.id, false));
    }

    public Bitmap getUserIcon(UserInfo userInfo) {
        Bitmap userIcon = this.mUserManager.getUserIcon(userInfo.id);
        return userIcon == null ? assignDefaultIcon(userInfo) : userIcon;
    }

    public void initDefaultGuestRestrictions() {
        Bundle bundle = new Bundle();
        for (String str : DEFAULT_GUEST_RESTRICTIONS) {
            bundle.putBoolean(str, true);
        }
        this.mUserManager.setDefaultGuestRestrictions(bundle);
    }

    public boolean isCurrentProcessAdminUser() {
        return this.mUserManager.isAdminUser();
    }

    public boolean isCurrentProcessDemoUser() {
        return this.mUserManager.isDemoUser();
    }

    public boolean isCurrentProcessGuestUser() {
        return this.mUserManager.isGuestUser();
    }

    public boolean isCurrentProcessRestrictedProfileUser() {
        return this.mUserManager.isRestrictedProfile();
    }

    public boolean isCurrentProcessSystemUser() {
        return this.mUserManager.isSystemUser();
    }

    public boolean isCurrentProcessUser(UserInfo userInfo) {
        return getCurrentProcessUserId() == userInfo.id;
    }

    public boolean isCurrentProcessUserHasRestriction(String str) {
        return this.mUserManager.hasUserRestriction(str);
    }

    public boolean isDefaultUser(UserInfo userInfo) {
        return userInfo.id == getDefaultBootUser();
    }

    public boolean isForegroundUser(UserInfo userInfo) {
        return getCurrentForegroundUserId() == userInfo.id;
    }

    public boolean isForegroundUserEphemeral() {
        return getCurrentForegroundUserInfo().isEphemeral();
    }

    public boolean isForegroundUserGuest() {
        return getCurrentForegroundUserInfo().isGuest();
    }

    public boolean isHeadlessSystemUser() {
        return SystemProperties.getBoolean(HEADLESS_SYSTEM_USER, false);
    }

    public boolean isLastActiveUser(UserInfo userInfo) {
        return userInfo.id == getLastActiveUser();
    }

    public boolean isPersistentUser(int i) {
        return !this.mUserManager.getUserInfo(i).isEphemeral();
    }

    public boolean isSystemUser(UserInfo userInfo) {
        return userInfo.id == 0;
    }

    public boolean isUserLimitReached() {
        int size = getAllUsersExceptGuests().size();
        int maxSupportedUsers = getMaxSupportedUsers();
        if (size <= maxSupportedUsers) {
            return getAllUsersExceptGuests().size() == maxSupportedUsers;
        }
        Log.e(TAG, "There are more users on the device than allowed.");
        return true;
    }

    public void registerOnUsersUpdateListener(OnUsersUpdateListener onUsersUpdateListener) {
        if (onUsersUpdateListener == null) {
            return;
        }
        synchronized (this.mUpdateListeners) {
            if (this.mUpdateListeners.isEmpty()) {
                registerReceiver();
            }
            if (!this.mUpdateListeners.contains(onUsersUpdateListener)) {
                this.mUpdateListeners.add(onUsersUpdateListener);
            }
        }
    }

    public boolean removeUser(UserInfo userInfo, String str) {
        if (isSystemUser(userInfo)) {
            StringBuilder e = a.e("User ");
            e.append(userInfo.id);
            e.append(" is system user, could not be removed.");
            Log.w(TAG, e.toString());
            return false;
        } else if (userInfo.isAdmin() && getAllAdminUsers().size() <= 1) {
            StringBuilder e2 = a.e("User ");
            e2.append(userInfo.id);
            e2.append(" is the last admin user on device.");
            Log.w(TAG, e2.toString());
            return false;
        } else if (!isCurrentProcessAdminUser() && !isCurrentProcessUser(userInfo)) {
            Log.e(TAG, "Non-admins cannot remove other users.");
            return false;
        } else {
            if (userInfo.id == getCurrentForegroundUserId()) {
                startNewGuestSession(str);
            }
            return this.mUserManager.removeUser(userInfo.id);
        }
    }

    public Drawable scaleUserIcon(Bitmap bitmap, int i) {
        return new BitmapDrawable(this.mContext.getResources(), Bitmap.createScaledBitmap(bitmap, i, i, true));
    }

    public void setDefaultBootUser(int i) {
        Settings.Global.putInt(this.mContext.getContentResolver(), CarSettings.Global.DEFAULT_USER_ID_TO_BOOT_INTO, i);
    }

    public void setLastActiveUser(int i, boolean z) {
        this.mLastActiveUser = i;
        if (z) {
            return;
        }
        Settings.Global.putInt(this.mContext.getContentResolver(), CarSettings.Global.LAST_ACTIVE_USER_ID, i);
    }

    public void setUserName(UserInfo userInfo, String str) {
        this.mUserManager.setUserName(userInfo.id, str);
    }

    public void setUserRestriction(UserInfo userInfo, String str, boolean z) {
        this.mUserManager.setUserRestriction(str, z, UserHandle.of(userInfo.id));
    }

    public boolean startNewGuestSession(String str) {
        UserInfo createGuest = this.mUserManager.createGuest(this.mContext, str);
        if (createGuest == null) {
            Log.w(TAG, "can't create user.");
            return false;
        }
        assignDefaultIcon(createGuest);
        return switchToUserId(createGuest.id);
    }

    public boolean switchToUser(UserInfo userInfo) {
        if (userInfo.id == getCurrentForegroundUserId()) {
            return false;
        }
        return switchToUserId(userInfo.id);
    }

    public boolean switchToUserId(int i) {
        if (i == 0 && isHeadlessSystemUser()) {
            return false;
        }
        return this.mActivityManager.switchUser(i);
    }

    public void unregisterOnUsersUpdateListener(OnUsersUpdateListener onUsersUpdateListener) {
        synchronized (this.mUpdateListeners) {
            if (this.mUpdateListeners.contains(onUsersUpdateListener)) {
                this.mUpdateListeners.remove(onUsersUpdateListener);
                if (this.mUpdateListeners.isEmpty()) {
                    unregisterReceiver();
                }
            }
        }
    }
}
