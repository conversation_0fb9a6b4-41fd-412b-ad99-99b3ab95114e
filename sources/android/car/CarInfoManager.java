package android.car;

import android.car.hardware.CarPropertyValue;
import android.car.hardware.property.CarPropertyManager;
import android.os.IBinder;

/* loaded from: classes.dex */
public final class CarInfoManager implements CarManagerBase {
    public static final int BASIC_INFO_EV_BATTERY_CAPACITY = 291504390;
    public static final int BASIC_INFO_EV_CONNECTOR_TYPES = 289472775;
    public static final int BASIC_INFO_FUEL_CAPACITY = 291504388;
    public static final int BASIC_INFO_FUEL_TYPES = 289472773;
    public static final int BASIC_INFO_KEY_MANUFACTURER = 286261505;
    public static final int BASIC_INFO_KEY_MODEL = 286261506;
    public static final int BASIC_INFO_KEY_MODEL_YEAR = 289407235;
    public static final String BASIC_INFO_KEY_VEHICLE_ID = "android.car.vehicle-id";
    public static final boolean DBG = false;
    public static final String INFO_KEY_PRODUCT_CONFIGURATION = "android.car.product-config";
    public static final String TAG = "CarInfoManager";
    public final CarPropertyManager mCarPropertyMgr;

    public CarInfoManager(IBinder iBinder) {
        this.mCarPropertyMgr = new CarPropertyManager(iBinder, null, false, TAG);
    }

    public float getEvBatteryCapacity() throws CarNotConnectedException {
        CarPropertyValue property = this.mCarPropertyMgr.getProperty(Float.class, 291504390, 0);
        if (property != null) {
            return ((Float) property.getValue()).floatValue();
        }
        return 0.0f;
    }

    public int[] getEvConnectorTypes() throws CarNotConnectedException {
        return this.mCarPropertyMgr.getIntArrayProperty(289472775, 0);
    }

    public float getFuelCapacity() throws CarNotConnectedException {
        CarPropertyValue property = this.mCarPropertyMgr.getProperty(Float.class, 291504388, 0);
        if (property != null) {
            return ((Float) property.getValue()).floatValue();
        }
        return 0.0f;
    }

    public int[] getFuelTypes() throws CarNotConnectedException {
        return this.mCarPropertyMgr.getIntArrayProperty(289472773, 0);
    }

    public String getManufacturer() throws CarNotConnectedException {
        CarPropertyValue property = this.mCarPropertyMgr.getProperty(String.class, 286261505, 0);
        if (property != null) {
            return (String) property.getValue();
        }
        return null;
    }

    public String getModel() throws CarNotConnectedException {
        CarPropertyValue property = this.mCarPropertyMgr.getProperty(String.class, 286261506, 0);
        if (property != null) {
            return (String) property.getValue();
        }
        return null;
    }

    public String getModelYear() throws CarNotConnectedException {
        CarPropertyValue property = this.mCarPropertyMgr.getProperty(String.class, 289407235, 0);
        if (property != null) {
            return (String) property.getValue();
        }
        return null;
    }

    public String getVehicleId() throws CarNotConnectedException {
        return "";
    }

    @Override // android.car.CarManagerBase
    public void onCarDisconnected() {
        this.mCarPropertyMgr.onCarDisconnected();
    }
}
