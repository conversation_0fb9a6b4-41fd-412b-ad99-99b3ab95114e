package android.car.drivingstate;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: classes.dex */
public interface ICarUxRestrictionsChangeListener extends IInterface {

    /* loaded from: classes.dex */
    public static abstract class Stub extends Binder implements ICarUxRestrictionsChangeListener {
        public static final String DESCRIPTOR = "android.car.drivingstate.ICarUxRestrictionsChangeListener";
        public static final int TRANSACTION_onUxRestrictionsChanged = 1;

        /* loaded from: classes.dex */
        public static class Proxy implements ICarUxRestrictionsChangeListener {
            public IBinder mRemote;

            public Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            @Override // android.car.drivingstate.ICarUxRestrictionsChangeListener
            public void onUxRestrictionsChanged(CarUxRestrictions carUxRestrictions) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (carUxRestrictions != null) {
                        obtain.writeInt(1);
                        carUxRestrictions.writeToParcel(obtain, 0);
                    } else {
                        obtain.writeInt(0);
                    }
                    this.mRemote.transact(1, obtain, null, 1);
                } finally {
                    obtain.recycle();
                }
            }
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static ICarUxRestrictionsChangeListener asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof ICarUxRestrictionsChangeListener)) {
                return (ICarUxRestrictionsChangeListener) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i == 1) {
                parcel.enforceInterface(DESCRIPTOR);
                onUxRestrictionsChanged(parcel.readInt() != 0 ? CarUxRestrictions.CREATOR.createFromParcel(parcel) : null);
                return true;
            } else if (i != 1598968902) {
                return super.onTransact(i, parcel, parcel2, i2);
            } else {
                parcel2.writeString(DESCRIPTOR);
                return true;
            }
        }
    }

    void onUxRestrictionsChanged(CarUxRestrictions carUxRestrictions) throws RemoteException;
}
