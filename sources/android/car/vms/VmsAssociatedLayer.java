package android.car.vms;

import android.os.Parcel;
import android.os.Parcelable;
import b.a.b.a.a;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/* loaded from: classes.dex */
public final class VmsAssociatedLayer implements Parcelable {
    public static final Parcelable.Creator<VmsAssociatedLayer> CREATOR = new Parcelable.Creator<VmsAssociatedLayer>() { // from class: android.car.vms.VmsAssociatedLayer.1
        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public VmsAssociatedLayer createFromParcel(Parcel parcel) {
            return new VmsAssociatedLayer(parcel);
        }

        /* JADX WARN: Can't rename method to resolve collision */
        @Override // android.os.Parcelable.Creator
        public VmsAssociatedLayer[] newArray(int i) {
            return new VmsAssociatedLayer[i];
        }
    };
    public final VmsLayer mLayer;
    public final Set<Integer> mPublisherIds;

    @Override // android.os.Parcelable
    public int describeContents() {
        return 0;
    }

    public boolean equals(Object obj) {
        if (obj instanceof VmsAssociatedLayer) {
            VmsAssociatedLayer vmsAssociatedLayer = (VmsAssociatedLayer) obj;
            return Objects.equals(vmsAssociatedLayer.mLayer, this.mLayer) && vmsAssociatedLayer.mPublisherIds.equals(this.mPublisherIds);
        }
        return false;
    }

    public Set<Integer> getPublisherIds() {
        return this.mPublisherIds;
    }

    public VmsLayer getVmsLayer() {
        return this.mLayer;
    }

    public int hashCode() {
        return Objects.hash(this.mLayer, this.mPublisherIds);
    }

    public String toString() {
        StringBuilder e = a.e("VmsAssociatedLayer{ VmsLayer: ");
        e.append(this.mLayer);
        e.append(", Publishers: ");
        e.append(this.mPublisherIds);
        e.append("}");
        return e.toString();
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeParcelable(this.mLayer, i);
        parcel.writeArray(this.mPublisherIds.toArray());
    }

    public VmsAssociatedLayer(VmsLayer vmsLayer, Set<Integer> set) {
        this.mLayer = vmsLayer;
        this.mPublisherIds = Collections.unmodifiableSet(set);
    }

    public VmsAssociatedLayer(Parcel parcel) {
        this.mLayer = (VmsLayer) parcel.readParcelable(VmsLayer.class.getClassLoader());
        Object[] readArray = parcel.readArray(Integer.class.getClassLoader());
        this.mPublisherIds = Collections.unmodifiableSet(new HashSet(Arrays.asList((Integer[]) Arrays.copyOf(readArray, readArray.length, Integer[].class))));
    }
}
