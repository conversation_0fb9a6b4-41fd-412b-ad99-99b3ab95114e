package android.car.hardware;

import android.car.CarApiUtil;
import android.car.CarLibLog;
import android.car.CarManagerBase;
import android.car.CarNotConnectedException;
import android.car.hardware.property.CarPropertyManager;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.RemoteException;
import android.util.ArraySet;
import android.util.Log;
import b.a.b.a.a;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.ref.WeakReference;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

/* loaded from: classes.dex */
public final class CarSensorManager implements CarManagerBase {
    public static final boolean DBG = false;
    public static final int INDEX_WHEEL_DISTANCE_ENABLE_FLAG = 0;
    public static final int INDEX_WHEEL_DISTANCE_FRONT_LEFT = 1;
    public static final int INDEX_WHEEL_DISTANCE_FRONT_RIGHT = 2;
    public static final int INDEX_WHEEL_DISTANCE_REAR_LEFT = 4;
    public static final int INDEX_WHEEL_DISTANCE_REAR_RIGHT = 3;
    public static final int SENSOR_RATE_FAST = 10;
    public static final int SENSOR_RATE_FASTEST = 100;
    public static final int SENSOR_RATE_NORMAL = 1;
    public static final int SENSOR_RATE_UI = 5;
    public static final int SENSOR_TYPE_ABS_ACTIVE = 287310858;
    public static final int SENSOR_TYPE_ADAPTIVE_CRUISE_CONTROL_ENGAGED = 555778086;
    public static final int SENSOR_TYPE_BRAKE_PEDAL_POSITION = 559972393;
    public static final int SENSOR_TYPE_CAR_SPEED = 291504647;
    public static final int SENSOR_TYPE_CURRENT_GEAR = 289408001;
    public static final int SENSOR_TYPE_ENGINE_OIL_LEVEL = 289407747;
    public static final int SENSOR_TYPE_ENGINE_RUNNING = 555778049;
    public static final int SENSOR_TYPE_ENV_OUTSIDE_TEMPERATURE = 291505923;
    public static final int SENSOR_TYPE_EV_BATTERY_CHARGE_RATE = 291504908;
    public static final int SENSOR_TYPE_EV_BATTERY_LEVEL = 291504905;
    public static final int SENSOR_TYPE_EV_CHARGE_PORT_CONNECTED = 287310603;
    public static final int SENSOR_TYPE_EV_CHARGE_PORT_OPEN = 287310602;
    public static final int SENSOR_TYPE_FUEL_DOOR_OPEN = 287310600;
    public static final int SENSOR_TYPE_FUEL_LEVEL = 291504903;
    public static final int SENSOR_TYPE_GEAR = 289408000;
    public static final int SENSOR_TYPE_GEAR_SELECTION = 289408000;
    public static final int SENSOR_TYPE_IGNITION_STATE = 289408009;
    public static final int SENSOR_TYPE_NIGHT = 287310855;
    public static final int SENSOR_TYPE_ODOMETER = 291504644;
    public static final int SENSOR_TYPE_PARKING_BRAKE = 287310850;
    public static final int SENSOR_TYPE_PERF_ODOMETER = 291504644;
    public static final int SENSOR_TYPE_PERF_VEHICLE_SPEED = 291504647;
    public static final int SENSOR_TYPE_PROPID_TEMP_AVERAGE_FUEL_CONSUMPTION = 559972405;
    public static final int SENSOR_TYPE_PROPID_TEMP_REMAIN_MILEAGE = 557875255;
    public static final int SENSOR_TYPE_PROPID_TEMP_VEHICLE_GEAR = 557875254;
    public static final int SENSOR_TYPE_RESERVED1 = 1;
    public static final int SENSOR_TYPE_RESERVED10 = 10;
    public static final int SENSOR_TYPE_RESERVED11 = 11;
    public static final int SENSOR_TYPE_RESERVED12 = 12;
    public static final int SENSOR_TYPE_RESERVED13 = 13;
    public static final int SENSOR_TYPE_RESERVED14 = 14;
    public static final int SENSOR_TYPE_RESERVED15 = 15;
    public static final int SENSOR_TYPE_RESERVED16 = 16;
    public static final int SENSOR_TYPE_RESERVED17 = 17;
    public static final int SENSOR_TYPE_RESERVED18 = 18;
    public static final int SENSOR_TYPE_RESERVED19 = 19;
    public static final int SENSOR_TYPE_RESERVED20 = 20;
    public static final int SENSOR_TYPE_RESERVED21 = 21;
    public static final int SENSOR_TYPE_RESERVED26 = 26;
    public static final int SENSOR_TYPE_RESERVED8 = 8;
    public static final int SENSOR_TYPE_RPM = 291504901;
    public static final int SENSOR_TYPE_TIRE_PRESSURE = 392168201;
    public static final int SENSOR_TYPE_TPMS_STATUS_LF = 557875202;
    public static final int SENSOR_TYPE_TPMS_STATUS_LR = 557875203;
    public static final int SENSOR_TYPE_TPMS_STATUS_RF = 557875204;
    public static final int SENSOR_TYPE_TPMS_STATUS_RR = 557875205;
    public static final int SENSOR_TYPE_TRACTION_CONTROL_ACTIVE = 287310859;
    public static final int SENSOR_TYPE_WHEEL_TICK_DISTANCE = 290521862;
    public static final String TAG = "CarSensorManager";
    public static final int WHEEL_TICK_DISTANCE_BUNDLE_SIZE = 6;
    public final CarPropertyManager mCarPropertyMgr;
    public final ArraySet<Integer> mSensorConfigIds = new ArraySet<>(Arrays.asList(291504647, 291504901, 291504644, 291504903, 287310850, 289408000, 287310855, 291505923, 289408009, 290521862, 287310858, 287310859, 287310600, 291504905, 287310602, 287310603, 291504908, 289407747, 291504644, 291504647, 555778086, 559972393, 559972405, 557875254, 557875255, 555778049, 289408000, 289408001, 557875202, 557875203, 557875204, 557875205, 392168201));
    public CarPropertyEventListenerToBase mCarPropertyEventListener = null;
    public final HashMap<OnSensorChangedListener, CarPropertyEventListenerToBase> mListenerMap = new HashMap<>();

    /* loaded from: classes.dex */
    public static class CarPropertyEventListenerToBase implements CarPropertyManager.CarPropertyEventListener {
        public final OnSensorChangedListener mListener;
        public final WeakReference<CarSensorManager> mManager;

        public CarPropertyEventListenerToBase(CarSensorManager carSensorManager, OnSensorChangedListener onSensorChangedListener) {
            this.mManager = new WeakReference<>(carSensorManager);
            this.mListener = onSensorChangedListener;
        }

        @Override // android.car.hardware.property.CarPropertyManager.CarPropertyEventListener
        public void onChangeEvent(CarPropertyValue carPropertyValue) {
            CarSensorManager carSensorManager = this.mManager.get();
            if (carSensorManager != null) {
                carSensorManager.handleOnChangeEvent(carPropertyValue, this.mListener);
            }
        }

        @Override // android.car.hardware.property.CarPropertyManager.CarPropertyEventListener
        public void onErrorEvent(int i, int i2) {
        }
    }

    /* loaded from: classes.dex */
    public interface OnSensorChangedListener {
        void onSensorChanged(CarSensorEvent carSensorEvent);
    }

    @Retention(RetentionPolicy.SOURCE)
    /* loaded from: classes.dex */
    public @interface SensorRate {
    }

    @Retention(RetentionPolicy.SOURCE)
    /* loaded from: classes.dex */
    public @interface SensorType {
    }

    public CarSensorManager(IBinder iBinder, Context context, Handler handler) {
        this.mCarPropertyMgr = new CarPropertyManager(iBinder, handler, false, TAG);
    }

    private CarSensorEvent createCarSensorEvent(CarPropertyValue carPropertyValue) {
        if (carPropertyValue == null) {
            Log.e(CarLibLog.TAG_SENSOR, "propertyValue is null");
            return null;
        }
        int propertyId = carPropertyValue.getPropertyId() & 16711680;
        if (propertyId == 2097152) {
            CarSensorEvent carSensorEvent = new CarSensorEvent(carPropertyValue.getPropertyId(), carPropertyValue.getTimestamp(), 0, 1, 0);
            carSensorEvent.intValues[0] = ((Boolean) carPropertyValue.getValue()).booleanValue() ? 1 : 0;
            return carSensorEvent;
        } else if (propertyId == 4194304) {
            CarSensorEvent carSensorEvent2 = new CarSensorEvent(carPropertyValue.getPropertyId(), carPropertyValue.getTimestamp(), 0, 1, 0);
            carSensorEvent2.intValues[0] = ((Integer) carPropertyValue.getValue()).intValue();
            return carSensorEvent2;
        } else if (propertyId == 5308416) {
            Object[] objArr = (Object[]) carPropertyValue.getValue();
            CarSensorEvent carSensorEvent3 = new CarSensorEvent(carPropertyValue.getPropertyId(), carPropertyValue.getTimestamp(), 0, 0, objArr.length);
            for (int i = 0; i < objArr.length; i++) {
                carSensorEvent3.longValues[i] = ((Long) objArr[i]).longValue();
            }
            return carSensorEvent3;
        } else if (propertyId != 6291456) {
            StringBuilder e = a.e("unhandled VehiclePropertyType for propId=");
            e.append(carPropertyValue.getPropertyId());
            Log.e(TAG, e.toString());
            return null;
        } else {
            CarSensorEvent carSensorEvent4 = new CarSensorEvent(carPropertyValue.getPropertyId(), carPropertyValue.getTimestamp(), 1, 0, 0);
            carSensorEvent4.floatValues[0] = ((Float) carPropertyValue.getValue()).floatValue();
            return carSensorEvent4;
        }
    }

    private Bundle createWheelDistanceTickBundle(List<Integer> list) {
        Bundle bundle = new Bundle(6);
        bundle.putInt(CarSensorConfig.WHEEL_TICK_DISTANCE_SUPPORTED_WHEELS, list.get(0).intValue());
        bundle.putInt(CarSensorConfig.WHEEL_TICK_DISTANCE_FRONT_LEFT_UM_PER_TICK, list.get(1).intValue());
        bundle.putInt(CarSensorConfig.WHEEL_TICK_DISTANCE_FRONT_RIGHT_UM_PER_TICK, list.get(2).intValue());
        bundle.putInt(CarSensorConfig.WHEEL_TICK_DISTANCE_REAR_RIGHT_UM_PER_TICK, list.get(3).intValue());
        bundle.putInt(CarSensorConfig.WHEEL_TICK_DISTANCE_REAR_LEFT_UM_PER_TICK, list.get(4).intValue());
        return bundle;
    }

    private void handleCarServiceRemoteExceptionAndThrow(RemoteException remoteException) throws CarNotConnectedException {
        if (Log.isLoggable(CarLibLog.TAG_SENSOR, 4)) {
            StringBuilder e = a.e("RemoteException from car service:");
            e.append(remoteException.getMessage());
            Log.i(CarLibLog.TAG_SENSOR, e.toString());
        }
        throw new CarNotConnectedException();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void handleOnChangeEvent(CarPropertyValue carPropertyValue, OnSensorChangedListener onSensorChangedListener) {
        synchronized (this.mListenerMap) {
            onSensorChangedListener.onSensorChanged(createCarSensorEvent(carPropertyValue));
        }
    }

    private void handleOnErrorEvent(int i, int i2) {
    }

    public CarSensorEvent getLatestSensorEvent(int i) throws CarNotConnectedException {
        try {
            return createCarSensorEvent(this.mCarPropertyMgr.getProperty(i, 0));
        } catch (IllegalStateException e) {
            CarApiUtil.checkCarNotConnectedExceptionFromCarService(e);
            return null;
        }
    }

    public List<CarPropertyConfig> getPropertyList() throws CarNotConnectedException {
        return this.mCarPropertyMgr.getPropertyList(this.mSensorConfigIds);
    }

    public int getPropertyStatus(int i, int i2) throws CarNotConnectedException {
        CarPropertyValue property = this.mCarPropertyMgr.getProperty(i, i2);
        if (property != null) {
            return property.getStatus();
        }
        return 2;
    }

    public CarSensorConfig getSensorConfig(int i) throws CarNotConnectedException {
        Bundle bundle;
        if (i != 290521862) {
            bundle = Bundle.EMPTY;
        } else {
            Iterator<CarPropertyConfig> it = this.mCarPropertyMgr.getPropertyList().iterator();
            while (true) {
                if (!it.hasNext()) {
                    bundle = null;
                    break;
                }
                CarPropertyConfig next = it.next();
                if (next.getPropertyId() == i) {
                    bundle = createWheelDistanceTickBundle(next.getConfigArray());
                    break;
                }
            }
        }
        return new CarSensorConfig(i, bundle);
    }

    public int[] getSupportedSensors() throws CarNotConnectedException {
        try {
            List<CarPropertyConfig> propertyList = getPropertyList();
            int size = propertyList.size();
            int[] iArr = new int[size];
            for (int i = 0; i < size; i++) {
                iArr[i] = propertyList.get(i).getPropertyId();
            }
            return iArr;
        } catch (IllegalStateException e) {
            CarApiUtil.checkCarNotConnectedExceptionFromCarService(e);
            return new int[0];
        }
    }

    public boolean isSensorSupported(int i) throws CarNotConnectedException {
        for (int i2 : getSupportedSensors()) {
            if (i == i2) {
                return true;
            }
        }
        return false;
    }

    @Override // android.car.CarManagerBase
    public void onCarDisconnected() {
        synchronized (this.mListenerMap) {
            this.mListenerMap.clear();
        }
        this.mCarPropertyMgr.onCarDisconnected();
    }

    public boolean registerListener(OnSensorChangedListener onSensorChangedListener, int i, int i2) throws CarNotConnectedException, IllegalArgumentException {
        if (i2 != 100 && i2 != 1 && i2 != 5 && i2 != 10) {
            throw new IllegalArgumentException(a.u("wrong rate ", i2));
        }
        if (this.mListenerMap.get(onSensorChangedListener) == null) {
            this.mCarPropertyEventListener = new CarPropertyEventListenerToBase(this, onSensorChangedListener);
        } else {
            this.mCarPropertyEventListener = this.mListenerMap.get(onSensorChangedListener);
        }
        if (this.mCarPropertyMgr.registerListener(this.mCarPropertyEventListener, i, i2)) {
            this.mListenerMap.put(onSensorChangedListener, this.mCarPropertyEventListener);
            return true;
        }
        return false;
    }

    public void unregisterListener(OnSensorChangedListener onSensorChangedListener) {
        synchronized (this.mListenerMap) {
            CarPropertyEventListenerToBase carPropertyEventListenerToBase = this.mListenerMap.get(onSensorChangedListener);
            this.mCarPropertyEventListener = carPropertyEventListenerToBase;
            this.mCarPropertyMgr.unregisterListener(carPropertyEventListenerToBase);
            this.mListenerMap.remove(onSensorChangedListener);
        }
    }

    public static boolean isSensorSupported(int[] iArr, int i) {
        for (int i2 : iArr) {
            if (i == i2) {
                return true;
            }
        }
        return false;
    }

    public void unregisterListener(OnSensorChangedListener onSensorChangedListener, int i) {
        CarPropertyEventListenerToBase carPropertyEventListenerToBase;
        synchronized (this.mListenerMap) {
            carPropertyEventListenerToBase = this.mListenerMap.get(onSensorChangedListener);
            this.mCarPropertyEventListener = carPropertyEventListenerToBase;
        }
        this.mCarPropertyMgr.unregisterListener(carPropertyEventListenerToBase, i);
    }
}
